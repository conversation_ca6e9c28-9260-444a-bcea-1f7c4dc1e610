// Mocks generated by Mockito 5.4.5 from annotations
// in onboard_wallet/test/helpers/test_helpers.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i18;
import 'dart:math' as _i11;
import 'dart:typed_data' as _i23;
import 'dart:ui' as _i19;

import 'package:crypton/crypton.dart' as _i14;
import 'package:dio/dio.dart' as _i29;
import 'package:eth_sig_util/eth_sig_util.dart' as _i22;
import 'package:firebase_analytics/firebase_analytics.dart' as _i3;
import 'package:firebase_messaging/firebase_messaging.dart' as _i31;
import 'package:flutter/material.dart' as _i15;
import 'package:flutter/services.dart' as _i10;
import 'package:flutter_inappwebview/flutter_inappwebview.dart' as _i27;
import 'package:hive_flutter/hive_flutter.dart' as _i8;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i17;
import 'package:nestcoinco_core_crypto_public_api/nestcoinco_core_crypto_public_api.dart'
    as _i28;
import 'package:nestcoinco_core_data_encryptor/nestcoinco_core_data_encryptor.dart'
    as _i36;
import 'package:nestcoinco_onboard_api_gateway/nestcoinco_onboard_api_gateway.dart'
    as _i12;
import 'package:nestcoinco_onboard_bridge_integration/nestcoinco_onboard_bridge_integration.dart'
    as _i42;
import 'package:nestcoinco_onboard_defi_bridge/nestcoinco_onboard_defi_bridge.dart'
    as _i37;
import 'package:onboard_wallet/api/onboard_exception.dart' as _i48;
import 'package:onboard_wallet/app/index.dart' as _i38;
import 'package:onboard_wallet/enums/backup_type.dart' as _i21;
import 'package:onboard_wallet/enums/enums.dart' as _i47;
import 'package:onboard_wallet/models/card/local_card_transaction_config/local_card_transaction_config.dart'
    as _i46;
import 'package:onboard_wallet/models/card/local_card_wallet_package/local_card_wallet_package.dart'
    as _i43;
import 'package:onboard_wallet/models/country.dart' as _i24;
import 'package:onboard_wallet/models/data_point/local_portfolio_prices.dart'
    as _i39;
import 'package:onboard_wallet/models/local/checklist_dto/local_checklist_dto.dart'
    as _i26;
import 'package:onboard_wallet/models/models.dart' as _i4;
import 'package:onboard_wallet/models/onboard_accounts_enum.dart' as _i32;
import 'package:onboard_wallet/models/token/local_token_market_data.dart'
    as _i30;
import 'package:onboard_wallet/models/token/wallet_type.dart' as _i33;
import 'package:onboard_wallet/models/virtual_account/local_virtual_account/local_virtual_account.dart'
    as _i44;
import 'package:onboard_wallet/models/virtual_account/local_virtual_account/local_virtual_account_usage_terms.dart'
    as _i45;
import 'package:onboard_wallet/models/virtual_account/transaction/local_virtual_account_transaction.dart'
    as _i49;
import 'package:onboard_wallet/services/services.dart' as _i7;
import 'package:onboard_wallet/ui/views/dapp_tabs/widgets/widgets.dart' as _i40;
import 'package:onboard_wallet/ui/views/fund_options/fund_options_viewmodel.dart'
    as _i34;
import 'package:onboard_wallet/ui/widgets/cards/onbw_checklist_card.dart'
    as _i25;
import 'package:passkeys/types.dart' as _i41;
import 'package:reown_walletkit/reown_walletkit.dart' as _i35;
import 'package:segment_analytics/analytics.dart' as _i2;
import 'package:stacked/stacked.dart' as _i9;
import 'package:stacked_services/stacked_services.dart' as _i16;
import 'package:tuple/tuple.dart' as _i13;
import 'package:web3auth_flutter/enums.dart' as _i20;
import 'package:web3auth_flutter/output.dart' as _i6;
import 'package:web3dart/web3dart.dart' as _i5;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAnalytics_0 extends _i1.SmartFake implements _i2.Analytics {
  _FakeAnalytics_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeFirebaseAnalyticsObserver_1 extends _i1.SmartFake
    implements _i3.FirebaseAnalyticsObserver {
  _FakeFirebaseAnalyticsObserver_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeResult_2<T> extends _i1.SmartFake implements _i4.Result<T> {
  _FakeResult_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeEthereumAddress_3 extends _i1.SmartFake
    implements _i5.EthereumAddress {
  _FakeEthereumAddress_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeWeb3Client_4 extends _i1.SmartFake implements _i5.Web3Client {
  _FakeWeb3Client_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDeployedContract_5 extends _i1.SmartFake
    implements _i5.DeployedContract {
  _FakeDeployedContract_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeEtherAmount_6 extends _i1.SmartFake implements _i5.EtherAmount {
  _FakeEtherAmount_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeTransaction_7 extends _i1.SmartFake implements _i5.Transaction {
  _FakeTransaction_7(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeWeb3AuthResponse_8 extends _i1.SmartFake
    implements _i6.Web3AuthResponse {
  _FakeWeb3AuthResponse_8(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDatabaseService_9 extends _i1.SmartFake
    implements _i7.DatabaseService {
  _FakeDatabaseService_9(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeBox_10<E> extends _i1.SmartFake implements _i8.Box<E> {
  _FakeBox_10(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeReactiveValue_11<T> extends _i1.SmartFake
    implements _i9.ReactiveValue<T> {
  _FakeReactiveValue_11(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeMethodChannel_12 extends _i1.SmartFake
    implements _i10.MethodChannel {
  _FakeMethodChannel_12(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeRandom_13 extends _i1.SmartFake implements _i11.Random {
  _FakeRandom_13(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeWalletsSwapClient_14 extends _i1.SmartFake
    implements _i12.WalletsSwapClient {
  _FakeWalletsSwapClient_14(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeTuple2_15<T1, T2> extends _i1.SmartFake
    implements _i13.Tuple2<T1, T2> {
  _FakeTuple2_15(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDuration_16 extends _i1.SmartFake implements Duration {
  _FakeDuration_16(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeWalletService_17 extends _i1.SmartFake implements _i7.WalletService {
  _FakeWalletService_17(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeRSAKeypair_18 extends _i1.SmartFake implements _i14.RSAKeypair {
  _FakeRSAKeypair_18(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDirectAccountsAccountManagementClient_19 extends _i1.SmartFake
    implements _i12.DirectAccountsAccountManagementClient {
  _FakeDirectAccountsAccountManagementClient_19(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDirectAccountsDepositsClient_20 extends _i1.SmartFake
    implements _i12.DirectAccountsDepositsClient {
  _FakeDirectAccountsDepositsClient_20(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDirectAccountsTransactionsClient_21 extends _i1.SmartFake
    implements _i12.DirectAccountsTransactionsClient {
  _FakeDirectAccountsTransactionsClient_21(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeFocusNode_22 extends _i1.SmartFake implements _i15.FocusNode {
  _FakeFocusNode_22(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );

  @override
  String toString(
          {_i15.DiagnosticLevel? minLevel = _i15.DiagnosticLevel.info}) =>
      super.toString();
}

class _FakeReactiveList_23<E> extends _i1.SmartFake
    implements _i9.ReactiveList<E> {
  _FakeReactiveList_23(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakePreferenceService_24 extends _i1.SmartFake
    implements _i7.PreferenceService {
  _FakePreferenceService_24(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [NavigationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNavigationService extends _i1.Mock implements _i16.NavigationService {
  @override
  String get previousRoute => (super.noSuchMethod(
        Invocation.getter(#previousRoute),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#previousRoute),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#previousRoute),
        ),
      ) as String);

  @override
  String get currentRoute => (super.noSuchMethod(
        Invocation.getter(#currentRoute),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#currentRoute),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#currentRoute),
        ),
      ) as String);

  @override
  _i15.GlobalKey<_i15.NavigatorState>? nestedNavigationKey(int? index) =>
      (super.noSuchMethod(
        Invocation.method(
          #nestedNavigationKey,
          [index],
        ),
        returnValueForMissingStub: null,
      ) as _i15.GlobalKey<_i15.NavigatorState>?);

  @override
  void config({
    bool? enableLog,
    bool? defaultPopGesture,
    bool? defaultOpaqueRoute,
    Duration? defaultDurationTransition,
    bool? defaultGlobalState,
    _i16.Transition? defaultTransitionStyle,
    String? defaultTransition,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #config,
          [],
          {
            #enableLog: enableLog,
            #defaultPopGesture: defaultPopGesture,
            #defaultOpaqueRoute: defaultOpaqueRoute,
            #defaultDurationTransition: defaultDurationTransition,
            #defaultGlobalState: defaultGlobalState,
            #defaultTransitionStyle: defaultTransitionStyle,
            #defaultTransition: defaultTransition,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i18.Future<T?>? navigateWithTransition<T>(
    _i15.Widget? page, {
    bool? opaque,
    String? transition = '',
    Duration? duration,
    bool? popGesture,
    int? id,
    _i15.Curve? curve,
    bool? fullscreenDialog = false,
    bool? preventDuplicates = true,
    _i16.Transition? transitionClass,
    _i16.Transition? transitionStyle,
    String? routeName,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #navigateWithTransition,
          [page],
          {
            #opaque: opaque,
            #transition: transition,
            #duration: duration,
            #popGesture: popGesture,
            #id: id,
            #curve: curve,
            #fullscreenDialog: fullscreenDialog,
            #preventDuplicates: preventDuplicates,
            #transitionClass: transitionClass,
            #transitionStyle: transitionStyle,
            #routeName: routeName,
          },
        ),
        returnValueForMissingStub: null,
      ) as _i18.Future<T?>?);

  @override
  _i18.Future<T?>? replaceWithTransition<T>(
    _i15.Widget? page, {
    bool? opaque,
    String? transition = '',
    Duration? duration,
    bool? popGesture,
    int? id,
    _i15.Curve? curve,
    bool? fullscreenDialog = false,
    bool? preventDuplicates = true,
    _i16.Transition? transitionClass,
    _i16.Transition? transitionStyle,
    String? routeName,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #replaceWithTransition,
          [page],
          {
            #opaque: opaque,
            #transition: transition,
            #duration: duration,
            #popGesture: popGesture,
            #id: id,
            #curve: curve,
            #fullscreenDialog: fullscreenDialog,
            #preventDuplicates: preventDuplicates,
            #transitionClass: transitionClass,
            #transitionStyle: transitionStyle,
            #routeName: routeName,
          },
        ),
        returnValueForMissingStub: null,
      ) as _i18.Future<T?>?);

  @override
  bool back<T>({
    dynamic result,
    int? id,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #back,
          [],
          {
            #result: result,
            #id: id,
          },
        ),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  void popUntil(
    _i15.RoutePredicate? predicate, {
    int? id,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #popUntil,
          [predicate],
          {#id: id},
        ),
        returnValueForMissingStub: null,
      );

  @override
  void popRepeated(int? popTimes) => super.noSuchMethod(
        Invocation.method(
          #popRepeated,
          [popTimes],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i18.Future<T?>? navigateTo<T>(
    String? routeName, {
    dynamic arguments,
    int? id,
    bool? preventDuplicates = true,
    Map<String, String>? parameters,
    _i15.RouteTransitionsBuilder? transition,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #navigateTo,
          [routeName],
          {
            #arguments: arguments,
            #id: id,
            #preventDuplicates: preventDuplicates,
            #parameters: parameters,
            #transition: transition,
          },
        ),
        returnValueForMissingStub: null,
      ) as _i18.Future<T?>?);

  @override
  _i18.Future<T?>? navigateToView<T>(
    _i15.Widget? view, {
    dynamic arguments,
    int? id,
    bool? opaque,
    _i15.Curve? curve,
    Duration? duration,
    bool? fullscreenDialog = false,
    bool? popGesture,
    bool? preventDuplicates = true,
    _i16.Transition? transition,
    _i16.Transition? transitionStyle,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #navigateToView,
          [view],
          {
            #arguments: arguments,
            #id: id,
            #opaque: opaque,
            #curve: curve,
            #duration: duration,
            #fullscreenDialog: fullscreenDialog,
            #popGesture: popGesture,
            #preventDuplicates: preventDuplicates,
            #transition: transition,
            #transitionStyle: transitionStyle,
          },
        ),
        returnValueForMissingStub: null,
      ) as _i18.Future<T?>?);

  @override
  _i18.Future<T?>? replaceWith<T>(
    String? routeName, {
    dynamic arguments,
    int? id,
    bool? preventDuplicates = true,
    Map<String, String>? parameters,
    _i15.RouteTransitionsBuilder? transition,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #replaceWith,
          [routeName],
          {
            #arguments: arguments,
            #id: id,
            #preventDuplicates: preventDuplicates,
            #parameters: parameters,
            #transition: transition,
          },
        ),
        returnValueForMissingStub: null,
      ) as _i18.Future<T?>?);

  @override
  _i18.Future<T?>? clearStackAndShow<T>(
    String? routeName, {
    dynamic arguments,
    int? id,
    Map<String, String>? parameters,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #clearStackAndShow,
          [routeName],
          {
            #arguments: arguments,
            #id: id,
            #parameters: parameters,
          },
        ),
        returnValueForMissingStub: null,
      ) as _i18.Future<T?>?);

  @override
  _i18.Future<T?>? clearStackAndShowView<T>(
    _i15.Widget? view, {
    dynamic arguments,
    int? id,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #clearStackAndShowView,
          [view],
          {
            #arguments: arguments,
            #id: id,
          },
        ),
        returnValueForMissingStub: null,
      ) as _i18.Future<T?>?);

  @override
  _i18.Future<T?>? clearTillFirstAndShow<T>(
    String? routeName, {
    dynamic arguments,
    int? id,
    bool? preventDuplicates = true,
    Map<String, String>? parameters,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #clearTillFirstAndShow,
          [routeName],
          {
            #arguments: arguments,
            #id: id,
            #preventDuplicates: preventDuplicates,
            #parameters: parameters,
          },
        ),
        returnValueForMissingStub: null,
      ) as _i18.Future<T?>?);

  @override
  _i18.Future<T?>? clearTillFirstAndShowView<T>(
    _i15.Widget? view, {
    dynamic arguments,
    int? id,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #clearTillFirstAndShowView,
          [view],
          {
            #arguments: arguments,
            #id: id,
          },
        ),
        returnValueForMissingStub: null,
      ) as _i18.Future<T?>?);

  @override
  _i18.Future<T?>? pushNamedAndRemoveUntil<T>(
    String? routeName, {
    _i15.RoutePredicate? predicate,
    dynamic arguments,
    int? id,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #pushNamedAndRemoveUntil,
          [routeName],
          {
            #predicate: predicate,
            #arguments: arguments,
            #id: id,
          },
        ),
        returnValueForMissingStub: null,
      ) as _i18.Future<T?>?);
}

/// A class which mocks [BottomSheetService].
///
/// See the documentation for Mockito's code generation for more information.
class MockBottomSheetService extends _i1.Mock
    implements _i16.BottomSheetService {
  @override
  void setCustomSheetBuilders(Map<dynamic, _i16.SheetBuilder>? builders) =>
      super.noSuchMethod(
        Invocation.method(
          #setCustomSheetBuilders,
          [builders],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i18.Future<_i16.SheetResponse<dynamic>?> showBottomSheet({
    required String? title,
    String? description,
    String? confirmButtonTitle = 'Ok',
    String? cancelButtonTitle,
    bool? enableDrag = true,
    bool? barrierDismissible = true,
    bool? isScrollControlled = false,
    Duration? exitBottomSheetDuration,
    Duration? enterBottomSheetDuration,
    bool? ignoreSafeArea,
    bool? useRootNavigator = false,
    double? elevation = 1.0,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #showBottomSheet,
          [],
          {
            #title: title,
            #description: description,
            #confirmButtonTitle: confirmButtonTitle,
            #cancelButtonTitle: cancelButtonTitle,
            #enableDrag: enableDrag,
            #barrierDismissible: barrierDismissible,
            #isScrollControlled: isScrollControlled,
            #exitBottomSheetDuration: exitBottomSheetDuration,
            #enterBottomSheetDuration: enterBottomSheetDuration,
            #ignoreSafeArea: ignoreSafeArea,
            #useRootNavigator: useRootNavigator,
            #elevation: elevation,
          },
        ),
        returnValue: _i18.Future<_i16.SheetResponse<dynamic>?>.value(),
        returnValueForMissingStub:
            _i18.Future<_i16.SheetResponse<dynamic>?>.value(),
      ) as _i18.Future<_i16.SheetResponse<dynamic>?>);

  @override
  _i18.Future<_i16.SheetResponse<T>?> showCustomSheet<T, R>({
    dynamic variant,
    String? title,
    String? description,
    bool? hasImage = false,
    String? imageUrl,
    bool? showIconInMainButton = false,
    String? mainButtonTitle,
    bool? showIconInSecondaryButton = false,
    String? secondaryButtonTitle,
    bool? showIconInAdditionalButton = false,
    String? additionalButtonTitle,
    bool? takesInput = false,
    _i19.Color? barrierColor = const _i19.Color(2315255808),
    double? elevation = 1.0,
    bool? barrierDismissible = true,
    bool? isScrollControlled = false,
    String? barrierLabel = '',
    dynamic customData,
    R? data,
    bool? enableDrag = true,
    Duration? exitBottomSheetDuration,
    Duration? enterBottomSheetDuration,
    bool? ignoreSafeArea,
    bool? useRootNavigator = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #showCustomSheet,
          [],
          {
            #variant: variant,
            #title: title,
            #description: description,
            #hasImage: hasImage,
            #imageUrl: imageUrl,
            #showIconInMainButton: showIconInMainButton,
            #mainButtonTitle: mainButtonTitle,
            #showIconInSecondaryButton: showIconInSecondaryButton,
            #secondaryButtonTitle: secondaryButtonTitle,
            #showIconInAdditionalButton: showIconInAdditionalButton,
            #additionalButtonTitle: additionalButtonTitle,
            #takesInput: takesInput,
            #barrierColor: barrierColor,
            #elevation: elevation,
            #barrierDismissible: barrierDismissible,
            #isScrollControlled: isScrollControlled,
            #barrierLabel: barrierLabel,
            #customData: customData,
            #data: data,
            #enableDrag: enableDrag,
            #exitBottomSheetDuration: exitBottomSheetDuration,
            #enterBottomSheetDuration: enterBottomSheetDuration,
            #ignoreSafeArea: ignoreSafeArea,
            #useRootNavigator: useRootNavigator,
          },
        ),
        returnValue: _i18.Future<_i16.SheetResponse<T>?>.value(),
        returnValueForMissingStub: _i18.Future<_i16.SheetResponse<T>?>.value(),
      ) as _i18.Future<_i16.SheetResponse<T>?>);

  @override
  void completeSheet(_i16.SheetResponse<dynamic>? response) =>
      super.noSuchMethod(
        Invocation.method(
          #completeSheet,
          [response],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [DialogService].
///
/// See the documentation for Mockito's code generation for more information.
class MockDialogService extends _i1.Mock implements _i16.DialogService {
  @override
  void registerCustomDialogBuilders(
          Map<dynamic, _i16.DialogBuilder>? builders) =>
      super.noSuchMethod(
        Invocation.method(
          #registerCustomDialogBuilders,
          [builders],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void registerCustomDialogBuilder({
    required dynamic variant,
    required _i15.Widget Function(
      _i15.BuildContext,
      _i16.DialogRequest<dynamic>,
      dynamic Function(_i16.DialogResponse<dynamic>),
    )? builder,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #registerCustomDialogBuilder,
          [],
          {
            #variant: variant,
            #builder: builder,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i18.Future<_i16.DialogResponse<dynamic>?> showDialog({
    String? title,
    String? description,
    String? cancelTitle,
    _i19.Color? cancelTitleColor,
    String? buttonTitle = 'Ok',
    _i19.Color? buttonTitleColor,
    bool? barrierDismissible = false,
    _i15.RouteSettings? routeSettings,
    _i15.GlobalKey<_i15.NavigatorState>? navigatorKey,
    _i16.DialogPlatform? dialogPlatform,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #showDialog,
          [],
          {
            #title: title,
            #description: description,
            #cancelTitle: cancelTitle,
            #cancelTitleColor: cancelTitleColor,
            #buttonTitle: buttonTitle,
            #buttonTitleColor: buttonTitleColor,
            #barrierDismissible: barrierDismissible,
            #routeSettings: routeSettings,
            #navigatorKey: navigatorKey,
            #dialogPlatform: dialogPlatform,
          },
        ),
        returnValue: _i18.Future<_i16.DialogResponse<dynamic>?>.value(),
        returnValueForMissingStub:
            _i18.Future<_i16.DialogResponse<dynamic>?>.value(),
      ) as _i18.Future<_i16.DialogResponse<dynamic>?>);

  @override
  _i18.Future<_i16.DialogResponse<T>?> showCustomDialog<T, R>({
    dynamic variant,
    String? title,
    String? description,
    bool? hasImage = false,
    String? imageUrl,
    bool? showIconInMainButton = false,
    String? mainButtonTitle,
    bool? showIconInSecondaryButton = false,
    String? secondaryButtonTitle,
    bool? showIconInAdditionalButton = false,
    String? additionalButtonTitle,
    bool? takesInput = false,
    _i19.Color? barrierColor = const _i19.Color(2315255808),
    bool? barrierDismissible = false,
    String? barrierLabel = '',
    bool? useSafeArea = true,
    _i15.RouteSettings? routeSettings,
    _i15.GlobalKey<_i15.NavigatorState>? navigatorKey,
    _i15.RouteTransitionsBuilder? transitionBuilder,
    dynamic customData,
    R? data,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #showCustomDialog,
          [],
          {
            #variant: variant,
            #title: title,
            #description: description,
            #hasImage: hasImage,
            #imageUrl: imageUrl,
            #showIconInMainButton: showIconInMainButton,
            #mainButtonTitle: mainButtonTitle,
            #showIconInSecondaryButton: showIconInSecondaryButton,
            #secondaryButtonTitle: secondaryButtonTitle,
            #showIconInAdditionalButton: showIconInAdditionalButton,
            #additionalButtonTitle: additionalButtonTitle,
            #takesInput: takesInput,
            #barrierColor: barrierColor,
            #barrierDismissible: barrierDismissible,
            #barrierLabel: barrierLabel,
            #useSafeArea: useSafeArea,
            #routeSettings: routeSettings,
            #navigatorKey: navigatorKey,
            #transitionBuilder: transitionBuilder,
            #customData: customData,
            #data: data,
          },
        ),
        returnValue: _i18.Future<_i16.DialogResponse<T>?>.value(),
        returnValueForMissingStub: _i18.Future<_i16.DialogResponse<T>?>.value(),
      ) as _i18.Future<_i16.DialogResponse<T>?>);

  @override
  _i18.Future<_i16.DialogResponse<dynamic>?> showConfirmationDialog({
    String? title,
    String? description,
    String? cancelTitle = 'Cancel',
    _i19.Color? cancelTitleColor,
    String? confirmationTitle = 'Ok',
    _i19.Color? confirmationTitleColor,
    bool? barrierDismissible = false,
    _i15.RouteSettings? routeSettings,
    _i16.DialogPlatform? dialogPlatform,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #showConfirmationDialog,
          [],
          {
            #title: title,
            #description: description,
            #cancelTitle: cancelTitle,
            #cancelTitleColor: cancelTitleColor,
            #confirmationTitle: confirmationTitle,
            #confirmationTitleColor: confirmationTitleColor,
            #barrierDismissible: barrierDismissible,
            #routeSettings: routeSettings,
            #dialogPlatform: dialogPlatform,
          },
        ),
        returnValue: _i18.Future<_i16.DialogResponse<dynamic>?>.value(),
        returnValueForMissingStub:
            _i18.Future<_i16.DialogResponse<dynamic>?>.value(),
      ) as _i18.Future<_i16.DialogResponse<dynamic>?>);

  @override
  void completeDialog(_i16.DialogResponse<dynamic>? response) =>
      super.noSuchMethod(
        Invocation.method(
          #completeDialog,
          [response],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [AnalyticsService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAnalyticsService extends _i1.Mock implements _i7.AnalyticsService {
  @override
  _i2.Analytics get segmentAnalytics => (super.noSuchMethod(
        Invocation.getter(#segmentAnalytics),
        returnValue: _FakeAnalytics_0(
          this,
          Invocation.getter(#segmentAnalytics),
        ),
        returnValueForMissingStub: _FakeAnalytics_0(
          this,
          Invocation.getter(#segmentAnalytics),
        ),
      ) as _i2.Analytics);

  @override
  set segmentAnalytics(_i2.Analytics? _segmentAnalytics) => super.noSuchMethod(
        Invocation.setter(
          #segmentAnalytics,
          _segmentAnalytics,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i3.FirebaseAnalyticsObserver get analyticObserver => (super.noSuchMethod(
        Invocation.getter(#analyticObserver),
        returnValue: _FakeFirebaseAnalyticsObserver_1(
          this,
          Invocation.getter(#analyticObserver),
        ),
        returnValueForMissingStub: _FakeFirebaseAnalyticsObserver_1(
          this,
          Invocation.getter(#analyticObserver),
        ),
      ) as _i3.FirebaseAnalyticsObserver);

  @override
  _i18.Future<void> logEvent({
    required String? eventName,
    Map<String, dynamic>? parameters,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #logEvent,
          [],
          {
            #eventName: eventName,
            #parameters: parameters,
          },
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  _i18.Future<dynamic> setCurrentScreen({
    required String? screenName,
    Map<String, dynamic>? parameters,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #setCurrentScreen,
          [],
          {
            #screenName: screenName,
            #parameters: parameters,
          },
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> setUserId({required String? userId}) =>
      (super.noSuchMethod(
        Invocation.method(
          #setUserId,
          [],
          {#userId: userId},
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> setUserEmail({required String? email}) =>
      (super.noSuchMethod(
        Invocation.method(
          #setUserEmail,
          [],
          {#email: email},
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> setUserProperties({
    required String? name,
    String? value,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #setUserProperties,
          [],
          {
            #name: name,
            #value: value,
          },
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> logout() => (super.noSuchMethod(
        Invocation.method(
          #logout,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> reportUserProperty(_i4.OnboardUser? user) =>
      (super.noSuchMethod(
        Invocation.method(
          #reportUserProperty,
          [user],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> registerDeviceToken({required String? deviceToken}) =>
      (super.noSuchMethod(
        Invocation.method(
          #registerDeviceToken,
          [],
          {#deviceToken: deviceToken},
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);
}

/// A class which mocks [WalletService].
///
/// See the documentation for Mockito's code generation for more information.
class MockWalletService extends _i1.Mock implements _i7.WalletService {
  @override
  bool get secretNotFound => (super.noSuchMethod(
        Invocation.getter(#secretNotFound),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  set secretNotFound(bool? _secretNotFound) => super.noSuchMethod(
        Invocation.setter(
          #secretNotFound,
          _secretNotFound,
        ),
        returnValueForMissingStub: null,
      );

  @override
  bool get isDefiWalletSetUp => (super.noSuchMethod(
        Invocation.getter(#isDefiWalletSetUp),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  bool get hasSeedPhrase => (super.noSuchMethod(
        Invocation.getter(#hasSeedPhrase),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  int get listenersCount => (super.noSuchMethod(
        Invocation.getter(#listenersCount),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as int);

  @override
  bool isValidAddress({required String? address}) => (super.noSuchMethod(
        Invocation.method(
          #isValidAddress,
          [],
          {#address: address},
        ),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  bool isTronAddress(String? address) => (super.noSuchMethod(
        Invocation.method(
          #isTronAddress,
          [address],
        ),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  bool isValidMnemonic({required String? mnemonic}) => (super.noSuchMethod(
        Invocation.method(
          #isValidMnemonic,
          [],
          {#mnemonic: mnemonic},
        ),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  String? extractWalletAddress(String? privateKey) => (super.noSuchMethod(
        Invocation.method(
          #extractWalletAddress,
          [privateKey],
        ),
        returnValueForMissingStub: null,
      ) as String?);

  @override
  String generateMnemonic() => (super.noSuchMethod(
        Invocation.method(
          #generateMnemonic,
          [],
        ),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.method(
            #generateMnemonic,
            [],
          ),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.method(
            #generateMnemonic,
            [],
          ),
        ),
      ) as String);

  @override
  _i18.Future<_i4.Result<List<_i7.WalletAccountModel>>> importAndSetupWallet({
    required String? mnemonic,
    String? privateKey,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #importAndSetupWallet,
          [],
          {
            #mnemonic: mnemonic,
            #privateKey: privateKey,
          },
        ),
        returnValue:
            _i18.Future<_i4.Result<List<_i7.WalletAccountModel>>>.value(
                _FakeResult_2<List<_i7.WalletAccountModel>>(
          this,
          Invocation.method(
            #importAndSetupWallet,
            [],
            {
              #mnemonic: mnemonic,
              #privateKey: privateKey,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<List<_i7.WalletAccountModel>>>.value(
                _FakeResult_2<List<_i7.WalletAccountModel>>(
          this,
          Invocation.method(
            #importAndSetupWallet,
            [],
            {
              #mnemonic: mnemonic,
              #privateKey: privateKey,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<List<_i7.WalletAccountModel>>>);

  @override
  _i18.Future<_i4.Result<_i6.Web3AuthResponse?>> loginAndSetupWallet(
          {required _i20.TypeOfLogin? typeOfLogin}) =>
      (super.noSuchMethod(
        Invocation.method(
          #loginAndSetupWallet,
          [],
          {#typeOfLogin: typeOfLogin},
        ),
        returnValue: _i18.Future<_i4.Result<_i6.Web3AuthResponse?>>.value(
            _FakeResult_2<_i6.Web3AuthResponse?>(
          this,
          Invocation.method(
            #loginAndSetupWallet,
            [],
            {#typeOfLogin: typeOfLogin},
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i6.Web3AuthResponse?>>.value(
                _FakeResult_2<_i6.Web3AuthResponse?>(
          this,
          Invocation.method(
            #loginAndSetupWallet,
            [],
            {#typeOfLogin: typeOfLogin},
          ),
        )),
      ) as _i18.Future<_i4.Result<_i6.Web3AuthResponse?>>);

  @override
  _i18.Future<bool> initializeWallet({bool? useBiometric = true}) =>
      (super.noSuchMethod(
        Invocation.method(
          #initializeWallet,
          [],
          {#useBiometric: useBiometric},
        ),
        returnValue: _i18.Future<bool>.value(false),
        returnValueForMissingStub: _i18.Future<bool>.value(false),
      ) as _i18.Future<bool>);

  @override
  _i18.Future<bool> initWalletWithPasscode({required String? passcode}) =>
      (super.noSuchMethod(
        Invocation.method(
          #initWalletWithPasscode,
          [],
          {#passcode: passcode},
        ),
        returnValue: _i18.Future<bool>.value(false),
        returnValueForMissingStub: _i18.Future<bool>.value(false),
      ) as _i18.Future<bool>);

  @override
  _i18.Future<String?> retrieveEncryptedPrivateKeyWithBiometrics() =>
      (super.noSuchMethod(
        Invocation.method(
          #retrieveEncryptedPrivateKeyWithBiometrics,
          [],
        ),
        returnValue: _i18.Future<String?>.value(),
        returnValueForMissingStub: _i18.Future<String?>.value(),
      ) as _i18.Future<String?>);

  @override
  _i18.Future<String?> retrievePrivateKeyWithBiometrics() =>
      (super.noSuchMethod(
        Invocation.method(
          #retrievePrivateKeyWithBiometrics,
          [],
        ),
        returnValue: _i18.Future<String?>.value(),
        returnValueForMissingStub: _i18.Future<String?>.value(),
      ) as _i18.Future<String?>);

  @override
  _i18.Future<bool> encryptCredentials({
    required String? privateKey,
    String? seedPhrase,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #encryptCredentials,
          [],
          {
            #privateKey: privateKey,
            #seedPhrase: seedPhrase,
          },
        ),
        returnValue: _i18.Future<bool>.value(false),
        returnValueForMissingStub: _i18.Future<bool>.value(false),
      ) as _i18.Future<bool>);

  @override
  _i18.Future<bool> backupWallet({
    required _i21.BackupType? backupType,
    String? passcode,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #backupWallet,
          [],
          {
            #backupType: backupType,
            #passcode: passcode,
          },
        ),
        returnValue: _i18.Future<bool>.value(false),
        returnValueForMissingStub: _i18.Future<bool>.value(false),
      ) as _i18.Future<bool>);

  @override
  _i18.Future<String?> decryptSeedPhrase() => (super.noSuchMethod(
        Invocation.method(
          #decryptSeedPhrase,
          [],
        ),
        returnValue: _i18.Future<String?>.value(),
        returnValueForMissingStub: _i18.Future<String?>.value(),
      ) as _i18.Future<String?>);

  @override
  _i18.Future<String?> retrievePrivateKeyWithPasscode(
          {required String? passcode}) =>
      (super.noSuchMethod(
        Invocation.method(
          #retrievePrivateKeyWithPasscode,
          [],
          {#passcode: passcode},
        ),
        returnValue: _i18.Future<String?>.value(),
        returnValueForMissingStub: _i18.Future<String?>.value(),
      ) as _i18.Future<String?>);

  @override
  bool isWalletPasscodeBackedUp() => (super.noSuchMethod(
        Invocation.method(
          #isWalletPasscodeBackedUp,
          [],
        ),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i18.Future<dynamic> getNestcoinRpc() => (super.noSuchMethod(
        Invocation.method(
          #getNestcoinRpc,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> selectAccount(_i7.WalletAccountModel? account) =>
      (super.noSuchMethod(
        Invocation.method(
          #selectAccount,
          [account],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  bool isSpotWallet(String? address) => (super.noSuchMethod(
        Invocation.method(
          #isSpotWallet,
          [address],
        ),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i18.Future<String?> getPrivateKey() => (super.noSuchMethod(
        Invocation.method(
          #getPrivateKey,
          [],
        ),
        returnValue: _i18.Future<String?>.value(),
        returnValueForMissingStub: _i18.Future<String?>.value(),
      ) as _i18.Future<String?>);

  @override
  _i18.Future<String?> decryptPrivateKey() => (super.noSuchMethod(
        Invocation.method(
          #decryptPrivateKey,
          [],
        ),
        returnValue: _i18.Future<String?>.value(),
        returnValueForMissingStub: _i18.Future<String?>.value(),
      ) as _i18.Future<String?>);

  @override
  _i18.Future<bool> hasWalletProtected() => (super.noSuchMethod(
        Invocation.method(
          #hasWalletProtected,
          [],
        ),
        returnValue: _i18.Future<bool>.value(false),
        returnValueForMissingStub: _i18.Future<bool>.value(false),
      ) as _i18.Future<bool>);

  @override
  _i18.Future<bool> hasWalletSecurity() => (super.noSuchMethod(
        Invocation.method(
          #hasWalletSecurity,
          [],
        ),
        returnValue: _i18.Future<bool>.value(false),
        returnValueForMissingStub: _i18.Future<bool>.value(false),
      ) as _i18.Future<bool>);

  @override
  _i18.Future<_i4.Result<String?>> createWallet() => (super.noSuchMethod(
        Invocation.method(
          #createWallet,
          [],
        ),
        returnValue:
            _i18.Future<_i4.Result<String?>>.value(_FakeResult_2<String?>(
          this,
          Invocation.method(
            #createWallet,
            [],
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<String?>>.value(_FakeResult_2<String?>(
          this,
          Invocation.method(
            #createWallet,
            [],
          ),
        )),
      ) as _i18.Future<_i4.Result<String?>>);

  @override
  _i18.Future<_i4.Result<String>> setUpRecoveredWallet(String? privateKey) =>
      (super.noSuchMethod(
        Invocation.method(
          #setUpRecoveredWallet,
          [privateKey],
        ),
        returnValue:
            _i18.Future<_i4.Result<String>>.value(_FakeResult_2<String>(
          this,
          Invocation.method(
            #setUpRecoveredWallet,
            [privateKey],
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<String>>.value(_FakeResult_2<String>(
          this,
          Invocation.method(
            #setUpRecoveredWallet,
            [privateKey],
          ),
        )),
      ) as _i18.Future<_i4.Result<String>>);

  @override
  void logout() => super.noSuchMethod(
        Invocation.method(
          #logout,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  dynamic setUpOrRecoverDefiWallet(
          {required dynamic Function()? onCompleteSetup}) =>
      super.noSuchMethod(
        Invocation.method(
          #setUpOrRecoverDefiWallet,
          [],
          {#onCompleteSetup: onCompleteSetup},
        ),
        returnValueForMissingStub: null,
      );

  @override
  void listenToReactiveValues(List<dynamic>? reactiveValues) =>
      super.noSuchMethod(
        Invocation.method(
          #listenToReactiveValues,
          [reactiveValues],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [Web3Service].
///
/// See the documentation for Mockito's code generation for more information.
class MockWeb3Service extends _i1.Mock implements _i7.Web3Service {
  @override
  _i5.EthereumAddress get zeroAddress => (super.noSuchMethod(
        Invocation.getter(#zeroAddress),
        returnValue: _FakeEthereumAddress_3(
          this,
          Invocation.getter(#zeroAddress),
        ),
        returnValueForMissingStub: _FakeEthereumAddress_3(
          this,
          Invocation.getter(#zeroAddress),
        ),
      ) as _i5.EthereumAddress);

  @override
  _i5.Web3Client web3client(String? rpcUrl) => (super.noSuchMethod(
        Invocation.method(
          #web3client,
          [rpcUrl],
        ),
        returnValue: _FakeWeb3Client_4(
          this,
          Invocation.method(
            #web3client,
            [rpcUrl],
          ),
        ),
        returnValueForMissingStub: _FakeWeb3Client_4(
          this,
          Invocation.method(
            #web3client,
            [rpcUrl],
          ),
        ),
      ) as _i5.Web3Client);

  @override
  _i18.Future<_i5.DeployedContract> getContract({
    required String? name,
    required String? contractAddress,
    required String? jsonAbiPath,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getContract,
          [],
          {
            #name: name,
            #contractAddress: contractAddress,
            #jsonAbiPath: jsonAbiPath,
          },
        ),
        returnValue:
            _i18.Future<_i5.DeployedContract>.value(_FakeDeployedContract_5(
          this,
          Invocation.method(
            #getContract,
            [],
            {
              #name: name,
              #contractAddress: contractAddress,
              #jsonAbiPath: jsonAbiPath,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i5.DeployedContract>.value(_FakeDeployedContract_5(
          this,
          Invocation.method(
            #getContract,
            [],
            {
              #name: name,
              #contractAddress: contractAddress,
              #jsonAbiPath: jsonAbiPath,
            },
          ),
        )),
      ) as _i18.Future<_i5.DeployedContract>);

  @override
  _i18.Future<_i5.TransactionReceipt?> getTransactionReceipt({
    required String? rpcUrl,
    required String? hash,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getTransactionReceipt,
          [],
          {
            #rpcUrl: rpcUrl,
            #hash: hash,
          },
        ),
        returnValue: _i18.Future<_i5.TransactionReceipt?>.value(),
        returnValueForMissingStub: _i18.Future<_i5.TransactionReceipt?>.value(),
      ) as _i18.Future<_i5.TransactionReceipt?>);

  @override
  String? extractAddressFromPrivateKey(String? privateKey) =>
      (super.noSuchMethod(
        Invocation.method(
          #extractAddressFromPrivateKey,
          [privateKey],
        ),
        returnValueForMissingStub: null,
      ) as String?);

  @override
  _i18.Future<String?> signMessageWithPrivateKey({
    required String? privateKey,
    required String? message,
    required bool? isTypedData,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #signMessageWithPrivateKey,
          [],
          {
            #privateKey: privateKey,
            #message: message,
            #isTypedData: isTypedData,
          },
        ),
        returnValue: _i18.Future<String?>.value(),
        returnValueForMissingStub: _i18.Future<String?>.value(),
      ) as _i18.Future<String?>);

  @override
  String signTypedData({
    required String? privateKey,
    required String? message,
    required _i22.TypedDataVersion? version,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #signTypedData,
          [],
          {
            #privateKey: privateKey,
            #message: message,
            #version: version,
          },
        ),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.method(
            #signTypedData,
            [],
            {
              #privateKey: privateKey,
              #message: message,
              #version: version,
            },
          ),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.method(
            #signTypedData,
            [],
            {
              #privateKey: privateKey,
              #message: message,
              #version: version,
            },
          ),
        ),
      ) as String);

  @override
  _i18.Future<String?> signPersonalMessageWithPrivateKey({
    required String? privateKey,
    required String? message,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #signPersonalMessageWithPrivateKey,
          [],
          {
            #privateKey: privateKey,
            #message: message,
          },
        ),
        returnValue: _i18.Future<String?>.value(),
        returnValueForMissingStub: _i18.Future<String?>.value(),
      ) as _i18.Future<String?>);

  @override
  _i18.Future<String?> signTransaction({
    required String? rpcUrl,
    required _i5.Transaction? transaction,
    required String? privateKey,
    int? chainId,
    bool? fetchChainIdFromNetworkId = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #signTransaction,
          [],
          {
            #rpcUrl: rpcUrl,
            #transaction: transaction,
            #privateKey: privateKey,
            #chainId: chainId,
            #fetchChainIdFromNetworkId: fetchChainIdFromNetworkId,
          },
        ),
        returnValue: _i18.Future<String?>.value(),
        returnValueForMissingStub: _i18.Future<String?>.value(),
      ) as _i18.Future<String?>);

  @override
  _i18.Future<BigInt> getGasEstimate({
    _i5.EthereumAddress? fromAddress,
    required String? rpcUrl,
    _i5.EthereumAddress? toAddress,
    _i5.EtherAmount? value,
    _i23.Uint8List? data,
    _i5.EtherAmount? gasPrice,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getGasEstimate,
          [],
          {
            #fromAddress: fromAddress,
            #rpcUrl: rpcUrl,
            #toAddress: toAddress,
            #value: value,
            #data: data,
            #gasPrice: gasPrice,
          },
        ),
        returnValue: _i18.Future<BigInt>.value(_i17.dummyValue<BigInt>(
          this,
          Invocation.method(
            #getGasEstimate,
            [],
            {
              #fromAddress: fromAddress,
              #rpcUrl: rpcUrl,
              #toAddress: toAddress,
              #value: value,
              #data: data,
              #gasPrice: gasPrice,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<BigInt>.value(_i17.dummyValue<BigInt>(
          this,
          Invocation.method(
            #getGasEstimate,
            [],
            {
              #fromAddress: fromAddress,
              #rpcUrl: rpcUrl,
              #toAddress: toAddress,
              #value: value,
              #data: data,
              #gasPrice: gasPrice,
            },
          ),
        )),
      ) as _i18.Future<BigInt>);

  @override
  _i18.Future<_i5.EtherAmount> getGasPrice({required String? rpcUrl}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getGasPrice,
          [],
          {#rpcUrl: rpcUrl},
        ),
        returnValue: _i18.Future<_i5.EtherAmount>.value(_FakeEtherAmount_6(
          this,
          Invocation.method(
            #getGasPrice,
            [],
            {#rpcUrl: rpcUrl},
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i5.EtherAmount>.value(_FakeEtherAmount_6(
          this,
          Invocation.method(
            #getGasPrice,
            [],
            {#rpcUrl: rpcUrl},
          ),
        )),
      ) as _i18.Future<_i5.EtherAmount>);

  @override
  _i18.Future<String?> sendTransaction({
    required String? rpcUrl,
    required _i5.Transaction? transaction,
    required String? privateKey,
    int? chainId,
    bool? fetchChainIdFromNetworkId = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #sendTransaction,
          [],
          {
            #rpcUrl: rpcUrl,
            #transaction: transaction,
            #privateKey: privateKey,
            #chainId: chainId,
            #fetchChainIdFromNetworkId: fetchChainIdFromNetworkId,
          },
        ),
        returnValue: _i18.Future<String?>.value(),
        returnValueForMissingStub: _i18.Future<String?>.value(),
      ) as _i18.Future<String?>);

  @override
  _i18.Future<_i5.Transaction> composeTradingWalletContractCall({
    required String? smartContractAddress,
    required String? contractName,
    required String? functionName,
    required List<dynamic>? parameters,
    required String? fromAddress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #composeTradingWalletContractCall,
          [],
          {
            #smartContractAddress: smartContractAddress,
            #contractName: contractName,
            #functionName: functionName,
            #parameters: parameters,
            #fromAddress: fromAddress,
          },
        ),
        returnValue: _i18.Future<_i5.Transaction>.value(_FakeTransaction_7(
          this,
          Invocation.method(
            #composeTradingWalletContractCall,
            [],
            {
              #smartContractAddress: smartContractAddress,
              #contractName: contractName,
              #functionName: functionName,
              #parameters: parameters,
              #fromAddress: fromAddress,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i5.Transaction>.value(_FakeTransaction_7(
          this,
          Invocation.method(
            #composeTradingWalletContractCall,
            [],
            {
              #smartContractAddress: smartContractAddress,
              #contractName: contractName,
              #functionName: functionName,
              #parameters: parameters,
              #fromAddress: fromAddress,
            },
          ),
        )),
      ) as _i18.Future<_i5.Transaction>);

  @override
  _i18.Future<_i5.Transaction> composeContractCall({
    required String? functionName,
    required String? contractName,
    required bool? isCoin,
    String? contractAddress,
    required String? toAddress,
    required String? fromAddress,
    required _i5.EtherAmount? amount,
    required List<dynamic>? parameters,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #composeContractCall,
          [],
          {
            #functionName: functionName,
            #contractName: contractName,
            #isCoin: isCoin,
            #contractAddress: contractAddress,
            #toAddress: toAddress,
            #fromAddress: fromAddress,
            #amount: amount,
            #parameters: parameters,
          },
        ),
        returnValue: _i18.Future<_i5.Transaction>.value(_FakeTransaction_7(
          this,
          Invocation.method(
            #composeContractCall,
            [],
            {
              #functionName: functionName,
              #contractName: contractName,
              #isCoin: isCoin,
              #contractAddress: contractAddress,
              #toAddress: toAddress,
              #fromAddress: fromAddress,
              #amount: amount,
              #parameters: parameters,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i5.Transaction>.value(_FakeTransaction_7(
          this,
          Invocation.method(
            #composeContractCall,
            [],
            {
              #functionName: functionName,
              #contractName: contractName,
              #isCoin: isCoin,
              #contractAddress: contractAddress,
              #toAddress: toAddress,
              #fromAddress: fromAddress,
              #amount: amount,
              #parameters: parameters,
            },
          ),
        )),
      ) as _i18.Future<_i5.Transaction>);

  @override
  _i18.Future<int?> getTransactionCount({
    required String? rpcUrl,
    required String? address,
    String? atBlock,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getTransactionCount,
          [],
          {
            #rpcUrl: rpcUrl,
            #address: address,
            #atBlock: atBlock,
          },
        ),
        returnValue: _i18.Future<int?>.value(),
        returnValueForMissingStub: _i18.Future<int?>.value(),
      ) as _i18.Future<int?>);

  @override
  _i18.Future<String?> getBalance({
    required String? fromAddress,
    required String? rpcUrl,
    int? decimal,
    String? contractAddress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBalance,
          [],
          {
            #fromAddress: fromAddress,
            #rpcUrl: rpcUrl,
            #decimal: decimal,
            #contractAddress: contractAddress,
          },
        ),
        returnValue: _i18.Future<String?>.value(),
        returnValueForMissingStub: _i18.Future<String?>.value(),
      ) as _i18.Future<String?>);

  @override
  _i18.Future<_i5.Transaction?> checkAllowance({
    required String? rpcUrl,
    required String? tokenAddress,
    required String? approvalAddress,
    required String? walletAddress,
    required BigInt? amount,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #checkAllowance,
          [],
          {
            #rpcUrl: rpcUrl,
            #tokenAddress: tokenAddress,
            #approvalAddress: approvalAddress,
            #walletAddress: walletAddress,
            #amount: amount,
          },
        ),
        returnValue: _i18.Future<_i5.Transaction?>.value(),
        returnValueForMissingStub: _i18.Future<_i5.Transaction?>.value(),
      ) as _i18.Future<_i5.Transaction?>);

  @override
  _i18.Future<_i5.Transaction?> getApprovalTransaction({
    required String? tokenAddress,
    required String? approvalAddress,
    required String? walletAddress,
    required BigInt? amount,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getApprovalTransaction,
          [],
          {
            #tokenAddress: tokenAddress,
            #approvalAddress: approvalAddress,
            #walletAddress: walletAddress,
            #amount: amount,
          },
        ),
        returnValue: _i18.Future<_i5.Transaction?>.value(),
        returnValueForMissingStub: _i18.Future<_i5.Transaction?>.value(),
      ) as _i18.Future<_i5.Transaction?>);
}

/// A class which mocks [WalletAuthService].
///
/// See the documentation for Mockito's code generation for more information.
class MockWalletAuthService extends _i1.Mock implements _i7.WalletAuthService {
  @override
  _i18.Future<dynamic> init() => (super.noSuchMethod(
        Invocation.method(
          #init,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<_i6.Web3AuthResponse> login(
          {required _i20.TypeOfLogin? typeOfLogin}) =>
      (super.noSuchMethod(
        Invocation.method(
          #login,
          [],
          {#typeOfLogin: typeOfLogin},
        ),
        returnValue:
            _i18.Future<_i6.Web3AuthResponse>.value(_FakeWeb3AuthResponse_8(
          this,
          Invocation.method(
            #login,
            [],
            {#typeOfLogin: typeOfLogin},
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i6.Web3AuthResponse>.value(_FakeWeb3AuthResponse_8(
          this,
          Invocation.method(
            #login,
            [],
            {#typeOfLogin: typeOfLogin},
          ),
        )),
      ) as _i18.Future<_i6.Web3AuthResponse>);

  @override
  _i18.Future<dynamic> logout() => (super.noSuchMethod(
        Invocation.method(
          #logout,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);
}

/// A class which mocks [SecureStorageService].
///
/// See the documentation for Mockito's code generation for more information.
class MockSecureStorageService extends _i1.Mock
    implements _i7.SecureStorageService {
  @override
  _i18.Future<void> delete({required String? key}) => (super.noSuchMethod(
        Invocation.method(
          #delete,
          [],
          {#key: key},
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  _i18.Future<String?>? read({required String? key}) => (super.noSuchMethod(
        Invocation.method(
          #read,
          [],
          {#key: key},
        ),
        returnValueForMissingStub: null,
      ) as _i18.Future<String?>?);

  @override
  _i18.Future<void> write({
    required String? key,
    required String? value,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #write,
          [],
          {
            #key: key,
            #value: value,
          },
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  _i18.Future<bool> containsKey({required String? key}) => (super.noSuchMethod(
        Invocation.method(
          #containsKey,
          [],
          {#key: key},
        ),
        returnValue: _i18.Future<bool>.value(false),
        returnValueForMissingStub: _i18.Future<bool>.value(false),
      ) as _i18.Future<bool>);

  @override
  _i18.Future<dynamic> deleteAll() => (super.noSuchMethod(
        Invocation.method(
          #deleteAll,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);
}

/// A class which mocks [AuthenticationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthenticationService extends _i1.Mock
    implements _i7.AuthenticationService {
  @override
  _i18.Future<bool> isLoggedIn() => (super.noSuchMethod(
        Invocation.method(
          #isLoggedIn,
          [],
        ),
        returnValue: _i18.Future<bool>.value(false),
        returnValueForMissingStub: _i18.Future<bool>.value(false),
      ) as _i18.Future<bool>);

  @override
  _i18.Future<String?> getAuthToken() => (super.noSuchMethod(
        Invocation.method(
          #getAuthToken,
          [],
        ),
        returnValue: _i18.Future<String?>.value(),
        returnValueForMissingStub: _i18.Future<String?>.value(),
      ) as _i18.Future<String?>);

  @override
  _i18.Future<String?> getRefreshToken() => (super.noSuchMethod(
        Invocation.method(
          #getRefreshToken,
          [],
        ),
        returnValue: _i18.Future<String?>.value(),
        returnValueForMissingStub: _i18.Future<String?>.value(),
      ) as _i18.Future<String?>);

  @override
  _i18.Future<_i4.Result<String?>> onboardUserAuthentication({
    required String? email,
    String? referredBy,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #onboardUserAuthentication,
          [],
          {
            #email: email,
            #referredBy: referredBy,
          },
        ),
        returnValue:
            _i18.Future<_i4.Result<String?>>.value(_FakeResult_2<String?>(
          this,
          Invocation.method(
            #onboardUserAuthentication,
            [],
            {
              #email: email,
              #referredBy: referredBy,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<String?>>.value(_FakeResult_2<String?>(
          this,
          Invocation.method(
            #onboardUserAuthentication,
            [],
            {
              #email: email,
              #referredBy: referredBy,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<String?>>);

  @override
  _i18.Future<_i4.Result<String?>> resendOtp({
    required String? authSessionId,
    required _i12.OtpPurpose? otpPurpose,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #resendOtp,
          [],
          {
            #authSessionId: authSessionId,
            #otpPurpose: otpPurpose,
          },
        ),
        returnValue:
            _i18.Future<_i4.Result<String?>>.value(_FakeResult_2<String?>(
          this,
          Invocation.method(
            #resendOtp,
            [],
            {
              #authSessionId: authSessionId,
              #otpPurpose: otpPurpose,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<String?>>.value(_FakeResult_2<String?>(
          this,
          Invocation.method(
            #resendOtp,
            [],
            {
              #authSessionId: authSessionId,
              #otpPurpose: otpPurpose,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<String?>>);

  @override
  _i18.Future<_i4.Result<String?>> verifyAuthOTP({
    required String? authSessionId,
    required String? otp,
    _i12.AddressSignatureBuilder? addressSignatureBuilder,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #verifyAuthOTP,
          [],
          {
            #authSessionId: authSessionId,
            #otp: otp,
            #addressSignatureBuilder: addressSignatureBuilder,
          },
        ),
        returnValue:
            _i18.Future<_i4.Result<String?>>.value(_FakeResult_2<String?>(
          this,
          Invocation.method(
            #verifyAuthOTP,
            [],
            {
              #authSessionId: authSessionId,
              #otp: otp,
              #addressSignatureBuilder: addressSignatureBuilder,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<String?>>.value(_FakeResult_2<String?>(
          this,
          Invocation.method(
            #verifyAuthOTP,
            [],
            {
              #authSessionId: authSessionId,
              #otp: otp,
              #addressSignatureBuilder: addressSignatureBuilder,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<String?>>);

  @override
  _i18.Future<_i4.Result<String?>> refreshAuthToken() => (super.noSuchMethod(
        Invocation.method(
          #refreshAuthToken,
          [],
        ),
        returnValue:
            _i18.Future<_i4.Result<String?>>.value(_FakeResult_2<String?>(
          this,
          Invocation.method(
            #refreshAuthToken,
            [],
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<String?>>.value(_FakeResult_2<String?>(
          this,
          Invocation.method(
            #refreshAuthToken,
            [],
          ),
        )),
      ) as _i18.Future<_i4.Result<String?>>);

  @override
  _i18.Future<dynamic> logout() => (super.noSuchMethod(
        Invocation.method(
          #logout,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> onboardLogout() => (super.noSuchMethod(
        Invocation.method(
          #onboardLogout,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> checkAndRefreshToken() => (super.noSuchMethod(
        Invocation.method(
          #checkAndRefreshToken,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<bool> hasTokenExpired() => (super.noSuchMethod(
        Invocation.method(
          #hasTokenExpired,
          [],
        ),
        returnValue: _i18.Future<bool>.value(false),
        returnValueForMissingStub: _i18.Future<bool>.value(false),
      ) as _i18.Future<bool>);

  @override
  _i18.Future<bool?> checkWalletExist({required String? walletAddress}) =>
      (super.noSuchMethod(
        Invocation.method(
          #checkWalletExist,
          [],
          {#walletAddress: walletAddress},
        ),
        returnValue: _i18.Future<bool?>.value(),
        returnValueForMissingStub: _i18.Future<bool?>.value(),
      ) as _i18.Future<bool?>);

  @override
  _i18.Future<_i4.Result<String?>> login({
    required String? email,
    required String? signature,
    required String? walletAddress,
    required String? message,
    required int? timestamp,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #login,
          [],
          {
            #email: email,
            #signature: signature,
            #walletAddress: walletAddress,
            #message: message,
            #timestamp: timestamp,
          },
        ),
        returnValue:
            _i18.Future<_i4.Result<String?>>.value(_FakeResult_2<String?>(
          this,
          Invocation.method(
            #login,
            [],
            {
              #email: email,
              #signature: signature,
              #walletAddress: walletAddress,
              #message: message,
              #timestamp: timestamp,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<String?>>.value(_FakeResult_2<String?>(
          this,
          Invocation.method(
            #login,
            [],
            {
              #email: email,
              #signature: signature,
              #walletAddress: walletAddress,
              #message: message,
              #timestamp: timestamp,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<String?>>);

  @override
  _i18.Future<_i4.Result<String?>> signUpWithWeb3Auth({
    required String? email,
    required String? walletAddress,
    required String? idToken,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #signUpWithWeb3Auth,
          [],
          {
            #email: email,
            #walletAddress: walletAddress,
            #idToken: idToken,
          },
        ),
        returnValue:
            _i18.Future<_i4.Result<String?>>.value(_FakeResult_2<String?>(
          this,
          Invocation.method(
            #signUpWithWeb3Auth,
            [],
            {
              #email: email,
              #walletAddress: walletAddress,
              #idToken: idToken,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<String?>>.value(_FakeResult_2<String?>(
          this,
          Invocation.method(
            #signUpWithWeb3Auth,
            [],
            {
              #email: email,
              #walletAddress: walletAddress,
              #idToken: idToken,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<String?>>);

  @override
  _i18.Future<String?> processAuthOtpResponseDto(
          _i12.AuthOtpResponseDto? response) =>
      (super.noSuchMethod(
        Invocation.method(
          #processAuthOtpResponseDto,
          [response],
        ),
        returnValue: _i18.Future<String?>.value(),
        returnValueForMissingStub: _i18.Future<String?>.value(),
      ) as _i18.Future<String?>);

  @override
  _i18.Future<dynamic> firebaseAnonymousSignIn() => (super.noSuchMethod(
        Invocation.method(
          #firebaseAnonymousSignIn,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<String?> getAuthenticationOts({required String? targetOrigin}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getAuthenticationOts,
          [],
          {#targetOrigin: targetOrigin},
        ),
        returnValue: _i18.Future<String?>.value(),
        returnValueForMissingStub: _i18.Future<String?>.value(),
      ) as _i18.Future<String?>);
}

/// A class which mocks [UserService].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserService extends _i1.Mock implements _i7.UserService {
  @override
  _i7.DatabaseService get databaseService => (super.noSuchMethod(
        Invocation.getter(#databaseService),
        returnValue: _FakeDatabaseService_9(
          this,
          Invocation.getter(#databaseService),
        ),
        returnValueForMissingStub: _FakeDatabaseService_9(
          this,
          Invocation.getter(#databaseService),
        ),
      ) as _i7.DatabaseService);

  @override
  set onCompleteOnboarding(dynamic Function()? _onCompleteOnboarding) =>
      super.noSuchMethod(
        Invocation.setter(
          #onCompleteOnboarding,
          _onCompleteOnboarding,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i8.Box<_i4.LocalReferrer> get referrerBox => (super.noSuchMethod(
        Invocation.getter(#referrerBox),
        returnValue: _FakeBox_10<_i4.LocalReferrer>(
          this,
          Invocation.getter(#referrerBox),
        ),
        returnValueForMissingStub: _FakeBox_10<_i4.LocalReferrer>(
          this,
          Invocation.getter(#referrerBox),
        ),
      ) as _i8.Box<_i4.LocalReferrer>);

  @override
  _i8.Box<_i24.LocalCountry> get userCountryFeatureBox => (super.noSuchMethod(
        Invocation.getter(#userCountryFeatureBox),
        returnValue: _FakeBox_10<_i24.LocalCountry>(
          this,
          Invocation.getter(#userCountryFeatureBox),
        ),
        returnValueForMissingStub: _FakeBox_10<_i24.LocalCountry>(
          this,
          Invocation.getter(#userCountryFeatureBox),
        ),
      ) as _i8.Box<_i24.LocalCountry>);

  @override
  bool get userIsMerchant => (super.noSuchMethod(
        Invocation.getter(#userIsMerchant),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  bool get hasPhoneNumber => (super.noSuchMethod(
        Invocation.getter(#hasPhoneNumber),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i18.Future<void> saveCurrentUser(_i4.OnboardUser? user) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveCurrentUser,
          [user],
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  _i18.Future<_i4.Result<dynamic>> deleteAccount({required String? otp}) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteAccount,
          [],
          {#otp: otp},
        ),
        returnValue:
            _i18.Future<_i4.Result<dynamic>>.value(_FakeResult_2<dynamic>(
          this,
          Invocation.method(
            #deleteAccount,
            [],
            {#otp: otp},
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<dynamic>>.value(_FakeResult_2<dynamic>(
          this,
          Invocation.method(
            #deleteAccount,
            [],
            {#otp: otp},
          ),
        )),
      ) as _i18.Future<_i4.Result<dynamic>>);

  @override
  _i18.Future<_i4.Result<String?>> sendOtp({
    required _i12.OtpPurpose? purpose,
    _i12.PhoneNumberVerificationChannel? verificationChannel,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #sendOtp,
          [],
          {
            #purpose: purpose,
            #verificationChannel: verificationChannel,
          },
        ),
        returnValue:
            _i18.Future<_i4.Result<String?>>.value(_FakeResult_2<String?>(
          this,
          Invocation.method(
            #sendOtp,
            [],
            {
              #purpose: purpose,
              #verificationChannel: verificationChannel,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<String?>>.value(_FakeResult_2<String?>(
          this,
          Invocation.method(
            #sendOtp,
            [],
            {
              #purpose: purpose,
              #verificationChannel: verificationChannel,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<String?>>);

  @override
  _i18.Future<_i4.Result<bool>> updatePhoneNumber(
    String? phoneNumber, {
    bool? verify = false,
    required _i12.PhoneNumberVerificationChannel? verificationChannel,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updatePhoneNumber,
          [phoneNumber],
          {
            #verify: verify,
            #verificationChannel: verificationChannel,
          },
        ),
        returnValue: _i18.Future<_i4.Result<bool>>.value(_FakeResult_2<bool>(
          this,
          Invocation.method(
            #updatePhoneNumber,
            [phoneNumber],
            {
              #verify: verify,
              #verificationChannel: verificationChannel,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<bool>>.value(_FakeResult_2<bool>(
          this,
          Invocation.method(
            #updatePhoneNumber,
            [phoneNumber],
            {
              #verify: verify,
              #verificationChannel: verificationChannel,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<bool>>);

  @override
  _i18.Future<_i4.OnboardUser?> getUpdatedUserInfo() => (super.noSuchMethod(
        Invocation.method(
          #getUpdatedUserInfo,
          [],
        ),
        returnValue: _i18.Future<_i4.OnboardUser?>.value(),
        returnValueForMissingStub: _i18.Future<_i4.OnboardUser?>.value(),
      ) as _i18.Future<_i4.OnboardUser?>);

  @override
  _i18.Future<List<_i12.PaymentMethodsSvcPaymentMethod>>
      getUserPaymentMethods() => (super.noSuchMethod(
            Invocation.method(
              #getUserPaymentMethods,
              [],
            ),
            returnValue:
                _i18.Future<List<_i12.PaymentMethodsSvcPaymentMethod>>.value(
                    <_i12.PaymentMethodsSvcPaymentMethod>[]),
            returnValueForMissingStub:
                _i18.Future<List<_i12.PaymentMethodsSvcPaymentMethod>>.value(
                    <_i12.PaymentMethodsSvcPaymentMethod>[]),
          ) as _i18.Future<List<_i12.PaymentMethodsSvcPaymentMethod>>);

  @override
  _i18.Future<_i4.Result<_i12.UserProfileDataDto?>> updateUserProfile({
    required String? fullName,
    DateTime? dob,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateUserProfile,
          [],
          {
            #fullName: fullName,
            #dob: dob,
          },
        ),
        returnValue: _i18.Future<_i4.Result<_i12.UserProfileDataDto?>>.value(
            _FakeResult_2<_i12.UserProfileDataDto?>(
          this,
          Invocation.method(
            #updateUserProfile,
            [],
            {
              #fullName: fullName,
              #dob: dob,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i12.UserProfileDataDto?>>.value(
                _FakeResult_2<_i12.UserProfileDataDto?>(
          this,
          Invocation.method(
            #updateUserProfile,
            [],
            {
              #fullName: fullName,
              #dob: dob,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<_i12.UserProfileDataDto?>>);

  @override
  _i18.Future<_i4.Result<_i12.UserProfileDataDto?>> updateUserCountry(
          {required String? countryCode}) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateUserCountry,
          [],
          {#countryCode: countryCode},
        ),
        returnValue: _i18.Future<_i4.Result<_i12.UserProfileDataDto?>>.value(
            _FakeResult_2<_i12.UserProfileDataDto?>(
          this,
          Invocation.method(
            #updateUserCountry,
            [],
            {#countryCode: countryCode},
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i12.UserProfileDataDto?>>.value(
                _FakeResult_2<_i12.UserProfileDataDto?>(
          this,
          Invocation.method(
            #updateUserCountry,
            [],
            {#countryCode: countryCode},
          ),
        )),
      ) as _i18.Future<_i4.Result<_i12.UserProfileDataDto?>>);

  @override
  _i18.Future<dynamic> verifyPhoneNumber(String? code) => (super.noSuchMethod(
        Invocation.method(
          #verifyPhoneNumber,
          [code],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<bool> checkDisplayNameExist(String? displayName) =>
      (super.noSuchMethod(
        Invocation.method(
          #checkDisplayNameExist,
          [displayName],
        ),
        returnValue: _i18.Future<bool>.value(false),
        returnValueForMissingStub: _i18.Future<bool>.value(false),
      ) as _i18.Future<bool>);

  @override
  _i18.Future<_i12.UserHomeAddress?> getHomeAddress() => (super.noSuchMethod(
        Invocation.method(
          #getHomeAddress,
          [],
        ),
        returnValue: _i18.Future<_i12.UserHomeAddress?>.value(),
        returnValueForMissingStub: _i18.Future<_i12.UserHomeAddress?>.value(),
      ) as _i18.Future<_i12.UserHomeAddress?>);

  @override
  _i18.Future<_i4.Result<_i12.ChecklistDto?>> getOnboardingCheckListItems(
          String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getOnboardingCheckListItems,
          [userId],
        ),
        returnValue: _i18.Future<_i4.Result<_i12.ChecklistDto?>>.value(
            _FakeResult_2<_i12.ChecklistDto?>(
          this,
          Invocation.method(
            #getOnboardingCheckListItems,
            [userId],
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i12.ChecklistDto?>>.value(
                _FakeResult_2<_i12.ChecklistDto?>(
          this,
          Invocation.method(
            #getOnboardingCheckListItems,
            [userId],
          ),
        )),
      ) as _i18.Future<_i4.Result<_i12.ChecklistDto?>>);

  @override
  List<_i25.OnbwCheckListItemsUIModel> getOnboardingUiModels(
          _i26.LocalChecklistDto? checklistDto) =>
      (super.noSuchMethod(
        Invocation.method(
          #getOnboardingUiModels,
          [checklistDto],
        ),
        returnValue: <_i25.OnbwCheckListItemsUIModel>[],
        returnValueForMissingStub: <_i25.OnbwCheckListItemsUIModel>[],
      ) as List<_i25.OnbwCheckListItemsUIModel>);

  @override
  _i18.Future<void> onCompleteBackup() => (super.noSuchMethod(
        Invocation.method(
          #onCompleteBackup,
          [],
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  _i18.Future<_i4.Result<_i12.AuthOtpResponseDto?>> addVerifiedAddress(
          _i12.AddressSignature? addressSignature) =>
      (super.noSuchMethod(
        Invocation.method(
          #addVerifiedAddress,
          [addressSignature],
        ),
        returnValue: _i18.Future<_i4.Result<_i12.AuthOtpResponseDto?>>.value(
            _FakeResult_2<_i12.AuthOtpResponseDto?>(
          this,
          Invocation.method(
            #addVerifiedAddress,
            [addressSignature],
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i12.AuthOtpResponseDto?>>.value(
                _FakeResult_2<_i12.AuthOtpResponseDto?>(
          this,
          Invocation.method(
            #addVerifiedAddress,
            [addressSignature],
          ),
        )),
      ) as _i18.Future<_i4.Result<_i12.AuthOtpResponseDto?>>);

  @override
  _i18.Future<_i4.LocalReferrer?> getMyReferrerDetails() => (super.noSuchMethod(
        Invocation.method(
          #getMyReferrerDetails,
          [],
        ),
        returnValue: _i18.Future<_i4.LocalReferrer?>.value(),
        returnValueForMissingStub: _i18.Future<_i4.LocalReferrer?>.value(),
      ) as _i18.Future<_i4.LocalReferrer?>);

  @override
  _i18.Future<String?> getUserTransactionCountryCode() => (super.noSuchMethod(
        Invocation.method(
          #getUserTransactionCountryCode,
          [],
        ),
        returnValue: _i18.Future<String?>.value(),
        returnValueForMissingStub: _i18.Future<String?>.value(),
      ) as _i18.Future<String?>);

  @override
  _i18.Future<_i4.Result<_i12.StatusDto?>> getUserSurveyStatus() =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserSurveyStatus,
          [],
        ),
        returnValue: _i18.Future<_i4.Result<_i12.StatusDto?>>.value(
            _FakeResult_2<_i12.StatusDto?>(
          this,
          Invocation.method(
            #getUserSurveyStatus,
            [],
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i12.StatusDto?>>.value(
                _FakeResult_2<_i12.StatusDto?>(
          this,
          Invocation.method(
            #getUserSurveyStatus,
            [],
          ),
        )),
      ) as _i18.Future<_i4.Result<_i12.StatusDto?>>);

  @override
  _i18.Future<
      _i4.Result<
          _i12.UsersSvcVerificationJobSummaryDto?>> getKycStatus() =>
      (super.noSuchMethod(
        Invocation.method(
          #getKycStatus,
          [],
        ),
        returnValue: _i18
            .Future<_i4.Result<_i12.UsersSvcVerificationJobSummaryDto?>>.value(
            _FakeResult_2<_i12.UsersSvcVerificationJobSummaryDto?>(
          this,
          Invocation.method(
            #getKycStatus,
            [],
          ),
        )),
        returnValueForMissingStub: _i18
            .Future<_i4.Result<_i12.UsersSvcVerificationJobSummaryDto?>>.value(
            _FakeResult_2<_i12.UsersSvcVerificationJobSummaryDto?>(
          this,
          Invocation.method(
            #getKycStatus,
            [],
          ),
        )),
      ) as _i18.Future<_i4.Result<_i12.UsersSvcVerificationJobSummaryDto?>>);

  @override
  _i18.Future<_i4.Result<_i12.StatusDto?>> registerReferral({
    required String? referenceSource,
    String? referredBy,
    String? persona,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #registerReferral,
          [],
          {
            #referenceSource: referenceSource,
            #referredBy: referredBy,
            #persona: persona,
          },
        ),
        returnValue: _i18.Future<_i4.Result<_i12.StatusDto?>>.value(
            _FakeResult_2<_i12.StatusDto?>(
          this,
          Invocation.method(
            #registerReferral,
            [],
            {
              #referenceSource: referenceSource,
              #referredBy: referredBy,
              #persona: persona,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i12.StatusDto?>>.value(
                _FakeResult_2<_i12.StatusDto?>(
          this,
          Invocation.method(
            #registerReferral,
            [],
            {
              #referenceSource: referenceSource,
              #referredBy: referredBy,
              #persona: persona,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<_i12.StatusDto?>>);

  @override
  _i18.Future<dynamic> saveUserCountryFeature(
          _i12.CountryFeature? countryFeature) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveUserCountryFeature,
          [countryFeature],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);
}

/// A class which mocks [EthereumService].
///
/// See the documentation for Mockito's code generation for more information.
class MockEthereumService extends _i1.Mock implements _i7.EthereumService {
  @override
  String get trustJavascript => (super.noSuchMethod(
        Invocation.getter(#trustJavascript),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#trustJavascript),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#trustJavascript),
        ),
      ) as String);

  @override
  String get initJavascript => (super.noSuchMethod(
        Invocation.getter(#initJavascript),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#initJavascript),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#initJavascript),
        ),
      ) as String);

  @override
  _i18.Future<void> init() => (super.noSuchMethod(
        Invocation.method(
          #init,
          [],
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  dynamic loadString({
    required String? address,
    required String? rpcUrl,
    required num? chainId,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #loadString,
          [],
          {
            #address: address,
            #rpcUrl: rpcUrl,
            #chainId: chainId,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i18.Future<void> attachEthProviders() => (super.noSuchMethod(
        Invocation.method(
          #attachEthProviders,
          [],
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  void addJavaScriptHandlers() => super.noSuchMethod(
        Invocation.method(
          #addJavaScriptHandlers,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void sendSuccessResponse({
    required String? id,
    String? data,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #sendSuccessResponse,
          [],
          {
            #id: id,
            #data: data,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  void sendErrorResponse({
    required String? id,
    required String? message,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #sendErrorResponse,
          [],
          {
            #id: id,
            #message: message,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i18.Future<void> toSwitchChainView(
    String? id,
    _i4.LocalNetwork? localNetwork,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #toSwitchChainView,
          [
            id,
            localNetwork,
          ],
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  dynamic onSwitchCancelled(String? id) => super.noSuchMethod(
        Invocation.method(
          #onSwitchCancelled,
          [id],
        ),
        returnValueForMissingStub: null,
      );

  @override
  dynamic onSwitchApproved({
    required String? requestId,
    num? chainId,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #onSwitchApproved,
          [],
          {
            #requestId: requestId,
            #chainId: chainId,
          },
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [WebViewService].
///
/// See the documentation for Mockito's code generation for more information.
class MockWebViewService extends _i1.Mock implements _i7.WebViewService {
  @override
  _i9.ReactiveValue<_i27.WebUri?> get visitedUrl => (super.noSuchMethod(
        Invocation.getter(#visitedUrl),
        returnValue: _FakeReactiveValue_11<_i27.WebUri?>(
          this,
          Invocation.getter(#visitedUrl),
        ),
        returnValueForMissingStub: _FakeReactiveValue_11<_i27.WebUri?>(
          this,
          Invocation.getter(#visitedUrl),
        ),
      ) as _i9.ReactiveValue<_i27.WebUri?>);

  @override
  set visitedUrl(_i9.ReactiveValue<_i27.WebUri?>? _visitedUrl) =>
      super.noSuchMethod(
        Invocation.setter(
          #visitedUrl,
          _visitedUrl,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i18.Future<Uri?> get currentUrl => (super.noSuchMethod(
        Invocation.getter(#currentUrl),
        returnValue: _i18.Future<Uri?>.value(),
        returnValueForMissingStub: _i18.Future<Uri?>.value(),
      ) as _i18.Future<Uri?>);

  @override
  int get listenersCount => (super.noSuchMethod(
        Invocation.getter(#listenersCount),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as int);

  @override
  _i18.Future<void> injectJavaScript(String? javascript) => (super.noSuchMethod(
        Invocation.method(
          #injectJavaScript,
          [javascript],
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  void initWebController(_i27.InAppWebViewController? controller) =>
      super.noSuchMethod(
        Invocation.method(
          #initWebController,
          [controller],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addJavaScriptHandler({
    required String? handlerName,
    required _i27.JavaScriptHandlerCallback? callback,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #addJavaScriptHandler,
          [],
          {
            #handlerName: handlerName,
            #callback: callback,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  void loadUrlPath(String? urlPath) => super.noSuchMethod(
        Invocation.method(
          #loadUrlPath,
          [urlPath],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i18.Future<void> loadUrl(Uri? uri) => (super.noSuchMethod(
        Invocation.method(
          #loadUrl,
          [uri],
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  _i18.Future<void> clearCacheAndCookies() => (super.noSuchMethod(
        Invocation.method(
          #clearCacheAndCookies,
          [],
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  _i18.Future<void> reload() => (super.noSuchMethod(
        Invocation.method(
          #reload,
          [],
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  void listenToReactiveValues(List<dynamic>? reactiveValues) =>
      super.noSuchMethod(
        Invocation.method(
          #listenToReactiveValues,
          [reactiveValues],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [OnboardProviderService].
///
/// See the documentation for Mockito's code generation for more information.
class MockOnboardProviderService extends _i1.Mock
    implements _i7.OnboardProviderService {
  @override
  _i18.Future<void> attachAppProviders() => (super.noSuchMethod(
        Invocation.method(
          #attachAppProviders,
          [],
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  void addJavaScriptHandlers() => super.noSuchMethod(
        Invocation.method(
          #addJavaScriptHandlers,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [NetworkService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNetworkService extends _i1.Mock implements _i7.NetworkService {
  @override
  _i9.ReactiveValue<_i4.LocalNetwork?> get currentChainReactive =>
      (super.noSuchMethod(
        Invocation.getter(#currentChainReactive),
        returnValue: _FakeReactiveValue_11<_i4.LocalNetwork?>(
          this,
          Invocation.getter(#currentChainReactive),
        ),
        returnValueForMissingStub: _FakeReactiveValue_11<_i4.LocalNetwork?>(
          this,
          Invocation.getter(#currentChainReactive),
        ),
      ) as _i9.ReactiveValue<_i4.LocalNetwork?>);

  @override
  _i9.ReactiveValue<_i4.LocalNetwork?> get lastSelectedChainReactive =>
      (super.noSuchMethod(
        Invocation.getter(#lastSelectedChainReactive),
        returnValue: _FakeReactiveValue_11<_i4.LocalNetwork?>(
          this,
          Invocation.getter(#lastSelectedChainReactive),
        ),
        returnValueForMissingStub: _FakeReactiveValue_11<_i4.LocalNetwork?>(
          this,
          Invocation.getter(#lastSelectedChainReactive),
        ),
      ) as _i9.ReactiveValue<_i4.LocalNetwork?>);

  @override
  _i9.ReactiveValue<_i4.LocalNetwork?> get lastSelectedNFTChainReactive =>
      (super.noSuchMethod(
        Invocation.getter(#lastSelectedNFTChainReactive),
        returnValue: _FakeReactiveValue_11<_i4.LocalNetwork?>(
          this,
          Invocation.getter(#lastSelectedNFTChainReactive),
        ),
        returnValueForMissingStub: _FakeReactiveValue_11<_i4.LocalNetwork?>(
          this,
          Invocation.getter(#lastSelectedNFTChainReactive),
        ),
      ) as _i9.ReactiveValue<_i4.LocalNetwork?>);

  @override
  bool get isCurrentNetworkSupportedByExchange => (super.noSuchMethod(
        Invocation.getter(#isCurrentNetworkSupportedByExchange),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  List<_i4.LocalNetwork> get networks => (super.noSuchMethod(
        Invocation.getter(#networks),
        returnValue: <_i4.LocalNetwork>[],
        returnValueForMissingStub: <_i4.LocalNetwork>[],
      ) as List<_i4.LocalNetwork>);

  @override
  int get listenersCount => (super.noSuchMethod(
        Invocation.getter(#listenersCount),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as int);

  @override
  dynamic setActiveChain(_i4.LocalNetwork? localNetwork) => super.noSuchMethod(
        Invocation.method(
          #setActiveChain,
          [localNetwork],
        ),
        returnValueForMissingStub: null,
      );

  @override
  dynamic setLastSelectedChain(_i4.LocalNetwork? localNetwork) =>
      super.noSuchMethod(
        Invocation.method(
          #setLastSelectedChain,
          [localNetwork],
        ),
        returnValueForMissingStub: null,
      );

  @override
  dynamic addChain(_i4.LocalNetwork? localNetwork) => super.noSuchMethod(
        Invocation.method(
          #addChain,
          [localNetwork],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i4.LocalNetwork? getAddedChain(String? chainId) => (super.noSuchMethod(
        Invocation.method(
          #getAddedChain,
          [chainId],
        ),
        returnValueForMissingStub: null,
      ) as _i4.LocalNetwork?);

  @override
  void getActiveChain() => super.noSuchMethod(
        Invocation.method(
          #getActiveChain,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  List<_i4.LocalNetwork> getAppNetworks() => (super.noSuchMethod(
        Invocation.method(
          #getAppNetworks,
          [],
        ),
        returnValue: <_i4.LocalNetwork>[],
        returnValueForMissingStub: <_i4.LocalNetwork>[],
      ) as List<_i4.LocalNetwork>);

  @override
  bool isNetworkSupportedByExchange(_i4.LocalNetwork? localNetwork) =>
      (super.noSuchMethod(
        Invocation.method(
          #isNetworkSupportedByExchange,
          [localNetwork],
        ),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  List<_i4.LocalNetwork> getExchangeNetworks() => (super.noSuchMethod(
        Invocation.method(
          #getExchangeNetworks,
          [],
        ),
        returnValue: <_i4.LocalNetwork>[],
        returnValueForMissingStub: <_i4.LocalNetwork>[],
      ) as List<_i4.LocalNetwork>);

  @override
  void saveActiveNetworkUsingId(String? coreNetworkId) => super.noSuchMethod(
        Invocation.method(
          #saveActiveNetworkUsingId,
          [coreNetworkId],
        ),
        returnValueForMissingStub: null,
      );

  @override
  int sortLocalTokenNetworks(
    _i4.LocalTokenNetworkBalance? a,
    _i4.LocalTokenNetworkBalance? b,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #sortLocalTokenNetworks,
          [
            a,
            b,
          ],
        ),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as int);

  @override
  int sortLocalNetwork(
    _i4.LocalNetwork? a,
    _i4.LocalNetwork? b,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #sortLocalNetwork,
          [
            a,
            b,
          ],
        ),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as int);

  @override
  void getLastSelectedChain() => super.noSuchMethod(
        Invocation.method(
          #getLastSelectedChain,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i4.LocalNetwork? findNetworkByChainId(int? chainId) => (super.noSuchMethod(
        Invocation.method(
          #findNetworkByChainId,
          [chainId],
        ),
        returnValueForMissingStub: null,
      ) as _i4.LocalNetwork?);

  @override
  void listenToReactiveValues(List<dynamic>? reactiveValues) =>
      super.noSuchMethod(
        Invocation.method(
          #listenToReactiveValues,
          [reactiveValues],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [SecureAppService].
///
/// See the documentation for Mockito's code generation for more information.
class MockSecureAppService extends _i1.Mock implements _i7.SecureAppService {
  @override
  _i10.MethodChannel get iosSecureScreenShotChannel => (super.noSuchMethod(
        Invocation.getter(#iosSecureScreenShotChannel),
        returnValue: _FakeMethodChannel_12(
          this,
          Invocation.getter(#iosSecureScreenShotChannel),
        ),
        returnValueForMissingStub: _FakeMethodChannel_12(
          this,
          Invocation.getter(#iosSecureScreenShotChannel),
        ),
      ) as _i10.MethodChannel);
}

/// A class which mocks [CryptoService].
///
/// See the documentation for Mockito's code generation for more information.
class MockCryptoService extends _i1.Mock implements _i7.CryptoService {
  @override
  _i9.ReactiveValue<bool> get tokenRefreshed => (super.noSuchMethod(
        Invocation.getter(#tokenRefreshed),
        returnValue: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#tokenRefreshed),
        ),
        returnValueForMissingStub: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#tokenRefreshed),
        ),
      ) as _i9.ReactiveValue<bool>);

  @override
  _i8.Box<_i4.LocalToken> get tokenBox => (super.noSuchMethod(
        Invocation.getter(#tokenBox),
        returnValue: _FakeBox_10<_i4.LocalToken>(
          this,
          Invocation.getter(#tokenBox),
        ),
        returnValueForMissingStub: _FakeBox_10<_i4.LocalToken>(
          this,
          Invocation.getter(#tokenBox),
        ),
      ) as _i8.Box<_i4.LocalToken>);

  @override
  _i8.Box<_i4.LocalToken> get topTokensBox => (super.noSuchMethod(
        Invocation.getter(#topTokensBox),
        returnValue: _FakeBox_10<_i4.LocalToken>(
          this,
          Invocation.getter(#topTokensBox),
        ),
        returnValueForMissingStub: _FakeBox_10<_i4.LocalToken>(
          this,
          Invocation.getter(#topTokensBox),
        ),
      ) as _i8.Box<_i4.LocalToken>);

  @override
  List<_i4.LocalToken> get topTokens => (super.noSuchMethod(
        Invocation.getter(#topTokens),
        returnValue: <_i4.LocalToken>[],
        returnValueForMissingStub: <_i4.LocalToken>[],
      ) as List<_i4.LocalToken>);

  @override
  bool get isTokenDbEmpty => (super.noSuchMethod(
        Invocation.getter(#isTokenDbEmpty),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  int get listenersCount => (super.noSuchMethod(
        Invocation.getter(#listenersCount),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as int);

  @override
  String balance() => (super.noSuchMethod(
        Invocation.method(
          #balance,
          [],
        ),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.method(
            #balance,
            [],
          ),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.method(
            #balance,
            [],
          ),
        ),
      ) as String);

  @override
  double spotBalanceAsDouble() => (super.noSuchMethod(
        Invocation.method(
          #spotBalanceAsDouble,
          [],
        ),
        returnValue: 0.0,
        returnValueForMissingStub: 0.0,
      ) as double);

  @override
  _i4.LocalToken? getTokenLocalGivenSymbol({required String? symbol}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getTokenLocalGivenSymbol,
          [],
          {#symbol: symbol},
        ),
        returnValueForMissingStub: null,
      ) as _i4.LocalToken?);

  @override
  _i4.LocalToken? getLocalTokenFrom({
    required String? contractAddress,
    required String? networkId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getLocalTokenFrom,
          [],
          {
            #contractAddress: contractAddress,
            #networkId: networkId,
          },
        ),
        returnValueForMissingStub: null,
      ) as _i4.LocalToken?);

  @override
  _i18.Future<dynamic> fetchNetwork() => (super.noSuchMethod(
        Invocation.method(
          #fetchNetwork,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> fetchExchangeNetworks() => (super.noSuchMethod(
        Invocation.method(
          #fetchExchangeNetworks,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<_i4.Result<List<_i4.LocalToken>>> getWalletToken({
    required String? networkId,
    bool? forceRefresh = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getWalletToken,
          [],
          {
            #networkId: networkId,
            #forceRefresh: forceRefresh,
          },
        ),
        returnValue: _i18.Future<_i4.Result<List<_i4.LocalToken>>>.value(
            _FakeResult_2<List<_i4.LocalToken>>(
          this,
          Invocation.method(
            #getWalletToken,
            [],
            {
              #networkId: networkId,
              #forceRefresh: forceRefresh,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<List<_i4.LocalToken>>>.value(
                _FakeResult_2<List<_i4.LocalToken>>(
          this,
          Invocation.method(
            #getWalletToken,
            [],
            {
              #networkId: networkId,
              #forceRefresh: forceRefresh,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<List<_i4.LocalToken>>>);

  @override
  _i18.Future<_i4.Result<_i12.GetWeightedRates200Response?>> getWeightedRates({
    String? fiatSymbol,
    String? tokenSymbol,
    _i12.OfferType? offerType,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getWeightedRates,
          [],
          {
            #fiatSymbol: fiatSymbol,
            #tokenSymbol: tokenSymbol,
            #offerType: offerType,
          },
        ),
        returnValue:
            _i18.Future<_i4.Result<_i12.GetWeightedRates200Response?>>.value(
                _FakeResult_2<_i12.GetWeightedRates200Response?>(
          this,
          Invocation.method(
            #getWeightedRates,
            [],
            {
              #fiatSymbol: fiatSymbol,
              #tokenSymbol: tokenSymbol,
              #offerType: offerType,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i12.GetWeightedRates200Response?>>.value(
                _FakeResult_2<_i12.GetWeightedRates200Response?>(
          this,
          Invocation.method(
            #getWeightedRates,
            [],
            {
              #fiatSymbol: fiatSymbol,
              #tokenSymbol: tokenSymbol,
              #offerType: offerType,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<_i12.GetWeightedRates200Response?>>);

  @override
  _i18.Future<dynamic> getTokenRate() => (super.noSuchMethod(
        Invocation.method(
          #getTokenRate,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> saveToken(
    _i4.LocalToken? token, {
    bool? hideAllSpamTokens,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveToken,
          [token],
          {#hideAllSpamTokens: hideAllSpamTokens},
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<_i4.LocalTransaction?> getTransactionByHash({
    required String? hash,
    required String? networkId,
    _i4.LocalToken? token,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getTransactionByHash,
          [],
          {
            #hash: hash,
            #networkId: networkId,
            #token: token,
          },
        ),
        returnValue: _i18.Future<_i4.LocalTransaction?>.value(),
        returnValueForMissingStub: _i18.Future<_i4.LocalTransaction?>.value(),
      ) as _i18.Future<_i4.LocalTransaction?>);

  @override
  _i18.Future<
      _i4.Result<_i28.AccountTokenTransactionsResponse>?> getTokenTransactions({
    required String? networkId,
    required String? contractAddress,
    required String? walletAddress,
    String? txHash,
    int? page = 1,
    int? size = 20,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getTokenTransactions,
          [],
          {
            #networkId: networkId,
            #contractAddress: contractAddress,
            #walletAddress: walletAddress,
            #txHash: txHash,
            #page: page,
            #size: size,
          },
        ),
        returnValue: _i18
            .Future<_i4.Result<_i28.AccountTokenTransactionsResponse>?>.value(),
        returnValueForMissingStub: _i18
            .Future<_i4.Result<_i28.AccountTokenTransactionsResponse>?>.value(),
      ) as _i18.Future<_i4.Result<_i28.AccountTokenTransactionsResponse>?>);

  @override
  _i18.Future<String?> getRpc({required String? networkId}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getRpc,
          [],
          {#networkId: networkId},
        ),
        returnValue: _i18.Future<String?>.value(),
        returnValueForMissingStub: _i18.Future<String?>.value(),
      ) as _i18.Future<String?>);

  @override
  _i18.Future<_i4.Result<_i28.TokenWithAssetId?>> getTokenByContractAddress({
    required String? networkId,
    required String? contractAddress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getTokenByContractAddress,
          [],
          {
            #networkId: networkId,
            #contractAddress: contractAddress,
          },
        ),
        returnValue: _i18.Future<_i4.Result<_i28.TokenWithAssetId?>>.value(
            _FakeResult_2<_i28.TokenWithAssetId?>(
          this,
          Invocation.method(
            #getTokenByContractAddress,
            [],
            {
              #networkId: networkId,
              #contractAddress: contractAddress,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i28.TokenWithAssetId?>>.value(
                _FakeResult_2<_i28.TokenWithAssetId?>(
          this,
          Invocation.method(
            #getTokenByContractAddress,
            [],
            {
              #networkId: networkId,
              #contractAddress: contractAddress,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<_i28.TokenWithAssetId?>>);

  @override
  _i18.Future<_i4.LocalTransaction?> getTradingTransactionByHash({
    required String? hash,
    required String? networkId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getTradingTransactionByHash,
          [],
          {
            #hash: hash,
            #networkId: networkId,
          },
        ),
        returnValue: _i18.Future<_i4.LocalTransaction?>.value(),
        returnValueForMissingStub: _i18.Future<_i4.LocalTransaction?>.value(),
      ) as _i18.Future<_i4.LocalTransaction?>);

  @override
  _i18.Future<_i4.Result<bool>> addCustomToken({
    required String? networkId,
    required String? address,
    required _i28.CustomAssetRequest? customAssetRequest,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #addCustomToken,
          [],
          {
            #networkId: networkId,
            #address: address,
            #customAssetRequest: customAssetRequest,
          },
        ),
        returnValue: _i18.Future<_i4.Result<bool>>.value(_FakeResult_2<bool>(
          this,
          Invocation.method(
            #addCustomToken,
            [],
            {
              #networkId: networkId,
              #address: address,
              #customAssetRequest: customAssetRequest,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<bool>>.value(_FakeResult_2<bool>(
          this,
          Invocation.method(
            #addCustomToken,
            [],
            {
              #networkId: networkId,
              #address: address,
              #customAssetRequest: customAssetRequest,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<bool>>);

  @override
  _i18.Future<_i4.Result<List<_i4.LocalToken>>> searchToken({
    String? networkId,
    String? symbol,
    String? name,
    String? keyword,
    int? size = 20,
    _i29.CancelToken? cancelToken,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchToken,
          [],
          {
            #networkId: networkId,
            #symbol: symbol,
            #name: name,
            #keyword: keyword,
            #size: size,
            #cancelToken: cancelToken,
          },
        ),
        returnValue: _i18.Future<_i4.Result<List<_i4.LocalToken>>>.value(
            _FakeResult_2<List<_i4.LocalToken>>(
          this,
          Invocation.method(
            #searchToken,
            [],
            {
              #networkId: networkId,
              #symbol: symbol,
              #name: name,
              #keyword: keyword,
              #size: size,
              #cancelToken: cancelToken,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<List<_i4.LocalToken>>>.value(
                _FakeResult_2<List<_i4.LocalToken>>(
          this,
          Invocation.method(
            #searchToken,
            [],
            {
              #networkId: networkId,
              #symbol: symbol,
              #name: name,
              #keyword: keyword,
              #size: size,
              #cancelToken: cancelToken,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<List<_i4.LocalToken>>>);

  @override
  _i18.Future<_i4.Result<_i28.GasEstimate?>> getGasEstimates({
    required String? networkId,
    required _i28.GasEstimateRequest? gasEstimateRequest,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getGasEstimates,
          [],
          {
            #networkId: networkId,
            #gasEstimateRequest: gasEstimateRequest,
          },
        ),
        returnValue: _i18.Future<_i4.Result<_i28.GasEstimate?>>.value(
            _FakeResult_2<_i28.GasEstimate?>(
          this,
          Invocation.method(
            #getGasEstimates,
            [],
            {
              #networkId: networkId,
              #gasEstimateRequest: gasEstimateRequest,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i28.GasEstimate?>>.value(
                _FakeResult_2<_i28.GasEstimate?>(
          this,
          Invocation.method(
            #getGasEstimates,
            [],
            {
              #networkId: networkId,
              #gasEstimateRequest: gasEstimateRequest,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<_i28.GasEstimate?>>);

  @override
  bool isUserToken(_i4.LocalToken? token) => (super.noSuchMethod(
        Invocation.method(
          #isUserToken,
          [token],
        ),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i18.Future<dynamic> hideOrShowAllSpamToken({required bool? hide}) =>
      (super.noSuchMethod(
        Invocation.method(
          #hideOrShowAllSpamToken,
          [],
          {#hide: hide},
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<List<_i4.LocalTransaction>> fetchTopTransaction({
    required String? networkId,
    required String? contractAddress,
    String? txHash,
    int? size = 20,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #fetchTopTransaction,
          [],
          {
            #networkId: networkId,
            #contractAddress: contractAddress,
            #txHash: txHash,
            #size: size,
          },
        ),
        returnValue: _i18.Future<List<_i4.LocalTransaction>>.value(
            <_i4.LocalTransaction>[]),
        returnValueForMissingStub:
            _i18.Future<List<_i4.LocalTransaction>>.value(
                <_i4.LocalTransaction>[]),
      ) as _i18.Future<List<_i4.LocalTransaction>>);

  @override
  _i18.Future<bool> checkRampHistory() => (super.noSuchMethod(
        Invocation.method(
          #checkRampHistory,
          [],
        ),
        returnValue: _i18.Future<bool>.value(false),
        returnValueForMissingStub: _i18.Future<bool>.value(false),
      ) as _i18.Future<bool>);

  @override
  _i18.Future<_i4.Result<_i30.LocalTokenMarketData>> getAssetInfo({
    required String? assetId,
    bool? forceRefresh = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getAssetInfo,
          [],
          {
            #assetId: assetId,
            #forceRefresh: forceRefresh,
          },
        ),
        returnValue: _i18.Future<_i4.Result<_i30.LocalTokenMarketData>>.value(
            _FakeResult_2<_i30.LocalTokenMarketData>(
          this,
          Invocation.method(
            #getAssetInfo,
            [],
            {
              #assetId: assetId,
              #forceRefresh: forceRefresh,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i30.LocalTokenMarketData>>.value(
                _FakeResult_2<_i30.LocalTokenMarketData>(
          this,
          Invocation.method(
            #getAssetInfo,
            [],
            {
              #assetId: assetId,
              #forceRefresh: forceRefresh,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<_i30.LocalTokenMarketData>>);

  @override
  void listenToReactiveValues(List<dynamic>? reactiveValues) =>
      super.noSuchMethod(
        Invocation.method(
          #listenToReactiveValues,
          [reactiveValues],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [NestcoinRpcDatabaseService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNestcoinRpcDatabaseService extends _i1.Mock
    implements _i7.NestcoinRpcDatabaseService {
  @override
  _i18.Future<dynamic> nukeDb() => (super.noSuchMethod(
        Invocation.method(
          #nukeDb,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<_i4.NestcoinRPC?> getRpc({required int? chainId}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getRpc,
          [],
          {#chainId: chainId},
        ),
        returnValue: _i18.Future<_i4.NestcoinRPC?>.value(),
        returnValueForMissingStub: _i18.Future<_i4.NestcoinRPC?>.value(),
      ) as _i18.Future<_i4.NestcoinRPC?>);

  @override
  _i18.Future<dynamic> saveRpc({required List<_i4.NestcoinRPC>? list}) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveRpc,
          [],
          {#list: list},
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);
}

/// A class which mocks [PushNotificationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockPushNotificationService extends _i1.Mock
    implements _i7.PushNotificationService {
  @override
  String get customerMode => (super.noSuchMethod(
        Invocation.getter(#customerMode),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#customerMode),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#customerMode),
        ),
      ) as String);

  @override
  String get merchantMode => (super.noSuchMethod(
        Invocation.getter(#merchantMode),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#merchantMode),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#merchantMode),
        ),
      ) as String);

  @override
  _i11.Random get random => (super.noSuchMethod(
        Invocation.getter(#random),
        returnValue: _FakeRandom_13(
          this,
          Invocation.getter(#random),
        ),
        returnValueForMissingStub: _FakeRandom_13(
          this,
          Invocation.getter(#random),
        ),
      ) as _i11.Random);

  @override
  set random(_i11.Random? _random) => super.noSuchMethod(
        Invocation.setter(
          #random,
          _random,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i18.Future<_i31.NotificationSettings?> requestNotificationPermission() =>
      (super.noSuchMethod(
        Invocation.method(
          #requestNotificationPermission,
          [],
        ),
        returnValue: _i18.Future<_i31.NotificationSettings?>.value(),
        returnValueForMissingStub:
            _i18.Future<_i31.NotificationSettings?>.value(),
      ) as _i18.Future<_i31.NotificationSettings?>);

  @override
  _i18.Future<void> setupInteractedMessage() => (super.noSuchMethod(
        Invocation.method(
          #setupInteractedMessage,
          [],
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  _i18.Future<void> deregisterPushToken() => (super.noSuchMethod(
        Invocation.method(
          #deregisterPushToken,
          [],
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  _i18.Future<void> updateNotificationToken() => (super.noSuchMethod(
        Invocation.method(
          #updateNotificationToken,
          [],
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  _i18.Future<dynamic> showNotification({
    required int? id,
    String? title,
    String? body,
    String? payload,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #showNotification,
          [],
          {
            #id: id,
            #title: title,
            #body: body,
            #payload: payload,
          },
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<String?> getToken() => (super.noSuchMethod(
        Invocation.method(
          #getToken,
          [],
        ),
        returnValue: _i18.Future<String?>.value(),
        returnValueForMissingStub: _i18.Future<String?>.value(),
      ) as _i18.Future<String?>);

  @override
  _i18.Future<bool> hasNotificationsPermission() => (super.noSuchMethod(
        Invocation.method(
          #hasNotificationsPermission,
          [],
        ),
        returnValue: _i18.Future<bool>.value(false),
        returnValueForMissingStub: _i18.Future<bool>.value(false),
      ) as _i18.Future<bool>);

  @override
  _i18.Future<dynamic> scheduleChatWithUsNotification() => (super.noSuchMethod(
        Invocation.method(
          #scheduleChatWithUsNotification,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> scheduleNotification({
    required int? id,
    required String? title,
    required String? subtitle,
    required DateTime? date,
    String? payload,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #scheduleNotification,
          [],
          {
            #id: id,
            #title: title,
            #subtitle: subtitle,
            #date: date,
            #payload: payload,
          },
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);
}

/// A class which mocks [PreferenceService].
///
/// See the documentation for Mockito's code generation for more information.
class MockPreferenceService extends _i1.Mock implements _i7.PreferenceService {
  @override
  _i18.Future<void> init() => (super.noSuchMethod(
        Invocation.method(
          #init,
          [],
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  String? getString({required String? key}) => (super.noSuchMethod(
        Invocation.method(
          #getString,
          [],
          {#key: key},
        ),
        returnValueForMissingStub: null,
      ) as String?);

  @override
  _i18.Future<dynamic> remove(String? key) => (super.noSuchMethod(
        Invocation.method(
          #remove,
          [key],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  bool? getBool({required String? key}) => (super.noSuchMethod(
        Invocation.method(
          #getBool,
          [],
          {#key: key},
        ),
        returnValueForMissingStub: null,
      ) as bool?);

  @override
  _i18.Future<bool> setString({
    required String? key,
    required String? value,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #setString,
          [],
          {
            #key: key,
            #value: value,
          },
        ),
        returnValue: _i18.Future<bool>.value(false),
        returnValueForMissingStub: _i18.Future<bool>.value(false),
      ) as _i18.Future<bool>);

  @override
  _i18.Future<bool> setBool({
    required String? key,
    required bool? value,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #setBool,
          [],
          {
            #key: key,
            #value: value,
          },
        ),
        returnValue: _i18.Future<bool>.value(false),
        returnValueForMissingStub: _i18.Future<bool>.value(false),
      ) as _i18.Future<bool>);

  @override
  _i18.Future<bool> setInt({
    required String? key,
    required int? value,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #setInt,
          [],
          {
            #key: key,
            #value: value,
          },
        ),
        returnValue: _i18.Future<bool>.value(false),
        returnValueForMissingStub: _i18.Future<bool>.value(false),
      ) as _i18.Future<bool>);

  @override
  _i18.Future<bool> setDouble({
    required String? key,
    required double? value,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #setDouble,
          [],
          {
            #key: key,
            #value: value,
          },
        ),
        returnValue: _i18.Future<bool>.value(false),
        returnValueForMissingStub: _i18.Future<bool>.value(false),
      ) as _i18.Future<bool>);

  @override
  _i18.Future<bool> setStringList(
    String? key,
    List<String>? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setStringList,
          [
            key,
            value,
          ],
        ),
        returnValue: _i18.Future<bool>.value(false),
        returnValueForMissingStub: _i18.Future<bool>.value(false),
      ) as _i18.Future<bool>);

  @override
  int? getInt({required String? key}) => (super.noSuchMethod(
        Invocation.method(
          #getInt,
          [],
          {#key: key},
        ),
        returnValueForMissingStub: null,
      ) as int?);

  @override
  double? getDouble({required String? key}) => (super.noSuchMethod(
        Invocation.method(
          #getDouble,
          [],
          {#key: key},
        ),
        returnValueForMissingStub: null,
      ) as double?);

  @override
  List<String>? getStringList({required String? key}) => (super.noSuchMethod(
        Invocation.method(
          #getStringList,
          [],
          {#key: key},
        ),
        returnValueForMissingStub: null,
      ) as List<String>?);

  @override
  _i18.Future<bool> clear() => (super.noSuchMethod(
        Invocation.method(
          #clear,
          [],
        ),
        returnValue: _i18.Future<bool>.value(false),
        returnValueForMissingStub: _i18.Future<bool>.value(false),
      ) as _i18.Future<bool>);

  @override
  bool hideSpamToken() => (super.noSuchMethod(
        Invocation.method(
          #hideSpamToken,
          [],
        ),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  bool containsKey(String? key) => (super.noSuchMethod(
        Invocation.method(
          #containsKey,
          [key],
        ),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);
}

/// A class which mocks [SnackbarService].
///
/// See the documentation for Mockito's code generation for more information.
class MockSnackbarService extends _i1.Mock implements _i16.SnackbarService {
  @override
  bool get isSnackbarOpen => (super.noSuchMethod(
        Invocation.getter(#isSnackbarOpen),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  void registerSnackbarConfig(_i16.SnackbarConfig? config) =>
      super.noSuchMethod(
        Invocation.method(
          #registerSnackbarConfig,
          [config],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void registerCustomMainButtonBuilder({
    dynamic variant,
    _i15.Widget Function(
      String?,
      Function?,
    )? builder,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #registerCustomMainButtonBuilder,
          [],
          {
            #variant: variant,
            #builder: builder,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  void registerCustomSnackbarConfig({
    required dynamic variant,
    _i16.SnackbarConfig? config,
    _i16.SnackbarConfig Function()? configBuilder,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #registerCustomSnackbarConfig,
          [],
          {
            #variant: variant,
            #config: config,
            #configBuilder: configBuilder,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  void showSnackbar({
    String? title = '',
    required String? message,
    dynamic Function(dynamic)? onTap,
    Duration? duration,
    String? mainButtonTitle,
    void Function()? onMainButtonTapped,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #showSnackbar,
          [],
          {
            #title: title,
            #message: message,
            #onTap: onTap,
            #duration: duration,
            #mainButtonTitle: mainButtonTitle,
            #onMainButtonTapped: onMainButtonTapped,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i18.Future<dynamic>? showCustomSnackBar({
    required String? message,
    _i15.TextStyle? messageTextStyle,
    required dynamic variant,
    String? title,
    _i15.TextStyle? titleTextStyle,
    String? mainButtonTitle,
    _i15.ButtonStyle? mainButtonStyle,
    void Function()? onMainButtonTapped,
    Function? onTap,
    Duration? duration,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #showCustomSnackBar,
          [],
          {
            #message: message,
            #messageTextStyle: messageTextStyle,
            #variant: variant,
            #title: title,
            #titleTextStyle: titleTextStyle,
            #mainButtonTitle: mainButtonTitle,
            #mainButtonStyle: mainButtonStyle,
            #onMainButtonTapped: onMainButtonTapped,
            #onTap: onTap,
            #duration: duration,
          },
        ),
        returnValueForMissingStub: null,
      ) as _i18.Future<dynamic>?);

  @override
  _i18.Future<void> closeSnackbar() => (super.noSuchMethod(
        Invocation.method(
          #closeSnackbar,
          [],
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);
}

/// A class which mocks [SwapService].
///
/// See the documentation for Mockito's code generation for more information.
class MockSwapService extends _i1.Mock implements _i7.SwapService {
  @override
  _i12.WalletsSwapClient get walletsSwapClient => (super.noSuchMethod(
        Invocation.getter(#walletsSwapClient),
        returnValue: _FakeWalletsSwapClient_14(
          this,
          Invocation.getter(#walletsSwapClient),
        ),
        returnValueForMissingStub: _FakeWalletsSwapClient_14(
          this,
          Invocation.getter(#walletsSwapClient),
        ),
      ) as _i12.WalletsSwapClient);

  @override
  _i9.ReactiveValue<List<_i4.LocalToken>> get swappableTokensReactive =>
      (super.noSuchMethod(
        Invocation.getter(#swappableTokensReactive),
        returnValue: _FakeReactiveValue_11<List<_i4.LocalToken>>(
          this,
          Invocation.getter(#swappableTokensReactive),
        ),
        returnValueForMissingStub: _FakeReactiveValue_11<List<_i4.LocalToken>>(
          this,
          Invocation.getter(#swappableTokensReactive),
        ),
      ) as _i9.ReactiveValue<List<_i4.LocalToken>>);

  @override
  set swapFeeConfig(_i12.SwapFeeConfig? _swapFeeConfig) => super.noSuchMethod(
        Invocation.setter(
          #swapFeeConfig,
          _swapFeeConfig,
        ),
        returnValueForMissingStub: null,
      );

  @override
  List<_i4.LocalToken> get swappableTokensList => (super.noSuchMethod(
        Invocation.getter(#swappableTokensList),
        returnValue: <_i4.LocalToken>[],
        returnValueForMissingStub: <_i4.LocalToken>[],
      ) as List<_i4.LocalToken>);

  @override
  int get listenersCount => (super.noSuchMethod(
        Invocation.getter(#listenersCount),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as int);

  @override
  _i18.Future<dynamic> getAvailableTokens(
          {required List<_i28.SupportedNetwork>? networks}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getAvailableTokens,
          [],
          {#networks: networks},
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<List<_i4.LocalToken>> fetchTokens(
          _i7.SwapIsolateArgs? swapIsolateArgs) =>
      (super.noSuchMethod(
        Invocation.method(
          #fetchTokens,
          [swapIsolateArgs],
        ),
        returnValue:
            _i18.Future<List<_i4.LocalToken>>.value(<_i4.LocalToken>[]),
        returnValueForMissingStub:
            _i18.Future<List<_i4.LocalToken>>.value(<_i4.LocalToken>[]),
      ) as _i18.Future<List<_i4.LocalToken>>);

  @override
  _i18.Future<_i4.Result<bool>> isApprovalRequired({
    required String? tokenAddress,
    required String? walletAddress,
    required String? amount,
    required String? chainId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #isApprovalRequired,
          [],
          {
            #tokenAddress: tokenAddress,
            #walletAddress: walletAddress,
            #amount: amount,
            #chainId: chainId,
          },
        ),
        returnValue: _i18.Future<_i4.Result<bool>>.value(_FakeResult_2<bool>(
          this,
          Invocation.method(
            #isApprovalRequired,
            [],
            {
              #tokenAddress: tokenAddress,
              #walletAddress: walletAddress,
              #amount: amount,
              #chainId: chainId,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<bool>>.value(_FakeResult_2<bool>(
          this,
          Invocation.method(
            #isApprovalRequired,
            [],
            {
              #tokenAddress: tokenAddress,
              #walletAddress: walletAddress,
              #amount: amount,
              #chainId: chainId,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<bool>>);

  @override
  _i18.Future<_i4.Result<_i4.SwapQuote>> getQuote({
    required Map<String, dynamic>? query,
    required String? chainId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getQuote,
          [],
          {
            #query: query,
            #chainId: chainId,
          },
        ),
        returnValue: _i18.Future<_i4.Result<_i4.SwapQuote>>.value(
            _FakeResult_2<_i4.SwapQuote>(
          this,
          Invocation.method(
            #getQuote,
            [],
            {
              #query: query,
              #chainId: chainId,
            },
          ),
        )),
        returnValueForMissingStub: _i18.Future<_i4.Result<_i4.SwapQuote>>.value(
            _FakeResult_2<_i4.SwapQuote>(
          this,
          Invocation.method(
            #getQuote,
            [],
            {
              #query: query,
              #chainId: chainId,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<_i4.SwapQuote>>);

  @override
  _i18.Future<_i4.Result<Map<String, dynamic>>>
      buildTxForApproveTradeWithRouter({
    required String? tokenAddress,
    required String? amount,
    required String? chainId,
  }) =>
          (super.noSuchMethod(
            Invocation.method(
              #buildTxForApproveTradeWithRouter,
              [],
              {
                #tokenAddress: tokenAddress,
                #amount: amount,
                #chainId: chainId,
              },
            ),
            returnValue: _i18.Future<_i4.Result<Map<String, dynamic>>>.value(
                _FakeResult_2<Map<String, dynamic>>(
              this,
              Invocation.method(
                #buildTxForApproveTradeWithRouter,
                [],
                {
                  #tokenAddress: tokenAddress,
                  #amount: amount,
                  #chainId: chainId,
                },
              ),
            )),
            returnValueForMissingStub:
                _i18.Future<_i4.Result<Map<String, dynamic>>>.value(
                    _FakeResult_2<Map<String, dynamic>>(
              this,
              Invocation.method(
                #buildTxForApproveTradeWithRouter,
                [],
                {
                  #tokenAddress: tokenAddress,
                  #amount: amount,
                  #chainId: chainId,
                },
              ),
            )),
          ) as _i18.Future<_i4.Result<Map<String, dynamic>>>);

  @override
  _i18.Future<_i4.Result<Map<String, dynamic>>> buildTxForSwap({
    required String? chainId,
    required String? walletAddress,
    required String? fromTokenAddress,
    required String? toTokenAddress,
    required String? amount,
    required String? receiverWalletAddress,
    num? slippage = 1,
    num? fee,
    String? referrerAddress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #buildTxForSwap,
          [],
          {
            #chainId: chainId,
            #walletAddress: walletAddress,
            #fromTokenAddress: fromTokenAddress,
            #toTokenAddress: toTokenAddress,
            #amount: amount,
            #receiverWalletAddress: receiverWalletAddress,
            #slippage: slippage,
            #fee: fee,
            #referrerAddress: referrerAddress,
          },
        ),
        returnValue: _i18.Future<_i4.Result<Map<String, dynamic>>>.value(
            _FakeResult_2<Map<String, dynamic>>(
          this,
          Invocation.method(
            #buildTxForSwap,
            [],
            {
              #chainId: chainId,
              #walletAddress: walletAddress,
              #fromTokenAddress: fromTokenAddress,
              #toTokenAddress: toTokenAddress,
              #amount: amount,
              #receiverWalletAddress: receiverWalletAddress,
              #slippage: slippage,
              #fee: fee,
              #referrerAddress: referrerAddress,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<Map<String, dynamic>>>.value(
                _FakeResult_2<Map<String, dynamic>>(
          this,
          Invocation.method(
            #buildTxForSwap,
            [],
            {
              #chainId: chainId,
              #walletAddress: walletAddress,
              #fromTokenAddress: fromTokenAddress,
              #toTokenAddress: toTokenAddress,
              #amount: amount,
              #receiverWalletAddress: receiverWalletAddress,
              #slippage: slippage,
              #fee: fee,
              #referrerAddress: referrerAddress,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<Map<String, dynamic>>>);

  @override
  bool isSwappableToken(_i4.LocalToken? token) => (super.noSuchMethod(
        Invocation.method(
          #isSwappableToken,
          [token],
        ),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i4.LocalToken? getNativeToken(String? networkId) => (super.noSuchMethod(
        Invocation.method(
          #getNativeToken,
          [networkId],
        ),
        returnValueForMissingStub: null,
      ) as _i4.LocalToken?);

  @override
  _i18.Future<_i4.Result<_i12.SwapFeeConfig?>> getSwapFeeConfig() =>
      (super.noSuchMethod(
        Invocation.method(
          #getSwapFeeConfig,
          [],
        ),
        returnValue: _i18.Future<_i4.Result<_i12.SwapFeeConfig?>>.value(
            _FakeResult_2<_i12.SwapFeeConfig?>(
          this,
          Invocation.method(
            #getSwapFeeConfig,
            [],
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i12.SwapFeeConfig?>>.value(
                _FakeResult_2<_i12.SwapFeeConfig?>(
          this,
          Invocation.method(
            #getSwapFeeConfig,
            [],
          ),
        )),
      ) as _i18.Future<_i4.Result<_i12.SwapFeeConfig?>>);

  @override
  num? getSwapFeeAsPercent({
    required _i12.SwapFeeConfig? swapFeeConfig,
    required num? amount,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getSwapFeeAsPercent,
          [],
          {
            #swapFeeConfig: swapFeeConfig,
            #amount: amount,
          },
        ),
        returnValueForMissingStub: null,
      ) as num?);

  @override
  void saveSlippage(num? slippage) => super.noSuchMethod(
        Invocation.method(
          #saveSlippage,
          [slippage],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void listenToReactiveValues(List<dynamic>? reactiveValues) =>
      super.noSuchMethod(
        Invocation.method(
          #listenToReactiveValues,
          [reactiveValues],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [PartnersService].
///
/// See the documentation for Mockito's code generation for more information.
class MockPartnersService extends _i1.Mock implements _i7.PartnersService {
  @override
  String get preferredNetworkKey => (super.noSuchMethod(
        Invocation.getter(#preferredNetworkKey),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#preferredNetworkKey),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#preferredNetworkKey),
        ),
      ) as String);

  @override
  bool get hasTradingAccount => (super.noSuchMethod(
        Invocation.getter(#hasTradingAccount),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  List<_i4.TradingAccount> get allAccounts => (super.noSuchMethod(
        Invocation.getter(#allAccounts),
        returnValue: <_i4.TradingAccount>[],
        returnValueForMissingStub: <_i4.TradingAccount>[],
      ) as List<_i4.TradingAccount>);

  @override
  List<_i4.LocalToken> get tradingBalances => (super.noSuchMethod(
        Invocation.getter(#tradingBalances),
        returnValue: <_i4.LocalToken>[],
        returnValueForMissingStub: <_i4.LocalToken>[],
      ) as List<_i4.LocalToken>);

  @override
  int get listenersCount => (super.noSuchMethod(
        Invocation.getter(#listenersCount),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as int);

  @override
  _i13.Tuple2<double, double> allTradingWalletBalance() => (super.noSuchMethod(
        Invocation.method(
          #allTradingWalletBalance,
          [],
        ),
        returnValue: _FakeTuple2_15<double, double>(
          this,
          Invocation.method(
            #allTradingWalletBalance,
            [],
          ),
        ),
        returnValueForMissingStub: _FakeTuple2_15<double, double>(
          this,
          Invocation.method(
            #allTradingWalletBalance,
            [],
          ),
        ),
      ) as _i13.Tuple2<double, double>);

  @override
  _i13.Tuple2<double, double> tradingWalletNetworkBalance(String? networkId) =>
      (super.noSuchMethod(
        Invocation.method(
          #tradingWalletNetworkBalance,
          [networkId],
        ),
        returnValue: _FakeTuple2_15<double, double>(
          this,
          Invocation.method(
            #tradingWalletNetworkBalance,
            [networkId],
          ),
        ),
        returnValueForMissingStub: _FakeTuple2_15<double, double>(
          this,
          Invocation.method(
            #tradingWalletNetworkBalance,
            [networkId],
          ),
        ),
      ) as _i13.Tuple2<double, double>);

  @override
  void setCurrentNetwork(_i4.LocalNetwork? network) => super.noSuchMethod(
        Invocation.method(
          #setCurrentNetwork,
          [network],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i18.Future<_i4.LocalNetwork?> getPreferredNetwork() => (super.noSuchMethod(
        Invocation.method(
          #getPreferredNetwork,
          [],
        ),
        returnValue: _i18.Future<_i4.LocalNetwork?>.value(),
        returnValueForMissingStub: _i18.Future<_i4.LocalNetwork?>.value(),
      ) as _i18.Future<_i4.LocalNetwork?>);

  @override
  void loadLocalData() => super.noSuchMethod(
        Invocation.method(
          #loadLocalData,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i18.Future<_i4.Result<String?>> createTradingAccount(
          {required String? networkId}) =>
      (super.noSuchMethod(
        Invocation.method(
          #createTradingAccount,
          [],
          {#networkId: networkId},
        ),
        returnValue:
            _i18.Future<_i4.Result<String?>>.value(_FakeResult_2<String?>(
          this,
          Invocation.method(
            #createTradingAccount,
            [],
            {#networkId: networkId},
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<String?>>.value(_FakeResult_2<String?>(
          this,
          Invocation.method(
            #createTradingAccount,
            [],
            {#networkId: networkId},
          ),
        )),
      ) as _i18.Future<_i4.Result<String?>>);

  @override
  _i18.Future<dynamic> getPartnerTradingAccount() => (super.noSuchMethod(
        Invocation.method(
          #getPartnerTradingAccount,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> getPartnerTradingAccountBalance() => (super.noSuchMethod(
        Invocation.method(
          #getPartnerTradingAccountBalance,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  List<_i4.LocalNetwork> getTradingAccountNetworks() => (super.noSuchMethod(
        Invocation.method(
          #getTradingAccountNetworks,
          [],
        ),
        returnValue: <_i4.LocalNetwork>[],
        returnValueForMissingStub: <_i4.LocalNetwork>[],
      ) as List<_i4.LocalNetwork>);

  @override
  bool isTradingWallet({
    String? address,
    String? networkId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #isTradingWallet,
          [],
          {
            #address: address,
            #networkId: networkId,
          },
        ),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  void onNetworkChanged(String? networkId) => super.noSuchMethod(
        Invocation.method(
          #onNetworkChanged,
          [networkId],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void listenToReactiveValues(List<dynamic>? reactiveValues) =>
      super.noSuchMethod(
        Invocation.method(
          #listenToReactiveValues,
          [reactiveValues],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [AppSettingsService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAppSettingsService extends _i1.Mock
    implements _i7.AppSettingsService {
  @override
  _i9.ReactiveValue<bool> get hasSeenSpotWallet => (super.noSuchMethod(
        Invocation.getter(#hasSeenSpotWallet),
        returnValue: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#hasSeenSpotWallet),
        ),
        returnValueForMissingStub: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#hasSeenSpotWallet),
        ),
      ) as _i9.ReactiveValue<bool>);

  @override
  _i9.ReactiveValue<bool> get hasSeenTradingWallet => (super.noSuchMethod(
        Invocation.getter(#hasSeenTradingWallet),
        returnValue: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#hasSeenTradingWallet),
        ),
        returnValueForMissingStub: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#hasSeenTradingWallet),
        ),
      ) as _i9.ReactiveValue<bool>);

  @override
  _i9.ReactiveValue<bool> get hasSeenWalletConnectIntro => (super.noSuchMethod(
        Invocation.getter(#hasSeenWalletConnectIntro),
        returnValue: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#hasSeenWalletConnectIntro),
        ),
        returnValueForMissingStub: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#hasSeenWalletConnectIntro),
        ),
      ) as _i9.ReactiveValue<bool>);

  @override
  _i9.ReactiveValue<bool> get showNewOnCardWithdrawal => (super.noSuchMethod(
        Invocation.getter(#showNewOnCardWithdrawal),
        returnValue: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#showNewOnCardWithdrawal),
        ),
        returnValueForMissingStub: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#showNewOnCardWithdrawal),
        ),
      ) as _i9.ReactiveValue<bool>);

  @override
  _i9.ReactiveValue<bool> get showNewOnCardFunding => (super.noSuchMethod(
        Invocation.getter(#showNewOnCardFunding),
        returnValue: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#showNewOnCardFunding),
        ),
        returnValueForMissingStub: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#showNewOnCardFunding),
        ),
      ) as _i9.ReactiveValue<bool>);

  @override
  _i9.ReactiveValue<bool> get hideSpamTokens => (super.noSuchMethod(
        Invocation.getter(#hideSpamTokens),
        returnValue: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#hideSpamTokens),
        ),
        returnValueForMissingStub: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#hideSpamTokens),
        ),
      ) as _i9.ReactiveValue<bool>);

  @override
  _i9.ReactiveValue<bool> get showOnboard2AnnouncementTag =>
      (super.noSuchMethod(
        Invocation.getter(#showOnboard2AnnouncementTag),
        returnValue: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#showOnboard2AnnouncementTag),
        ),
        returnValueForMissingStub: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#showOnboard2AnnouncementTag),
        ),
      ) as _i9.ReactiveValue<bool>);

  @override
  _i9.ReactiveValue<bool> get showNewOnOnboardPay => (super.noSuchMethod(
        Invocation.getter(#showNewOnOnboardPay),
        returnValue: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#showNewOnOnboardPay),
        ),
        returnValueForMissingStub: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#showNewOnOnboardPay),
        ),
      ) as _i9.ReactiveValue<bool>);

  @override
  _i9.ReactiveValue<bool> get showRedDotOnCardSubscriptionsReactiveValue =>
      (super.noSuchMethod(
        Invocation.getter(#showRedDotOnCardSubscriptionsReactiveValue),
        returnValue: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#showRedDotOnCardSubscriptionsReactiveValue),
        ),
        returnValueForMissingStub: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#showRedDotOnCardSubscriptionsReactiveValue),
        ),
      ) as _i9.ReactiveValue<bool>);

  @override
  _i9.ReactiveValue<
      bool> get showCardSubscriptionNextPaymentDateChangeWarning => (super
          .noSuchMethod(
        Invocation.getter(#showCardSubscriptionNextPaymentDateChangeWarning),
        returnValue: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#showCardSubscriptionNextPaymentDateChangeWarning),
        ),
        returnValueForMissingStub: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#showCardSubscriptionNextPaymentDateChangeWarning),
        ),
      ) as _i9.ReactiveValue<bool>);

  @override
  _i9.ReactiveValue<int?> get currentHomeTab => (super.noSuchMethod(
        Invocation.getter(#currentHomeTab),
        returnValue: _FakeReactiveValue_11<int?>(
          this,
          Invocation.getter(#currentHomeTab),
        ),
        returnValueForMissingStub: _FakeReactiveValue_11<int?>(
          this,
          Invocation.getter(#currentHomeTab),
        ),
      ) as _i9.ReactiveValue<int?>);

  @override
  _i8.Box<bool> get balanceHiddenBox => (super.noSuchMethod(
        Invocation.getter(#balanceHiddenBox),
        returnValue: _FakeBox_10<bool>(
          this,
          Invocation.getter(#balanceHiddenBox),
        ),
        returnValueForMissingStub: _FakeBox_10<bool>(
          this,
          Invocation.getter(#balanceHiddenBox),
        ),
      ) as _i8.Box<bool>);

  @override
  _i8.Box<bool> get cardSubscriptionNudgeBannerBox => (super.noSuchMethod(
        Invocation.getter(#cardSubscriptionNudgeBannerBox),
        returnValue: _FakeBox_10<bool>(
          this,
          Invocation.getter(#cardSubscriptionNudgeBannerBox),
        ),
        returnValueForMissingStub: _FakeBox_10<bool>(
          this,
          Invocation.getter(#cardSubscriptionNudgeBannerBox),
        ),
      ) as _i8.Box<bool>);

  @override
  bool get balanceHidden => (super.noSuchMethod(
        Invocation.getter(#balanceHidden),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  bool get showRedDotOnCardSubscriptions => (super.noSuchMethod(
        Invocation.getter(#showRedDotOnCardSubscriptions),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  int get listenersCount => (super.noSuchMethod(
        Invocation.getter(#listenersCount),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as int);

  @override
  _i18.Future<dynamic> setHasSeenWalletConnectIntro() => (super.noSuchMethod(
        Invocation.method(
          #setHasSeenWalletConnectIntro,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> hideRedDotOnCardSubscriptionsTab() =>
      (super.noSuchMethod(
        Invocation.method(
          #hideRedDotOnCardSubscriptionsTab,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> hideCardSubscriptionNextPaymentDateChangeWarning() =>
      (super.noSuchMethod(
        Invocation.method(
          #hideCardSubscriptionNextPaymentDateChangeWarning,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> setHideSpamToken(bool? isOn) => (super.noSuchMethod(
        Invocation.method(
          #setHideSpamToken,
          [isOn],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> setSeenSpotWallet() => (super.noSuchMethod(
        Invocation.method(
          #setSeenSpotWallet,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> setHideOnboard2Tag() => (super.noSuchMethod(
        Invocation.method(
          #setHideOnboard2Tag,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<bool> showSwitchNetworkToolTip() => (super.noSuchMethod(
        Invocation.method(
          #showSwitchNetworkToolTip,
          [],
        ),
        returnValue: _i18.Future<bool>.value(false),
        returnValueForMissingStub: _i18.Future<bool>.value(false),
      ) as _i18.Future<bool>);

  @override
  _i18.Future<bool> showHubIntroScreens() => (super.noSuchMethod(
        Invocation.method(
          #showHubIntroScreens,
          [],
        ),
        returnValue: _i18.Future<bool>.value(false),
        returnValueForMissingStub: _i18.Future<bool>.value(false),
      ) as _i18.Future<bool>);

  @override
  _i18.Future<bool> showChatWithUsToolTip() => (super.noSuchMethod(
        Invocation.method(
          #showChatWithUsToolTip,
          [],
        ),
        returnValue: _i18.Future<bool>.value(false),
        returnValueForMissingStub: _i18.Future<bool>.value(false),
      ) as _i18.Future<bool>);

  @override
  _i18.Future<dynamic> setSeenTradingWallet() => (super.noSuchMethod(
        Invocation.method(
          #setSeenTradingWallet,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> setTradingWalletIntroSeen(bool? seen) =>
      (super.noSuchMethod(
        Invocation.method(
          #setTradingWalletIntroSeen,
          [seen],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<bool> shouldShowTradingWalletIntro() => (super.noSuchMethod(
        Invocation.method(
          #shouldShowTradingWalletIntro,
          [],
        ),
        returnValue: _i18.Future<bool>.value(false),
        returnValueForMissingStub: _i18.Future<bool>.value(false),
      ) as _i18.Future<bool>);

  @override
  _i18.Future<void> showCrispChat() => (super.noSuchMethod(
        Invocation.method(
          #showCrispChat,
          [],
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  _i18.Future<dynamic> setShowNewOnCardWithdrawal(bool? show) =>
      (super.noSuchMethod(
        Invocation.method(
          #setShowNewOnCardWithdrawal,
          [show],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> setShowNewOnCardFunding(bool? show) =>
      (super.noSuchMethod(
        Invocation.method(
          #setShowNewOnCardFunding,
          [show],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> hideNewOnOnboardPay() => (super.noSuchMethod(
        Invocation.method(
          #hideNewOnOnboardPay,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> hideBalanceInAppSwitcher(bool? hide) =>
      (super.noSuchMethod(
        Invocation.method(
          #hideBalanceInAppSwitcher,
          [hide],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> logout() => (super.noSuchMethod(
        Invocation.method(
          #logout,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  void updateLastSelectedOnboardAccount(_i32.OnboardAccountsEnum? value) =>
      super.noSuchMethod(
        Invocation.method(
          #updateLastSelectedOnboardAccount,
          [value],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void listenToReactiveValues(List<dynamic>? reactiveValues) =>
      super.noSuchMethod(
        Invocation.method(
          #listenToReactiveValues,
          [reactiveValues],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [PartnerDatabaseService].
///
/// See the documentation for Mockito's code generation for more information.
class MockPartnerDatabaseService extends _i1.Mock
    implements _i7.PartnerDatabaseService {
  @override
  _i8.Box<_i4.LocalToken> get tradingBalancesBox => (super.noSuchMethod(
        Invocation.getter(#tradingBalancesBox),
        returnValue: _FakeBox_10<_i4.LocalToken>(
          this,
          Invocation.getter(#tradingBalancesBox),
        ),
        returnValueForMissingStub: _FakeBox_10<_i4.LocalToken>(
          this,
          Invocation.getter(#tradingBalancesBox),
        ),
      ) as _i8.Box<_i4.LocalToken>);

  @override
  set tradingBalancesBox(_i8.Box<_i4.LocalToken>? _tradingBalancesBox) =>
      super.noSuchMethod(
        Invocation.setter(
          #tradingBalancesBox,
          _tradingBalancesBox,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i18.Future<dynamic> nukeDb() => (super.noSuchMethod(
        Invocation.method(
          #nukeDb,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> saveTradingAccounts(
          List<_i4.TradingAccount>? accounts) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveTradingAccounts,
          [accounts],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> saveTradingBalances(List<_i4.LocalToken>? balances) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveTradingBalances,
          [balances],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> saveTradingBalance(_i4.LocalToken? balance) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveTradingBalance,
          [balance],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  List<_i4.LocalToken> getAllTradingBalances() => (super.noSuchMethod(
        Invocation.method(
          #getAllTradingBalances,
          [],
        ),
        returnValue: <_i4.LocalToken>[],
        returnValueForMissingStub: <_i4.LocalToken>[],
      ) as List<_i4.LocalToken>);

  @override
  List<_i4.TradingAccount> getAllTradingAccounts() => (super.noSuchMethod(
        Invocation.method(
          #getAllTradingAccounts,
          [],
        ),
        returnValue: <_i4.TradingAccount>[],
        returnValueForMissingStub: <_i4.TradingAccount>[],
      ) as List<_i4.TradingAccount>);

  @override
  _i4.TradingAccount? getTradingAccountForNetwork(String? networkId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getTradingAccountForNetwork,
          [networkId],
        ),
        returnValueForMissingStub: null,
      ) as _i4.TradingAccount?);
}

/// A class which mocks [RatesService].
///
/// See the documentation for Mockito's code generation for more information.
class MockRatesService extends _i1.Mock implements _i7.RatesService {
  @override
  int get listenersCount => (super.noSuchMethod(
        Invocation.getter(#listenersCount),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as int);

  @override
  _i18.Future<dynamic> nukeDb() => (super.noSuchMethod(
        Invocation.method(
          #nukeDb,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> getRatesForBalances(List<_i4.LocalToken>? balances) =>
      (super.noSuchMethod(
        Invocation.method(
          #getRatesForBalances,
          [balances],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> getRatesForLocalToken(List<_i4.LocalToken>? tokens) =>
      (super.noSuchMethod(
        Invocation.method(
          #getRatesForLocalToken,
          [tokens],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  double getRateForSymbolPair(String? pair) => (super.noSuchMethod(
        Invocation.method(
          #getRateForSymbolPair,
          [pair],
        ),
        returnValue: 0.0,
        returnValueForMissingStub: 0.0,
      ) as double);

  @override
  void listenToReactiveValues(List<dynamic>? reactiveValues) =>
      super.noSuchMethod(
        Invocation.method(
          #listenToReactiveValues,
          [reactiveValues],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [ConfigService].
///
/// See the documentation for Mockito's code generation for more information.
class MockConfigService extends _i1.Mock implements _i7.ConfigService {
  @override
  _i8.Box<_i4.LocalConfig> get configBox => (super.noSuchMethod(
        Invocation.getter(#configBox),
        returnValue: _FakeBox_10<_i4.LocalConfig>(
          this,
          Invocation.getter(#configBox),
        ),
        returnValueForMissingStub: _FakeBox_10<_i4.LocalConfig>(
          this,
          Invocation.getter(#configBox),
        ),
      ) as _i8.Box<_i4.LocalConfig>);

  @override
  _i18.Future<_i4.LocalConfig?> getAssetConfig() => (super.noSuchMethod(
        Invocation.method(
          #getAssetConfig,
          [],
        ),
        returnValue: _i18.Future<_i4.LocalConfig?>.value(),
        returnValueForMissingStub: _i18.Future<_i4.LocalConfig?>.value(),
      ) as _i18.Future<_i4.LocalConfig?>);

  @override
  _i18.Future<dynamic> nukeDb() => (super.noSuchMethod(
        Invocation.method(
          #nukeDb,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  List<_i4.LocalToken> getActiveNetworkTokensAndMerge({
    List<_i4.LocalToken>? tokens = const [],
    required String? networkId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getActiveNetworkTokensAndMerge,
          [],
          {
            #tokens: tokens,
            #networkId: networkId,
          },
        ),
        returnValue: <_i4.LocalToken>[],
        returnValueForMissingStub: <_i4.LocalToken>[],
      ) as List<_i4.LocalToken>);

  @override
  bool isAssetSupportedByExchangeOnNetwork(
    _i4.LocalToken? token,
    String? networkId,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #isAssetSupportedByExchangeOnNetwork,
          [
            token,
            networkId,
          ],
        ),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i18.Future<_i4.Result<_i12.CountryFeatureResponse?>> getCountryFeatures(
          {String? countryCode}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCountryFeatures,
          [],
          {#countryCode: countryCode},
        ),
        returnValue:
            _i18.Future<_i4.Result<_i12.CountryFeatureResponse?>>.value(
                _FakeResult_2<_i12.CountryFeatureResponse?>(
          this,
          Invocation.method(
            #getCountryFeatures,
            [],
            {#countryCode: countryCode},
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i12.CountryFeatureResponse?>>.value(
                _FakeResult_2<_i12.CountryFeatureResponse?>(
          this,
          Invocation.method(
            #getCountryFeatures,
            [],
            {#countryCode: countryCode},
          ),
        )),
      ) as _i18.Future<_i4.Result<_i12.CountryFeatureResponse?>>);
}

/// A class which mocks [TransactionHashMonitoringService].
///
/// See the documentation for Mockito's code generation for more information.
class MockTransactionHashMonitoringService extends _i1.Mock
    implements _i7.TransactionHashMonitoringService {
  @override
  _i9.ReactiveValue<_i7.TransactionHashModel?> get pendingLocalTransaction =>
      (super.noSuchMethod(
        Invocation.getter(#pendingLocalTransaction),
        returnValue: _FakeReactiveValue_11<_i7.TransactionHashModel?>(
          this,
          Invocation.getter(#pendingLocalTransaction),
        ),
        returnValueForMissingStub:
            _FakeReactiveValue_11<_i7.TransactionHashModel?>(
          this,
          Invocation.getter(#pendingLocalTransaction),
        ),
      ) as _i9.ReactiveValue<_i7.TransactionHashModel?>);

  @override
  void observeTransactionHash({
    required _i4.LocalTransaction? localTransaction,
    required String? rpcUrl,
    required String? tokenSymbol,
    void Function(_i5.TransactionReceipt)? onReceipt,
    bool? showTransactionToast = true,
    required _i7.OnboardCryptoTransactionType? cryptoTransactionType,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #observeTransactionHash,
          [],
          {
            #localTransaction: localTransaction,
            #rpcUrl: rpcUrl,
            #tokenSymbol: tokenSymbol,
            #onReceipt: onReceipt,
            #showTransactionToast: showTransactionToast,
            #cryptoTransactionType: cryptoTransactionType,
          },
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [RemoteConfigService].
///
/// See the documentation for Mockito's code generation for more information.
class MockRemoteConfigService extends _i1.Mock
    implements _i7.RemoteConfigService {
  @override
  bool get isCardTurnedOn => (super.noSuchMethod(
        Invocation.getter(#isCardTurnedOn),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  set isCardTurnedOn(bool? _isCardTurnedOn) => super.noSuchMethod(
        Invocation.setter(
          #isCardTurnedOn,
          _isCardTurnedOn,
        ),
        returnValueForMissingStub: null,
      );

  @override
  int get minimumCardBuildNumber => (super.noSuchMethod(
        Invocation.getter(#minimumCardBuildNumber),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as int);

  @override
  String get leaveFeedback => (super.noSuchMethod(
        Invocation.getter(#leaveFeedback),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#leaveFeedback),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#leaveFeedback),
        ),
      ) as String);

  @override
  String get communityUrl => (super.noSuchMethod(
        Invocation.getter(#communityUrl),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#communityUrl),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#communityUrl),
        ),
      ) as String);

  @override
  bool get showPhoneNUmberNudge => (super.noSuchMethod(
        Invocation.getter(#showPhoneNUmberNudge),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  int get latestBuildNumber => (super.noSuchMethod(
        Invocation.getter(#latestBuildNumber),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as int);

  @override
  String get testFlightRedirectUrl => (super.noSuchMethod(
        Invocation.getter(#testFlightRedirectUrl),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#testFlightRedirectUrl),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#testFlightRedirectUrl),
        ),
      ) as String);

  @override
  String get playStoreRedirectUrl => (super.noSuchMethod(
        Invocation.getter(#playStoreRedirectUrl),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#playStoreRedirectUrl),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#playStoreRedirectUrl),
        ),
      ) as String);

  @override
  String get faqUrl => (super.noSuchMethod(
        Invocation.getter(#faqUrl),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#faqUrl),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#faqUrl),
        ),
      ) as String);

  @override
  String get cardFaqUrl => (super.noSuchMethod(
        Invocation.getter(#cardFaqUrl),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#cardFaqUrl),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#cardFaqUrl),
        ),
      ) as String);

  @override
  String get joinTestFlightUrl => (super.noSuchMethod(
        Invocation.getter(#joinTestFlightUrl),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#joinTestFlightUrl),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#joinTestFlightUrl),
        ),
      ) as String);

  @override
  int get cardLowBalanceValue => (super.noSuchMethod(
        Invocation.getter(#cardLowBalanceValue),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as int);

  @override
  int get cardTerminationGracePeriod => (super.noSuchMethod(
        Invocation.getter(#cardTerminationGracePeriod),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as int);

  @override
  String get userSurveyUrl => (super.noSuchMethod(
        Invocation.getter(#userSurveyUrl),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#userSurveyUrl),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#userSurveyUrl),
        ),
      ) as String);

  @override
  String get cardFxTransactionsSubtitle => (super.noSuchMethod(
        Invocation.getter(#cardFxTransactionsSubtitle),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#cardFxTransactionsSubtitle),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#cardFxTransactionsSubtitle),
        ),
      ) as String);

  @override
  String get onboardInstagramUrl => (super.noSuchMethod(
        Invocation.getter(#onboardInstagramUrl),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#onboardInstagramUrl),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#onboardInstagramUrl),
        ),
      ) as String);

  @override
  String get onboardTwitterUrl => (super.noSuchMethod(
        Invocation.getter(#onboardTwitterUrl),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#onboardTwitterUrl),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#onboardTwitterUrl),
        ),
      ) as String);

  @override
  String get onboardTelegramUrl => (super.noSuchMethod(
        Invocation.getter(#onboardTelegramUrl),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#onboardTelegramUrl),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#onboardTelegramUrl),
        ),
      ) as String);

  @override
  String get cardCurrency => (super.noSuchMethod(
        Invocation.getter(#cardCurrency),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#cardCurrency),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#cardCurrency),
        ),
      ) as String);

  @override
  String get referralTermsLink => (super.noSuchMethod(
        Invocation.getter(#referralTermsLink),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#referralTermsLink),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#referralTermsLink),
        ),
      ) as String);

  @override
  String get compassByOnboardUrl => (super.noSuchMethod(
        Invocation.getter(#compassByOnboardUrl),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#compassByOnboardUrl),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#compassByOnboardUrl),
        ),
      ) as String);

  @override
  String get marketingBannerJson => (super.noSuchMethod(
        Invocation.getter(#marketingBannerJson),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#marketingBannerJson),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#marketingBannerJson),
        ),
      ) as String);

  @override
  String get defaultNetwork => (super.noSuchMethod(
        Invocation.getter(#defaultNetwork),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#defaultNetwork),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#defaultNetwork),
        ),
      ) as String);

  @override
  String get homeBannerJson => (super.noSuchMethod(
        Invocation.getter(#homeBannerJson),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#homeBannerJson),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#homeBannerJson),
        ),
      ) as String);

  @override
  String get communityBannerWebUrl => (super.noSuchMethod(
        Invocation.getter(#communityBannerWebUrl),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#communityBannerWebUrl),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#communityBannerWebUrl),
        ),
      ) as String);

  @override
  String get feedbackUrl => (super.noSuchMethod(
        Invocation.getter(#feedbackUrl),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#feedbackUrl),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#feedbackUrl),
        ),
      ) as String);

  @override
  String get changeLogUrl => (super.noSuchMethod(
        Invocation.getter(#changeLogUrl),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#changeLogUrl),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#changeLogUrl),
        ),
      ) as String);

  @override
  String get onboard2TweetUrl => (super.noSuchMethod(
        Invocation.getter(#onboard2TweetUrl),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.getter(#onboard2TweetUrl),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.getter(#onboard2TweetUrl),
        ),
      ) as String);

  @override
  bool getBool(String? key) => (super.noSuchMethod(
        Invocation.method(
          #getBool,
          [key],
        ),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  String getString(String? key) => (super.noSuchMethod(
        Invocation.method(
          #getString,
          [key],
        ),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.method(
            #getString,
            [key],
          ),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.method(
            #getString,
            [key],
          ),
        ),
      ) as String);

  @override
  _i18.Future<bool> showCard() => (super.noSuchMethod(
        Invocation.method(
          #showCard,
          [],
        ),
        returnValue: _i18.Future<bool>.value(false),
        returnValueForMissingStub: _i18.Future<bool>.value(false),
      ) as _i18.Future<bool>);

  @override
  _i18.Future<dynamic> setup() => (super.noSuchMethod(
        Invocation.method(
          #setup,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);
}

/// A class which mocks [WalletNavigationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockWalletNavigationService extends _i1.Mock
    implements _i7.WalletNavigationService {
  @override
  _i18.Future<void> navigateToExchange(
    String? urlPath, {
    void Function()? onExchangeClose,
    String? loaderMessage,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #navigateToExchange,
          [urlPath],
          {
            #onExchangeClose: onExchangeClose,
            #loaderMessage: loaderMessage,
          },
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  void navigateToFundingOption({
    required _i4.LocalAddressTokenBalance? tokenBalance,
    required _i33.WalletType? walletType,
    required _i34.FundOptionFLowType? fundOptionType,
    _i4.LocalTokenNetworkBalance? tokenNetworkBalance,
    _i12.RailProvider? provider,
    List<_i12.RailProvider>? providers,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #navigateToFundingOption,
          [],
          {
            #tokenBalance: tokenBalance,
            #walletType: walletType,
            #fundOptionType: fundOptionType,
            #tokenNetworkBalance: tokenNetworkBalance,
            #provider: provider,
            #providers: providers,
          },
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [WalletConnectService].
///
/// See the documentation for Mockito's code generation for more information.
class MockWalletConnectService extends _i1.Mock
    implements _i7.WalletConnectService {
  @override
  Duration get timerDuration => (super.noSuchMethod(
        Invocation.getter(#timerDuration),
        returnValue: _FakeDuration_16(
          this,
          Invocation.getter(#timerDuration),
        ),
        returnValueForMissingStub: _FakeDuration_16(
          this,
          Invocation.getter(#timerDuration),
        ),
      ) as Duration);

  @override
  set timerDuration(Duration? _timerDuration) => super.noSuchMethod(
        Invocation.setter(
          #timerDuration,
          _timerDuration,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i7.WalletService get walletService => (super.noSuchMethod(
        Invocation.getter(#walletService),
        returnValue: _FakeWalletService_17(
          this,
          Invocation.getter(#walletService),
        ),
        returnValueForMissingStub: _FakeWalletService_17(
          this,
          Invocation.getter(#walletService),
        ),
      ) as _i7.WalletService);

  @override
  bool get isInitialized => (super.noSuchMethod(
        Invocation.getter(#isInitialized),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  set isInitialized(bool? _isInitialized) => super.noSuchMethod(
        Invocation.setter(
          #isInitialized,
          _isInitialized,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set walletKit(_i35.ReownWalletKit? _walletKit) => super.noSuchMethod(
        Invocation.setter(
          #walletKit,
          _walletKit,
        ),
        returnValueForMissingStub: null,
      );

  @override
  List<_i35.SessionData> get sessions => (super.noSuchMethod(
        Invocation.getter(#sessions),
        returnValue: <_i35.SessionData>[],
        returnValueForMissingStub: <_i35.SessionData>[],
      ) as List<_i35.SessionData>);

  @override
  set sessions(List<_i35.SessionData>? _sessions) => super.noSuchMethod(
        Invocation.setter(
          #sessions,
          _sessions,
        ),
        returnValueForMissingStub: null,
      );

  @override
  List<String> get getEvents => (super.noSuchMethod(
        Invocation.getter(#getEvents),
        returnValue: <String>[],
        returnValueForMissingStub: <String>[],
      ) as List<String>);

  @override
  set getEvents(List<String>? _getEvents) => super.noSuchMethod(
        Invocation.setter(
          #getEvents,
          _getEvents,
        ),
        returnValueForMissingStub: null,
      );

  @override
  List<_i35.PairingInfo> get connections => (super.noSuchMethod(
        Invocation.getter(#connections),
        returnValue: <_i35.PairingInfo>[],
        returnValueForMissingStub: <_i35.PairingInfo>[],
      ) as List<_i35.PairingInfo>);

  @override
  int get listenersCount => (super.noSuchMethod(
        Invocation.getter(#listenersCount),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as int);

  @override
  _i18.Future<void> create() => (super.noSuchMethod(
        Invocation.method(
          #create,
          [],
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  void cancelConnectionCountDown() => super.noSuchMethod(
        Invocation.method(
          #cancelConnectionCountDown,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i18.Future<void> connectWithUri(String? uriString) => (super.noSuchMethod(
        Invocation.method(
          #connectWithUri,
          [uriString],
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  bool isValidWalletConnectUri(String? uriString) => (super.noSuchMethod(
        Invocation.method(
          #isValidWalletConnectUri,
          [uriString],
        ),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i18.Future<dynamic> disconnectPair(_i35.PairingInfo? pairingInfo) =>
      (super.noSuchMethod(
        Invocation.method(
          #disconnectPair,
          [pairingInfo],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> logout() => (super.noSuchMethod(
        Invocation.method(
          #logout,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  void registerAccountAndEventHandler(
          {required List<_i4.LocalNetwork>? networks}) =>
      super.noSuchMethod(
        Invocation.method(
          #registerAccountAndEventHandler,
          [],
          {#networks: networks},
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i18.Future<dynamic> deleteSession(String? topic) => (super.noSuchMethod(
        Invocation.method(
          #deleteSession,
          [topic],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> clearExpiredOrInactivePairing() => (super.noSuchMethod(
        Invocation.method(
          #clearExpiredOrInactivePairing,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  void reconnectIfDisconnected() => super.noSuchMethod(
        Invocation.method(
          #reconnectIfDisconnected,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void showConnecting() => super.noSuchMethod(
        Invocation.method(
          #showConnecting,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void clearWalletConnectState() => super.noSuchMethod(
        Invocation.method(
          #clearWalletConnectState,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i18.Future<void> reinitializeWalletConnect() => (super.noSuchMethod(
        Invocation.method(
          #reinitializeWalletConnect,
          [],
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  void listenToReactiveValues(List<dynamic>? reactiveValues) =>
      super.noSuchMethod(
        Invocation.method(
          #listenToReactiveValues,
          [reactiveValues],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [EncryptionService].
///
/// See the documentation for Mockito's code generation for more information.
class MockEncryptionService extends _i1.Mock implements _i7.EncryptionService {
  @override
  _i14.RSAKeypair get getWalletEncryptionKey => (super.noSuchMethod(
        Invocation.getter(#getWalletEncryptionKey),
        returnValue: _FakeRSAKeypair_18(
          this,
          Invocation.getter(#getWalletEncryptionKey),
        ),
        returnValueForMissingStub: _FakeRSAKeypair_18(
          this,
          Invocation.getter(#getWalletEncryptionKey),
        ),
      ) as _i14.RSAKeypair);

  @override
  _i14.RSAKeypair generateRSAkeyPair() => (super.noSuchMethod(
        Invocation.method(
          #generateRSAkeyPair,
          [],
        ),
        returnValue: _FakeRSAKeypair_18(
          this,
          Invocation.method(
            #generateRSAkeyPair,
            [],
          ),
        ),
        returnValueForMissingStub: _FakeRSAKeypair_18(
          this,
          Invocation.method(
            #generateRSAkeyPair,
            [],
          ),
        ),
      ) as _i14.RSAKeypair);

  @override
  String rsaDecryptMessage(
    _i14.RSAPrivateKey? myPrivate,
    String? cipherText,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #rsaDecryptMessage,
          [
            myPrivate,
            cipherText,
          ],
        ),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.method(
            #rsaDecryptMessage,
            [
              myPrivate,
              cipherText,
            ],
          ),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.method(
            #rsaDecryptMessage,
            [
              myPrivate,
              cipherText,
            ],
          ),
        ),
      ) as String);

  @override
  String encrypt({
    required _i14.RSAPublicKey? encryptionKey,
    required String? input,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #encrypt,
          [],
          {
            #encryptionKey: encryptionKey,
            #input: input,
          },
        ),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.method(
            #encrypt,
            [],
            {
              #encryptionKey: encryptionKey,
              #input: input,
            },
          ),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.method(
            #encrypt,
            [],
            {
              #encryptionKey: encryptionKey,
              #input: input,
            },
          ),
        ),
      ) as String);

  @override
  _i18.Future<_i4.Result<_i36.ProcessResult?>> remoteEncrypt({
    required String? input,
    required String? encryptionRequestToken,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #remoteEncrypt,
          [],
          {
            #input: input,
            #encryptionRequestToken: encryptionRequestToken,
          },
        ),
        returnValue: _i18.Future<_i4.Result<_i36.ProcessResult?>>.value(
            _FakeResult_2<_i36.ProcessResult?>(
          this,
          Invocation.method(
            #remoteEncrypt,
            [],
            {
              #input: input,
              #encryptionRequestToken: encryptionRequestToken,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i36.ProcessResult?>>.value(
                _FakeResult_2<_i36.ProcessResult?>(
          this,
          Invocation.method(
            #remoteEncrypt,
            [],
            {
              #input: input,
              #encryptionRequestToken: encryptionRequestToken,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<_i36.ProcessResult?>>);

  @override
  String hashEncryptionKey(String? encryptionKey) => (super.noSuchMethod(
        Invocation.method(
          #hashEncryptionKey,
          [encryptionKey],
        ),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.method(
            #hashEncryptionKey,
            [encryptionKey],
          ),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.method(
            #hashEncryptionKey,
            [encryptionKey],
          ),
        ),
      ) as String);
}

/// A class which mocks [OnboardExchangeService].
///
/// See the documentation for Mockito's code generation for more information.
class MockOnboardExchangeService extends _i1.Mock
    implements _i7.OnboardExchangeService {
  @override
  List<_i12.TransactionOrderStatus> get ongoingCustomerOrderStatus =>
      (super.noSuchMethod(
        Invocation.getter(#ongoingCustomerOrderStatus),
        returnValue: <_i12.TransactionOrderStatus>[],
        returnValueForMissingStub: <_i12.TransactionOrderStatus>[],
      ) as List<_i12.TransactionOrderStatus>);

  @override
  set ongoingCustomerOrderStatus(
          List<_i12.TransactionOrderStatus>? _ongoingCustomerOrderStatus) =>
      super.noSuchMethod(
        Invocation.setter(
          #ongoingCustomerOrderStatus,
          _ongoingCustomerOrderStatus,
        ),
        returnValueForMissingStub: null,
      );

  @override
  List<_i12.TransactionOrderStatus> get ongoingMerchantOrderStatus =>
      (super.noSuchMethod(
        Invocation.getter(#ongoingMerchantOrderStatus),
        returnValue: <_i12.TransactionOrderStatus>[],
        returnValueForMissingStub: <_i12.TransactionOrderStatus>[],
      ) as List<_i12.TransactionOrderStatus>);

  @override
  set ongoingMerchantOrderStatus(
          List<_i12.TransactionOrderStatus>? _ongoingMerchantOrderStatus) =>
      super.noSuchMethod(
        Invocation.setter(
          #ongoingMerchantOrderStatus,
          _ongoingMerchantOrderStatus,
        ),
        returnValueForMissingStub: null,
      );

  @override
  List<_i12.TransactionOrderStatus> get disputeOrderStatus =>
      (super.noSuchMethod(
        Invocation.getter(#disputeOrderStatus),
        returnValue: <_i12.TransactionOrderStatus>[],
        returnValueForMissingStub: <_i12.TransactionOrderStatus>[],
      ) as List<_i12.TransactionOrderStatus>);

  @override
  set disputeOrderStatus(
          List<_i12.TransactionOrderStatus>? _disputeOrderStatus) =>
      super.noSuchMethod(
        Invocation.setter(
          #disputeOrderStatus,
          _disputeOrderStatus,
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addOrder(String? orderId) => super.noSuchMethod(
        Invocation.method(
          #addOrder,
          [orderId],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void cancel() => super.noSuchMethod(
        Invocation.method(
          #cancel,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i18.Future<_i4.Result<_i12.TransactionsResponse?>> getCustomerOngoingOrders(
          {required String? userId}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCustomerOngoingOrders,
          [],
          {#userId: userId},
        ),
        returnValue: _i18.Future<_i4.Result<_i12.TransactionsResponse?>>.value(
            _FakeResult_2<_i12.TransactionsResponse?>(
          this,
          Invocation.method(
            #getCustomerOngoingOrders,
            [],
            {#userId: userId},
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i12.TransactionsResponse?>>.value(
                _FakeResult_2<_i12.TransactionsResponse?>(
          this,
          Invocation.method(
            #getCustomerOngoingOrders,
            [],
            {#userId: userId},
          ),
        )),
      ) as _i18.Future<_i4.Result<_i12.TransactionsResponse?>>);

  @override
  _i18.Future<_i4.Result<_i12.TransactionsResponse?>> getCustomerDisputeOrders(
          {required String? userId}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCustomerDisputeOrders,
          [],
          {#userId: userId},
        ),
        returnValue: _i18.Future<_i4.Result<_i12.TransactionsResponse?>>.value(
            _FakeResult_2<_i12.TransactionsResponse?>(
          this,
          Invocation.method(
            #getCustomerDisputeOrders,
            [],
            {#userId: userId},
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i12.TransactionsResponse?>>.value(
                _FakeResult_2<_i12.TransactionsResponse?>(
          this,
          Invocation.method(
            #getCustomerDisputeOrders,
            [],
            {#userId: userId},
          ),
        )),
      ) as _i18.Future<_i4.Result<_i12.TransactionsResponse?>>);

  @override
  _i18.Future<_i4.Result<_i12.TransactionsResponse?>> getMerchantOngoingOrders(
          {required String? userId}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getMerchantOngoingOrders,
          [],
          {#userId: userId},
        ),
        returnValue: _i18.Future<_i4.Result<_i12.TransactionsResponse?>>.value(
            _FakeResult_2<_i12.TransactionsResponse?>(
          this,
          Invocation.method(
            #getMerchantOngoingOrders,
            [],
            {#userId: userId},
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i12.TransactionsResponse?>>.value(
                _FakeResult_2<_i12.TransactionsResponse?>(
          this,
          Invocation.method(
            #getMerchantOngoingOrders,
            [],
            {#userId: userId},
          ),
        )),
      ) as _i18.Future<_i4.Result<_i12.TransactionsResponse?>>);

  @override
  _i18.Future<_i4.Result<_i12.TransactionsResponse?>> getMerchantDisputeOrders(
          {required String? userId}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getMerchantDisputeOrders,
          [],
          {#userId: userId},
        ),
        returnValue: _i18.Future<_i4.Result<_i12.TransactionsResponse?>>.value(
            _FakeResult_2<_i12.TransactionsResponse?>(
          this,
          Invocation.method(
            #getMerchantDisputeOrders,
            [],
            {#userId: userId},
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i12.TransactionsResponse?>>.value(
                _FakeResult_2<_i12.TransactionsResponse?>(
          this,
          Invocation.method(
            #getMerchantDisputeOrders,
            [],
            {#userId: userId},
          ),
        )),
      ) as _i18.Future<_i4.Result<_i12.TransactionsResponse?>>);

  @override
  _i18.Future<_i4.Result<_i37.OrderTransactionList?>> getOpnOngoingOrders() =>
      (super.noSuchMethod(
        Invocation.method(
          #getOpnOngoingOrders,
          [],
        ),
        returnValue: _i18.Future<_i4.Result<_i37.OrderTransactionList?>>.value(
            _FakeResult_2<_i37.OrderTransactionList?>(
          this,
          Invocation.method(
            #getOpnOngoingOrders,
            [],
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i37.OrderTransactionList?>>.value(
                _FakeResult_2<_i37.OrderTransactionList?>(
          this,
          Invocation.method(
            #getOpnOngoingOrders,
            [],
          ),
        )),
      ) as _i18.Future<_i4.Result<_i37.OrderTransactionList?>>);

  @override
  _i18.Future<_i4.Result<_i37.OrderTransactionList?>>
      getOpnSwapFailedOrders() => (super.noSuchMethod(
            Invocation.method(
              #getOpnSwapFailedOrders,
              [],
            ),
            returnValue:
                _i18.Future<_i4.Result<_i37.OrderTransactionList?>>.value(
                    _FakeResult_2<_i37.OrderTransactionList?>(
              this,
              Invocation.method(
                #getOpnSwapFailedOrders,
                [],
              ),
            )),
            returnValueForMissingStub:
                _i18.Future<_i4.Result<_i37.OrderTransactionList?>>.value(
                    _FakeResult_2<_i37.OrderTransactionList?>(
              this,
              Invocation.method(
                #getOpnSwapFailedOrders,
                [],
              ),
            )),
          ) as _i18.Future<_i4.Result<_i37.OrderTransactionList?>>);

  @override
  _i18.Future<_i4.Result<List<_i4.LocalToken>>> getDefiBridgeTokens() =>
      (super.noSuchMethod(
        Invocation.method(
          #getDefiBridgeTokens,
          [],
        ),
        returnValue: _i18.Future<_i4.Result<List<_i4.LocalToken>>>.value(
            _FakeResult_2<List<_i4.LocalToken>>(
          this,
          Invocation.method(
            #getDefiBridgeTokens,
            [],
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<List<_i4.LocalToken>>>.value(
                _FakeResult_2<List<_i4.LocalToken>>(
          this,
          Invocation.method(
            #getDefiBridgeTokens,
            [],
          ),
        )),
      ) as _i18.Future<_i4.Result<List<_i4.LocalToken>>>);

  @override
  _i18.Future<_i4.Result<_i37.TokenRate?>> getOnboardDefiBridgeRates({
    required String? fromToken,
    required String? toFiat,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getOnboardDefiBridgeRates,
          [],
          {
            #fromToken: fromToken,
            #toFiat: toFiat,
          },
        ),
        returnValue: _i18.Future<_i4.Result<_i37.TokenRate?>>.value(
            _FakeResult_2<_i37.TokenRate?>(
          this,
          Invocation.method(
            #getOnboardDefiBridgeRates,
            [],
            {
              #fromToken: fromToken,
              #toFiat: toFiat,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i37.TokenRate?>>.value(
                _FakeResult_2<_i37.TokenRate?>(
          this,
          Invocation.method(
            #getOnboardDefiBridgeRates,
            [],
            {
              #fromToken: fromToken,
              #toFiat: toFiat,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<_i37.TokenRate?>>);

  @override
  _i18.Future<_i4.Result<_i12.PaymentMethodsList?>> getUserPaymentMethods() =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserPaymentMethods,
          [],
        ),
        returnValue: _i18.Future<_i4.Result<_i12.PaymentMethodsList?>>.value(
            _FakeResult_2<_i12.PaymentMethodsList?>(
          this,
          Invocation.method(
            #getUserPaymentMethods,
            [],
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i12.PaymentMethodsList?>>.value(
                _FakeResult_2<_i12.PaymentMethodsList?>(
          this,
          Invocation.method(
            #getUserPaymentMethods,
            [],
          ),
        )),
      ) as _i18.Future<_i4.Result<_i12.PaymentMethodsList?>>);
}

/// A class which mocks [DecryptionService].
///
/// See the documentation for Mockito's code generation for more information.
class MockDecryptionService extends _i1.Mock implements _i7.DecryptionService {
  @override
  _i18.Future<_i4.Result<_i36.ProcessResult?>> remoteDecryptData({
    required String? input,
    required String? decryptionRequestToken,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #remoteDecryptData,
          [],
          {
            #input: input,
            #decryptionRequestToken: decryptionRequestToken,
          },
        ),
        returnValue: _i18.Future<_i4.Result<_i36.ProcessResult?>>.value(
            _FakeResult_2<_i36.ProcessResult?>(
          this,
          Invocation.method(
            #remoteDecryptData,
            [],
            {
              #input: input,
              #decryptionRequestToken: decryptionRequestToken,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i36.ProcessResult?>>.value(
                _FakeResult_2<_i36.ProcessResult?>(
          this,
          Invocation.method(
            #remoteDecryptData,
            [],
            {
              #input: input,
              #decryptionRequestToken: decryptionRequestToken,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<_i36.ProcessResult?>>);

  @override
  String decrypt({
    required _i14.RSAPrivateKey? decryptionKey,
    required String? input,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #decrypt,
          [],
          {
            #decryptionKey: decryptionKey,
            #input: input,
          },
        ),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.method(
            #decrypt,
            [],
            {
              #decryptionKey: decryptionKey,
              #input: input,
            },
          ),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.method(
            #decrypt,
            [],
            {
              #decryptionKey: decryptionKey,
              #input: input,
            },
          ),
        ),
      ) as String);
}

/// A class which mocks [LocationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockLocationService extends _i1.Mock implements _i7.LocationService {
  @override
  _i18.Future<dynamic> getDeviceCountry() => (super.noSuchMethod(
        Invocation.method(
          #getDeviceCountry,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);
}

/// A class which mocks [DirectAccountService].
///
/// See the documentation for Mockito's code generation for more information.
class MockDirectAccountService extends _i1.Mock
    implements _i7.DirectAccountService {
  @override
  _i12.DirectAccountsAccountManagementClient
      get directAccountsManagementClient => (super.noSuchMethod(
            Invocation.getter(#directAccountsManagementClient),
            returnValue: _FakeDirectAccountsAccountManagementClient_19(
              this,
              Invocation.getter(#directAccountsManagementClient),
            ),
            returnValueForMissingStub:
                _FakeDirectAccountsAccountManagementClient_19(
              this,
              Invocation.getter(#directAccountsManagementClient),
            ),
          ) as _i12.DirectAccountsAccountManagementClient);

  @override
  _i12.DirectAccountsDepositsClient get directAccountsDepositsClient =>
      (super.noSuchMethod(
        Invocation.getter(#directAccountsDepositsClient),
        returnValue: _FakeDirectAccountsDepositsClient_20(
          this,
          Invocation.getter(#directAccountsDepositsClient),
        ),
        returnValueForMissingStub: _FakeDirectAccountsDepositsClient_20(
          this,
          Invocation.getter(#directAccountsDepositsClient),
        ),
      ) as _i12.DirectAccountsDepositsClient);

  @override
  _i12.DirectAccountsTransactionsClient get directAccountsTransactionsClient =>
      (super.noSuchMethod(
        Invocation.getter(#directAccountsTransactionsClient),
        returnValue: _FakeDirectAccountsTransactionsClient_21(
          this,
          Invocation.getter(#directAccountsTransactionsClient),
        ),
        returnValueForMissingStub: _FakeDirectAccountsTransactionsClient_21(
          this,
          Invocation.getter(#directAccountsTransactionsClient),
        ),
      ) as _i12.DirectAccountsTransactionsClient);

  @override
  set directAccountAmountEntryViewArguments(
          _i38.DirectAccountAmountEntryViewArguments?
              directAccountAmountEntryViewArguments) =>
      super.noSuchMethod(
        Invocation.setter(
          #directAccountAmountEntryViewArguments,
          directAccountAmountEntryViewArguments,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i18.Future<_i4.Result<_i12.DirectAccountsList?>> getActiveDirectAccounts(
          String? accountCurrency) =>
      (super.noSuchMethod(
        Invocation.method(
          #getActiveDirectAccounts,
          [accountCurrency],
        ),
        returnValue: _i18.Future<_i4.Result<_i12.DirectAccountsList?>>.value(
            _FakeResult_2<_i12.DirectAccountsList?>(
          this,
          Invocation.method(
            #getActiveDirectAccounts,
            [accountCurrency],
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i12.DirectAccountsList?>>.value(
                _FakeResult_2<_i12.DirectAccountsList?>(
          this,
          Invocation.method(
            #getActiveDirectAccounts,
            [accountCurrency],
          ),
        )),
      ) as _i18.Future<_i4.Result<_i12.DirectAccountsList?>>);

  @override
  _i18.Future<_i4.Result<_i12.DirectAccountDto?>> getDirectAccountsById(
          String? accountId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getDirectAccountsById,
          [accountId],
        ),
        returnValue: _i18.Future<_i4.Result<_i12.DirectAccountDto?>>.value(
            _FakeResult_2<_i12.DirectAccountDto?>(
          this,
          Invocation.method(
            #getDirectAccountsById,
            [accountId],
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i12.DirectAccountDto?>>.value(
                _FakeResult_2<_i12.DirectAccountDto?>(
          this,
          Invocation.method(
            #getDirectAccountsById,
            [accountId],
          ),
        )),
      ) as _i18.Future<_i4.Result<_i12.DirectAccountDto?>>);

  @override
  _i18.Future<_i4.Result<_i12.DirectAccountAddressList?>>
      getAddressesForDirectAccount(String? accountId) => (super.noSuchMethod(
            Invocation.method(
              #getAddressesForDirectAccount,
              [accountId],
            ),
            returnValue:
                _i18.Future<_i4.Result<_i12.DirectAccountAddressList?>>.value(
                    _FakeResult_2<_i12.DirectAccountAddressList?>(
              this,
              Invocation.method(
                #getAddressesForDirectAccount,
                [accountId],
              ),
            )),
            returnValueForMissingStub:
                _i18.Future<_i4.Result<_i12.DirectAccountAddressList?>>.value(
                    _FakeResult_2<_i12.DirectAccountAddressList?>(
              this,
              Invocation.method(
                #getAddressesForDirectAccount,
                [accountId],
              ),
            )),
          ) as _i18.Future<_i4.Result<_i12.DirectAccountAddressList?>>);

  @override
  _i18.Future<_i4.Result<_i12.AccountAddressDto?>>
      createAddressForDirectAccount({
    required String? accountId,
    required String? networkId,
  }) =>
          (super.noSuchMethod(
            Invocation.method(
              #createAddressForDirectAccount,
              [],
              {
                #accountId: accountId,
                #networkId: networkId,
              },
            ),
            returnValue: _i18.Future<_i4.Result<_i12.AccountAddressDto?>>.value(
                _FakeResult_2<_i12.AccountAddressDto?>(
              this,
              Invocation.method(
                #createAddressForDirectAccount,
                [],
                {
                  #accountId: accountId,
                  #networkId: networkId,
                },
              ),
            )),
            returnValueForMissingStub:
                _i18.Future<_i4.Result<_i12.AccountAddressDto?>>.value(
                    _FakeResult_2<_i12.AccountAddressDto?>(
              this,
              Invocation.method(
                #createAddressForDirectAccount,
                [],
                {
                  #accountId: accountId,
                  #networkId: networkId,
                },
              ),
            )),
          ) as _i18.Future<_i4.Result<_i12.AccountAddressDto?>>);

  @override
  _i18.Future<_i4.Result<_i12.CryptoAssetsDepositsConfig?>> getDepositConfig({
    String? currency,
    String? country,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getDepositConfig,
          [],
          {
            #currency: currency,
            #country: country,
          },
        ),
        returnValue:
            _i18.Future<_i4.Result<_i12.CryptoAssetsDepositsConfig?>>.value(
                _FakeResult_2<_i12.CryptoAssetsDepositsConfig?>(
          this,
          Invocation.method(
            #getDepositConfig,
            [],
            {
              #currency: currency,
              #country: country,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i12.CryptoAssetsDepositsConfig?>>.value(
                _FakeResult_2<_i12.CryptoAssetsDepositsConfig?>(
          this,
          Invocation.method(
            #getDepositConfig,
            [],
            {
              #currency: currency,
              #country: country,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<_i12.CryptoAssetsDepositsConfig?>>);

  @override
  _i18.Future<_i4.Result<_i12.DepositExchangeRateInfo?>> getQuote({
    required String? tokenCode,
    required String? currency,
    num? amount,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getQuote,
          [],
          {
            #tokenCode: tokenCode,
            #currency: currency,
            #amount: amount,
          },
        ),
        returnValue:
            _i18.Future<_i4.Result<_i12.DepositExchangeRateInfo?>>.value(
                _FakeResult_2<_i12.DepositExchangeRateInfo?>(
          this,
          Invocation.method(
            #getQuote,
            [],
            {
              #tokenCode: tokenCode,
              #currency: currency,
              #amount: amount,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i12.DepositExchangeRateInfo?>>.value(
                _FakeResult_2<_i12.DepositExchangeRateInfo?>(
          this,
          Invocation.method(
            #getQuote,
            [],
            {
              #tokenCode: tokenCode,
              #currency: currency,
              #amount: amount,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<_i12.DepositExchangeRateInfo?>>);

  @override
  _i18.Future<_i4.Result<_i12.DepositExchangeRateInfo?>> getReferenceRate({
    required String? tokenCode,
    required String? currency,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getReferenceRate,
          [],
          {
            #tokenCode: tokenCode,
            #currency: currency,
          },
        ),
        returnValue:
            _i18.Future<_i4.Result<_i12.DepositExchangeRateInfo?>>.value(
                _FakeResult_2<_i12.DepositExchangeRateInfo?>(
          this,
          Invocation.method(
            #getReferenceRate,
            [],
            {
              #tokenCode: tokenCode,
              #currency: currency,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i12.DepositExchangeRateInfo?>>.value(
                _FakeResult_2<_i12.DepositExchangeRateInfo?>(
          this,
          Invocation.method(
            #getReferenceRate,
            [],
            {
              #tokenCode: tokenCode,
              #currency: currency,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<_i12.DepositExchangeRateInfo?>>);

  @override
  _i18.Future<_i4.Result<List<_i12.DepositTransactionDto>>>
      getDirectAccountFailedTransactions() => (super.noSuchMethod(
            Invocation.method(
              #getDirectAccountFailedTransactions,
              [],
            ),
            returnValue:
                _i18.Future<_i4.Result<List<_i12.DepositTransactionDto>>>.value(
                    _FakeResult_2<List<_i12.DepositTransactionDto>>(
              this,
              Invocation.method(
                #getDirectAccountFailedTransactions,
                [],
              ),
            )),
            returnValueForMissingStub:
                _i18.Future<_i4.Result<List<_i12.DepositTransactionDto>>>.value(
                    _FakeResult_2<List<_i12.DepositTransactionDto>>(
              this,
              Invocation.method(
                #getDirectAccountFailedTransactions,
                [],
              ),
            )),
          ) as _i18.Future<_i4.Result<List<_i12.DepositTransactionDto>>>);

  @override
  _i18.Future<_i4.Result<_i12.DepositTransactionsList?>>
      getDirectAccountOngoingTransactions() => (super.noSuchMethod(
            Invocation.method(
              #getDirectAccountOngoingTransactions,
              [],
            ),
            returnValue:
                _i18.Future<_i4.Result<_i12.DepositTransactionsList?>>.value(
                    _FakeResult_2<_i12.DepositTransactionsList?>(
              this,
              Invocation.method(
                #getDirectAccountOngoingTransactions,
                [],
              ),
            )),
            returnValueForMissingStub:
                _i18.Future<_i4.Result<_i12.DepositTransactionsList?>>.value(
                    _FakeResult_2<_i12.DepositTransactionsList?>(
              this,
              Invocation.method(
                #getDirectAccountOngoingTransactions,
                [],
              ),
            )),
          ) as _i18.Future<_i4.Result<_i12.DepositTransactionsList?>>);
}

/// A class which mocks [TokenService].
///
/// See the documentation for Mockito's code generation for more information.
class MockTokenService extends _i1.Mock implements _i7.TokenService {
  @override
  _i9.ReactiveValue<bool> get balanceRefreshed => (super.noSuchMethod(
        Invocation.getter(#balanceRefreshed),
        returnValue: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#balanceRefreshed),
        ),
        returnValueForMissingStub: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#balanceRefreshed),
        ),
      ) as _i9.ReactiveValue<bool>);

  @override
  List<_i4.LocalAddressTokenBalance> get topTokens => (super.noSuchMethod(
        Invocation.getter(#topTokens),
        returnValue: <_i4.LocalAddressTokenBalance>[],
        returnValueForMissingStub: <_i4.LocalAddressTokenBalance>[],
      ) as List<_i4.LocalAddressTokenBalance>);

  @override
  set topTokens(List<_i4.LocalAddressTokenBalance>? _topTokens) =>
      super.noSuchMethod(
        Invocation.setter(
          #topTokens,
          _topTokens,
        ),
        returnValueForMissingStub: null,
      );

  @override
  List<_i4.LocalAddressTokenBalance> get topGainerTokens => (super.noSuchMethod(
        Invocation.getter(#topGainerTokens),
        returnValue: <_i4.LocalAddressTokenBalance>[],
        returnValueForMissingStub: <_i4.LocalAddressTokenBalance>[],
      ) as List<_i4.LocalAddressTokenBalance>);

  @override
  set topGainerTokens(List<_i4.LocalAddressTokenBalance>? _topGainerTokens) =>
      super.noSuchMethod(
        Invocation.setter(
          #topGainerTokens,
          _topGainerTokens,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i8.Box<_i4.LocalAddressTokenBalance> get addressTokenBox =>
      (super.noSuchMethod(
        Invocation.getter(#addressTokenBox),
        returnValue: _FakeBox_10<_i4.LocalAddressTokenBalance>(
          this,
          Invocation.getter(#addressTokenBox),
        ),
        returnValueForMissingStub: _FakeBox_10<_i4.LocalAddressTokenBalance>(
          this,
          Invocation.getter(#addressTokenBox),
        ),
      ) as _i8.Box<_i4.LocalAddressTokenBalance>);

  @override
  _i8.Box<_i4.LocalAddressTokenBalance> get myPortfolioBoxName =>
      (super.noSuchMethod(
        Invocation.getter(#myPortfolioBoxName),
        returnValue: _FakeBox_10<_i4.LocalAddressTokenBalance>(
          this,
          Invocation.getter(#myPortfolioBoxName),
        ),
        returnValueForMissingStub: _FakeBox_10<_i4.LocalAddressTokenBalance>(
          this,
          Invocation.getter(#myPortfolioBoxName),
        ),
      ) as _i8.Box<_i4.LocalAddressTokenBalance>);

  @override
  _i8.Box<_i39.LocalPortfolioPrices> get priceChangesBox => (super.noSuchMethod(
        Invocation.getter(#priceChangesBox),
        returnValue: _FakeBox_10<_i39.LocalPortfolioPrices>(
          this,
          Invocation.getter(#priceChangesBox),
        ),
        returnValueForMissingStub: _FakeBox_10<_i39.LocalPortfolioPrices>(
          this,
          Invocation.getter(#priceChangesBox),
        ),
      ) as _i8.Box<_i39.LocalPortfolioPrices>);

  @override
  List<_i4.LocalToken> get userTokensAsLocalToken => (super.noSuchMethod(
        Invocation.getter(#userTokensAsLocalToken),
        returnValue: <_i4.LocalToken>[],
        returnValueForMissingStub: <_i4.LocalToken>[],
      ) as List<_i4.LocalToken>);

  @override
  int get listenersCount => (super.noSuchMethod(
        Invocation.getter(#listenersCount),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as int);

  @override
  num getPortfolioBalance() => (super.noSuchMethod(
        Invocation.method(
          #getPortfolioBalance,
          [],
        ),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as num);

  @override
  num getPortfolioBalanceOnNetwork(String? networkId) => (super.noSuchMethod(
        Invocation.method(
          #getPortfolioBalanceOnNetwork,
          [networkId],
        ),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as num);

  @override
  _i18.Future<void> getTestnetToken({
    required String? networkId,
    bool? forceRefresh = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getTestnetToken,
          [],
          {
            #networkId: networkId,
            #forceRefresh: forceRefresh,
          },
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  _i18.Future<_i4.Result<_i28.TopTokenResponse>> getTopTokens({
    int? page = 1,
    int? size = 100,
    _i28.TopTokenOrderCategory? category =
        _i28.TopTokenOrderCategory.MARKET_CAP,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getTopTokens,
          [],
          {
            #page: page,
            #size: size,
            #category: category,
          },
        ),
        returnValue: _i18.Future<_i4.Result<_i28.TopTokenResponse>>.value(
            _FakeResult_2<_i28.TopTokenResponse>(
          this,
          Invocation.method(
            #getTopTokens,
            [],
            {
              #page: page,
              #size: size,
              #category: category,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i28.TopTokenResponse>>.value(
                _FakeResult_2<_i28.TopTokenResponse>(
          this,
          Invocation.method(
            #getTopTokens,
            [],
            {
              #page: page,
              #size: size,
              #category: category,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<_i28.TopTokenResponse>>);

  @override
  _i18.Future<_i4.Result<dynamic>> getTokenBalances({
    bool? forceRefresh = false,
    required bool? clearDb,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getTokenBalances,
          [],
          {
            #forceRefresh: forceRefresh,
            #clearDb: clearDb,
          },
        ),
        returnValue:
            _i18.Future<_i4.Result<dynamic>>.value(_FakeResult_2<dynamic>(
          this,
          Invocation.method(
            #getTokenBalances,
            [],
            {
              #forceRefresh: forceRefresh,
              #clearDb: clearDb,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<dynamic>>.value(_FakeResult_2<dynamic>(
          this,
          Invocation.method(
            #getTokenBalances,
            [],
            {
              #forceRefresh: forceRefresh,
              #clearDb: clearDb,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<dynamic>>);

  @override
  _i18.Future<_i4.Result<dynamic>> getSpecificTokenBalances(
          {required String? tokenId}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getSpecificTokenBalances,
          [],
          {#tokenId: tokenId},
        ),
        returnValue:
            _i18.Future<_i4.Result<dynamic>>.value(_FakeResult_2<dynamic>(
          this,
          Invocation.method(
            #getSpecificTokenBalances,
            [],
            {#tokenId: tokenId},
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<dynamic>>.value(_FakeResult_2<dynamic>(
          this,
          Invocation.method(
            #getSpecificTokenBalances,
            [],
            {#tokenId: tokenId},
          ),
        )),
      ) as _i18.Future<_i4.Result<dynamic>>);

  @override
  _i18.Future<
      _i4.Result<List<_i4.LocalTokenNetworkBalance>>> getTokenDetailsBySymbol(
          {required String? symbol}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getTokenDetailsBySymbol,
          [],
          {#symbol: symbol},
        ),
        returnValue:
            _i18.Future<_i4.Result<List<_i4.LocalTokenNetworkBalance>>>.value(
                _FakeResult_2<List<_i4.LocalTokenNetworkBalance>>(
          this,
          Invocation.method(
            #getTokenDetailsBySymbol,
            [],
            {#symbol: symbol},
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<List<_i4.LocalTokenNetworkBalance>>>.value(
                _FakeResult_2<List<_i4.LocalTokenNetworkBalance>>(
          this,
          Invocation.method(
            #getTokenDetailsBySymbol,
            [],
            {#symbol: symbol},
          ),
        )),
      ) as _i18.Future<_i4.Result<List<_i4.LocalTokenNetworkBalance>>>);

  @override
  _i18.Future<dynamic> saveAddressTokenBalances(
          _i4.LocalAddressTokenBalance? balance) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveAddressTokenBalances,
          [balance],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<dynamic> getTokenBalanceRate() => (super.noSuchMethod(
        Invocation.method(
          #getTokenBalanceRate,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<_i4.Result<_i39.LocalPortfolioPrices>> getPortfolioAssetChanges({
    required String? assetId,
    required _i28.Currency? currency,
    required _i28.PortfolioHistoryInterval? interval,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getPortfolioAssetChanges,
          [],
          {
            #assetId: assetId,
            #currency: currency,
            #interval: interval,
          },
        ),
        returnValue: _i18.Future<_i4.Result<_i39.LocalPortfolioPrices>>.value(
            _FakeResult_2<_i39.LocalPortfolioPrices>(
          this,
          Invocation.method(
            #getPortfolioAssetChanges,
            [],
            {
              #assetId: assetId,
              #currency: currency,
              #interval: interval,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i39.LocalPortfolioPrices>>.value(
                _FakeResult_2<_i39.LocalPortfolioPrices>(
          this,
          Invocation.method(
            #getPortfolioAssetChanges,
            [],
            {
              #assetId: assetId,
              #currency: currency,
              #interval: interval,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<_i39.LocalPortfolioPrices>>);

  @override
  _i39.LocalPortfolioPrices? getCachedPortfolioAssetChanges({
    required String? assetId,
    required _i28.Currency? currency,
    required _i28.PortfolioHistoryInterval? interval,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCachedPortfolioAssetChanges,
          [],
          {
            #assetId: assetId,
            #currency: currency,
            #interval: interval,
          },
        ),
        returnValueForMissingStub: null,
      ) as _i39.LocalPortfolioPrices?);

  @override
  int sortTokenByMarketCap(
    _i4.LocalAddressTokenBalance? a,
    _i4.LocalAddressTokenBalance? b,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #sortTokenByMarketCap,
          [
            a,
            b,
          ],
        ),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as int);

  @override
  int sortTokenByPriceChange24h(
    _i4.LocalAddressTokenBalance? a,
    _i4.LocalAddressTokenBalance? b,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #sortTokenByPriceChange24h,
          [
            a,
            b,
          ],
        ),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as int);

  @override
  void updateTokenBalance(_i4.LocalAddressTokenBalance? localToken) =>
      super.noSuchMethod(
        Invocation.method(
          #updateTokenBalance,
          [localToken],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i18.Future<dynamic> clearPreviousBalance() => (super.noSuchMethod(
        Invocation.method(
          #clearPreviousBalance,
          [],
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i18.Future<_i4.LocalAddressTokenBalance?> getNativeToken(
          String? networkId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getNativeToken,
          [networkId],
        ),
        returnValue: _i18.Future<_i4.LocalAddressTokenBalance?>.value(),
        returnValueForMissingStub:
            _i18.Future<_i4.LocalAddressTokenBalance?>.value(),
      ) as _i18.Future<_i4.LocalAddressTokenBalance?>);

  @override
  _i18.Future<_i4.Result<List<_i4.LocalAddressTokenBalance>>> searchToken({
    required String? symbol,
    required int? page,
    required int? size,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchToken,
          [],
          {
            #symbol: symbol,
            #page: page,
            #size: size,
          },
        ),
        returnValue:
            _i18.Future<_i4.Result<List<_i4.LocalAddressTokenBalance>>>.value(
                _FakeResult_2<List<_i4.LocalAddressTokenBalance>>(
          this,
          Invocation.method(
            #searchToken,
            [],
            {
              #symbol: symbol,
              #page: page,
              #size: size,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<List<_i4.LocalAddressTokenBalance>>>.value(
                _FakeResult_2<List<_i4.LocalAddressTokenBalance>>(
          this,
          Invocation.method(
            #searchToken,
            [],
            {
              #symbol: symbol,
              #page: page,
              #size: size,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<List<_i4.LocalAddressTokenBalance>>>);

  @override
  _i18.Future<_i4.Result<bool>> resetBalance({required String? address}) =>
      (super.noSuchMethod(
        Invocation.method(
          #resetBalance,
          [],
          {#address: address},
        ),
        returnValue: _i18.Future<_i4.Result<bool>>.value(_FakeResult_2<bool>(
          this,
          Invocation.method(
            #resetBalance,
            [],
            {#address: address},
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<bool>>.value(_FakeResult_2<bool>(
          this,
          Invocation.method(
            #resetBalance,
            [],
            {#address: address},
          ),
        )),
      ) as _i18.Future<_i4.Result<bool>>);

  @override
  _i4.LocalAddressTokenBalance? getTokenLocalGivenSymbol(
          {required String? symbol}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getTokenLocalGivenSymbol,
          [],
          {#symbol: symbol},
        ),
        returnValueForMissingStub: null,
      ) as _i4.LocalAddressTokenBalance?);

  @override
  List<_i4.LocalToken> getUserTokensAsLocalToken() => (super.noSuchMethod(
        Invocation.method(
          #getUserTokensAsLocalToken,
          [],
        ),
        returnValue: <_i4.LocalToken>[],
        returnValueForMissingStub: <_i4.LocalToken>[],
      ) as List<_i4.LocalToken>);

  @override
  void listenToReactiveValues(List<dynamic>? reactiveValues) =>
      super.noSuchMethod(
        Invocation.method(
          #listenToReactiveValues,
          [reactiveValues],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [FeatureService].
///
/// See the documentation for Mockito's code generation for more information.
class MockFeatureService extends _i1.Mock implements _i7.FeatureService {
  @override
  bool get isAppStoreBuild => (super.noSuchMethod(
        Invocation.getter(#isAppStoreBuild),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  bool get isOnRampOff => (super.noSuchMethod(
        Invocation.getter(#isOnRampOff),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  bool get isOffRampOff => (super.noSuchMethod(
        Invocation.getter(#isOffRampOff),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  bool get isSwapOff => (super.noSuchMethod(
        Invocation.getter(#isSwapOff),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  bool get isSellOff => (super.noSuchMethod(
        Invocation.getter(#isSellOff),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  bool get isHubOff => (super.noSuchMethod(
        Invocation.getter(#isHubOff),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  bool get isVirtualAccountOff => (super.noSuchMethod(
        Invocation.getter(#isVirtualAccountOff),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  String keyForName(String? name) => (super.noSuchMethod(
        Invocation.method(
          #keyForName,
          [name],
        ),
        returnValue: _i17.dummyValue<String>(
          this,
          Invocation.method(
            #keyForName,
            [name],
          ),
        ),
        returnValueForMissingStub: _i17.dummyValue<String>(
          this,
          Invocation.method(
            #keyForName,
            [name],
          ),
        ),
      ) as String);

  @override
  List<String> getBlockedHubCategories() => (super.noSuchMethod(
        Invocation.method(
          #getBlockedHubCategories,
          [],
        ),
        returnValue: <String>[],
        returnValueForMissingStub: <String>[],
      ) as List<String>);

  @override
  List<String> getBlockedDapp() => (super.noSuchMethod(
        Invocation.method(
          #getBlockedDapp,
          [],
        ),
        returnValue: <String>[],
        returnValueForMissingStub: <String>[],
      ) as List<String>);
}

/// A class which mocks [LifeCycleService].
///
/// See the documentation for Mockito's code generation for more information.
class MockLifeCycleService extends _i1.Mock implements _i7.LifeCycleService {
  @override
  void addStream(_i19.AppLifecycleState? state) => super.noSuchMethod(
        Invocation.method(
          #addStream,
          [state],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i18.Stream<dynamic> getStream() => (super.noSuchMethod(
        Invocation.method(
          #getStream,
          [],
        ),
        returnValue: _i18.Stream<dynamic>.empty(),
        returnValueForMissingStub: _i18.Stream<dynamic>.empty(),
      ) as _i18.Stream<dynamic>);

  @override
  _i18.Future<void> closeStream() => (super.noSuchMethod(
        Invocation.method(
          #closeStream,
          [],
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);
}

/// A class which mocks [DappService].
///
/// See the documentation for Mockito's code generation for more information.
class MockDappService extends _i1.Mock implements _i7.DappService {
  @override
  List<_i40.DappTabItem> get tabs => (super.noSuchMethod(
        Invocation.getter(#tabs),
        returnValue: <_i40.DappTabItem>[],
        returnValueForMissingStub: <_i40.DappTabItem>[],
      ) as List<_i40.DappTabItem>);

  @override
  set tabs(List<_i40.DappTabItem>? _tabs) => super.noSuchMethod(
        Invocation.setter(
          #tabs,
          _tabs,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i9.ReactiveValue<bool> get isRefreshed => (super.noSuchMethod(
        Invocation.getter(#isRefreshed),
        returnValue: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#isRefreshed),
        ),
        returnValueForMissingStub: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#isRefreshed),
        ),
      ) as _i9.ReactiveValue<bool>);

  @override
  set isRefreshed(_i9.ReactiveValue<bool>? _isRefreshed) => super.noSuchMethod(
        Invocation.setter(
          #isRefreshed,
          _isRefreshed,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i15.FocusNode get focusNode => (super.noSuchMethod(
        Invocation.getter(#focusNode),
        returnValue: _FakeFocusNode_22(
          this,
          Invocation.getter(#focusNode),
        ),
        returnValueForMissingStub: _FakeFocusNode_22(
          this,
          Invocation.getter(#focusNode),
        ),
      ) as _i15.FocusNode);

  @override
  _i9.ReactiveList<_i4.Dapp> get dapps => (super.noSuchMethod(
        Invocation.getter(#dapps),
        returnValue: _FakeReactiveList_23<_i4.Dapp>(
          this,
          Invocation.getter(#dapps),
        ),
        returnValueForMissingStub: _FakeReactiveList_23<_i4.Dapp>(
          this,
          Invocation.getter(#dapps),
        ),
      ) as _i9.ReactiveList<_i4.Dapp>);

  @override
  _i8.Box<_i4.Dapp> get dappTabBox => (super.noSuchMethod(
        Invocation.getter(#dappTabBox),
        returnValue: _FakeBox_10<_i4.Dapp>(
          this,
          Invocation.getter(#dappTabBox),
        ),
        returnValueForMissingStub: _FakeBox_10<_i4.Dapp>(
          this,
          Invocation.getter(#dappTabBox),
        ),
      ) as _i8.Box<_i4.Dapp>);

  @override
  int get listenersCount => (super.noSuchMethod(
        Invocation.getter(#listenersCount),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as int);

  @override
  void loadTabs() => super.noSuchMethod(
        Invocation.method(
          #loadTabs,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i18.Future<dynamic> saveDapp({
    required _i4.Dapp? dapp,
    required String? tabId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveDapp,
          [],
          {
            #dapp: dapp,
            #tabId: tabId,
          },
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  _i4.Dapp? getTab(String? tabId) => (super.noSuchMethod(
        Invocation.method(
          #getTab,
          [tabId],
        ),
        returnValueForMissingStub: null,
      ) as _i4.Dapp?);

  @override
  dynamic removeTab({required String? tabId}) => super.noSuchMethod(
        Invocation.method(
          #removeTab,
          [],
          {#tabId: tabId},
        ),
        returnValueForMissingStub: null,
      );

  @override
  void listenToReactiveValues(List<dynamic>? reactiveValues) =>
      super.noSuchMethod(
        Invocation.method(
          #listenToReactiveValues,
          [reactiveValues],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [NftsService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNftsService extends _i1.Mock implements _i7.NftsService {
  @override
  _i8.Box<_i4.LocalNFTBrief> get nftsBox => (super.noSuchMethod(
        Invocation.getter(#nftsBox),
        returnValue: _FakeBox_10<_i4.LocalNFTBrief>(
          this,
          Invocation.getter(#nftsBox),
        ),
        returnValueForMissingStub: _FakeBox_10<_i4.LocalNFTBrief>(
          this,
          Invocation.getter(#nftsBox),
        ),
      ) as _i8.Box<_i4.LocalNFTBrief>);

  @override
  int get listenersCount => (super.noSuchMethod(
        Invocation.getter(#listenersCount),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as int);

  @override
  _i18.Future<List<_i4.LocalNFTBrief>> getNftForNetwork(
          {required String? networkId}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getNftForNetwork,
          [],
          {#networkId: networkId},
        ),
        returnValue:
            _i18.Future<List<_i4.LocalNFTBrief>>.value(<_i4.LocalNFTBrief>[]),
        returnValueForMissingStub:
            _i18.Future<List<_i4.LocalNFTBrief>>.value(<_i4.LocalNFTBrief>[]),
      ) as _i18.Future<List<_i4.LocalNFTBrief>>);

  @override
  List<_i4.LocalNFTBrief> getAllHiddenNFTs() => (super.noSuchMethod(
        Invocation.method(
          #getAllHiddenNFTs,
          [],
        ),
        returnValue: <_i4.LocalNFTBrief>[],
        returnValueForMissingStub: <_i4.LocalNFTBrief>[],
      ) as List<_i4.LocalNFTBrief>);

  @override
  int getTotalHiddenNFTsCount() => (super.noSuchMethod(
        Invocation.method(
          #getTotalHiddenNFTsCount,
          [],
        ),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as int);

  @override
  _i18.Future<dynamic> hideNFT({
    required String? collectionUniqueId,
    required String? nftUniqueId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #hideNFT,
          [],
          {
            #collectionUniqueId: collectionUniqueId,
            #nftUniqueId: nftUniqueId,
          },
        ),
        returnValue: _i18.Future<dynamic>.value(),
        returnValueForMissingStub: _i18.Future<dynamic>.value(),
      ) as _i18.Future<dynamic>);

  @override
  Map<String, List<String>> getHiddenNFTs() => (super.noSuchMethod(
        Invocation.method(
          #getHiddenNFTs,
          [],
        ),
        returnValue: <String, List<String>>{},
        returnValueForMissingStub: <String, List<String>>{},
      ) as Map<String, List<String>>);

  @override
  _i18.Future<void> saveHiddenNFTs(
          {required Map<String, List<String>>? hiddenNFTs}) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveHiddenNFTs,
          [],
          {#hiddenNFTs: hiddenNFTs},
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  bool isNFTHidden({
    required String? collectionUniqueId,
    required String? nftUniqueId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #isNFTHidden,
          [],
          {
            #collectionUniqueId: collectionUniqueId,
            #nftUniqueId: nftUniqueId,
          },
        ),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i18.Future<void> unHideNFT({
    required String? collectionUniqueId,
    required String? nftUniqueId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #unHideNFT,
          [],
          {
            #collectionUniqueId: collectionUniqueId,
            #nftUniqueId: nftUniqueId,
          },
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  bool isCollectionHidden({
    required String? collectionUniqueId,
    required List<String>? allUniqueNFTIds,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #isCollectionHidden,
          [],
          {
            #collectionUniqueId: collectionUniqueId,
            #allUniqueNFTIds: allUniqueNFTIds,
          },
        ),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i18.Future<void> hideNFTCollection({
    required String? collectionUniqueId,
    required List<String>? allUniqueNFTIds,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #hideNFTCollection,
          [],
          {
            #collectionUniqueId: collectionUniqueId,
            #allUniqueNFTIds: allUniqueNFTIds,
          },
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  _i18.Future<void> unHideNFTCollection(
          {required String? collectionUniqueId}) =>
      (super.noSuchMethod(
        Invocation.method(
          #unHideNFTCollection,
          [],
          {#collectionUniqueId: collectionUniqueId},
        ),
        returnValue: _i18.Future<void>.value(),
        returnValueForMissingStub: _i18.Future<void>.value(),
      ) as _i18.Future<void>);

  @override
  void listenToReactiveValues(List<dynamic>? reactiveValues) =>
      super.noSuchMethod(
        Invocation.method(
          #listenToReactiveValues,
          [reactiveValues],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [PasskeyService].
///
/// See the documentation for Mockito's code generation for more information.
class MockPasskeyService extends _i1.Mock implements _i7.PasskeyService {
  @override
  _i9.ReactiveList<_i12.PasskeyCredentialInfo> get passkeysList =>
      (super.noSuchMethod(
        Invocation.getter(#passkeysList),
        returnValue: _FakeReactiveList_23<_i12.PasskeyCredentialInfo>(
          this,
          Invocation.getter(#passkeysList),
        ),
        returnValueForMissingStub:
            _FakeReactiveList_23<_i12.PasskeyCredentialInfo>(
          this,
          Invocation.getter(#passkeysList),
        ),
      ) as _i9.ReactiveList<_i12.PasskeyCredentialInfo>);

  @override
  set passkeysList(
          _i9.ReactiveList<_i12.PasskeyCredentialInfo>? _passkeysList) =>
      super.noSuchMethod(
        Invocation.setter(
          #passkeysList,
          _passkeysList,
        ),
        returnValueForMissingStub: null,
      );

  @override
  int get listenersCount => (super.noSuchMethod(
        Invocation.getter(#listenersCount),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as int);

  @override
  _i18.Future<bool> checkPasskeyAvailability() => (super.noSuchMethod(
        Invocation.method(
          #checkPasskeyAvailability,
          [],
        ),
        returnValue: _i18.Future<bool>.value(false),
        returnValueForMissingStub: _i18.Future<bool>.value(false),
      ) as _i18.Future<bool>);

  @override
  _i18.Future<_i4.Result<bool>> startPasskeyRegistration() =>
      (super.noSuchMethod(
        Invocation.method(
          #startPasskeyRegistration,
          [],
        ),
        returnValue: _i18.Future<_i4.Result<bool>>.value(_FakeResult_2<bool>(
          this,
          Invocation.method(
            #startPasskeyRegistration,
            [],
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<bool>>.value(_FakeResult_2<bool>(
          this,
          Invocation.method(
            #startPasskeyRegistration,
            [],
          ),
        )),
      ) as _i18.Future<_i4.Result<bool>>);

  @override
  _i18.Future<_i4.Result<List<_i12.PasskeyCredentialInfo>>>
      getRegisteredPasskeys() => (super.noSuchMethod(
            Invocation.method(
              #getRegisteredPasskeys,
              [],
            ),
            returnValue:
                _i18.Future<_i4.Result<List<_i12.PasskeyCredentialInfo>>>.value(
                    _FakeResult_2<List<_i12.PasskeyCredentialInfo>>(
              this,
              Invocation.method(
                #getRegisteredPasskeys,
                [],
              ),
            )),
            returnValueForMissingStub:
                _i18.Future<_i4.Result<List<_i12.PasskeyCredentialInfo>>>.value(
                    _FakeResult_2<List<_i12.PasskeyCredentialInfo>>(
              this,
              Invocation.method(
                #getRegisteredPasskeys,
                [],
              ),
            )),
          ) as _i18.Future<_i4.Result<List<_i12.PasskeyCredentialInfo>>>);

  @override
  _i18.Future<_i4.Result<_i29.Response<dynamic>>> deletePasskey(
          {required String? passkeyId}) =>
      (super.noSuchMethod(
        Invocation.method(
          #deletePasskey,
          [],
          {#passkeyId: passkeyId},
        ),
        returnValue: _i18.Future<_i4.Result<_i29.Response<dynamic>>>.value(
            _FakeResult_2<_i29.Response<dynamic>>(
          this,
          Invocation.method(
            #deletePasskey,
            [],
            {#passkeyId: passkeyId},
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i29.Response<dynamic>>>.value(
                _FakeResult_2<_i29.Response<dynamic>>(
          this,
          Invocation.method(
            #deletePasskey,
            [],
            {#passkeyId: passkeyId},
          ),
        )),
      ) as _i18.Future<_i4.Result<_i29.Response<dynamic>>>);

  @override
  _i18.Future<_i4.Result<_i12.PasskeyLoginDTO>> generateLoginChallenge(
          {required String? email}) =>
      (super.noSuchMethod(
        Invocation.method(
          #generateLoginChallenge,
          [],
          {#email: email},
        ),
        returnValue: _i18.Future<_i4.Result<_i12.PasskeyLoginDTO>>.value(
            _FakeResult_2<_i12.PasskeyLoginDTO>(
          this,
          Invocation.method(
            #generateLoginChallenge,
            [],
            {#email: email},
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i12.PasskeyLoginDTO>>.value(
                _FakeResult_2<_i12.PasskeyLoginDTO>(
          this,
          Invocation.method(
            #generateLoginChallenge,
            [],
            {#email: email},
          ),
        )),
      ) as _i18.Future<_i4.Result<_i12.PasskeyLoginDTO>>);

  @override
  _i18.Future<_i4.Result<_i41.AuthenticateResponseType>>
      authenticateWithPlatformAndLogin(_i12.PasskeyLoginDTO? passkeyLoginDto) =>
          (super.noSuchMethod(
            Invocation.method(
              #authenticateWithPlatformAndLogin,
              [passkeyLoginDto],
            ),
            returnValue:
                _i18.Future<_i4.Result<_i41.AuthenticateResponseType>>.value(
                    _FakeResult_2<_i41.AuthenticateResponseType>(
              this,
              Invocation.method(
                #authenticateWithPlatformAndLogin,
                [passkeyLoginDto],
              ),
            )),
            returnValueForMissingStub:
                _i18.Future<_i4.Result<_i41.AuthenticateResponseType>>.value(
                    _FakeResult_2<_i41.AuthenticateResponseType>(
              this,
              Invocation.method(
                #authenticateWithPlatformAndLogin,
                [passkeyLoginDto],
              ),
            )),
          ) as _i18.Future<_i4.Result<_i41.AuthenticateResponseType>>);

  @override
  _i18.Future<_i4.Result<_i12.AuthOtpResponseDto?>>
      authenticateWithPlatformAndLoginPK(
              _i12.PasskeyLoginDTO? passkeyLoginDto) =>
          (super.noSuchMethod(
            Invocation.method(
              #authenticateWithPlatformAndLoginPK,
              [passkeyLoginDto],
            ),
            returnValue:
                _i18.Future<_i4.Result<_i12.AuthOtpResponseDto?>>.value(
                    _FakeResult_2<_i12.AuthOtpResponseDto?>(
              this,
              Invocation.method(
                #authenticateWithPlatformAndLoginPK,
                [passkeyLoginDto],
              ),
            )),
            returnValueForMissingStub:
                _i18.Future<_i4.Result<_i12.AuthOtpResponseDto?>>.value(
                    _FakeResult_2<_i12.AuthOtpResponseDto?>(
              this,
              Invocation.method(
                #authenticateWithPlatformAndLoginPK,
                [passkeyLoginDto],
              ),
            )),
          ) as _i18.Future<_i4.Result<_i12.AuthOtpResponseDto?>>);

  @override
  _i18.Future<_i41.AuthenticateResponseType?>
      authenticateWithPlatformForAuthorization(
              _i12.AuthorizationSessionPasskeyChallenge? sessionChallenge) =>
          (super.noSuchMethod(
            Invocation.method(
              #authenticateWithPlatformForAuthorization,
              [sessionChallenge],
            ),
            returnValue: _i18.Future<_i41.AuthenticateResponseType?>.value(),
            returnValueForMissingStub:
                _i18.Future<_i41.AuthenticateResponseType?>.value(),
          ) as _i18.Future<_i41.AuthenticateResponseType?>);

  @override
  void listenToReactiveValues(List<dynamic>? reactiveValues) =>
      super.noSuchMethod(
        Invocation.method(
          #listenToReactiveValues,
          [reactiveValues],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [VirtualAccountService].
///
/// See the documentation for Mockito's code generation for more information.
class MockVirtualAccountService extends _i1.Mock
    implements _i7.VirtualAccountService {
  @override
  _i9.ReactiveValue<bool> get virtualAccountRefreshed => (super.noSuchMethod(
        Invocation.getter(#virtualAccountRefreshed),
        returnValue: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#virtualAccountRefreshed),
        ),
        returnValueForMissingStub: _FakeReactiveValue_11<bool>(
          this,
          Invocation.getter(#virtualAccountRefreshed),
        ),
      ) as _i9.ReactiveValue<bool>);

  @override
  _i9.ReactiveValue<int?> get activeVirtualAccountLimit => (super.noSuchMethod(
        Invocation.getter(#activeVirtualAccountLimit),
        returnValue: _FakeReactiveValue_11<int?>(
          this,
          Invocation.getter(#activeVirtualAccountLimit),
        ),
        returnValueForMissingStub: _FakeReactiveValue_11<int?>(
          this,
          Invocation.getter(#activeVirtualAccountLimit),
        ),
      ) as _i9.ReactiveValue<int?>);

  @override
  set activeVirtualAccountLimit(
          _i9.ReactiveValue<int?>? _activeVirtualAccountLimit) =>
      super.noSuchMethod(
        Invocation.setter(
          #activeVirtualAccountLimit,
          _activeVirtualAccountLimit,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i9.ReactiveValue<_i42.UserOnboardingDetails?> get userOnboardingDetails =>
      (super.noSuchMethod(
        Invocation.getter(#userOnboardingDetails),
        returnValue: _FakeReactiveValue_11<_i42.UserOnboardingDetails?>(
          this,
          Invocation.getter(#userOnboardingDetails),
        ),
        returnValueForMissingStub:
            _FakeReactiveValue_11<_i42.UserOnboardingDetails?>(
          this,
          Invocation.getter(#userOnboardingDetails),
        ),
      ) as _i9.ReactiveValue<_i42.UserOnboardingDetails?>);

  @override
  _i8.Box<_i43.LocalCardWalletPackage> get cardWalletPackagesBox =>
      (super.noSuchMethod(
        Invocation.getter(#cardWalletPackagesBox),
        returnValue: _FakeBox_10<_i43.LocalCardWalletPackage>(
          this,
          Invocation.getter(#cardWalletPackagesBox),
        ),
        returnValueForMissingStub: _FakeBox_10<_i43.LocalCardWalletPackage>(
          this,
          Invocation.getter(#cardWalletPackagesBox),
        ),
      ) as _i8.Box<_i43.LocalCardWalletPackage>);

  @override
  set cardWalletPackagesBox(
          _i8.Box<_i43.LocalCardWalletPackage>? _cardWalletPackagesBox) =>
      super.noSuchMethod(
        Invocation.setter(
          #cardWalletPackagesBox,
          _cardWalletPackagesBox,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i7.PreferenceService get prefService => (super.noSuchMethod(
        Invocation.getter(#prefService),
        returnValue: _FakePreferenceService_24(
          this,
          Invocation.getter(#prefService),
        ),
        returnValueForMissingStub: _FakePreferenceService_24(
          this,
          Invocation.getter(#prefService),
        ),
      ) as _i7.PreferenceService);

  @override
  _i8.Box<_i44.LocalVirtualAccount> get virtualAccountBox =>
      (super.noSuchMethod(
        Invocation.getter(#virtualAccountBox),
        returnValue: _FakeBox_10<_i44.LocalVirtualAccount>(
          this,
          Invocation.getter(#virtualAccountBox),
        ),
        returnValueForMissingStub: _FakeBox_10<_i44.LocalVirtualAccount>(
          this,
          Invocation.getter(#virtualAccountBox),
        ),
      ) as _i8.Box<_i44.LocalVirtualAccount>);

  @override
  List<_i44.LocalVirtualAccount> get activeVirtualAccounts =>
      (super.noSuchMethod(
        Invocation.getter(#activeVirtualAccounts),
        returnValue: <_i44.LocalVirtualAccount>[],
        returnValueForMissingStub: <_i44.LocalVirtualAccount>[],
      ) as List<_i44.LocalVirtualAccount>);

  @override
  List<_i4.LocalNetwork> get networks => (super.noSuchMethod(
        Invocation.getter(#networks),
        returnValue: <_i4.LocalNetwork>[],
        returnValueForMissingStub: <_i4.LocalNetwork>[],
      ) as List<_i4.LocalNetwork>);

  @override
  bool get hasAccount => (super.noSuchMethod(
        Invocation.getter(#hasAccount),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  bool get hasNonTerminatedAccount => (super.noSuchMethod(
        Invocation.getter(#hasNonTerminatedAccount),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  int get listenersCount => (super.noSuchMethod(
        Invocation.getter(#listenersCount),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as int);

  @override
  _i18.Future<_i4.Result<_i44.LocalVirtualAccount?>> createVirtualAccount({
    required String? accountCurrency,
    String? label,
    _i12.FundingRequestBuilder? initialFunding,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createVirtualAccount,
          [],
          {
            #accountCurrency: accountCurrency,
            #label: label,
            #initialFunding: initialFunding,
          },
        ),
        returnValue: _i18.Future<_i4.Result<_i44.LocalVirtualAccount?>>.value(
            _FakeResult_2<_i44.LocalVirtualAccount?>(
          this,
          Invocation.method(
            #createVirtualAccount,
            [],
            {
              #accountCurrency: accountCurrency,
              #label: label,
              #initialFunding: initialFunding,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i44.LocalVirtualAccount?>>.value(
                _FakeResult_2<_i44.LocalVirtualAccount?>(
          this,
          Invocation.method(
            #createVirtualAccount,
            [],
            {
              #accountCurrency: accountCurrency,
              #label: label,
              #initialFunding: initialFunding,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<_i44.LocalVirtualAccount?>>);

  @override
  _i18.Future<_i4.Result<_i44.LocalVirtualAccount?>> getVirtualAccountById(
          {required String? accountId}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getVirtualAccountById,
          [],
          {#accountId: accountId},
        ),
        returnValue: _i18.Future<_i4.Result<_i44.LocalVirtualAccount?>>.value(
            _FakeResult_2<_i44.LocalVirtualAccount?>(
          this,
          Invocation.method(
            #getVirtualAccountById,
            [],
            {#accountId: accountId},
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i44.LocalVirtualAccount?>>.value(
                _FakeResult_2<_i44.LocalVirtualAccount?>(
          this,
          Invocation.method(
            #getVirtualAccountById,
            [],
            {#accountId: accountId},
          ),
        )),
      ) as _i18.Future<_i4.Result<_i44.LocalVirtualAccount?>>);

  @override
  _i18.Future<_i4.Result<_i12.StatusDto?>> terminateVirtualAccount({
    required String? accountId,
    required _i12.VirtualAccountOpRequest? opRequest,
    required String? cardCurrency,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #terminateVirtualAccount,
          [],
          {
            #accountId: accountId,
            #opRequest: opRequest,
            #cardCurrency: cardCurrency,
          },
        ),
        returnValue: _i18.Future<_i4.Result<_i12.StatusDto?>>.value(
            _FakeResult_2<_i12.StatusDto?>(
          this,
          Invocation.method(
            #terminateVirtualAccount,
            [],
            {
              #accountId: accountId,
              #opRequest: opRequest,
              #cardCurrency: cardCurrency,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i12.StatusDto?>>.value(
                _FakeResult_2<_i12.StatusDto?>(
          this,
          Invocation.method(
            #terminateVirtualAccount,
            [],
            {
              #accountId: accountId,
              #opRequest: opRequest,
              #cardCurrency: cardCurrency,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<_i12.StatusDto?>>);

  @override
  _i18.Future<_i4.Result<_i12.VirtualAccountsList?>> getVirtualAccounts(
          {String? currency}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getVirtualAccounts,
          [],
          {#currency: currency},
        ),
        returnValue: _i18.Future<_i4.Result<_i12.VirtualAccountsList?>>.value(
            _FakeResult_2<_i12.VirtualAccountsList?>(
          this,
          Invocation.method(
            #getVirtualAccounts,
            [],
            {#currency: currency},
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i12.VirtualAccountsList?>>.value(
                _FakeResult_2<_i12.VirtualAccountsList?>(
          this,
          Invocation.method(
            #getVirtualAccounts,
            [],
            {#currency: currency},
          ),
        )),
      ) as _i18.Future<_i4.Result<_i12.VirtualAccountsList?>>);

  @override
  _i18.Future<
      _i4.Result<
          _i45.LocalVirtualAccountUsageTerms?>> getAccountTerms() =>
      (super.noSuchMethod(
        Invocation.method(
          #getAccountTerms,
          [],
        ),
        returnValue:
            _i18.Future<_i4.Result<_i45.LocalVirtualAccountUsageTerms?>>.value(
                _FakeResult_2<_i45.LocalVirtualAccountUsageTerms?>(
          this,
          Invocation.method(
            #getAccountTerms,
            [],
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i45.LocalVirtualAccountUsageTerms?>>.value(
                _FakeResult_2<_i45.LocalVirtualAccountUsageTerms?>(
          this,
          Invocation.method(
            #getAccountTerms,
            [],
          ),
        )),
      ) as _i18.Future<_i4.Result<_i45.LocalVirtualAccountUsageTerms?>>);

  @override
  _i18.Future<_i42.UserOnboardingDetails?> getBridgeOnboardingInfo(
          {bool? showToast = false}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBridgeOnboardingInfo,
          [],
          {#showToast: showToast},
        ),
        returnValue: _i18.Future<_i42.UserOnboardingDetails?>.value(),
        returnValueForMissingStub:
            _i18.Future<_i42.UserOnboardingDetails?>.value(),
      ) as _i18.Future<_i42.UserOnboardingDetails?>);

  @override
  _i18.Future<_i4.Result<_i12.VirtualAccountTransactionConfig?>>
      getRemoteWithdrawalConfig({
    required String? country,
    required String? accountCurrency,
  }) =>
          (super.noSuchMethod(
            Invocation.method(
              #getRemoteWithdrawalConfig,
              [],
              {
                #country: country,
                #accountCurrency: accountCurrency,
              },
            ),
            returnValue: _i18.Future<
                    _i4.Result<_i12.VirtualAccountTransactionConfig?>>.value(
                _FakeResult_2<_i12.VirtualAccountTransactionConfig?>(
              this,
              Invocation.method(
                #getRemoteWithdrawalConfig,
                [],
                {
                  #country: country,
                  #accountCurrency: accountCurrency,
                },
              ),
            )),
            returnValueForMissingStub: _i18.Future<
                    _i4.Result<_i12.VirtualAccountTransactionConfig?>>.value(
                _FakeResult_2<_i12.VirtualAccountTransactionConfig?>(
              this,
              Invocation.method(
                #getRemoteWithdrawalConfig,
                [],
                {
                  #country: country,
                  #accountCurrency: accountCurrency,
                },
              ),
            )),
          ) as _i18.Future<_i4.Result<_i12.VirtualAccountTransactionConfig?>>);

  @override
  _i18.Future<_i4.Result<_i46.LocalCardTransactionConfig?>> getFundingConfig({
    required String? country,
    required String? accountCurrency,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getFundingConfig,
          [],
          {
            #country: country,
            #accountCurrency: accountCurrency,
          },
        ),
        returnValue:
            _i18.Future<_i4.Result<_i46.LocalCardTransactionConfig?>>.value(
                _FakeResult_2<_i46.LocalCardTransactionConfig?>(
          this,
          Invocation.method(
            #getFundingConfig,
            [],
            {
              #country: country,
              #accountCurrency: accountCurrency,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i46.LocalCardTransactionConfig?>>.value(
                _FakeResult_2<_i46.LocalCardTransactionConfig?>(
          this,
          Invocation.method(
            #getFundingConfig,
            [],
            {
              #country: country,
              #accountCurrency: accountCurrency,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<_i46.LocalCardTransactionConfig?>>);

  @override
  _i18.Future<_i4.Result<_i12.CryptoRateInfo?>> getFundingRateForCryptoToken({
    required String? tokenCode,
    String? accountCurrency,
    required bool? initialFunding,
    num? amount,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getFundingRateForCryptoToken,
          [],
          {
            #tokenCode: tokenCode,
            #accountCurrency: accountCurrency,
            #initialFunding: initialFunding,
            #amount: amount,
          },
        ),
        returnValue: _i18.Future<_i4.Result<_i12.CryptoRateInfo?>>.value(
            _FakeResult_2<_i12.CryptoRateInfo?>(
          this,
          Invocation.method(
            #getFundingRateForCryptoToken,
            [],
            {
              #tokenCode: tokenCode,
              #accountCurrency: accountCurrency,
              #initialFunding: initialFunding,
              #amount: amount,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i12.CryptoRateInfo?>>.value(
                _FakeResult_2<_i12.CryptoRateInfo?>(
          this,
          Invocation.method(
            #getFundingRateForCryptoToken,
            [],
            {
              #tokenCode: tokenCode,
              #accountCurrency: accountCurrency,
              #initialFunding: initialFunding,
              #amount: amount,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<_i12.CryptoRateInfo?>>);

  @override
  _i18.Future<_i4.Result<_i12.CashRateInfo?>>
      getFundingRatesForFiatVirtualAccounts({
    required String? channelId,
    required String? fiatCurrency,
    String? accountCurrency,
    required bool? initialFunding,
    num? amount,
  }) =>
          (super.noSuchMethod(
            Invocation.method(
              #getFundingRatesForFiatVirtualAccounts,
              [],
              {
                #channelId: channelId,
                #fiatCurrency: fiatCurrency,
                #accountCurrency: accountCurrency,
                #initialFunding: initialFunding,
                #amount: amount,
              },
            ),
            returnValue: _i18.Future<_i4.Result<_i12.CashRateInfo?>>.value(
                _FakeResult_2<_i12.CashRateInfo?>(
              this,
              Invocation.method(
                #getFundingRatesForFiatVirtualAccounts,
                [],
                {
                  #channelId: channelId,
                  #fiatCurrency: fiatCurrency,
                  #accountCurrency: accountCurrency,
                  #initialFunding: initialFunding,
                  #amount: amount,
                },
              ),
            )),
            returnValueForMissingStub:
                _i18.Future<_i4.Result<_i12.CashRateInfo?>>.value(
                    _FakeResult_2<_i12.CashRateInfo?>(
              this,
              Invocation.method(
                #getFundingRatesForFiatVirtualAccounts,
                [],
                {
                  #channelId: channelId,
                  #fiatCurrency: fiatCurrency,
                  #accountCurrency: accountCurrency,
                  #initialFunding: initialFunding,
                  #amount: amount,
                },
              ),
            )),
          ) as _i18.Future<_i4.Result<_i12.CashRateInfo?>>);

  @override
  _i18.Future<_i4.Result<_i12.CashRateInfo?>>
      getWithdrawalRatesForFiatVirtualAccounts({
    required String? channelId,
    required String? fiatCurrency,
    required String? accountCurrency,
    num? amount,
  }) =>
          (super.noSuchMethod(
            Invocation.method(
              #getWithdrawalRatesForFiatVirtualAccounts,
              [],
              {
                #channelId: channelId,
                #fiatCurrency: fiatCurrency,
                #accountCurrency: accountCurrency,
                #amount: amount,
              },
            ),
            returnValue: _i18.Future<_i4.Result<_i12.CashRateInfo?>>.value(
                _FakeResult_2<_i12.CashRateInfo?>(
              this,
              Invocation.method(
                #getWithdrawalRatesForFiatVirtualAccounts,
                [],
                {
                  #channelId: channelId,
                  #fiatCurrency: fiatCurrency,
                  #accountCurrency: accountCurrency,
                  #amount: amount,
                },
              ),
            )),
            returnValueForMissingStub:
                _i18.Future<_i4.Result<_i12.CashRateInfo?>>.value(
                    _FakeResult_2<_i12.CashRateInfo?>(
              this,
              Invocation.method(
                #getWithdrawalRatesForFiatVirtualAccounts,
                [],
                {
                  #channelId: channelId,
                  #fiatCurrency: fiatCurrency,
                  #accountCurrency: accountCurrency,
                  #amount: amount,
                },
              ),
            )),
          ) as _i18.Future<_i4.Result<_i12.CashRateInfo?>>);

  @override
  _i18.Future<_i4.Result<_i12.WithdrawalTransactionResponse?>>
      createCashWithdrawalTransaction(
              {required _i12.CashWithdrawalRequest? cashWithdrawalRequest}) =>
          (super.noSuchMethod(
            Invocation.method(
              #createCashWithdrawalTransaction,
              [],
              {#cashWithdrawalRequest: cashWithdrawalRequest},
            ),
            returnValue: _i18
                .Future<_i4.Result<_i12.WithdrawalTransactionResponse?>>.value(
                _FakeResult_2<_i12.WithdrawalTransactionResponse?>(
              this,
              Invocation.method(
                #createCashWithdrawalTransaction,
                [],
                {#cashWithdrawalRequest: cashWithdrawalRequest},
              ),
            )),
            returnValueForMissingStub: _i18
                .Future<_i4.Result<_i12.WithdrawalTransactionResponse?>>.value(
                _FakeResult_2<_i12.WithdrawalTransactionResponse?>(
              this,
              Invocation.method(
                #createCashWithdrawalTransaction,
                [],
                {#cashWithdrawalRequest: cashWithdrawalRequest},
              ),
            )),
          ) as _i18.Future<_i4.Result<_i12.WithdrawalTransactionResponse?>>);

  @override
  _i18.Future<_i4.Result<_i12.WithdrawalTransactionResponse?>>
      createCryptoWithdrawalTransaction(
              {required _i12.CryptoWithdrawalRequest?
                  cryptoWithdrawalRequest}) =>
          (super.noSuchMethod(
            Invocation.method(
              #createCryptoWithdrawalTransaction,
              [],
              {#cryptoWithdrawalRequest: cryptoWithdrawalRequest},
            ),
            returnValue: _i18
                .Future<_i4.Result<_i12.WithdrawalTransactionResponse?>>.value(
                _FakeResult_2<_i12.WithdrawalTransactionResponse?>(
              this,
              Invocation.method(
                #createCryptoWithdrawalTransaction,
                [],
                {#cryptoWithdrawalRequest: cryptoWithdrawalRequest},
              ),
            )),
            returnValueForMissingStub: _i18
                .Future<_i4.Result<_i12.WithdrawalTransactionResponse?>>.value(
                _FakeResult_2<_i12.WithdrawalTransactionResponse?>(
              this,
              Invocation.method(
                #createCryptoWithdrawalTransaction,
                [],
                {#cryptoWithdrawalRequest: cryptoWithdrawalRequest},
              ),
            )),
          ) as _i18.Future<_i4.Result<_i12.WithdrawalTransactionResponse?>>);

  @override
  _i18.Future<_i4.Result<_i12.FundingTransactionResponse?>>
      initiateFundingRequest({
    required String? accountId,
    required _i12.FundingRequest? fundingRequest,
  }) =>
          (super.noSuchMethod(
            Invocation.method(
              #initiateFundingRequest,
              [],
              {
                #accountId: accountId,
                #fundingRequest: fundingRequest,
              },
            ),
            returnValue:
                _i18.Future<_i4.Result<_i12.FundingTransactionResponse?>>.value(
                    _FakeResult_2<_i12.FundingTransactionResponse?>(
              this,
              Invocation.method(
                #initiateFundingRequest,
                [],
                {
                  #accountId: accountId,
                  #fundingRequest: fundingRequest,
                },
              ),
            )),
            returnValueForMissingStub:
                _i18.Future<_i4.Result<_i12.FundingTransactionResponse?>>.value(
                    _FakeResult_2<_i12.FundingTransactionResponse?>(
              this,
              Invocation.method(
                #initiateFundingRequest,
                [],
                {
                  #accountId: accountId,
                  #fundingRequest: fundingRequest,
                },
              ),
            )),
          ) as _i18.Future<_i4.Result<_i12.FundingTransactionResponse?>>);

  @override
  _i18.Future<_i4.Result<_i12.CryptoRateInfo?>> getWithdrawalCryptoRates({
    required String? accountCurrency,
    required String? tokenCode,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getWithdrawalCryptoRates,
          [],
          {
            #accountCurrency: accountCurrency,
            #tokenCode: tokenCode,
          },
        ),
        returnValue: _i18.Future<_i4.Result<_i12.CryptoRateInfo?>>.value(
            _FakeResult_2<_i12.CryptoRateInfo?>(
          this,
          Invocation.method(
            #getWithdrawalCryptoRates,
            [],
            {
              #accountCurrency: accountCurrency,
              #tokenCode: tokenCode,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i12.CryptoRateInfo?>>.value(
                _FakeResult_2<_i12.CryptoRateInfo?>(
          this,
          Invocation.method(
            #getWithdrawalCryptoRates,
            [],
            {
              #accountCurrency: accountCurrency,
              #tokenCode: tokenCode,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<_i12.CryptoRateInfo?>>);

  @override
  dynamic createVirtualAccountViaFunding({
    required String? currencyName,
    required _i47.CardFundingType? cardFundingType,
    required void Function(_i44.LocalVirtualAccount?)? onCreateAccountSuccess,
    required void Function(_i12.FundingTransactionResponse?)?
        onUpdateFundingSuccess,
    required _i12.FundingRequest? fundingRequest,
    String? packageId,
    required void Function(_i48.OnboardExceptions)? onFailure,
    String? accountId,
    String? label,
    String? color,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #createVirtualAccountViaFunding,
          [],
          {
            #currencyName: currencyName,
            #cardFundingType: cardFundingType,
            #onCreateAccountSuccess: onCreateAccountSuccess,
            #onUpdateFundingSuccess: onUpdateFundingSuccess,
            #fundingRequest: fundingRequest,
            #packageId: packageId,
            #onFailure: onFailure,
            #accountId: accountId,
            #label: label,
            #color: color,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i18.Future<_i4.Result<_i12.FundingTransactionResponse?>>
      replaceVirtualAccountInitialFunding({
    required String? accountId,
    required _i12.FundingRequest? updatedInitialFunding,
  }) =>
          (super.noSuchMethod(
            Invocation.method(
              #replaceVirtualAccountInitialFunding,
              [],
              {
                #accountId: accountId,
                #updatedInitialFunding: updatedInitialFunding,
              },
            ),
            returnValue:
                _i18.Future<_i4.Result<_i12.FundingTransactionResponse?>>.value(
                    _FakeResult_2<_i12.FundingTransactionResponse?>(
              this,
              Invocation.method(
                #replaceVirtualAccountInitialFunding,
                [],
                {
                  #accountId: accountId,
                  #updatedInitialFunding: updatedInitialFunding,
                },
              ),
            )),
            returnValueForMissingStub:
                _i18.Future<_i4.Result<_i12.FundingTransactionResponse?>>.value(
                    _FakeResult_2<_i12.FundingTransactionResponse?>(
              this,
              Invocation.method(
                #replaceVirtualAccountInitialFunding,
                [],
                {
                  #accountId: accountId,
                  #updatedInitialFunding: updatedInitialFunding,
                },
              ),
            )),
          ) as _i18.Future<_i4.Result<_i12.FundingTransactionResponse?>>);

  @override
  _i18.Future<_i4.Result<_i12.VirtualAccountTransactionsListResponse?>?>
      getVirtualAccountTransactions({
    required String? accountId,
    required int? page,
    int? size = 20,
  }) =>
          (super.noSuchMethod(
            Invocation.method(
              #getVirtualAccountTransactions,
              [],
              {
                #accountId: accountId,
                #page: page,
                #size: size,
              },
            ),
            returnValue: _i18.Future<
                _i4
                .Result<_i12.VirtualAccountTransactionsListResponse?>?>.value(),
            returnValueForMissingStub: _i18.Future<
                _i4
                .Result<_i12.VirtualAccountTransactionsListResponse?>?>.value(),
          ) as _i18.Future<
              _i4.Result<_i12.VirtualAccountTransactionsListResponse?>?>);

  @override
  _i18.Future<_i4.Result<_i49.LocalVirtualAccountTransaction?>>
      getVirtualAccountTransactionById({
    required String? accountId,
    required String? transactionId,
  }) =>
          (super.noSuchMethod(
            Invocation.method(
              #getVirtualAccountTransactionById,
              [],
              {
                #accountId: accountId,
                #transactionId: transactionId,
              },
            ),
            returnValue: _i18
                .Future<_i4.Result<_i49.LocalVirtualAccountTransaction?>>.value(
                _FakeResult_2<_i49.LocalVirtualAccountTransaction?>(
              this,
              Invocation.method(
                #getVirtualAccountTransactionById,
                [],
                {
                  #accountId: accountId,
                  #transactionId: transactionId,
                },
              ),
            )),
            returnValueForMissingStub: _i18
                .Future<_i4.Result<_i49.LocalVirtualAccountTransaction?>>.value(
                _FakeResult_2<_i49.LocalVirtualAccountTransaction?>(
              this,
              Invocation.method(
                #getVirtualAccountTransactionById,
                [],
                {
                  #accountId: accountId,
                  #transactionId: transactionId,
                },
              ),
            )),
          ) as _i18.Future<_i4.Result<_i49.LocalVirtualAccountTransaction?>>);

  @override
  _i18.Future<_i4.Result<_i12.StatusDto?>> verifyPaymentMethod({
    required String? channelId,
    required _i12.VirtualAccountsSvcPaymentMethodValidationRequest?
        paymentMethodValidationRequest,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #verifyPaymentMethod,
          [],
          {
            #channelId: channelId,
            #paymentMethodValidationRequest: paymentMethodValidationRequest,
          },
        ),
        returnValue: _i18.Future<_i4.Result<_i12.StatusDto?>>.value(
            _FakeResult_2<_i12.StatusDto?>(
          this,
          Invocation.method(
            #verifyPaymentMethod,
            [],
            {
              #channelId: channelId,
              #paymentMethodValidationRequest: paymentMethodValidationRequest,
            },
          ),
        )),
        returnValueForMissingStub:
            _i18.Future<_i4.Result<_i12.StatusDto?>>.value(
                _FakeResult_2<_i12.StatusDto?>(
          this,
          Invocation.method(
            #verifyPaymentMethod,
            [],
            {
              #channelId: channelId,
              #paymentMethodValidationRequest: paymentMethodValidationRequest,
            },
          ),
        )),
      ) as _i18.Future<_i4.Result<_i12.StatusDto?>>);

  @override
  void listenToReactiveValues(List<dynamic>? reactiveValues) =>
      super.noSuchMethod(
        Invocation.method(
          #listenToReactiveValues,
          [reactiveValues],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(void Function()? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
