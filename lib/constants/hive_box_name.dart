class HiveBoxName {
  static const String nativeCurrencyBoxName = 'nativeCurrency';
  static const String localTokensBoxName = 'localTokens';
  static const String localAddressTokenBalanceBoxName =
      'localAddressTokenBalanceBox';
  static const String supportedNetworkBoxName = 'supportedNetworks';
  static const String exchangeNetworkBoxName = 'exchangeNetworks';
  static const String activeNetworkBoxName = "activeNetwork";
  static const String walletTransactionsBoxName = 'walletTransactions';
  static const String tradingWalletTransactionsBoxName =
      'tradingWalletTransactions';
  static const String nestcoinRpcBoxName = 'nestcoinRpc';
  static const String tradingAccountsBoxName = 'tradingAccounts';
  static const String tradingBalancesBoxName = 'tradingBalances';
  static const String localRatesBoxName = 'localRates';
  static const String localConfigsBoxName = 'localConfigs';
  static const String assetConfigBoxName = 'assetConfigs';
  static const String fiatConfigBoxName = 'fiatConfigs';
  static const String walletConnectSessionStore = 'walletConnectSessionStore';
  static const String cardCountriesBox = 'cardCountriesBox';
  static const String cardFundingConfigBox = 'cardFundingConfigBox';
  static const String cardWithdrawalConfigBox = 'cardWithdrawalConfigBox';
  static const String wcv2PairingDate = 'wcv2PairingDate';
  static const String cardAccountBoxName = 'cardAccountBox';
  static const String virtualAccountBoxName = 'virtualAccountBox';
  static const String cardTransactionsBoxName = 'cardTransactions';
  static const String localCheckListBox = 'localCheckListBox';
  static const String topTokensBoxName = 'topTokensBox';
  static const String localWalletAddressBoxName = 'localWalletAddressBoxName';
  static const String walletBackupBoxName = 'walletBackupBoxName';
  static const String smileJobParamsBoxName = 'smileJobsParamBoxName';
  static const String referrerBoxName = 'referrerBoxName';
  static const String recentRailProviderBoxName = "recentRailProviderBoxName";
  static const String recentRailProviderTokenNetworkIdBoxName =
      'recentRailProviderTokenNetworkIdBoxName';
  static const String balanceHiddenBoxName = "balanceHiddenBoxName";
  static const String priceChangesBoxName = 'priceChangesBoxName';
  static const String localPortfolioBoxName = 'localPortfolioBoxName';
  static const String localCardWalletPackagesBox = 'localCardWalletPackagesBox';
  static const String dappHistoryBoxName = 'dappHistoryBoxName';
  static const String dappTabBoxName = 'dappTabsBoxName';
  static const String nftsBoxName = 'nftsBox';
  static const String cardSubscriptionNudgeBannerBoxName =
      "cardSubscriptionNudgeBannerBoxName";
  static const String userCountryFeatureBoxName = 'userCountryFeatureBoxName';
  static const String tradingWalletBalanceBoxName = "tradingBalanceBoxName";
}
