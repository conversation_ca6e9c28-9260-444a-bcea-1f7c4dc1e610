const kSmileJobParamsHiveTypeId = 120;
const kSmileJobTypeId = 121;
const kLocalRailProviderTypeId = 122;
const kLocalTokenBalanceTypeId = 123;
const kLocalAddressTokenBalanceTypeId = 124;
const kLocalTokenNetworkBalanceTypeId = 125;
const kLocalDataPointTypeId = 126;
const kLocalPortfolioPricesTypeId = 127;
const kLocalCardType = 129;
const kLocalCardWalletPackage = 128;

const kLocalBridgePermitResponseTypeId = 140;

//Trading
const kLocalAssetInfo = 219;
const kLocalMarketDataLink = 220;
const kLocalAssetMarketData = 221;
const kLocalTradingBalance = 222;