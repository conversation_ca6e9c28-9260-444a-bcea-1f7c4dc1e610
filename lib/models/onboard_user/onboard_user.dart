import 'package:hive_flutter/adapters.dart';
import 'package:nestcoinco_onboard_api_gateway/nestcoinco_onboard_api_gateway.dart';
import 'package:onboard_wallet/app/app.locator.dart';
import 'package:onboard_wallet/models/onboard_user/transaction_limit.dart';
import 'package:onboard_wallet/services/services.dart';

import 'onboard_profile.dart';

part 'onboard_user.g.dart';

const String onboardUsersBoxName = "onboardUsers";
const String currentOnboardUsersBoxName = "currentOnboardUsers";

@HiveType(typeId: 3)
class OnboardUser extends HiveObject {
  @HiveField(0)
  final String userId;
  @HiveField(1)
  final String email;
  @HiveField(2)
  final String? name;
  @HiveField(3)
  final String? phoneNumber;
  @HiveField(4)
  final String? fullName;
  @HiveField(5)
  final String? referralLink;
  @HiveField(6)
  final String? userType;
  @HiveField(7)
  final List<String>? roles;
  @HiveField(8)
  final String? kycStatus;
  @HiveField(9)
  final List<String>? comments;
  @HiveField(10)
  final Map<dynamic, dynamic>? includes;
  @HiveField(11)
  final List<String>? supportedCurrencies;
  @HiveField(12)
  final bool? availability;
  @HiveField(13)
  final String? country;
  @HiveField(14)
  final OnboardProfile? profile;
  @HiveField(15)
  final TransactionLimit? transactionLimit;
  @HiveField(16)
  final bool? isMerchant;
  @HiveField(17)
  final bool? phoneNumberVerified;
  @HiveField(18)
  final bool? isCustomer;
  @HiveField(19)
  final bool? isOpnMerchant;
  @HiveField(20)
  final bool? isMerchantNetwork;
  @HiveField(21)
  final String? verifiedAddress;
  @HiveField(22)
  final num? kycRetryCount;
  @HiveField(23)
  final num? kycRetryLimit;

  bool get hasRegisteredBackUp => locator<CloudBackupService>().hasBackUp;

  bool get hasVerifiedWalletAddress => verifiedAddress?.isNotEmpty == true;

  bool get isProfileDetailsComplete {
    if (profile == null) return false;
    final name = profile!.fullname;
    final dob = profile!.dob;
    final isPhoneVerified = phoneNumberVerified;
    return name != null && dob != null && isPhoneVerified == true;
  }

  OnboardUser({
    required this.userId,
    required this.email,
    this.name,
    this.phoneNumber,
    this.fullName,
    this.referralLink,
    required this.userType,
    this.roles,
    this.kycStatus,
    this.comments,
    this.includes,
    this.supportedCurrencies,
    this.availability,
    this.country,
    this.profile,
    this.transactionLimit,
    this.isMerchant,
    this.phoneNumberVerified,
    this.isCustomer,
    this.isOpnMerchant,
    this.isMerchantNetwork,
    this.verifiedAddress,
    this.kycRetryCount,
    this.kycRetryLimit,
  });

  factory OnboardUser.fromUsersSvcUserDataDto(
      UsersSvcUserDataDto usersSvcUserDataDto) {
    return OnboardUser(
      userId: usersSvcUserDataDto.userId,
      email: usersSvcUserDataDto.email,
      name: usersSvcUserDataDto.name,
      phoneNumber: usersSvcUserDataDto.phoneNumber,
      fullName: usersSvcUserDataDto.fullName,
      referralLink: usersSvcUserDataDto.referralLink,
      userType: usersSvcUserDataDto.userType?.name,
      roles: usersSvcUserDataDto.roles?.map((p0) => p0.name).toList(),
      kycStatus: usersSvcUserDataDto.kycStatus,
      comments: usersSvcUserDataDto.comments?.map((e) => e.comment ?? "").toList(),
      includes: usersSvcUserDataDto.includes?.asMap,
      supportedCurrencies: usersSvcUserDataDto.supportedCurrencies?.toList(),
      availability: usersSvcUserDataDto.availability,
      country:
          usersSvcUserDataDto.country ?? usersSvcUserDataDto.profile?.country,
      profile: OnboardProfile.fromProfileDataDto(usersSvcUserDataDto.profile),
      transactionLimit: TransactionLimit.fromUserTransactionLimit(
          usersSvcUserDataDto.transactionLimit),
      isMerchant: usersSvcUserDataDto.isMerchant,
      phoneNumberVerified: usersSvcUserDataDto.phoneNumberVerified,
      isCustomer: usersSvcUserDataDto.isCustomer,
      isMerchantNetwork: usersSvcUserDataDto.isMerchantNetwork,
      isOpnMerchant: usersSvcUserDataDto.isOpnMerchant,
      verifiedAddress: usersSvcUserDataDto.verifiedAddress,
      kycRetryCount: usersSvcUserDataDto.kycRetryCount,
      kycRetryLimit: usersSvcUserDataDto.kycRetryLimit,
    );
  }
}
