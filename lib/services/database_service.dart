import 'package:hive_flutter/hive_flutter.dart';
import 'package:onboard_wallet/constants/constants.dart';
import 'package:onboard_wallet/haven/models/balance/local_trading_balance.dart';
import 'package:onboard_wallet/models/card/initial_funding/local_bridge_permit_response.dart';
import 'package:onboard_wallet/models/card/local_card_account/local_card_usage_terms_tx_limit.dart';
import 'package:onboard_wallet/models/card/local_card_transaction_config/local_card_transaction_config.dart';
import 'package:onboard_wallet/models/card/local_card_transaction_type/local_card_transaction_type.dart';
import 'package:onboard_wallet/models/card/local_card_wallet_package/local_card_type.dart';
import 'package:onboard_wallet/models/card/local_card_wallet_package/local_card_wallet_package.dart';
import 'package:onboard_wallet/models/card/local_transaction_asset_type/local_transaction_asset_type.dart';
import 'package:onboard_wallet/models/card/local_wallet_cards_payment_channel_type/local_wallet_cards_payment_channel_type.dart';
import 'package:onboard_wallet/models/country.dart';
import 'package:onboard_wallet/models/data_point/local_data_point.dart';
import 'package:onboard_wallet/models/data_point/local_portfolio_prices.dart';
import 'package:onboard_wallet/models/kyc/smile_job_params.dart';
import 'package:onboard_wallet/models/kyc/smile_job_type.dart';
import 'package:onboard_wallet/models/local/checklist_dto/local_checklist_dto.dart';
import 'package:onboard_wallet/models/local/fiat_config/local_fiat_config.dart';
import 'package:onboard_wallet/models/local/transaction/card_transaction/local_asset_amount.dart';
import 'package:onboard_wallet/models/local/transaction/local_transaction_type.dart';
import 'package:onboard_wallet/models/local/transaction/transaction_direction.dart';
import 'package:onboard_wallet/models/models.dart';
import 'package:onboard_wallet/models/token/local_token_market_data.dart';
import 'package:onboard_wallet/models/token/local_token_network.dart';
import 'package:onboard_wallet/models/token/wallet_type.dart';
import 'package:onboard_wallet/models/virtual_account/local_virtual_account/local_account_provider_details.dart';
import 'package:onboard_wallet/models/virtual_account/local_virtual_account/local_fiat_payment_rail.dart';
import 'package:onboard_wallet/models/virtual_account/local_virtual_account/local_virtual_account.dart';
import 'package:onboard_wallet/models/virtual_account/local_virtual_account/local_virtual_account_balance_info.dart';
import 'package:onboard_wallet/models/virtual_account/local_virtual_account/local_virtual_account_details.dart';
import 'package:onboard_wallet/models/virtual_account/local_virtual_account/local_virtual_account_status.dart';
import 'package:onboard_wallet/models/virtual_account/local_virtual_account/local_virtual_account_terms_time_range.dart';
import 'package:onboard_wallet/models/virtual_account/local_virtual_account/local_virtual_account_terms_time_unit.dart';
import 'package:onboard_wallet/models/virtual_account/local_virtual_account/local_virtual_account_usage_terms.dart';
import 'package:onboard_wallet/models/virtual_account/local_virtual_account/local_virtual_accounts_svc_fiat_currency.dart';
import 'package:onboard_wallet/models/wallet_address/local_wallet_address.dart';
import 'package:onboard_wallet/models/wallet_address/local_wallet_address_type.dart';
import 'package:onboard_wallet/services/services.dart';

import '../app/index.dart';
import '../models/card/local_card_account/index.dart';
import '../models/virtual_account/local_virtual_account/local_virtual_account_type.dart';
import '../models/wallet_back_up/drive_type.dart' as drive_type;

class DatabaseService {
  Future initializeDb() async {
    await Hive.initFlutter();
    _registerAdapters();

    await Hive.openBox<NestcoinRPC>(HiveBoxName.nestcoinRpcBoxName);
    await Hive.openBox<LocalTransaction>(HiveBoxName.walletTransactionsBoxName);
    await Hive.openBox<LocalTransaction>(
        HiveBoxName.tradingWalletTransactionsBoxName);
    await Hive.openBox<LocalChecklistDto>(HiveBoxName.localCheckListBox);
    await Hive.openBox<LocalCountry>(HiveBoxName.cardCountriesBox);
    await Hive.openBox<LocalNetwork>(HiveBoxName.exchangeNetworkBoxName);
    await Hive.openBox<LocalNativeCurrency>(HiveBoxName.nativeCurrencyBoxName);
    await Hive.openBox<LocalNetwork>(HiveBoxName.supportedNetworkBoxName);
    await Hive.openBox<LocalNetwork>(HiveBoxName.activeNetworkBoxName);
    await Hive.openBox<OnboardUser>(currentOnboardUsersBoxName);
    await Hive.openBox<LocalToken>(HiveBoxName.localTokensBoxName);
    await Hive.openBox<LocalAddressTokenBalance>(
        HiveBoxName.localAddressTokenBalanceBoxName);
    await Hive.openBox<LocalAddressTokenBalance>(
        HiveBoxName.localPortfolioBoxName);
    await Hive.openBox<LocalCardWalletPackage>(
        HiveBoxName.localCardWalletPackagesBox);

    await Hive.openBox<LocalPortfolioPrices>(HiveBoxName.priceChangesBoxName);
    await Hive.openBox<LocalToken>(HiveBoxName.tradingBalancesBoxName);
    await Hive.openBox<TradingAccount>(HiveBoxName.tradingAccountsBoxName);
    await Hive.openBox<LocalRate>(HiveBoxName.localRatesBoxName);
    await Hive.openBox<LocalConfig>(HiveBoxName.localConfigsBoxName);
    await Hive.openBox<LocalCardTransactionConfig>(
        HiveBoxName.cardFundingConfigBox);
    await Hive.openBox<LocalCardTransactionConfig>(
        HiveBoxName.cardWithdrawalConfigBox);
    await Hive.openBox<Map<dynamic, dynamic>>(
        HiveBoxName.walletConnectSessionStore);
    await Hive.openBox<String>(HiveBoxName.wcv2PairingDate);
    await Hive.openBox<LocalCardAccount>(HiveBoxName.cardAccountBoxName);
    await Hive.openBox<LocalCardTransaction>(
        HiveBoxName.cardTransactionsBoxName);
    await Hive.openBox<LocalToken>(HiveBoxName.topTokensBoxName);
    await Hive.openBox<LocalWalletAddress>(
        HiveBoxName.localWalletAddressBoxName);
    await Hive.openBox<WalletBackUp>(HiveBoxName.walletBackupBoxName);
    await Hive.openBox<SmileJobParams>(HiveBoxName.smileJobParamsBoxName);
    await Hive.openBox<LocalReferrer>(HiveBoxName.referrerBoxName);
    await Hive.openBox<String>(HiveBoxName.recentRailProviderBoxName);
    await Hive.openBox<String>(
        HiveBoxName.recentRailProviderTokenNetworkIdBoxName);
    await Hive.openBox<bool>(HiveBoxName.balanceHiddenBoxName);
    await Hive.openBox<bool>(HiveBoxName.cardSubscriptionNudgeBannerBoxName);
    await Hive.openBox<LocalPortfolioPrices>(HiveBoxName.priceChangesBoxName);

    await Hive.openBox<Dapp>(HiveBoxName.dappHistoryBoxName);
    await Hive.openBox<Dapp>(HiveBoxName.dappTabBoxName);
    await Hive.openBox<LocalNFTBrief>(HiveBoxName.nftsBoxName);
    await Hive.openBox<LocalVirtualAccount>(HiveBoxName.virtualAccountBoxName);
    await Hive.openBox<LocalCountry>(HiveBoxName.userCountryFeatureBoxName);

    await Hive.openBox<LocalTradingBalance>(HiveBoxName.tradingWalletBalanceBoxName);
  }

  Future saveCurrentUser(OnboardUser user) async {
    try {
      Box<OnboardUser> currentUserBox = Hive.box(currentOnboardUsersBoxName);

      /// This box should only contain one user
      await currentUserBox.clear();
      currentUserBox.add(user);
    } catch (_) {}
  }

  Future clearDb() async {
    Box<OnboardUser> currentUserBox = Hive.box(currentOnboardUsersBoxName);
    Box<LocalToken> tokenBox = Hive.box(HiveBoxName.localTokensBoxName);
    Box<String> dateBox = Hive.box(HiveBoxName.wcv2PairingDate);
    Box<LocalCardAccount> cardAccountBox =
        Hive.box<LocalCardAccount>(HiveBoxName.cardAccountBoxName);
    Box<LocalChecklistDto> checkListBox =
        Hive.box(HiveBoxName.localCheckListBox);
    Box<LocalToken> topTokensBox = Hive.box(HiveBoxName.topTokensBoxName);
    Box<LocalWalletAddress> localWalletAddress =
        Hive.box(HiveBoxName.localWalletAddressBoxName);
    Box<SmileJobParams> smileJobParamsBox =
        Hive.box(HiveBoxName.smileJobParamsBoxName);
    Box<LocalReferrer> referrer = Hive.box(HiveBoxName.referrerBoxName);
    Box<String> recentRailProviderBox =
        Hive.box(HiveBoxName.recentRailProviderBoxName);
    Box<String> recentRailProviderTokenNetworkIdBox =
        Hive.box(HiveBoxName.recentRailProviderTokenNetworkIdBoxName);
    Box<bool> balanceHiddenBox = Hive.box(HiveBoxName.balanceHiddenBoxName);
    Box<bool> cardSubscriptionBannerBox =
        Hive.box(HiveBoxName.cardSubscriptionNudgeBannerBoxName);

    Box<LocalAddressTokenBalance> addressTokenBox =
        Hive.box<LocalAddressTokenBalance>(
            HiveBoxName.localAddressTokenBalanceBoxName);
    Box<LocalPortfolioPrices> priceChangesBox =
        Hive.box<LocalPortfolioPrices>(HiveBoxName.priceChangesBoxName);

    Box<LocalAddressTokenBalance> myPortfolioBox =
        Hive.box<LocalAddressTokenBalance>(HiveBoxName.localPortfolioBoxName);

    Box<Dapp> dappHistoryBox = Hive.box<Dapp>(HiveBoxName.dappHistoryBoxName);
    Box<Dapp> dappTabBox = Hive.box<Dapp>(HiveBoxName.dappTabBoxName);
    Box<LocalNFTBrief> nftsBox =
        Hive.box<LocalNFTBrief>(HiveBoxName.nftsBoxName);
    Box<LocalVirtualAccount> virtualAccountBox =
        Hive.box<LocalVirtualAccount>(HiveBoxName.virtualAccountBoxName);

    Box<LocalCountry> countryBox =
        Hive.box<LocalCountry>(HiveBoxName.userCountryFeatureBoxName);

    Box<LocalTradingBalance> tradingBalanceBox =
        Hive.box<LocalTradingBalance>(HiveBoxName.tradingWalletBalanceBoxName);

    await localWalletAddress.clear();
    await dateBox.clear();
    await tokenBox.clear();
    await cardAccountBox.clear();
    await currentUserBox.clear();
    await checkListBox.clear();
    await topTokensBox.clear();
    await locator<NetworkDatabaseService>().nukeDb();
    await locator<NestcoinRpcDatabaseService>().nukeDb();
    await locator<TransactionDbService>().nukeDb();
    await locator<PartnerDatabaseService>().nukeDb();
    await locator<RatesService>().nukeDb();
    await locator<ConfigService>().nukeDb();
    await locator<CloudBackupService>().clearDb();
    await smileJobParamsBox.clear();
    await referrer.clear();
    await recentRailProviderBox.clear();
    await addressTokenBox.clear();
    await balanceHiddenBox.clear();
    await priceChangesBox.clear();
    await recentRailProviderTokenNetworkIdBox.clear();
    await myPortfolioBox.clear();
    await dappHistoryBox.clear();
    await dappTabBox.clear();
    await nftsBox.clear();
    await cardSubscriptionBannerBox.clear();
    await virtualAccountBox.clear();
    await countryBox.clear();
    await tradingBalanceBox.clear();
  }

  OnboardUser? getCurrentUser() {
    Box<OnboardUser> currentUserBox = Hive.box(currentOnboardUsersBoxName);
    return currentUserBox.values.isNotEmpty
        ? currentUserBox.values.first
        : null;
  }

  void _registerAdapters() {
    Hive.registerAdapter<TransactionLimit>(TransactionLimitAdapter());
    Hive.registerAdapter<TradeHistoryDetails>(TradeHistoryDetailsAdapter());
    Hive.registerAdapter<OnboardProfile>(OnboardProfileAdapter());
    Hive.registerAdapter<OnboardUser>(OnboardUserAdapter());
    Hive.registerAdapter<LocalCoin>(LocalCoinAdapter());
    Hive.registerAdapter<LocalNetworkExplorer>(LocalNetworkExplorerAdapter());
    Hive.registerAdapter<LocalTokenInfoLink>(LocalTokenInfoLinkAdapter());
    Hive.registerAdapter<LocalTokenNetwork>(LocalTokenNetworkAdapter());
    Hive.registerAdapter<LocalTokenStatus>(LocalTokenStatusAdapter());
    Hive.registerAdapter<LocalTokenType>(LocalTokenTypeAdapter());
    Hive.registerAdapter<Rate>(RateAdapter());
    Hive.registerAdapter<LocalToken>(LocalTokenAdapter());
    Hive.registerAdapter<LocalNativeCurrency>(LocalNativeCurrencyAdapter());
    Hive.registerAdapter<LocalNetwork>(LocalNetworkAdapter());
    Hive.registerAdapter<LocalDecodedCallData>(LocalDecodedCallDataAdapter());
    Hive.registerAdapter<LocalCallDataParam>(LocalCallDataParamAdapter());
    Hive.registerAdapter<LocalTransaction>(LocalTransactionAdapter());
    Hive.registerAdapter<TransactionDirection>(TransactionDirectionAdapter());
    Hive.registerAdapter<LocalTransactionStatus>(
        LocalTransactionStatusAdapter());
    Hive.registerAdapter<LocalTransactionType>(LocalTransactionTypeAdapter());
    Hive.registerAdapter<LocalTransfer>(LocalTransferAdapter());
    Hive.registerAdapter<WalletType>(WalletTypeAdapter());
    Hive.registerAdapter<NestcoinRPC>(NestcoinRPCAdapter());
    Hive.registerAdapter<TradingAccount>(TradingAccountAdapter());
    Hive.registerAdapter<LocalRate>(LocalRateAdapter());
    Hive.registerAdapter<LocalTradeConfig>(LocalTradeConfigAdapter());
    Hive.registerAdapter<LocalCryptoAsset>(LocalCryptoAssetAdapter());
    Hive.registerAdapter<LocalAssetToken>(LocalAssetTokenAdapter());
    Hive.registerAdapter<LocalConfig>(LocalConfigAdapter());
    Hive.registerAdapter<LocalCountry>(LocalCountryAdapter());
    Hive.registerAdapter<LocalPaymentMethod>(LocalPaymentMethodAdapter());

    Hive.registerAdapter<LocalAddressTokenBalance>(
        LocalAddressTokenBalanceAdapter());
    Hive.registerAdapter<LocalTokenBalance>(LocalTokenBalanceAdapter());
    Hive.registerAdapter<LocalTokenNetworkBalance>(
        LocalTokenNetworkBalanceAdapter());

    Hive.registerAdapter<LocalCardsTransactionType>(
        LocalCardsTransactionTypeAdapter());
    Hive.registerAdapter<LocalTransactionAssetType>(
        LocalTransactionAssetTypeAdapter());
    Hive.registerAdapter<LocalWalletCardsSvcPaymentChannelType>(
        LocalWalletCardsSvcPaymentChannelTypeAdapter());
    Hive.registerAdapter<LocalFiatConfig>(LocalFiatConfigAdapter());
    Hive.registerAdapter<LocalTokenMarketData>(LocalTokenMarketDataAdapter());
    Hive.registerAdapter<LocalWalletAddress>(LocalWalletAddressAdapter());
    Hive.registerAdapter<LocalWalletAddressType>(
        LocalWalletAddressTypeAdapter());
    Hive.registerAdapter<LocalDataPoint>(LocalDataPointAdapter());
    Hive.registerAdapter<LocalPortfolioPrices>(LocalPortfolioPricesAdapter());
    Hive.registerAdapter<LocalWithdrawalTransaction>(
        LocalWithdrawalTransactionAdapter());

    /// Card
    Hive.registerAdapter<LocalTxChannel>(LocalTxChannelAdapter());
    Hive.registerAdapter<LocalCardTransactionConfig>(
        LocalCardTransactionConfigAdapter());
    Hive.registerAdapter<LocalRecurrenceFrequency>(
        LocalRecurrenceFrequencyAdapter());
    Hive.registerAdapter<LocalFeeType>(LocalFeeTypeAdapter());
    Hive.registerAdapter<LocalFeeAmountType>(LocalFeeAmountTypeAdapter());
    Hive.registerAdapter<LocalCardUsageTerms>(LocalCardUsageTermsAdapter());
    Hive.registerAdapter<LocalCardUsageFee>(LocalCardUsageFeeAdapter());
    Hive.registerAdapter<LocalCardStatus>(LocalCardStatusAdapter());
    Hive.registerAdapter<LocalCardHolderType>(LocalCardHolderTypeAdapter());
    Hive.registerAdapter<LocalCardBrand>(LocalCardBrandAdapter());
    Hive.registerAdapter<LocalCardType>(LocalCardTypeAdapter());
    Hive.registerAdapter<LocalCardWalletPackage>(
        LocalCardWalletPackageAdapter());
    Hive.registerAdapter<LocalCardBillingAddress>(
        LocalCardBillingAddressAdapter());
    Hive.registerAdapter<LocalCardAccountStatus>(
        LocalCardAccountStatusAdapter());
    Hive.registerAdapter<LocalCard>(LocalCardAdapter());
    Hive.registerAdapter<LocalCardUsageTermsTxLimit>(
        LocalCardUsageTermsTxLimitAdapter());
    Hive.registerAdapter<LocalFundingAddress>(LocalFundingAddressAdapter());
    Hive.registerAdapter<LocalInitialFunding>(LocalInitialFundingAdapter());
    Hive.registerAdapter<LocalBridgePermitResponse>(
        LocalBridgePermitResponseAdapter());
    Hive.registerAdapter<LocalCardAccount>(LocalCardAccountAdapter());
    Hive.registerAdapter<LocalAssetAmount>(LocalAssetAmountAdapter());
    Hive.registerAdapter<LocalCardTransactionType>(
        LocalCardTransactionTypeAdapter());
    Hive.registerAdapter<LocalCardTransaction>(LocalCardTransactionAdapter());

    ///backups,checklist
    Hive.registerAdapter<LocalChecklistDto>(LocalChecklistDtoAdapter());
    Hive.registerAdapter<drive_type.DriveType>(drive_type.DriveTypeAdapter());
    Hive.registerAdapter<WalletBackUp>(WalletBackUpAdapter());
    Hive.registerAdapter<WalletBackup>(WalletBackupAdapter());
    Hive.registerAdapter<SecondaryAuth>(SecondaryAuthAdapter());
    Hive.registerAdapter<SmileJobParams>(SmileJobParamsAdapter());
    Hive.registerAdapter<SmileJobType>(SmileJobTypeAdapter());

    /// Referrer
    Hive.registerAdapter<LocalReferrer>(LocalReferrerAdapter());
    Hive.registerAdapter<Dapp>(DappAdapter());
    Hive.registerAdapter<HubRouting>(HubRoutingAdapter());
    Hive.registerAdapter<LocalNFTAttribute>(LocalNFTAttributeAdapter());
    Hive.registerAdapter<LocalNFTData>(LocalNFTDataAdapter());
    Hive.registerAdapter<LocalNFTBrief>(LocalNFTBriefAdapter());

    ///VirtualAccount
    Hive.registerAdapter<LocalVirtualAccount>(LocalVirtualAccountAdapter());
    Hive.registerAdapter<LocalVirtualAccountDetails>(
        LocalVirtualAccountDetailsAdapter());
    Hive.registerAdapter<LocalVirtualAccountUsageTerms>(
        LocalVirtualAccountUsageTermsAdapter());
    Hive.registerAdapter<LocalVirtualAccountType>(
        LocalVirtualAccountTypeAdapter());
    Hive.registerAdapter<LocalVirtualAccountStatus>(
        LocalVirtualAccountStatusAdapter());
    Hive.registerAdapter<LocalVirtualAccountsSvcFiatCurrency>(
        LocalVirtualAccountsSvcFiatCurrencyAdapter());
    Hive.registerAdapter<LocalFiatPaymentRail>(LocalFiatPaymentRailAdapter());
    Hive.registerAdapter<LocalAccountProviderDetails>(
        LocalAccountProviderDetailsAdapter());
    Hive.registerAdapter<LocalVirtualAccountBalanceInfo>(
        LocalVirtualAccountBalanceInfoAdapter());
    Hive.registerAdapter<LocalVirtualAccountTermsTimeRange>(
        LocalVirtualAccountTermsTimeRangeAdapter());
    Hive.registerAdapter<LocalVirtualAccountTermsTimeUnit>(
        LocalVirtualAccountTermsTimeUnitAdapter());

    //Trading
    Hive.registerAdapter<LocalTradingBalance>(LocalTradingBalanceAdapter());
  }
}
