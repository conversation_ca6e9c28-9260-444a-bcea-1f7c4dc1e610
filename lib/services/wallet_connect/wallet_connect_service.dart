import 'dart:async';
import 'dart:typed_data';

import 'package:collection/collection.dart';
import 'package:eth_sig_util/eth_sig_util.dart' as eth_sig_util;
import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:onboard_wallet/app/index.dart';
import 'package:onboard_wallet/constants/constants.dart';
import 'package:onboard_wallet/flavors.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/manager/manager.dart';
import 'package:onboard_wallet/models/wc/ethereum_transaction.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:onboard_wallet/ui/views/sign_transaction/sign_transaction_view.dart';
import 'package:onboard_wallet/ui/views/web_sign_message/web_sign_message_view.dart';
import 'package:onboard_wallet/ui/widgets/wc/wc_connection_request/wc_session_auth_Request_widget.dart';
import 'package:reown_walletkit/reown_walletkit.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';
import 'package:web3dart/crypto.dart';
import '../../models/models.dart';
import '../../ui/widgets/widgets.dart';

class WalletConnectService with ListenableServiceMixin {
  static const namespace = 'eip155';
  static const pSign = 'personal_sign';
  static const eSign = 'eth_sign';
  static const eSignTransaction = 'eth_signTransaction';
  static const eSignTypedData = 'eth_signTypedData';
  static const eSignTypedDataV3 = 'eth_signTypedData_v3';
  static const eSignTypedDataV4 = 'eth_signTypedData_v4';
  static const eSendTransaction = 'eth_sendTransaction';
  static const walletSwitchEthereumChain = 'wallet_switchEthereumChain';

  static const walletConnectPeerDescription =
      'The simplest and most secure wallet to manage your assets, with no middlemen.';
  static const walletConnectPeerIcon =
      "https://firebasestorage.googleapis.com/v0/b/onboard-wallet.appspot.com/o/onboardLogo.png?alt=media&token=a0aad8d7-fd45-4553-8173-42e0776faca2";
  final _toastManager = locator<ToastManager>();
  final _analyticsService = locator<AnalyticsService>();
  final _navigationService = locator<NavigationService>();
  Timer? _connectionCountDownTimer;
  Duration timerDuration = const Duration(seconds: 30);
  final WalletService walletService = locator<WalletService>();
  bool isInitialized = false;

  // v2
  ReownWalletKit? walletKit;
  final ReactiveList<PairingInfo> _reactivePairing = ReactiveList();
  List<SessionData> sessions = [];
  List<String> getEvents = [
    'chainChanged',
    'accountsChanged',
    'message',
    'disconnect',
    'connect'
  ];

  List<PairingInfo> get connections => _reactivePairing.toList();

  WalletConnectService() {
    listenToReactiveValues([_reactivePairing]);
  }

  Future<void> create() async {
    if (walletKit != null) return;
    try {
      walletKit = ReownWalletKit(
        core: ReownCore(
          projectId: F.walletConnectProjectId,
        ),
        metadata: PairingMetadata(
          name: F.name,
          description: WalletConnectService.walletConnectPeerDescription,
          url: 'https://onboard.xyz',
          icons: [WalletConnectService.walletConnectPeerIcon],
          redirect: const Redirect(
              native: "onboardwallet://",
              universal: "https://wallet.onboard.xyz"),
        ),
      );
      // Setup our listeners
      walletKit!.core.pairing.onPairingInvalid.subscribe(_onPairingInvalid);
      walletKit!.core.pairing.onPairingCreate.subscribe(_onPairingCreate);
      walletKit!.core.pairing.onPairingDelete.subscribe(_onPairingDelete);
      walletKit!.core.pairing.onPairingExpire.subscribe(_onPairingDelete);
      walletKit!.pairings.onSync.subscribe(_onPairingsSync);
      walletKit!.onSessionDelete.subscribe((args) {
        getLogger("onSessionDelete className").i(args);
        deleteSession(args.topic);
      });
      walletKit!.onSessionExpire.subscribe((args) {
        deleteSession(args.topic);
      });
      walletKit!.onSessionAuthRequest.subscribe(_onSessionAuthRequest);
      walletKit!.onSessionRequest.subscribe(_onSessionRequest);
      walletKit!.onSessionProposal.subscribe(_onSessionProposal);
      walletKit!.onSessionProposalError.subscribe(_onSessionProposalError);
      walletKit!.onSessionConnect.subscribe(_onSessionConnect);
      walletKit!.core.relayClient.onRelayClientDisconnect.subscribe((args) {
        walletKit?.core.relayClient.connect();
      });
      await walletKit!.init();
      await clearExpiredOrInactivePairing();
    } catch (e) {
      getLogger(toString()).e("CREATE $e");
    }
  }

  void _startTimer() {
    timerDuration = const Duration(seconds: 30);
    cancelConnectionCountDown();
    _connectionCountDownTimer = Timer(
      const Duration(seconds: 30),
      () {
        _analyticsService.logEvent(
            eventName: AnalyticsEvent.walletConnectTimerElapse);
        _toastManager.showErrorToast(
          text: S.current.connectionFailedRefreshDapp,
        );
        cancelConnectionCountDown();
      },
    );
  }

  void cancelConnectionCountDown() {
    _connectionCountDownTimer?.cancel();
  }

  Future<void> connectWithUri(String uriString) async {
    try {
      final Uri uriData = Uri.parse(uriString);
      await walletKit!.pair(
        uri: uriData,
      );
      _startTimer();
    } catch (e) {
      getLogger(toString()).e(e);
      _toastManager.showErrorToast(
        text: e.toString(),
      );
    }
  }

  bool isValidWalletConnectUri(String uriString) {
    try {
      final URIParseResult uriParseResult =
          ReownCoreUtils.parseUri(Uri.parse(uriString));
      return uriParseResult.version == URIVersion.v2;
    } catch (_) {
      return false;
    }
  }

  Future disconnectPair(PairingInfo pairingInfo) async {
    try {
      await _disconnectTopic(pairingInfo.topic);
      updateSync();
    } catch (e) {
      getLogger(toString()).e(e);
    }
  }

  Future _disconnectTopic(String topic) async {
    try {
      final sessions =
          walletKit!.getSessionsForPairing(pairingTopic: topic).values.toList();
      walletKit!.core.pairing.disconnect(topic: topic);
      for (var session in sessions) {
        await walletKit?.disconnectSession(
          topic: session.topic,
          reason: const ReownCoreError(
            code: 6000,
            message: 'User disconnected session',
          ).toSignError(),
        );
      }
    } on ReownCoreError catch (e) {
      getLogger("${toString()} WalletConnectError").i(e);
    } catch (e) {
      getLogger(toString()).e(e);
    }
  }

  Future logout() async {
    _reactivePairing.clear();
    final allPairs = walletKit?.pairings.getAll() ?? [];
    for (var pair in allPairs) {
      _disconnectTopic(pair.topic);
    }
  }

  _onPairingInvalid(PairingInvalidEvent? event) {
    getLogger(toString()).d('Pairing Invalid Event: $event');
  }

  _onPairingCreate(PairingEvent? event) {
    getLogger(toString()).d('Pairing Create Event: $event');
  }

  _onPairingsSync(StoreSyncEvent? storeSyncEvent) {
    if (storeSyncEvent != null) {
      updateSync();
    }
  }

  Future _onSessionAuthRequest(SessionAuthRequest? args) async {
    if (args != null) {
      _toastManager.dismissToast();
      cancelConnectionCountDown();
      final SessionAuthPayload authPayload = args.authPayload;
      final networks = locator<NetworkDatabaseService>().getAllNetworks();

      final supportedChains = networks.map((e) => e.namespace);
      final supportedMethods = SupportedEVMMethods.values.map((e) => e.name);
      final newAuthPayload = AuthSignature.populateAuthPayload(
        authPayload: authPayload,
        chains: supportedChains.toList(),
        methods: supportedMethods.toList(),
      );
      final cacaoRequestPayload = CacaoRequestPayload.fromSessionAuthPayload(
        newAuthPayload,
      );
      final List<Map<String, dynamic>> formattedMessages = [];
      for (var chain in newAuthPayload.chains) {
        final iss = 'did:pkh:$chain:${walletService.getWalletAddress}';
        final message = walletKit!.formatAuthMessage(
          iss: iss,
          cacaoPayload: cacaoRequestPayload,
        );
        formattedMessages.add({iss: message});
      }

      BuildContext? context = StackedService.navigatorKey?.currentContext;
      final Widget w = WCSessionAuthRequestWidget(
        child: WCConnectionRequestWidget(
          sessionAuthPayload: newAuthPayload,
          verifyContext: args.verifyContext,
          metadata: args.requester,
        ),
        onSignOne: () {
          _signOneOrAll(
              isSignOne: true,
              formattedMessages: formattedMessages,
              cacaoRequestPayload: cacaoRequestPayload,
              args: args);
        },
        onSignAll: () {
          _signOneOrAll(
              isSignOne: true,
              formattedMessages: formattedMessages,
              cacaoRequestPayload: cacaoRequestPayload,
              args: args);
        },
        onCancel: () async {
          await walletKit!.rejectSessionAuthenticate(
            id: args.id,
            reason: Errors.getSdkError(Errors.USER_REJECTED_AUTH).toSignError(),
          );
        },
      );

      ExternalCloseSheet.showModal(context!,
          child: SizedBox(
              height: MediaQuery.of(context).size.height * 0.80, child: w));
    }
  }

  Future _signOneOrAll({
    required bool isSignOne,
    required List<Map<String, dynamic>> formattedMessages,
    required CacaoRequestPayload cacaoRequestPayload,
    required SessionAuthRequest args,
  }) async {
    final privateKey = await walletService.getPrivateKey();
    final credentials = EthPrivateKey.fromHex(privateKey!);
    //
    final messageToSign = formattedMessages.length;
    final count = isSignOne ? 1 : messageToSign;
    //
    final List<Cacao> cacaos = [];
    for (var i = 0; i < count; i++) {
      final iss = formattedMessages[i].keys.first;
      final message = formattedMessages[i].values.first;
      final signature = credentials.signPersonalMessageToUint8List(
        Uint8List.fromList(message.codeUnits),
      );
      final hexSignature = bytesToHex(signature, include0x: true);
      cacaos.add(
        AuthSignature.buildAuthObject(
          requestPayload: cacaoRequestPayload,
          signature: CacaoSignature(
            t: CacaoSignature.EIP191,
            s: hexSignature,
          ),
          iss: iss,
        ),
      );
    }
    //
    final _ = await walletKit!.approveSessionAuthenticate(
      id: args.id,
      auths: cacaos,
    );
  }

  Future _onSessionProposal(SessionProposalEvent? event) async {
    getLogger(toString()).d('_onSessionProposal Event: $event');
    if (event != null) {
      _toastManager.dismissToast();
      cancelConnectionCountDown();
      final name = event.params.proposer.metadata.name;
      final iconUrl = event.params.proposer.metadata.icons.isEmpty
          ? ""
          : event.params.proposer.metadata.icons.first;
      final requiredNameSpace = event.params.requiredNamespaces["eip155"];
      final optionalNameSpace = event.params.optionalNamespaces["eip155"];
      final requiredChains = requiredNameSpace?.chains ?? [];
      final optionalChains = optionalNameSpace?.chains ?? [];
      Set<String> mergedChains = requiredChains.toSet();
      mergedChains.addAll(optionalChains);
      final supportedNetworks =
          _supportAllSupportedNamespace(mergedChains.toList());
      if (supportedNetworks.isEmpty) {
        await walletKit!.rejectSession(
          id: event.id,
          reason: Errors.getSdkError(
            Errors.USER_REJECTED,
          ).toSignError(),
        );
        return;
      }
      BuildContext? context = StackedService.navigatorKey?.currentContext;
      if (context == null) return;
      _navigationService.navigateToWalletConnectionModal(
        name: name,
        networks: supportedNetworks,
        iconUrl: iconUrl,
        onApprove: () {
          _analyticsService.logEvent(
            eventName: AnalyticsEvent.wcRequestApprove,
            parameters: {
              EventParameterKey.siteName: name,
            },
          );
          walletKit!.approveSession(
            id: event.id,
            namespaces: event.params.generatedNamespaces!,
          );
          _toastManager.showToast(
            text: S.current.walletConnected,
            subtext: S.current.returnToConnectedToDapp(name),
          );
        },
        onReject: () async {
          _analyticsService
              .logEvent(eventName: AnalyticsEvent.wcRequestDenied, parameters: {
            EventParameterKey.siteName: name,
          });
          await walletKit!.rejectSession(
            id: event.id,
            reason: Errors.getSdkError(
              Errors.USER_REJECTED,
            ).toSignError(),
          );
          sessions = walletKit!.sessions.getAll();
          final list = walletKit!.pairings.getAll().toList();
          List<PairingInfo> pairingInfo = [];
          for (var pair in list) {
            final session = sessions.firstWhereOrNull(
                (element) => element.pairingTopic == pair.topic);
            if (session != null) {
              pairingInfo.add(pair);
            }
          }
          _reactivePairing.assignAll(pairingInfo);
        },
      );
    }
  }

  _onSessionProposalError(SessionProposalErrorEvent? event) {
    getLogger(toString()).d("SessionProposalErrorEvent $event");
    _toastManager.showErrorToast(
      text: "Connection failed",
    );
    cancelConnectionCountDown();
  }

  _onSessionConnect(SessionConnect? sessionConnect) async {
    getLogger(toString()).d("SessionProposalErrorEvent $sessionConnect");
    if (sessionConnect != null) {
      sessions.add(sessionConnect.session);
      Box<String> dateBox = Hive.box(HiveBoxName.wcv2PairingDate);
      await dateBox.put(
        sessionConnect.session.pairingTopic,
        DateTime.now().millisecondsSinceEpoch.toString(),
      );
      updateSync();
    }
  }

  _onSessionRequest(SessionRequestEvent? event) {
    getLogger(toString()).d("SessionRequestEvent $event");
  }

  _onAuthRequest(SessionAuthRequest? args) async {
    if (args != null) {
      final String iss = 'did:pkh:eip155:1:${walletService.getWalletAddress}';
      BuildContext? context = StackedService.navigatorKey?.currentContext;
      final supportedChains = locator<NetworkDatabaseService>()
          .getAllNetworks()
          .map((e) => e.namespace)
          .toList();
      final supportedMethods = ['personal_sign', 'eth_sendTransaction'];
      final SessionAuthPayload authPayload = AuthSignature.populateAuthPayload(
        authPayload: args.authPayload,
        chains: supportedChains,
        methods: supportedMethods,
      );
      final cacaoRequestPayload = CacaoRequestPayload.fromSessionAuthPayload(
        authPayload,
      );
      final Widget w = WCRequestWidget(
        child: WCConnectionRequestWidget(
          sessionAuthPayload: authPayload,
          metadata: args.requester,
        ),
        onAccept: () async {
          final String message = walletKit!.formatAuthMessage(
            iss: iss,
            cacaoPayload: cacaoRequestPayload,
          );
          final privateKey = await walletService.getPrivateKey();
          final String sig = eth_sig_util.EthSigUtil.signPersonalMessage(
            message: Uint8List.fromList(message.codeUnits),
            privateKey: privateKey,
          );

          final cacao = AuthSignature.buildAuthObject(
            requestPayload: cacaoRequestPayload,
            signature: CacaoSignature(
              t: CacaoSignature.EIP191,
              s: sig,
            ),
            iss: iss,
          );

          await walletKit!.approveSessionAuthenticate(
            id: args.id,
            auths: [cacao],
          );
        },
        onReject: () async {
          await walletKit!.rejectSessionAuthenticate(
            id: args.id,
            reason: Errors.getSdkError(
              Errors.USER_REJECTED_AUTH,
            ).toSignError(),
          );
        },
      );

      ExternalCloseSheet.showModal(context!, child: w);
    }
  }

  Future _onPairingDelete(PairingEvent? event) async {
    _toastManager.showToast(
        text: "${S.current.walletDisconnected}!", subtext: "");
    updateSync();
  }

  void registerAccountAndEventHandler({required List<LocalNetwork> networks}) {
    final address = locator<WalletService>().getWalletAddress;
    if (address == null) return;
    // Setup our accounts\
    for (final network in networks) {
      for (final String event in getEvents) {
        walletKit?.registerEventEmitter(
          chainId: network.namespace,
          event: event,
        );
      }
      getLogger(toString()).d(network.namespace);
      walletKit?.registerAccount(
        chainId: network.namespace,
        accountAddress: address,
      );
      _registerHandlerForNetwork(network);
    }
    isInitialized = true;
  }

  Future deleteSession(String topic) async {
    try {
      final session =
          sessions.firstWhereOrNull((element) => element.topic == topic);
      if (session == null) return;
      final pairing = _reactivePairing
          .toList()
          .firstWhereOrNull((element) => element.topic == session.pairingTopic);
      if (pairing == null) return;
      await walletKit!.pairings.delete(pairing.topic);
      final list = walletKit!.pairings.getAll().toList();
      list.removeWhere((p0) => p0.topic == pairing.topic);
      _reactivePairing.assignAll(list);
      Box<String> dateBox = Hive.box(HiveBoxName.wcv2PairingDate);
      await dateBox.delete(pairing.topic);
    } catch (_) {}
  }

  updateSync() {
    final list = walletKit!.pairings.getAll().toList();
    _reactivePairing.assignAll(list);
    sessions = walletKit!.sessions.getAll();
  }

  Future clearExpiredOrInactivePairing() async {
    try {
      final list = walletKit!.pairings.getAll().toList();
      final activeSessions = walletKit!.getActiveSessions().values;
      if (activeSessions.isNotEmpty) {
        for (var pair in list) {
          final session = activeSessions.firstWhereOrNull(
              (element) => element.pairingTopic == pair.topic);
          if (!pair.active || session == null) {
            _disconnectTopic(pair.topic);
          }
        }
      }
    } catch (e) {
      getLogger(toString()).i(e);
    }
  }

  void reconnectIfDisconnected() {
    final isConnected = walletKit?.core.relayClient.isConnected ?? false;
    if (!isConnected) {
      walletKit?.core.relayClient.connect();
    }
  }

  _registerHandlerForNetwork(LocalNetwork network) {
    walletKit?.registerRequestHandler(
      chainId: network.namespace,
      method: pSign,
      handler: (topic, parameters) => _personalSign(
          chainId: network.chainId, topic: topic, parameters: parameters),
    );
    walletKit?.registerRequestHandler(
      chainId: network.namespace,
      method: eSign,
      handler: (topic, parameters) => _ethSign(
          chainId: network.chainId, topic: topic, parameters: parameters),
    );
    walletKit?.registerRequestHandler(
      chainId: network.namespace,
      method: eSignTransaction,
      handler: (topic, parameters) => _ethSignTransaction(
          chainId: network.chainId, topic: topic, parameters: parameters),
    );
    walletKit?.registerRequestHandler(
      chainId: network.namespace,
      method: eSendTransaction,
      handler: (topic, parameters) => _onEthSendTransaction(
          chainId: network.chainId, topic: topic, parameters: parameters),
    );
    walletKit?.registerRequestHandler(
      chainId: network.namespace,
      method: eSignTypedData,
      handler: (topic, parameters) => _ethSignTypedData(
          chainId: network.chainId, topic: topic, parameters: parameters),
    );
    walletKit?.registerRequestHandler(
      chainId: network.namespace,
      method: eSignTypedDataV3,
      handler: (topic, parameters) => _ethSignTypedData(
          chainId: network.chainId, topic: topic, parameters: parameters),
    );
    walletKit?.registerRequestHandler(
      chainId: network.namespace,
      method: eSignTypedDataV4,
      handler: (topic, parameters) => _ethSignTypedData(
          chainId: network.chainId, topic: topic, parameters: parameters),
    );
    walletKit?.registerRequestHandler(
      chainId: network.namespace,
      method: walletSwitchEthereumChain,
      handler: (String topic, dynamic parameters) {
        getLogger(toString()).d(parameters);
      },
    );
  }

  Future _personalSign(
      {num? chainId,
      required String topic,
      required dynamic parameters}) async {
    final pRequest = walletKit!.pendingRequests.getAll().last;
    var response = JsonRpcResponse(
      id: pRequest.id,
      jsonrpc: '2.0',
    );
    try {
      BuildContext? context = StackedService.navigatorKey?.currentContext;
      if (context == null) {
        response = response.copyWith(
            error: JsonRpcError(
                code: 0,
                message:
                    Errors.getSdkError(Errors.USER_REJECTED_SIGN).message));
        walletKit?.respondSessionRequest(topic: topic, response: response);
        return;
      }
      ExternalCloseSheet.showModal(
        context,
        child: SizedBox(
          height: MediaQuery.of(context).size.height * 0.80,
          child: WebSignMessageView(
            message: parameters[0],
            isPersonalSign: true,
            isDappBrowser: true,
            isTypedData: false,
            onDone: (signature) {
              if (signature is String) {
                if (signature.startsWith("0x")) {
                  response = response.copyWith(result: signature);
                  walletKit?.respondSessionRequest(
                      topic: topic, response: response);
                  return;
                } else {
                  response = response.copyWith(
                      error: JsonRpcError(
                          code: 0,
                          message: Errors.getSdkError(Errors.USER_REJECTED_SIGN)
                              .message));
                  walletKit?.respondSessionRequest(
                      topic: topic, response: response);
                }
              } else {
                response = response.copyWith(
                    error: JsonRpcError(
                        code: 0,
                        message: Errors.getSdkError(Errors.USER_REJECTED_SIGN)
                            .message));
                walletKit?.respondSessionRequest(
                    topic: topic, response: response);
              }
            },
          ),
        ),
      );
    } catch (e) {
      getLogger(toString()).d(e);
      response = response.copyWith(
        error: JsonRpcError(code: 0, message: e.toString()),
      );
      walletKit?.respondSessionRequest(topic: topic, response: response);
    }
  }

  Future _ethSign(
      {num? chainId,
      required String topic,
      required dynamic parameters}) async {
    final pRequest = walletKit!.pendingRequests.getAll().last;
    var response = JsonRpcResponse(
      id: pRequest.id,
      jsonrpc: '2.0',
    );
    try {
      BuildContext? context = StackedService.navigatorKey?.currentContext;
      if (context == null) {
        response = response.copyWith(
            error: JsonRpcError(
                code: 0,
                message:
                    Errors.getSdkError(Errors.USER_REJECTED_SIGN).message));
        walletKit?.respondSessionRequest(topic: topic, response: response);
        return;
      }
      ExternalCloseSheet.showModal(
        context,
        child: SizedBox(
          height: MediaQuery.of(context).size.height * 0.80,
          child: WebSignMessageView(
            message: parameters[1],
            isPersonalSign: true,
            isDappBrowser: true,
            isTypedData: false,
            onDone: (signature) {
              if (signature is String) {
                if (signature.startsWith("0x")) {
                  response = response.copyWith(result: signature);
                  walletKit?.respondSessionRequest(
                      topic: topic, response: response);
                  return;
                } else {
                  response = response.copyWith(
                      error: JsonRpcError(
                          code: 0,
                          message: Errors.getSdkError(Errors.USER_REJECTED_SIGN)
                              .message));
                  walletKit?.respondSessionRequest(
                      topic: topic, response: response);
                }
              } else {
                response = response.copyWith(
                    error: JsonRpcError(
                        code: 0,
                        message: Errors.getSdkError(Errors.USER_REJECTED_SIGN)
                            .message));
                walletKit?.respondSessionRequest(
                    topic: topic, response: response);
              }
            },
          ),
        ),
      );
    } catch (e) {
      response = response.copyWith(
        error: JsonRpcError(code: 0, message: e.toString()),
      );
      walletKit?.respondSessionRequest(topic: topic, response: response);
    }
  }

  _onEthSendTransaction(
      {num? chainId,
      required String topic,
      required dynamic parameters}) async {
    getLogger(toString()).i(parameters);
    EthereumTransaction ethTransaction = EthereumTransaction.fromJson(
      parameters[0],
    );
    final networks = locator<NetworkDatabaseService>().getAllNetworks();
    LocalNetwork? preferredNetwork =
        networks.firstWhereOrNull((element) => element.chainId == chainId);
    final pRequest = walletKit!.pendingRequests.getAll().last;
    var response = JsonRpcResponse(
      id: pRequest.id,
      jsonrpc: '2.0',
    );
    try {
      final pairing = _getPairingInfoByTopic(topic);
      final transaction = wcEthTxToWeb3Tx(ethTransaction);

      BuildContext? context = StackedService.navigatorKey?.currentContext;
      ExternalCloseSheet.showModal(
        context!,
        child: SizedBox(
          height: MediaQuery.of(context).size.height * 0.80,
          child: SignTransactionView(
            transaction: transaction,
            signTransactionOnly: false,
            remotePeerName: pairing?.peerMetadata?.name,
            preferredNetwork: preferredNetwork,
            isDappBrowser: true,
            onSign: (signature) {
              if (signature?.startsWith("0x") == true) {
                response = response.copyWith(result: signature);
              } else {
                response = response.copyWith(
                  error: JsonRpcError(code: -1, message: signature ?? ''),
                );
              }
              walletKit?.respondSessionRequest(
                  topic: topic, response: response);
            },
            onRejectTransaction: (errorMessage) {
              response = response.copyWith(
                  error: JsonRpcError(
                      code: 0,
                      message:
                          Errors.getSdkError(Errors.USER_REJECTED).message));
              walletKit?.respondSessionRequest(
                  topic: topic, response: response);
            },
          ),
        ),
      );
    } catch (e) {
      getLogger(toString()).d(e);
      response = response.copyWith(
        error: JsonRpcError(code: 0, message: e.toString()),
      );
      walletKit?.respondSessionRequest(topic: topic, response: response);
    }
  }

  Future _ethSignTransaction(
      {num? chainId,
      required String topic,
      required dynamic parameters}) async {
    getLogger(toString()).i(parameters);
    EthereumTransaction ethTransaction = EthereumTransaction.fromJson(
      parameters[0],
    );
    final pRequest = walletKit!.pendingRequests.getAll().last;
    var response = JsonRpcResponse(
      id: pRequest.id,
      jsonrpc: '2.0',
    );
    try {
      final networks = locator<NetworkDatabaseService>().getAllNetworks();
      LocalNetwork? preferredNetwork =
          networks.firstWhereOrNull((element) => element.chainId == chainId);
      final pairing = _getPairingInfoByTopic(topic);
      final transaction = wcEthTxToWeb3Tx(ethTransaction);
      BuildContext? context = StackedService.navigatorKey?.currentContext;
      ExternalCloseSheet.showModal(
        context!,
        child: SizedBox(
          height: MediaQuery.of(context).size.height * 0.80,
          child: SignTransactionView(
            transaction: transaction,
            signTransactionOnly: true,
            remotePeerName: pairing?.peerMetadata?.name,
            preferredNetwork: preferredNetwork,
            isDappBrowser: true,
            onSign: (signature) {
              if (signature?.startsWith("0x") == true) {
                response = response.copyWith(result: signature);
              } else {
                response = response.copyWith(
                  error: JsonRpcError(code: -1, message: signature ?? ''),
                );
              }
              walletKit?.respondSessionRequest(
                  topic: topic, response: response);
            },
            onRejectTransaction: (errorMessage) {
              response = response.copyWith(
                  error: JsonRpcError(
                      code: 0,
                      message:
                          Errors.getSdkError(Errors.USER_REJECTED).message));
              walletKit?.respondSessionRequest(
                  topic: topic, response: response);
            },
          ),
        ),
      );
    } catch (e) {
      response = response.copyWith(
        error: JsonRpcError(code: 0, message: e.toString()),
      );
      walletKit?.respondSessionRequest(topic: topic, response: response);
    }
  }

  Future _ethSignTypedData(
      {num? chainId,
      required String topic,
      required dynamic parameters}) async {
    final String data = parameters[1];
    final pRequest = walletKit!.pendingRequests.getAll().last;
    var response = JsonRpcResponse(
      id: pRequest.id,
      jsonrpc: '2.0',
    );
    try {
      final signature = await _navigationService.navigateToWebSignMessageView(
        message: data,
        isPersonalSign: false,
        isTypedData: true,
      );
      if (signature is String) {
        response = response.copyWith(result: signature);
      } else {
        response = response.copyWith(
            error: JsonRpcError(
                code: 0,
                message: Errors.getSdkError(Errors.USER_REJECTED).message));
      }
    } catch (e) {
      response = response.copyWith(
        error: JsonRpcError(code: 0, message: e.toString()),
      );
    }
    walletKit?.respondSessionRequest(topic: topic, response: response);
  }

  PairingInfo? _getPairingInfoByTopic(String topic) {
    if (walletKit == null) return null;
    final sessions = walletKit!.sessions.getAll();

    final session =
        sessions.firstWhereOrNull((element) => element.pairingTopic == topic);
    if (session == null) return null;
    final pairings = walletKit!.pairings.getAll().toList();
    final pairing = pairings
        .firstWhereOrNull((element) => element.topic == session.pairingTopic);
    return pairing;
  }

  void showConnecting() {
    _toastManager.showToast(
        text: S.current.walletConnect,
        subtext: S.current.connectingMayTakeAFewSeconds,
        duration: const Duration(seconds: 25));
  }

  List<LocalNetwork> _supportAllSupportedNamespace(List<String> chains) {
    final networks = locator<NetworkDatabaseService>().getAllNetworks();
    if (chains.isEmpty) return [];
    List<LocalNetwork> supported = [];
    for (var network in networks) {
      final index = chains.indexWhere((chain) => chain == network.namespace);
      if (index != -1) {
        supported.add(network);
      }
    }
    return supported;
  }

  void clearWalletConnectState() {
    walletKit = null;
    isInitialized = false;
  }

  Future<void> reinitializeWalletConnect() async {
    clearWalletConnectState();
    await create(); // This method reinitializes the Web3Wallet and sets up listeners
    registerAccountAndEventHandler(
      networks: locator<NetworkDatabaseService>().getAllNetworks(),
    );
  }
}

Transaction wcEthTxToWeb3Tx(EthereumTransaction ethTransaction) {
  return Transaction(
    from: EthereumAddress.fromHex(ethTransaction.from),
    to: EthereumAddress.fromHex(ethTransaction.to),
    value: ethTransaction.value != null
        ? EtherAmount.fromBigInt(
            EtherUnit.wei,
            BigInt.tryParse(ethTransaction.value!) ?? BigInt.zero,
          )
        : null,
    gasPrice: ethTransaction.gasPrice != null
        ? EtherAmount.fromBigInt(
            EtherUnit.wei,
            BigInt.tryParse(ethTransaction.gasPrice!) ?? BigInt.zero,
          )
        : null,
    maxFeePerGas: ethTransaction.maxFeePerGas != null
        ? EtherAmount.fromBigInt(
            EtherUnit.wei,
            BigInt.tryParse(ethTransaction.maxFeePerGas!) ?? BigInt.zero,
          )
        : null,
    maxPriorityFeePerGas: ethTransaction.maxPriorityFeePerGas != null
        ? EtherAmount.fromBigInt(
            EtherUnit.wei,
            BigInt.tryParse(ethTransaction.maxPriorityFeePerGas!) ??
                BigInt.zero,
          )
        : null,
    maxGas: BigInt.tryParse(ethTransaction.gasLimit ?? '')?.toInt(),
    data: (ethTransaction.data != null && ethTransaction.data != '0x')
        ? hexToBytes(ethTransaction.data!)
        : null,
  );
}

enum SupportedEVMMethods {
  ethSign,
  ethSignTransaction,
  ethSignTypedData,
  ethSignTypedDataV4,
  switchChain,
  personalSign,
  ethSendTransaction;

  String get name {
    switch (this) {
      case ethSign:
        return 'eth_sign';
      case ethSignTransaction:
        return 'eth_signTransaction';
      case ethSignTypedData:
        return 'eth_signTypedData';
      case ethSignTypedDataV4:
        return 'eth_signTypedData_v4';
      case switchChain:
        return 'wallet_switchEthereumChain';
      case personalSign:
        return 'personal_sign';
      case ethSendTransaction:
        return 'eth_sendTransaction';
    }
  }
}
