import 'dart:async';

import 'package:app_links/app_links.dart';
import 'package:collection/collection.dart';
import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:nestcoinco_onboard_api_gateway_lite/nestcoinco_onboard_api_gateway_lite.dart'
    as haven;
import 'package:onboard_wallet/app/index.dart';
import 'package:onboard_wallet/constants/constants.dart';
import 'package:onboard_wallet/flavors.dart';
import 'package:onboard_wallet/models/card/local_card_account/index.dart';
import 'package:onboard_wallet/models/models.dart';
import 'package:onboard_wallet/models/token/wallet_type.dart';
import 'package:onboard_wallet/models/virtual_account/local_virtual_account/local_virtual_account.dart';
import 'package:onboard_wallet/models/virtual_account/transaction/local_virtual_account_transaction.dart';
import 'package:onboard_wallet/services/card_subscription_service.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:onboard_wallet/ui/views/home/<USER>';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';
import 'package:url_launcher/url_launcher.dart';

class DynamicLinkService {
  ReactiveValue<bool?> initiateDepositFlow = ReactiveValue(null);
  final _appLinks = AppLinks();
  StreamController<Uri>? _dynamicLinksEventController;
  bool _isListenerSetup = false;

  Stream<Uri>? get dynamicLinksStream => _dynamicLinksEventController?.stream;

  DynamicLinkService() {
    _dynamicLinksEventController = StreamController.broadcast();
  }

  void setupDynamicLink() {
    _initDynamicLink();
  }

  void _initDynamicLink() async {
    if (_isListenerSetup) return;

    /// Listen to Dynamic link event when app is in background/foreground
    FirebaseDynamicLinks.instance.onLink.listen((dynamicLinkData) {
      final Uri deeplink = dynamicLinkData.link;
      _dynamicLinksEventController?.add(deeplink);

      /// navigate to path
      navigate(deeplink);
    }).onError((error) {
      // Handle error
    });

    final walletDeeplink = Uri.parse(F.deeplinkHost);
    _appLinks.uriLinkStream.listen((uri) {
      if (uri.host != walletDeeplink.host) {
        navigate(uri);
      }
    });

    /// Check App was launched via a link
    final PendingDynamicLinkData? pendingDynamicLinkData =
        await FirebaseDynamicLinks.instance.getInitialLink();
    final Uri? deeplink = pendingDynamicLinkData?.link;

    /// navigate to path
    if (deeplink != null) {
      navigate(deeplink);
    }

    /// Check app was launched via app links
    final uri = await _appLinks.getInitialLink();
    if (uri != null) {
      final host = uri.host;
      getLogger(toString()).d(host);
      if (host != walletDeeplink.host) {
        navigate(uri);
      }
    }

    _isListenerSetup = true;
  }

  /// Handle deep link from url
  Future<void> navigate(Uri deeplink) async {
    String path = deeplink.path;
    final AuthenticationService authenticationService =
        locator<AuthenticationService>();
    bool isLoggedIn = await authenticationService.isLoggedIn();
    if (!isLoggedIn) {
      if (path.isEmpty || path == DeepLinkPath.kycRedirect.path) {
        locator<KycService>().closeChromeTab();
      }
      return;
    }
    String? referralLink = extractReferrerValue(deeplink);
    if (referralLink != null) return;

    final navigationService = locator<NavigationService>();
    DeepLinkPath? deepLinkPath =
        DeepLinkPath.values.firstWhereOrNull((element) => element.path == path);

    if (deepLinkPath == null) {
      if (path.endsWith("/")) {
        path = path.substring(0, path.length - 1);
        deepLinkPath = DeepLinkPath.values
            .firstWhereOrNull((element) => element.path == path);
      }
    }

    if (deeplink.host == F.applicationId) {
      locator<KycService>().closeChromeTab();
      return;
    }

    switch (deepLinkPath) {
      case DeepLinkPath.deposit:
        final query = deeplink.queryParameters;
        final networkId = query["network"];
        LocalAddressTokenBalance? token;
        LocalTokenNetworkBalance? tokenNetworkBalance;
        if (networkId != null) {
          final symbol = query["token"];
          if (symbol != null) {
            token = locator<TokenService>()
                .getTokenLocalGivenSymbol(symbol: symbol);
          }
          if (token != null) {
            tokenNetworkBalance = token.networks?.firstWhereOrNull(
              (element) => element.id?.toLowerCase() == networkId.toLowerCase(),
            );
          }
        }
        if (tokenNetworkBalance != null && token != null) {
          navigationService.navigateToDepositView(
            walletType: WalletType.spot,
            token: token,
          );
        }
        break;
      case DeepLinkPath.walletInfo:
        navigationService.navigateToWalletInfoView();
        break;
      case DeepLinkPath.trade:
        navigationService.navigateToSelectAssetsView();
        break;
      case DeepLinkPath.manageToken:
        navigationService.navigateToManageTokensView();
        break;
      case DeepLinkPath.support:
        locator<AnalyticsService>()
            .logEvent(eventName: AnalyticsEvent.openSupportViaDeepLink);
        locator<AppSettingsService>().showCrispChat();
        break;
      case DeepLinkPath.walletHome:
        if (navigationService.currentRoute != Routes.homeView) {
          navigationService.clearStackAndShow(Routes.homeView);
        } else {
          _switchHomeTab(0);
        }
        break;
      case DeepLinkPath.cardFunding:
      case DeepLinkPath.cardCreate:
      case DeepLinkPath.cardInfoBanner:
      case DeepLinkPath.cardHome:
      case DeepLinkPath.cardMore:
      case DeepLinkPath.cardSuspend:
        _toCardDashBoard(navigationService);
        break;
      case DeepLinkPath.cardTransaction:
        _toCardsTransaction();
        break;
      case DeepLinkPath.fund:
        navigationService.navigateToSelectAssetsView();
        break;
      case DeepLinkPath.swap:
        navigationService.navigateToSwapView();
        break;
      case DeepLinkPath.transfer:
        navigationService.navigateToTransferSelectTokenView(
          fromWalletType: WalletType.spot,
          toWalletType: null,
          onSelectToken: (_) {},
          shouldContinueToFundingOption: true,
          showExternalWalletWarning: true,
        );
        break;

      case DeepLinkPath.referAndEarn:
        navigationService.navigateToReferAndEarnView();
        break;
      case DeepLinkPath.hub:
        if (navigationService.currentRoute != Routes.homeView) {
          navigationService.clearStackAndShow(
            Routes.homeView,
            arguments: const HomeViewArguments(initialIndex: 1),
          );
        } else {
          _switchHomeTab(1);
        }
        break;
      case DeepLinkPath.cardSubscriptionHome:
        final query = deeplink.queryParameters;

        final cardAccountId = query["cardAccountId"];

        _toCardDashBoard(
          navigationService,
          initialActiveTab: 1,
          initialCardAccountId: cardAccountId,
        );
        break;
      case DeepLinkPath.cardSubscriptionDetail:
        final query = deeplink.queryParameters;

        final cardAccountId = query["cardAccountId"];
        final reminderId = query['subscriptionReminderId'];

        if (reminderId == null || cardAccountId == null) {
          _toCardDashBoard(navigationService);
          return;
        }

        final cardSubscriptionService = locator<CardSubscriptionService>();
        final response = await cardSubscriptionService.getSubscriptionReminder(
          reminderId,
        );
        LocalCardAccount? cardAccount =
            await _getCardAccountById(cardAccountId);

        if (cardAccount == null) {
          _toCardDashBoard(navigationService);
          return;
        }

        response.when(success: (success) async {
          if (success != null) {
            navigationService.navigateToCardSubscriptionDetailView(
              cardAccount: cardAccount,
              reminder: success,
            );
          } else {
            _toCardDashBoard(navigationService);
          }
        }, failure: (failure) {
          _toCardDashBoard(navigationService);
        });

        break;
      case DeepLinkPath.addCardSubscription:
        final query = deeplink.queryParameters;
        final cardAccountId = query["cardAccountId"];
        final cardTransactionId = query['cardTransactionId'];

        if (cardAccountId == null || cardTransactionId == null) {
          _toCardDashBoard(navigationService);
          return;
        }
        var cardService = locator<CardService>();
        LocalCardAccount? cardAccount =
            await _getCardAccountById(cardAccountId);

        if (cardAccount == null) {
          _toCardDashBoard(navigationService);
          return;
        }
        final cardTransaction = await cardService.getCardTransactionById(
          accountId: cardAccountId,
          cardTransactionId: cardTransactionId,
        );
        if (cardTransaction != null) {
          navigationService.navigateToAddCardSubscriptionView(
            cardAccount: cardAccount,
            cardTransaction: cardTransaction,
          );
        } else {
          _toCardDashBoard(navigationService);
        }

        break;
      case DeepLinkPath.virtualAccountHome:
        _toVirtualAccountDashBoard(navigationService);
        return;
      case DeepLinkPath.virtualAccountDetails:
        final query = deeplink.queryParameters;
        final accountId = query["accountId"];
        if (accountId == null) {
          _toVirtualAccountDashBoard(navigationService);
          return;
        }
        final account = await _getVirtualAccountById(accountId);
        if (account == null) {
          _toVirtualAccountDashBoard(navigationService);
          return;
        }
        navigationService.navigateToVirtualAccountDetailsView(
          virtualAccount: account,
        );
        break;
      case DeepLinkPath.transferMoney:
        final query = deeplink.queryParameters;
        final accountId = query["accountId"];
        if (accountId == null) {
          _toVirtualAccountDashBoard(navigationService);
          return;
        }
        final account = await _getVirtualAccountById(accountId);
        if (account == null) {
          _toVirtualAccountDashBoard(navigationService);
          return;
        }
        navigationService.navigateToVirtualAccountTransferOptionsView(
          virtualAccount: account,
        );
        break;
      case DeepLinkPath.addMoney:
        final query = deeplink.queryParameters;
        final accountId = query["accountId"];
        if (accountId == null) {
          _toVirtualAccountDashBoard(navigationService);
          return;
        }
        final account = await _getVirtualAccountById(accountId);
        if (account == null) {
          _toVirtualAccountDashBoard(navigationService);
          return;
        }
        navigationService.navigateToVirtualAccountDetailsView(
            virtualAccount: account);
        break;
      case DeepLinkPath.virtualAccountTransactionDetail:
        final query = deeplink.queryParameters;
        final accountId = query["accountId"];
        final transactionId = query["transactionId"];
        if (transactionId == null) {
          _toVirtualAccountDashBoard(navigationService);
          return;
        }
        if (accountId == null) {
          _toVirtualAccountDashBoard(navigationService);
          return;
        }
        LocalVirtualAccount? account;
        LocalVirtualAccountTransaction? transaction;
        try {
          final response = await Future.wait([
            _getVirtualAccountById(accountId),
            _getVirtualAccountTransaction(accountId, transactionId),
          ]);
          if (response.length < 2) {
            _toVirtualAccountDashBoard(navigationService);
          }
          account = response.first as LocalVirtualAccount?;
          transaction = response.last as LocalVirtualAccountTransaction?;
        } catch (_) {}

        if (account == null || transaction == null) {
          _toVirtualAccountDashBoard(navigationService);
          return;
        }

        navigationService.navigateToVirtualAccountTransactionDetailView(
          virtualAccountTransaction: transaction,
          virtualAccount: account,
        );
      case DeepLinkPath.dapp:
        final query = deeplink.queryParameters;
        final dappId = query["id"];
        final dapp = await locator<FireStoreService>().getDappById(dappId!);
        if (dapp != null) {
          openBrowser(dapp);
        }
        break;
      case DeepLinkPath.kycRedirect:
        final service = locator<KycService>();
        service.closeChromeTab();
        break;
      case DeepLinkPath.tradingDashboard:
        if (navigationService.currentRoute != Routes.havenHomeView) {
          navigationService.navigateTo(Routes.homeView);
        }
        break;
      case DeepLinkPath.tradingPortfolio:
        if (navigationService.currentRoute != Routes.havenHomeView) {
          navigationService.navigateToHavenHomeView(initialIndex: 1);
        }
      case DeepLinkPath.tradingPortfolioAsset:
        final query = deeplink.queryParameters;
        final asset = query["asset"];

        final assetBalance =
            haven.UserPortfolioAssetBalance((b) => b..asset = asset);

        navigationService.navigateToHavenPortfolioAssetDetailView(
            assetBalance: assetBalance, assetData: null);
        break;
      case DeepLinkPath.tradingTransactionDetails:
        final query = deeplink.queryParameters;

        final asset = query["asset"];
        final txnId = query["txId"];
        final type = query["type"];

        if (txnId != null && asset != null) {
          final navigationService = locator<NavigationService>();
          navigationService.navigateToHavenTransactionDetailView(
              isFromNotification: true,
              transactionType: type,
              assetSymbol: asset,
              transactionIdFromNotif: txnId);
        }

        break;
      case null:
        final onboardUri = Uri.parse(F.onboardWebBaseUrl);
        final bridgeUri = Uri.parse(F.onboardPayWebBaseUrl);
        final walletDeeplink = Uri.parse(F.deeplinkHost);
        if (deeplink.host == walletDeeplink.host) {
          Uri? link = await resolveShortLink(deeplink.toString());
          if (link != null) {
            _navigateToOnboardUrl(link.toString());
          }
        } else if (deeplink.host == onboardUri.host) {
          _navigateToOnboardUrl(deeplink.toString());
        } else if (deeplink.host == bridgeUri.host) {
          _navigateToOnboardUrl(deeplink.toString());
        } else if (deeplink.scheme == "wc") {
          _initiateWalletConnect(deeplink.toString());
        } else {
          if (deeplink.scheme == F.androidScheme) return;
          if (await canLaunchUrl(deeplink)) {
            launchUrl(deeplink);
          }
        }
        break;
    }
  }

  openBrowser(Dapp dapp) {
    locator<AnalyticsService>()
        .logEvent(eventName: AnalyticsEvent.openDappOnHubs, parameters: {
      EventParameterKey.url: dapp.url,
      EventParameterKey.name: dapp.name,
    });
    final navigationService = locator<NavigationService>();
    final routing = dapp.routing;
    if (routing == null) return;
    switch (routing) {
      case HubRouting.dappBrowser:
        navigationService.navigateToDappBrowserView(route: dapp.url);
        break;
      case HubRouting.internalBrowser:
        locator<WalletNavigationService>().navigateToExchange(dapp.url);
        break;
      case HubRouting.inApp:
        navigationService.navigateTo(dapp.url);
        break;
    }
  }

  Future<LocalCardAccount?> _getCardAccountById(String id) async {
    var cardService = locator<CardService>();

    LocalCardAccount? cardAccount =
        cardService.cardAccountBox.values.firstWhereOrNull(
      (element) => element.id?.toLowerCase() == id.toLowerCase(),
    );

    if (cardAccount == null) {
      final response = await cardService.getCardAccountById(accountId: id);
      response.when(
          success: (success) {
            cardAccount = success;
          },
          failure: (failure) {});
    }
    return cardAccount;
  }

  Future<LocalVirtualAccount?> _getVirtualAccountById(String id) async {
    final virtualAccountService = locator<VirtualAccountService>();

    LocalVirtualAccount? virtualAccount =
        virtualAccountService.virtualAccountBox.values.firstWhereOrNull(
      (element) => element.id?.toLowerCase() == id.toLowerCase(),
    );

    if (virtualAccount == null) {
      final response =
          await virtualAccountService.getVirtualAccountById(accountId: id);
      response.when(
          success: (success) {
            virtualAccount = success;
          },
          failure: (failure) {});
    }
    return virtualAccount;
  }

  Future<LocalVirtualAccountTransaction?> _getVirtualAccountTransaction(
      String accountId, String transactionId) async {
    final virtualAccountService = locator<VirtualAccountService>();
    LocalVirtualAccountTransaction? transaction;
    final response =
        await virtualAccountService.getVirtualAccountTransactionById(
            accountId: accountId, transactionId: transactionId);
    response.when(
        success: (success) {
          transaction = success;
        },
        failure: (failure) {});
    return transaction;
  }

  Future<void> cardsNavigate(String? path) async {
    final AuthenticationService authenticationService =
        locator<AuthenticationService>();
    bool isLogged = await authenticationService.isLoggedIn();
    if (isLogged) return;
    final NavigationService navigationService = locator<NavigationService>();
    switch (path) {
      case CardPath.cardTransaction:
        _toCardsTransaction();
        break;
      case CardPath.cardFunding:
      case CardPath.cardCreate:
      case CardPath.cardInfoBanner:
      case CardPath.cardHome:
      case CardPath.cardMore:
      case CardPath.cardSuspend:
      default:
        _toCardDashBoard(navigationService);
    }
  }

  _toCardDashBoard(NavigationService navigationService,
      {int? initialActiveTab, String? initialCardAccountId}) {
    String? currentRoute = navigationService.currentRoute;

    if (currentRoute == Routes.cardView ||
        currentRoute == DashboardRouteViewRoutes.cardView) {
      return;
    }
    var homeService = locator<HomeViewModel>();

    if ((currentRoute == Routes.homeView && homeService.currentTab == 0) ||
        DashboardRouteViewRoutes.all.contains(currentRoute)) {
      navigationService.navigateTo(DashboardRouteViewRoutes.cardView,
          id: 1,
          arguments: NestedCardViewArguments(
            initialActiveTab: initialActiveTab ?? 0,
            initialCardAccountId: initialCardAccountId,
          ));
    } else {
      navigationService.navigateToCardView(
        initialActiveTab: initialActiveTab ?? 0,
        initialCardAccountId: initialCardAccountId,
      );
    }
  }

  _toVirtualAccountDashBoard(NavigationService navigationService) {
    String? currentRoute = navigationService.currentRoute;

    if (currentRoute == Routes.cashDashboardView ||
        currentRoute == DashboardRouteViewRoutes.cashDashboardView) {
      return;
    }
    var homeService = locator<HomeViewModel>();

    if ((currentRoute == Routes.homeView && homeService.currentTab == 0) ||
        DashboardRouteViewRoutes.all.contains(currentRoute)) {
      navigationService.navigateTo(DashboardRouteViewRoutes.cashDashboardView,
          id: 1);
    } else {
      navigationService.navigateToCashDashboardView();
    }
  }

  _navigateToOnboardUrl(String url) {
    final referralLink = "${F.onboardWebBaseUrl}referrer";
    if (url.contains(referralLink)) return;
    final navigationService = locator<NavigationService>();
    final currentRoute = navigationService.currentRoute;
    if (currentRoute == Routes.onboardWebView) {
      final webViewService = locator<WebViewService>();
      webViewService.loadUrlPath(url);
    } else {
      locator<WalletNavigationService>().navigateToExchange(url);
    }
  }

  Future<bool> canHandleLink(Uri link) async {
    final AuthenticationService authenticationService =
        locator<AuthenticationService>();
    bool isLogged = await authenticationService.isLoggedIn();
    if (!isLogged) return false;
    String path = link.path;
    return DeepLinkPath.values
            .firstWhereOrNull((element) => element.path == path) !=
        null;
  }

  _toCardsTransaction() {
    var cardService = locator<CardService>();
    var navigationService = locator<NavigationService>();

    LocalCardAccount? cardAccount =
        cardService.cardAccountBox.values.firstOrNull;
    if (cardAccount != null) {
      navigationService.navigateToCardTransactionsView(
        cardAccount: cardAccount,
      );
    } else {
      navigationService.navigateToHomeView(
        initialIndex: 1,
      );
    }
  }

  _switchHomeTab(int index) {
    var homeService = locator<HomeViewModel>();
    homeService.switchTab(index);
  }

  Future<Uri?> resolveShortLink(String link) async {
    try {
      final details =
          await FirebaseDynamicLinks.instance.getDynamicLink(Uri.parse(link));
      return details?.link;
    } catch (_) {
      return null;
    }
  }

  Future<Uri> resolveIfShortUri(Uri uri) async {
    try {
      final walletDeeplink = Uri.parse(F.deeplinkHost);
      // Check if it is actually a short url;
      if (uri.host != walletDeeplink.host) {
        return uri;
      }
      final details = await FirebaseDynamicLinks.instance.getDynamicLink(uri);
      return details?.link ?? uri;
    } catch (_) {
      return uri;
    }
  }

  Future<void> _initiateWalletConnect(String deeplink) async {
    await Future.delayed(const Duration(seconds: 1));
    final walletConnect = locator<WalletConnectService>();
    final isConnectionRequest = walletConnect.isValidWalletConnectUri(deeplink);
    if (isConnectionRequest) {
      await locator<WalletConnectService>().reinitializeWalletConnect();
      await Future.delayed(const Duration(seconds: 2));
      walletConnect.connectWithUri(deeplink);
    }
  }

  Future<String?> getReferralLink() async {
    try {
      final PendingDynamicLinkData? pendingDynamicLinkData =
          await FirebaseDynamicLinks.instance.getInitialLink();
      final Uri? deeplink = pendingDynamicLinkData?.link;
      if (deeplink == null) return null;
      final resolvedUrl = await resolveIfShortUri(deeplink);
      return extractReferrerValue(resolvedUrl);
    } catch (e) {
      getLogger(toString()).d(e);
      return null;
    }
  }

  String? extractReferrerValue(Uri uri) {
    // Get the query parameters from the URL
    Map<String, String> queryParams = uri.queryParameters;

    return queryParams["referrer"];
  }
}
