import 'dart:async';
import 'dart:math';

import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:nestcoinco_onboard_api_gateway/nestcoinco_onboard_api_gateway.dart';
import 'package:onboard_wallet/api/interceptors.dart';
import 'package:onboard_wallet/api/onboard_exception.dart';
import 'package:onboard_wallet/app/index.dart';
import 'package:onboard_wallet/constants/constants.dart';
import 'package:onboard_wallet/constants/string_constants.dart';
import 'package:onboard_wallet/models/models.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:onboard_wallet/ui/views/dashboard/main_dashboard/main_dashboard_viewmodel.dart';
import 'package:onboard_wallet/utils/utils.dart';
import 'package:stacked/stacked_annotations.dart';

enum LoginState {
  fullLogin,
  walletOnly,
  none,
}

class AuthenticationService {
  final _onboardUserAuthClient = getOnboardApiGateway.getUsersUserauthClient();
  final SecureStorageService _secureStorageService =
      locator<SecureStorageService>();
  final _prefService = locator<PreferenceService>();
  Completer<Result<String?>>? _ongoingTokenRefresh;

  // Generate a new session ID for tracking requests
  String _generateSessionId() {
    final random = Random();
    return DateTime.now().millisecondsSinceEpoch.toString() +
        random.nextInt(999999).toString().padLeft(6, '0');
  }

  // Start a new session (call this when user logs in)
  void _startNewSession() {
    final sessionId = _generateSessionId();
    UnAuthorizedErrorHandler.updateSessionId(sessionId);
  }

  // Clear the current session (call this when user logs out)
  void _clearSession() {
    UnAuthorizedErrorHandler.clearSession();
  }

  // Future<LoginState> getLoginState() async {
  //   final walletExist = await locator<WalletService>().walletExist();
  //   final token = await getAuthToken();

  //   if (walletExist && !isNullOrEmpty(token)) {
  //     if (await hasTokenExpired()) {
  //       return LoginState.walletOnly;
  //     }
  //     return LoginState.fullLogin;
  //   } else if (walletExist) {
  //     return LoginState.walletOnly;
  //   } else {
  //     return LoginState.none;
  //   }
  // }

  Future<bool> isLoggedIn() async {
    final token = await getAuthToken();
    return !isNullOrEmpty(token);
  }

  Future<String?> getAuthToken() async =>
      await _secureStorageService.read(key: authTokenKey);

  Future<String?> getRefreshToken() async =>
      await _secureStorageService.read(key: refreshTokenKey);

  Future<Result<String?>> onboardUserAuthentication(
      {required String email, String? referredBy}) async {
    try {
      final authRequest = AuthenticationRequest((b) => b
        ..userType = UsersSvcUserType.CUSTOMER
        ..email = email
        ..referredBy = referredBy);
      final response = await _onboardUserAuthClient.authenticateUser(
          authRequest: authRequest);
      String authSessionId = response.data!.authSessionId!;
      final isReturningUser = response.data?.isReturning ?? false;
      _prefService.setBool(key: kReturningUser, value: isReturningUser);
      locator<AnalyticsService>().setUserProperties(
          name: "is_returning_user", value: "$isReturningUser");
      return Result.success(data: authSessionId);
    } on DioException catch (e) {
      final dioException = OnboardExceptions.fromDioError(e);
      return Result.failure(error: dioException);
    }
  }

  Future<Result<String?>> resendOtp(
      {required String authSessionId, required OtpPurpose otpPurpose}) async {
    try {
      final otpResend = ResendOtpRequest((b) => b
        ..authSessionId = authSessionId
        ..purpose = otpPurpose);
      final response =
          await _onboardUserAuthClient.resendOtp(otpResend: otpResend);
      return Result.success(data: response.data?.authSessionId);
    } on DioException catch (e) {
      final dioException = OnboardExceptions.fromDioError(e);
      return Result.failure(error: dioException);
    }
  }

  Future<Result<String?>> verifyAuthOTP(
      {required String authSessionId,
      required String otp,
      AddressSignatureBuilder? addressSignatureBuilder}) async {
    try {
      // Clear user backup
      await locator<CloudBackupService>().clearDb();
      final verifyUserOtp = AuthOtpVerificationRequest((b) => b
        ..authSessionId = authSessionId
        ..signature = addressSignatureBuilder
        ..code = otp);
      final response = await _onboardUserAuthClient.verifyMyOtp(
          verifyUserOtp: verifyUserOtp);
      // response.data.data
      String? token = await processAuthOtpResponseDto(response.data);
      await locator<PasskeyService>().getRegisteredPasskeys();
      await locator<UserService>().getUpdatedUserInfo();
      return Result.success(data: token);
    } on DioException catch (e) {
      final dioException = OnboardExceptions.fromDioError(e);
      return Result.failure(error: dioException);
    }
  }

  ///Returns the new auth token
  Future<Result<String?>> refreshAuthToken() async {
    if (_ongoingTokenRefresh != null) {
      // If an API call is in progress, wait for its completion
      return _ongoingTokenRefresh!.future;
    }
    _ongoingTokenRefresh = Completer<Result<String?>>();
    Future<Result<String?>> futureToReturn = _ongoingTokenRefresh!.future;
    try {
      String? refreshToken = await getRefreshToken();
      if (refreshToken == null) {
        _ongoingTokenRefresh!.completeError(Result.failure(
            error: OnboardExceptions.fromErrorMessage("Token not found")));
      } else {
        final response =
            await _onboardUserAuthClient.refreshAuthToken(headers: {
          AuthorizationTokenInjector.authKey: refreshToken,
        });
        final data = response.data;
        final tokenExpiry = data?.tokenExpiry?.millisecondsSinceEpoch;
        await _secureStorageService.write(
            key: authTokenKey, value: data?.token);
        await _secureStorageService.write(
            key: refreshTokenKey, value: data?.refreshToken);
        await _secureStorageService.write(
            key: tokenExpiryKey, value: tokenExpiry?.toString());
        _ongoingTokenRefresh!
            .complete(Result.success(data: response.data?.token));
      }
    } catch (e) {
      if (e is DioException) {
        final dioException = OnboardExceptions.fromDioError(e);
        _ongoingTokenRefresh!.complete(Result.failure(error: dioException));
      } else {
        _ongoingTokenRefresh!.complete(Result.failure(
            error: OnboardExceptions.fromErrorMessage(e.toString())));
      }
    } finally {
      _ongoingTokenRefresh = null;
    }
    return futureToReturn;
  }

  Future logout() async {
    try {
      // Clear the session first to prevent errors from pending requests
      _clearSession();

      final refreshToken =
          await locator<SecureStorageService>().read(key: refreshTokenKey);
      locator<WalletConnectService>().logout();
      locator<AnalyticsService>().logout();
      locator<AppSettingsService>().logout();
      locator<WalletService>().logout();
      locator<TokenService>().clearPreviousBalance();
      locator<PasskeyService>().onLogout();
      locator<KycService>().onLogout();
      await locator<PushNotificationService>().deregisterPushToken();
      if (refreshToken == null) {
        // Shouldn't happen. If it does, assume success
        return const Result.success(data: true);
      }
      var mainDashboardViewModel = locator<MainDashboardViewModel>();
      if (mainDashboardViewModel.disposed) {
        StackedLocator.instance
            .resetLazySingleton(instance: mainDashboardViewModel);
      }
      await _onboardUserAuthClient.logoutUser(headers: {
        AuthorizationTokenInjector.authKey: refreshToken,
      });
    } on DioException catch (_) {}
  }

  /// Clear only token, refresh token and expiry.
  Future onboardLogout() async {
    // Clear the session to prevent errors from pending requests
    _clearSession();

    final secureStorageService = locator<SecureStorageService>();
    await secureStorageService.delete(key: tokenExpiryKey);
    await secureStorageService.delete(key: refreshTokenKey);
    await secureStorageService.delete(key: authTokenKey);
  }

  ///This method checks if the refresh token is close to expiry (expires in less than an hour)
  ///and refreshes it
  Future checkAndRefreshToken() async {
    final secureStorageService = locator<SecureStorageService>();
    final tokenExpiry = await secureStorageService.read(key: tokenExpiryKey);
    final refreshToken = await getRefreshToken();
    int? millisecondsSinceEpoch = int.tryParse(tokenExpiry ?? '');

    if (millisecondsSinceEpoch != null && refreshToken != null) {
      final tokenExpiryTime =
          DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch);
      if (tokenExpiryTime.difference(DateTime.now()).inMinutes <= 60) {
        refreshAuthToken();
      }
    }
  }

  Future<bool> hasTokenExpired() async {
    final secureStorageService = locator<SecureStorageService>();
    final tokenExpiry = await secureStorageService.read(key: tokenExpiryKey);
    int? millisecondsSinceEpoch = int.tryParse(tokenExpiry ?? '');
    if (millisecondsSinceEpoch == null) {
      return true;
    }
    return millisecondsSinceEpoch < DateTime.now().millisecondsSinceEpoch;
  }

  Future<bool?> checkWalletExist({required String walletAddress}) async {
    try {
      final response = await _onboardUserAuthClient.verifyExistingAddress(
          address: walletAddress);
      return response.data?.exists;
    } catch (e) {
      return null;
    }
  }

  Future<Result<String?>> login(
      {required String email,
      required String signature,
      required String walletAddress,
      required String message,
      required int timestamp}) async {
    try {
      AddressSignature addressSignature = AddressSignature((b) => b
        ..walletAddress = walletAddress
        ..signature = signature
        ..message = message
        ..timestamp = timestamp);
      AuthAddressSignature authAddressSignature = AuthAddressSignature((b) => b
        ..email = email
        ..signature = addressSignature.toBuilder());
      final response =
          await getOnboardApiGatewayClient(handleUnauthorisedError: false)
              .getUsersUserauthClient()
              .authenticateWithSignature(
                  authenticationAddressSignature: authAddressSignature);

      String? token = await processAuthOtpResponseDto(response.data);
      return Result.success(data: token);
    } on DioException catch (e) {
      final dioException = OnboardExceptions.fromDioError(e);
      return Result.failure(error: dioException);
    }
  }

  Future<Result<String?>> signUpWithWeb3Auth({
    required String email,
    required String walletAddress,
    required String idToken,
  }) async {
    try {
      AuthWithWeb3Auth web3auth = AuthWithWeb3Auth((b) => b
        ..idToken = idToken
        ..walletAddress = walletAddress
        ..email = email
        ..userType = UsersSvcUserType.CUSTOMER.name);
      final response = await _onboardUserAuthClient
          .authenticateWithWeb3AuthToken(authenticationWeb3Auth: web3auth);
      String? token = await processAuthOtpResponseDto(response.data);
      return Result.success(data: token);
    } on DioException catch (e) {
      final dioException = OnboardExceptions.fromDioError(e);
      return Result.failure(error: dioException);
    }
  }

  Future<String?> processAuthOtpResponseDto(
      AuthOtpResponseDto? response) async {
    String? token = response?.token;
    var userObject = response?.data;
    if (userObject != null) {
      OnboardUser onboardUser = OnboardUser.fromUsersSvcUserDataDto(userObject);
      final analyticsService = locator<AnalyticsService>();
      analyticsService.reportUserProperty(onboardUser);
      await locator<UserService>().saveCurrentUser(onboardUser);
    }
    final tokenExpiry = response?.tokenExpiry?.millisecondsSinceEpoch;
    await _secureStorageService.write(key: authTokenKey, value: token);
    await _secureStorageService.write(
        key: refreshTokenKey, value: response?.refreshToken);
    await _secureStorageService.write(
        key: tokenExpiryKey, value: tokenExpiry?.toString());
    await locator<CloudBackupService>().getCurrentUserBackup();

    // Start a new session when user successfully logs in
    _startNewSession();

    return token;
  }

  Future firebaseAnonymousSignIn() async {
    if (FirebaseAuth.instance.currentUser != null) {
      return;
    }
    await FirebaseAuth.instance.signInAnonymously();
  }

  Future<String?> getAuthenticationOts({required String targetOrigin}) async {
    try {
      final response =
          await _onboardUserAuthClient.getOts(targetOrigin: targetOrigin);
      return response.data?.secret;
    } catch (e) {
      return null;
    }
  }
}
