import 'package:collection/collection.dart';
import 'package:dio/dio.dart';
import 'package:nestcoinco_onboard_api_gateway/nestcoinco_onboard_api_gateway.dart';
import 'package:onboard_wallet/api/api.dart';
import 'package:onboard_wallet/api/onboard_exception.dart';
import 'package:onboard_wallet/app/index.dart';
import 'package:onboard_wallet/models/models.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:stacked/stacked.dart';
import 'package:tuple/tuple.dart';

import '../flavors.dart';

class PartnersService with ListenableServiceMixin {
  final preferredNetworkKey = "com.onboard.wallet.preferred.trading.network";
  final _partnersClient =
      getOnboardApiGateway.getBlockchainGatewayPartnersClient();
  final _accountsClient =
      getOnboardApiGateway.getBlockchainGatewayAccountsClient();

  final _offersTradingWalletProxyClient =
      getOnboardApiGateway.getOffersTradingwalletproxyClient();

  final _blockchainGatewayConfigsClient =
      getOnboardApiGateway.getBlockchainGatewayConfigsClient();

  PreferenceService get _preferenceService => locator<PreferenceService>();

  final ReactiveValue<List<TradingAccount>> _tradingAccounts =
      ReactiveValue(List<TradingAccount>.empty(growable: true));
  final ReactiveValue<List<LocalToken>> _tradingBalances =
      ReactiveValue(List<LocalToken>.empty(growable: true));

  bool get hasTradingAccount => _tradingAccounts.value.isNotEmpty;

  List<BlockchainGatewaySvcAssetDto> supportedAssets = [];

  final ReactiveValue<LocalNetwork?> _selectedNetwork = ReactiveValue(null);

  LocalNetwork? get selectedNetwork => _selectedNetwork.value;

  List<TradingAccount> get allAccounts => _tradingAccounts.value;

  PartnersService() {
    listenToReactiveValues(
        [_tradingAccounts, _tradingBalances, _selectedNetwork]);
  }

  Tuple2<double, double> allTradingWalletBalance() {
    double availableSum = 0;
    double lockedSum = 0;
    final tokens = _tradingBalances.value;
    for (var element in tokens) {
      availableSum = availableSum + element.getFiatAvailableValueInDouble();
      lockedSum = lockedSum + element.getLockedFiatValueInDouble();
    }
    return Tuple2<double, double>(availableSum, lockedSum);
  }

  Tuple2<double, double> tradingWalletNetworkBalance(String networkId) {
    double availableSum = 0;
    double lockedSum = 0;

    final tokens = _tradingBalances.value.where((element) =>
        element.network?.id?.toLowerCase() == networkId.toLowerCase());
    for (var element in tokens) {
      availableSum = availableSum + element.getFiatAvailableValueInDouble();
      lockedSum = lockedSum + element.getLockedFiatValueInDouble();
    }
    return Tuple2<double, double>(availableSum, lockedSum);
  }

  void setCurrentNetwork(LocalNetwork? network) {
    final networkId = network?.networkId;
    _selectedNetwork.value = network;
    if (networkId != null) {
      _preferenceService.setString(key: preferredNetworkKey, value: networkId);
    }
  }

  Future<LocalNetwork?> getPreferredNetwork() async {
    final networkId = _preferenceService.getString(key: preferredNetworkKey);
    final networks =
        locator<NetworkDatabaseService>().exchangeNetworksBox.values;
    return networks.firstWhereOrNull((element) =>
        element.networkId?.toLowerCase() == networkId?.toLowerCase());
  }

  TradingAccount? getAccountForNetwork({String? networkId}) {
    if (networkId == null) return _tradingAccounts.value.firstOrNull;
    final network = networkId == CoreNetworkId.polygonTestnet
        ? "polygon_testnet"
        : networkId;
    return _tradingAccounts.value.firstWhereOrNull((element) =>
        element.coreNetworkId?.toLowerCase() == network.toLowerCase());
  }

  void loadLocalData() async {
    final db = locator<PartnerDatabaseService>();
    _tradingAccounts.value = db.getAllTradingAccounts();
    _tradingBalances.value = db.getAllTradingBalances();
  }

  Future<Result<String?>> createTradingAccount(
      {required String networkId}) async {
    try {
      networkId = F.isStaging ? networkId.replaceAll('-', '_') : networkId;
      final response = await _accountsClient.createEscrowAccount(
          networkId: networkId,
          body: EscrowAccountRequest((b) => b..allowAutoEscrow = true));
      await getPartnerTradingAccount();
      await getPartnerTradingAccountBalance();
      return Result.success(data: response.data?.escrowAddress);
    } on DioException catch (e) {
      final dioException = OnboardExceptions.fromDioError(e);
      return Result.failure(error: dioException);
    }
  }

  Future getPartnerTradingAccount() async {
    final db = locator<PartnerDatabaseService>();
    try {
      final response = await _partnersClient.getPartnerEscrows();
      var accounts = response.data?.accounts.toList();
      if (accounts != null) {
        var dbAccounts =
            accounts.map((e) => TradingAccount.fromEscrow(e)).toList();
        await db.saveTradingAccounts(dbAccounts);
        _tradingAccounts.value = dbAccounts;
        getSupportedMerchantTokens();
      }
    } on DioException catch (e) {
      final dioException = OnboardExceptions.fromDioError(e);
      return Result.failure(error: dioException);
    }
  }

  Future getPartnerTradingAccountBalance() async {
    final rateService = locator<RatesService>();
    final db = locator<PartnerDatabaseService>();
    try {
      final response = await _offersTradingWalletProxyClient
          .getTradingWalletBalancesWithAdsApplied();
      var balances = response.data?.balances.toList();
      if (balances != null) {
        var dbBalances = balances
            .map((e) => LocalToken.fromEscrowBalanceListBalancesInner(e))
            .toList();
        await db.saveTradingBalances(dbBalances);
        _tradingBalances.value = dbBalances;
        await rateService.getRatesForBalances(dbBalances);
      }
    } on DioException catch (e) {
      final dioException = OnboardExceptions.fromDioError(e);
      return Result.failure(error: dioException);
    }
  }

  TradingAccount? tradingAccountForActiveChain() {
    var currentChainNetworkId = selectedNetwork?.networkId;
    return _tradingAccounts.value.firstWhereOrNull((element) {
      return element.networkId?.toLowerCase() ==
          currentChainNetworkId?.toLowerCase();
    });
  }

  TradingAccount? tradingAccountForNetworkId(String? networkId) {
    if (networkId == null) return null;
    return _tradingAccounts.value.firstWhereOrNull((element) {
      return element.networkId?.toLowerCase() == networkId;
    });
  }

  List<LocalNetwork> getTradingAccountNetworks() {
    final networkDbService = locator<NetworkDatabaseService>();
    List<LocalNetwork> networks = [];
    for (TradingAccount account in _tradingAccounts.value) {
      LocalNetwork? network = networkDbService.exchangeNetworksBox.values
          .firstWhereOrNull(
              (element) => element.networkId == account.networkId);
      if (network != null) {
        networks.add(network);
      }
    }
    return networks;
  }

  bool isTradingWallet({String? address, String? networkId}) {
    if (address == null) return false;
    return tradingAccountForNetworkId(networkId)?.address == address;
  }

  List<LocalToken> get tradingBalances => _tradingBalances.value;

  void onNetworkChanged(String networkId) {
    loadLocalData();
    getPartnerTradingAccount();
    getPartnerTradingAccountBalance();
  }

  Future getSupportedMerchantTokens() async {
    try {
      final response = await _blockchainGatewayConfigsClient.getAssetConfigs();
      final assets = response.data?.assets?.toList() ?? [];
      supportedAssets = assets;
    } on DioException catch (e) {
      final dioException = OnboardExceptions.fromDioError(e);
      return Result.failure(error: dioException);
    }
  }

  bool isAssetSupportedByMerchant({
    required String assetSymbol,
    required String networkId,
  }) {
    return supportedAssets.firstWhereOrNull((asset) {
          // First check if the asset symbol matches
          if (asset.symbol.toLowerCase() != assetSymbol.toLowerCase()) {
            return false;
          }

          // Then check if any token in the asset's tokens array matches the networkId and address
          return asset.tokens?.firstWhereOrNull((token) =>
                  token.networkId.toLowerCase() == networkId.toLowerCase()) !=
              null;
        }) !=
        null;
  }
}
