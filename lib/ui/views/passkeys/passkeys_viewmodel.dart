import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:nestcoinco_onboard_api_gateway/nestcoinco_onboard_api_gateway.dart';
import 'package:onboard_wallet/app/index.dart';
import 'package:onboard_wallet/constants/string_constants.dart';
import 'package:onboard_wallet/enums/backup_type.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/manager/toast.dart';
import 'package:onboard_wallet/models/onboard_user/onboard_user.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:onboard_wallet/ui/widgets/widgets.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';

import 'close_passkey_setup_modal.dart';
import 'widgets/widgets.dart';

import 'package:onboard_wallet/constants/analytic_event.dart';
import 'package:onboard_wallet/enums/enums.dart';

class PasskeysViewModel extends ReactiveViewModel {
  final _passkeyService = locator<PasskeyService>();
  final _analyticsService = locator<AnalyticsService>();

  List<PasskeyCredentialInfo> get passkeys =>
      _passkeyService.passkeysList.toList();
  final _navigationService = locator<NavigationService>();
  final _toastService = locator<ToastManager>();
  final _userService = locator<UserService>();

  OnboardUser? get currentUser => _userService.getCurrentUser();
  final preferenceService = locator<PreferenceService>();

  onReady() {
    _passkeyService.getRegisteredPasskeys();
    _analyticsService.logEvent(eventName: AnalyticsEvent.viewCreatePasskey);
  }

  void addPasskey() {
    _analyticsService.logEvent(eventName: AnalyticsEvent.startPasskeyCreation);
    createPasskey(false);
  }

  void addPasskeyFromSecurity() {
    createPasskey(true);
  }

  String getBase64Data(String input) {
    if (input.contains(',')) {
      return input.split(',')[1];
    }
    return input;
  }

  _fetchPasskeys() async {
    if (passkeys.isEmpty) setBusy(true);

    final response = await _passkeyService
        .getRegisteredPasskeys()
        .whenComplete(() => setBusy(false));

    response.when(
        success: (success) {},
        failure: (failure) {
          _toastService.showErrorToast(text: failure.message);
        });
  }

  _completeDeletePasskey(String passkeyId) async {
    EasyLoading.show();

    final result = await _passkeyService
        .deletePasskey(passkeyId: passkeyId)
        .whenComplete(() => EasyLoading.dismiss());

    result.when(success: (success) {
      if (success.statusCode != null && success.statusCode! <= 300) {
        _toastService.showToast(text: S.current.passkeyDeleted);

        setBusy(true); //force the loader on the button
        _fetchPasskeys();
      } else {
        _toastService.showErrorToast(
            text: success.statusMessage ?? S.current.passkeyDeletionError);
      }
    }, failure: (failure) {
      _toastService.showErrorToast(text: failure.message);
    });
  }

  deletePasskey(String passkeyId) {
    final appContext = StackedService.navigatorKey?.currentContext;

    if (appContext != null) {
      ExternalCloseSheet.showModal(
        appContext,
        child: DeletePasskeyConfirmationModal(
          onDeletePasskey: () {
            _navigationService.back();
            _completeDeletePasskey(passkeyId);
          },
        ),
      );
    }
  }

  createPasskey(bool? isSecurity) async {
    if (currentUser != null) {
      EasyLoading.show();
      final response = await _passkeyService.startPasskeyRegistration();
      EasyLoading.dismiss();
      response.when(success: (data) {
        _analyticsService.logEvent(
            eventName: AnalyticsEvent.passkeyCreationSuccess);

        if (isSecurity == true) {
          _navigationService.back();
        } else {
          _navigationService.navigateToEnableNotifsView(
              authType: AuthType.create);
        }
        notifyListeners();
      }, failure: (failure) {
        getLogger(toString()).e(failure.message);
        _analyticsService.logEvent(
            eventName: AnalyticsEvent.passkeyCreationFailure);

        if (failure.message.contains("ExcludeCredentialsCanNotBeRegistered")) {
          _toastService.showErrorToast(text: S.current.passkeyExistOnDevice);
        } else if (failure.message.contains(kInvalidPasskeySession)) {
          final email = _userService.getCurrentUser()?.email;
          if (email != null) {
            _handleReAuth(email);
          } else {
            locator<ToastManager>()
                .showErrorToast(text: S.current.invalidPasskeySession);
          }
        } else {
          _toastService.showErrorToast(text: S.current.passkeyCreatedFailed);
        }
      });
    }
  }

  Future _handleReAuth(String email) async {
    _navigationService.navigateToSetEmailView(
      initialEmail: email,
      onCompleteActionCallback: () {
        _navigationService.popUntil((route) =>
            route.settings.name == Routes.enable2faView || route.isFirst);
        addPasskey();
      },
    );
  }

  onDoThisLater() {
    final appContext = StackedService.navigatorKey?.currentContext;

    if (appContext != null) {
      ExternalCloseSheet.showModal(
        appContext,
        child: ClosePasskeySetupModal(
          onCreatePasskey: () {
            _navigationService.back();
          },
          onTakeRisk: () {
            _analyticsService.logEvent(
                eventName: AnalyticsEvent.skipPasskeyCreation);
            if (preferenceService.getBool(key: kReturningUser) ?? false) {
              _navigationService.clearStackAndShow(Routes.homeView);
            } else {
              _navigationService.navigateToEnableNotifsView(
                  authType: AuthType.create);
            }
          },
        ),
      );
    }
  }

  @override
  List<ListenableServiceMixin> get listenableServices => [_passkeyService];
}
