import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:nestcoinco_onboard_api_gateway/nestcoinco_onboard_api_gateway.dart';
import 'package:onboard_wallet/app/index.dart';
import 'package:onboard_wallet/constants/string_constants.dart';
import 'package:onboard_wallet/enums/enums.dart';
import 'package:onboard_wallet/manager/toast.dart';
import 'package:onboard_wallet/models/onboard_user/onboard_user.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:onboard_wallet/ui/views/passcode/passcode_viewmodel.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';
import '../../../constants/analytic_event.dart';

class VerifyEmailViewModel extends BaseViewModel {
  String emailAddress;
  final _navigationService = locator<NavigationService>();
  final _authenticationService = locator<AuthenticationService>();
  final _userService = locator<UserService>();
  final _analyticsService = locator<AnalyticsService>();
  final _toastManager = locator<ToastManager>();
  final _prefsService = locator<PreferenceService>();
  final VoidCallback? onCompleteActionCallback;
  String authSessionId;
  String errorMessage = '';
  String? _code;
  Timer? _countdownTimer;
  Duration timerDuration = const Duration(seconds: 60);

  bool get isTimerActive => _countdownTimer?.isActive ?? false;

  String get timerDigit =>
      '00:${timerDuration.inSeconds.toString().padLeft(2, "0")}';

  VerifyEmailViewModel(
      this.emailAddress, this.authSessionId, this.onCompleteActionCallback);

  void onCodeChanged(String value) {
    _code = value;
  }

  onModelReady() {
    _startTimer();
  }

  Future changeEmailPressed() async {
    _navigationService.back();
  }

  void _startTimer() {
    timerDuration = const Duration(seconds: 60);
    _countdownTimer = Timer.periodic(
      const Duration(seconds: 1),
      (_) => _setCountDown(),
    );
  }

  void _setCountDown() {
    final seconds = timerDuration.inSeconds - 1;
    if (seconds < 0) {
      _countdownTimer!.cancel();
    } else {
      timerDuration = Duration(seconds: seconds);
    }
    notifyListeners();
  }

  Future resendOtp() async {
    EasyLoading.show();
    locator<AnalyticsService>()
        .logEvent(eventName: AnalyticsEvent.clicksResendOtp);
    final response = await _authenticationService.resendOtp(
        authSessionId: authSessionId, otpPurpose: OtpPurpose.SIGN_IN);

    response.when(success: (session) {
      EasyLoading.dismiss();
      if (session != null) {
        authSessionId = session;
      }
      _startTimer();
    }, failure: (failure) {
      if (failure.message
          .toLowerCase()
          .contains(kAuthSessionNotFound.toLowerCase())) {
        _navigationService.back();
      }
      EasyLoading.showError(failure.message);
    });
  }

  onVerifySuccess({String? token, required String email}) async {
    if (token == null) {
      _toastManager.showErrorToast(
          text: 'Authentication failed. Please try again.');

      return;
    }
    final user = _userService.getCurrentUser();
    if (user == null) {
      _toastManager.showErrorToast(
          text: 'Unable to retrieve your user information at the moment.');
      return;
    }

    _analyticsService.logEvent(eventName: AnalyticsEvent.verifiesOtp);
    _analyticsService.setUserProperties(
        name: "has_verified_address",
        value: "${user.hasVerifiedWalletAddress}");

    //Check if its returning user or not and log event
    final isReturningUser = _prefsService.getBool(key: kReturningUser) ?? false;
    await _prefsService.setBool(
        key: kShowOnBoardingWelcome, value: isReturningUser == false);
    if (isReturningUser) {
      _analyticsService.logEvent(eventName: AnalyticsEvent.login);
    } else {
      _analyticsService.logEvent(eventName: AnalyticsEvent.newUserSignUp);
    }

    _checkPinSetupStatus(isReturningUser, user);
  }

  void onCompleteAction(
    String code,
  ) {
    runBusyFuture(_verifyOTP(authSessionId: authSessionId, code: code));
  }

  Future _verifyOTP(
      {required String code, required String authSessionId}) async {
    final result = await _authenticationService.verifyAuthOTP(
      authSessionId: authSessionId,
      otp: code,
    );
    await result.when(
      success: (token) async {
        await locator<PasskeyService>().getRegisteredPasskeys();
        if (onCompleteActionCallback != null) {
          onCompleteActionCallback?.call();
        } else {
          await onVerifySuccess(email: emailAddress, token: token);
        }
      },
      failure: (failure) {
        locator<ToastManager>().showErrorToast(text: failure.message);
        setError(true);
        errorMessage = failure.message;
      },
    );
  }

  _checkPinSetupStatus(bool isReturningUser, OnboardUser user) async {
    try {
      final passcodeService = locator<PasscodeService>();

      final hasOnlinePinSetup = passcodeService.hasOnlinePinSetup();
      final hasLocalPinSetup = passcodeService.hasLocalPinSetup();

      if (hasOnlinePinSetup && hasLocalPinSetup) {
        // User has pin setup local and online
        _onPinSetupExist(user);
      } else {
        EasyLoading.show();

        // User hasn't done online pin setup on device, confirm with backend
        final result = await passcodeService.checkPinExists();

        result.when(success: (status) {
          if (status?.success == true) {
            // User has an online PIN but not set up locally
            // Navigate to verify access screen where they'll enter their PIN
            // Once verified with backend, it will be saved locally
            _onPinSetupDoNotExist(user, setupLocal: true);
          } else {
            // User has no online PIN - need to create a new one
            _onPinSetupDoNotExist(user, setupLocal: false);
          }
        }, failure: (error) {
          // Error checking PIN existence - default to creation flow
          _onPinSetupDoNotExist(user, setupLocal: false);
          getLogger(toString()).e(error.message);
        });
      }
    } catch (e) {
      getLogger(toString()).e(e.toString());
      // default to creation flow
      _onPinSetupDoNotExist(user, setupLocal: false);
    } finally {
      EasyLoading.dismiss();
    }
  }

  _onPinSetupExist(OnboardUser user) {
    final prefService = locator<PreferenceService>();
    final isReturningUser = prefService.getBool(key: kReturningUser);

    if (isReturningUser == true) {
      _navigationService.clearStackAndShow(Routes.homeView);
      return;
    }

    // For new users, check country settings
    if (user.country != null) {
      if (locator<PasskeyService>().passkeysList.isEmpty) {
        _navigationService.navigateToCreatePasskeyView();
      } else {
        _navigationService.navigateToEnableNotifsView(
            authType: AuthType.create);
      }
    } else {
      _navigationService.navigateToCountryOfResidenceAuthView(
        onContinue: (country) {
          _userService.saveUserCountryFeature(country);
          if (locator<PasskeyService>().passkeysList.isEmpty) {
            _navigationService.navigateToCreatePasskeyView();
          } else {
            _navigationService.navigateToEnableNotifsView(
                authType: AuthType.create);
          }
        },
        progress: 0.5,
      );
    }
  }

  // Handle navigation when PIN setup doesn't exist
  void _onPinSetupDoNotExist(OnboardUser user, {bool setupLocal = false}) {
    if (user.country != null) {
      _navigationService.navigateToPasscodeView(
        // If setupLocal is true, we want to verify the existing online PIN
        // If false, we need to create a new PIN
        passcodeType:
            setupLocal ? PasscodeType.verifyAccess : PasscodeType.create,
        passCodeEntryPoint: PassCodeEntryPoint.secureWallet,
      );
    } else {
      _navigationService.navigateToCountryOfResidenceAuthView(
        onContinue: (country) {
          _userService.saveUserCountryFeature(country);
          _navigationService.navigateToPasscodeView(
            passcodeType:
                setupLocal ? PasscodeType.verifyAccess : PasscodeType.create,
            passCodeEntryPoint: PassCodeEntryPoint.secureWallet,
          );
        },
        progress: 0.5,
      );
    }
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    super.dispose();
  }
}
