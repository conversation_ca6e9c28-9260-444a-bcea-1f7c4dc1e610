import 'package:flutter/material.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/models/models.dart';
import 'package:onboard_wallet/models/token/wallet_type.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';
import 'package:onboard_wallet/ui/views/crypto_cash_amount_entry/widgets/cash_crypto_input_field.dart';
import 'package:onboard_wallet/ui/views/transfer/internal/internal_amount_entry_view/subviews/continue_button.dart';
import 'package:onboard_wallet/ui/views/transfer/internal/internal_amount_entry_view/subviews/wallet_toggle_box.dart';
import 'package:onboard_wallet/ui/widgets/container/error_pill.dart';
import 'package:onboard_wallet/ui/widgets/onbw_scroll_view.dart';
import 'package:stacked/stacked.dart';

import '../../../../common/ui_helpers.dart';
import '../../../../widgets/widgets.dart';
import 'internal_transfer_amount_view_model.dart';

class InternalTransferAmountView
    extends StackedView<InternalTransferAmountViewModel> {
  const InternalTransferAmountView({
    super.key,
    required this.from,
    this.to,
    required this.localToken,
    required this.tokenNetworkBalance,
  });
  final WalletType from;
  final WalletType? to;
  final LocalAddressTokenBalance localToken;
  final LocalTokenNetworkBalance tokenNetworkBalance;

  @override
  Widget builder(BuildContext context,
      InternalTransferAmountViewModel viewModel, Widget? child) {
    return Scaffold(
      backgroundColor: Grey.grey25,
      resizeToAvoidBottomInset: true,
      appBar: TransparentAppBar(
        centerTitle: true,
        title: Text(
          S.current.transferWithTokenSymbol(viewModel.localToken.symbol ?? ''),
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Grey.grey90,
              ),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Stack(
            alignment: Alignment.bottomCenter,
            children: [
              Container(
                alignment: Alignment.topCenter,
                child: OnbwScrollView(
                  children: [
                    const verticalSpace(20),
                    WalletToggleBox(
                      fromWalletType: viewModel.transferModel.from,
                      toWalletType: viewModel.transferModel.to,
                      onFromWalletTap: () => viewModel.showFromWallets(),
                      onToWalletTap: () => viewModel.showToWallets(),
                      toggleWallet: () => viewModel.toggleWallet(),
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                    CashCryptoInputField(
                      inputFieldCrossAxisAlignment: CrossAxisAlignment.start,
                      textEditingController:
                          viewModel.tokenTextEditingController,
                      label: S.current.amount,
                      isEnabled: true,
                      currency: viewModel.currencyName,
                      inputFormatters: viewModel.inputFormatters,
                      onChanged: (value) => viewModel.onTokenTextChanged(value),
                      onCurrencyTapped: viewModel.switchInputMode,
                      bottomText:
                          '${S.current.available}: ${viewModel.formattedBalance ?? ''}',
                      balanceColor: viewModel.errorMessage?.isNotEmpty == true
                          ? ErrorColor.error500
                          : Grey.grey400,
                      onMax: viewModel.setMax,
                    ),
                    const verticalSpace(12),
                    Visibility(
                      visible: viewModel.isCardBelowMinBalanceError,
                      child: ErrorPill(message: viewModel.errorMessage ?? ''),
                    ),
                    const SizedBox(
                      height: 100,
                    ),
                    verticalSpace(
                      MediaQuery.of(context).viewInsets.bottom,
                    ),
                    // if (viewModel.isAssetSupported == false) ...[
                    //   ErrorPill(
                    //     message: S.current.assetNotSupportedByMerchant,
                    //   ),
                    // ],
                  ],
                ),
              ),
              const ContinueButton(),
            ],
          ),
        ),
      ),
    );
  }

  @override
  InternalTransferAmountViewModel viewModelBuilder(BuildContext context) =>
      InternalTransferAmountViewModel(
          localToken, tokenNetworkBalance, null, null);

  @override
  void onViewModelReady(InternalTransferAmountViewModel viewModel) =>
      viewModel.onModelReady(from: from, to: to);
}
