import 'dart:math';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:nestcoinco_onboard_api_gateway/nestcoinco_onboard_api_gateway.dart';
import 'package:onboard_wallet/app/index.dart';
import 'package:onboard_wallet/constants/hive_box_name.dart';
import 'package:onboard_wallet/enums/card_fund_options_enum.dart';
import 'package:onboard_wallet/enums/card_funding_type.dart';
import 'package:onboard_wallet/extensions/extensions.dart';
import 'package:onboard_wallet/manager/manager.dart';
import 'package:onboard_wallet/models/card/local_card_account/index.dart';
import 'package:onboard_wallet/models/card/local_card_transaction_config/local_card_transaction_config.dart';
import 'package:onboard_wallet/models/models.dart';
import 'package:onboard_wallet/models/token/wallet_type.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:onboard_wallet/ui/views/cards/fund/confirm_card_purchase/confirm_card_purchase_view.dart';
import 'package:onboard_wallet/ui/views/cards/withdraw/withdraw_via_onboard_wallet/widgets/wallet_selection_sheet.dart';
import 'package:onboard_wallet/ui/views/transfer/internal/data/data.dart';
import 'package:onboard_wallet/ui/views/transfer/internal/data/onboard_wallet_type.dart';
import 'package:onboard_wallet/ui/views/transfer/internal/data/spot_wallet.dart';
import 'package:onboard_wallet/ui/views/transfer/internal/data/trading_wallet.dart';
import 'package:onboard_wallet/ui/views/transfer/internal/data/virtual_card_wallet.dart';
import 'package:onboard_wallet/ui/widgets/bottomsheet/external_close_sheet.dart';
import 'package:onboard_wallet/utils/input_formatters.dart';
import 'package:stacked_services/stacked_services.dart';

import '../../../../../generated/l10n.dart';
import '../../../cards/withdraw/withdrawal_preview/card_withdrawal_preview_view.dart';
import '../../../send/data/send_asset_model.dart';
import '../../base_view_model/transfer_base_viewmodel.dart';

class InternalTransferAmountViewModel extends TransferBaseViewModel {
  final _navigationService = locator<NavigationService>();
  final _networkService = locator<NetworkService>();
  final _cardService = locator<CardService>();
  final _walletService = locator<WalletService>();
  final _toastManager = locator<ToastManager>();
  final _partnerService = locator<PartnersService>();
  final _tokenService = locator<TokenService>();

  String get networkSymbol => _networkService.currentChain?.symbol ?? '';

  @override
  List<TextInputFormatter> get inputFormatters {
    if (fromCard) {
      VirtualCardWallet virtualCardWallet =
          transferModel.from as VirtualCardWallet;
      String? cardCurrencySymbol =
          virtualCardWallet.cardAccount.cardUsageTerms?.cardCurrencySymbol ??
              "";
      return [
        AnyDpDecimalTextInputFormatter(leadingSymbol: cardCurrencySymbol)
      ];
    }
    return inputModeIsFiat ? fiatInputFormatters : tokenInputFormatters;
  }

  Box<TradingAccount> get tradingAccountsBox =>
      Hive.box(HiveBoxName.tradingAccountsBoxName);

  bool get isToggleDisabled =>
      fromCard && transferModel.to.walletType == WalletType.trading;

  bool get toCard => transferModel.to.walletType == WalletType.virtualCard;

  InternalTransferModel transferModel = InternalTransferModel();

  bool get isAssetSupported {
    if (transferModel.to.walletType != WalletType.trading) return true;
    final result = _partnerService.isAssetSupportedByMerchant(
      assetSymbol: localToken.symbol ?? '',
      networkId: tokenNetworkBalance.coreNetworkId ?? '',
    );
    return result;
  }

  bool get _hasTradingWalletOnActiveChain {
    return _partnerService
            .tradingAccountForNetworkId(tokenNetworkBalance.exchangeNetworkId)
            ?.address !=
        null;
  }

  LocalCardTransactionConfig? get cardTransactionConfig {
    Box<LocalCardTransactionConfig> box = fromCard
        ? _cardService.cardWithdrawalConfigBox
        : _cardService.cardFundingConfigBox;

    return box.values.firstOrNull;
  }

  double? get _cardBalance {
    if (fromCard) {
      final virtualCardWallet = transferModel.from as VirtualCardWallet;
      return virtualCardWallet.cardAccount.card?.balance?.toDouble();
    } else if (toCard) {
      final virtualCardWallet = transferModel.to as VirtualCardWallet;
      return virtualCardWallet.cardAccount.card?.balance?.toDouble();
    }
    return null;
  }

  @override
  double? get balanceAsDouble {
    if (fromCard) {
      return _cardBalance;
    } else if (inputModeIsFiat) {
      return fiatBalance;
    } else {
      return cryptoBalance;
    }
  }

  @override
  String? get formattedBalance {
    if (fromCard) {
      final virtualCardWallet = transferModel.from as VirtualCardWallet;
      final cardBalance =
          virtualCardWallet.cardAccount.card?.balance?.toDouble();
      final cardCurrency = virtualCardWallet.cardAccount.card?.currency;
      return "${cardBalance?.currencyFormat(symbol: "")} ${cardCurrency ?? ""}";
    } else {
      return inputMode == InputMode.crypto
          ? '${balanceAsDouble?.currencyFormat(decimalDigits: 4, symbol: "").removeTrailingZero()} ${localToken.symbol}'
          : getFiatValue();
    }
  }

  @override
  String? get currencyName {
    if (localToken.price() == null || fromCard) {
      return null;
    }
    if (inputModeIsFiat) {
      return localToken.symbol;
    } else {
      return inputCurrency.name;
    }
  }

  bool get fromCard => transferModel.from is VirtualCardWallet;

  InternalTransferAmountViewModel(
    super.localToken,
    super.tokenNetworkBalance,
    super.localNFTBrief,
    super.localNFTData,
  );

  bool buttonEnabled = false;
  String? errorMessage;

  bool get isCardBelowMinBalanceError {
    if (errorMessage == null) return false;
    final RegExp pattern = RegExp(
        r"^Withdrawal will set card below the min\. balance of \$\d+\.\d{2}$");

    final match = pattern.hasMatch(errorMessage!);
    return match;
  }

  _reset() {
    tokenTextEditingController.clear();
    errorMessage = null;
    onTokenTextChanged(tokenTextEditingController.text);
  }

  bool _isValidAmount() {
    double? valueAsDouble = double.tryParse(amount ?? '');
    double? balance = balanceAsDouble;
    if (valueAsDouble == null || balance == null) {
      return false;
    }
    if (valueAsDouble > balance) {
      return false;
    }
    return true;
  }

  num? get withdrawalProcessingFee {
    if (fromCard) {
      VirtualCardWallet wallet = transferModel.from as VirtualCardWallet;
      double? amountAsDouble = double.tryParse(tokenTextEditingController.text
          .removeSymbol(
              wallet.cardAccount.cardUsageTerms?.cardCurrencySymbol ?? "")
          .removeSymbol(inputCurrency.symbol)
          .removeCommas());
      amountAsDouble = inputMode == InputMode.crypto
          ? convertTokenAmountToFiat(amount) ?? amountAsDouble
          : amountAsDouble;

      if (amountAsDouble == null) {
        return null;
      }

      final feePercent = withdrawalCryptoRateInfo?.processingFeePercent ?? 0;

      final finalFee = ((amountAsDouble) * (feePercent / 100)) +
          (withdrawalCryptoRateInfo?.processingFee ?? 0);

      return max(finalFee, 0);
    } else {
      return null;
    }
  }

  @override
  onTokenTextChanged(String? value) {
    amount = value
        ?.removeCommas()
        .removeSymbol(inputCurrency.symbol)
        .removeSymbol(localToken.symbol ?? '');

    if (value?.isEmpty == true) {
      buttonEnabled = false;
      errorMessage = null;
      notifyListeners();
      return;
    }
    num valueAsDouble = num.tryParse(amount ?? '') ?? 0;

    if (fromCard && toCard == false && inputMode == InputMode.fiat) {
      valueAsDouble = valueAsDouble * (withdrawalCryptoRateInfo?.rate ?? 1);
    } else if (toCard && fromCard == false && inputMode == InputMode.fiat) {
      valueAsDouble = valueAsDouble * (fundingCryptoRateInfo?.rate ?? 1);
    }

    num? balance = fromCard ? _cardBalance : balanceAsDouble;
    if (balance == null) {
      buttonEnabled = false;
      errorMessage = S.current.invalidAmount;
      notifyListeners();
      return;
    }

    if (valueAsDouble > balance) {
      buttonEnabled = false;
      errorMessage = S.current.insufficientBalance;
      notifyListeners();
      return;
    }

    if (valueAsDouble <= 0) {
      buttonEnabled = false;
      errorMessage = null;
      notifyListeners();
      return;
    }

    if (toCard) {
      valueAsDouble = inputMode == InputMode.crypto
          ? convertTokenAmountToFiat(amount) ?? valueAsDouble
          : valueAsDouble;
    }

    bool belowMinTransactionAmount = false;
    bool aboveMaxTransactionAmount = false;
    if (fromCard) {
      num? minAmount = (transferModel.from as VirtualCardWallet)
          .cardAccount
          .cardUsageTerms
          ?.fundingLimit
          ?.minAmount;

      num? maxAmount = (transferModel.from as VirtualCardWallet)
          .cardAccount
          .cardUsageTerms
          ?.fundingLimit
          ?.maxAmount;

      belowMinTransactionAmount = minAmount == null
          ? false
          : valueAsDouble != 0 && valueAsDouble < minAmount;
      aboveMaxTransactionAmount =
          maxAmount == null ? false : valueAsDouble > maxAmount;

      if (belowMinTransactionAmount || aboveMaxTransactionAmount) {
        buttonEnabled = false;
        if (belowMinTransactionAmount) {
          errorMessage = S.current.fundingAmountTooLow;
        } else if (aboveMaxTransactionAmount) {
          errorMessage = S.current.fundingAmountTooHigh;
        }
        notifyListeners();
        return;
      }
    }

    if (toCard) {
      num? minAmount = fundingCryptoRateInfo?.minAmount ??
          (transferModel.to as VirtualCardWallet)
              .cardAccount
              .cardUsageTerms
              ?.fundingLimit
              ?.minAmount;

      num? maxAmount = fundingCryptoRateInfo?.maxAmount ??
          (transferModel.to as VirtualCardWallet)
              .cardAccount
              .cardUsageTerms
              ?.fundingLimit
              ?.maxAmount;

      belowMinTransactionAmount = minAmount == null
          ? false
          : valueAsDouble != 0 && valueAsDouble < minAmount;
      aboveMaxTransactionAmount =
          maxAmount == null ? false : valueAsDouble > maxAmount;

      if (belowMinTransactionAmount || aboveMaxTransactionAmount) {
        buttonEnabled = false;
        if (belowMinTransactionAmount) {
          errorMessage = S.current.fundingAmountTooLow;
        } else if (aboveMaxTransactionAmount) {
          errorMessage = S.current.fundingAmountTooHigh;
        }
        notifyListeners();
        return;
      }
    }

    if (fromCard) {
      num? minAmount = withdrawalCryptoRateInfo?.minAmount ??
          (transferModel.to as VirtualCardWallet)
              .cardAccount
              .cardUsageTerms
              ?.withdrawalLimit
              ?.minAmount;

      num? maxAmount = withdrawalCryptoRateInfo?.maxAmount ??
          (transferModel.to as VirtualCardWallet)
              .cardAccount
              .cardUsageTerms
              ?.withdrawalLimit
              ?.maxAmount;

      belowMinTransactionAmount = minAmount == null
          ? false
          : valueAsDouble != 0 && valueAsDouble < minAmount;
      aboveMaxTransactionAmount =
          maxAmount == null ? false : valueAsDouble > maxAmount;

      if (belowMinTransactionAmount == false &&
          withdrawalProcessingFee != null) {
        belowMinTransactionAmount =
            (valueAsDouble - withdrawalProcessingFee!) <= 0;
      }

      if (belowMinTransactionAmount || aboveMaxTransactionAmount) {
        buttonEnabled = false;
        if (belowMinTransactionAmount) {
          errorMessage = S.current.fundingAmountTooLow;
        } else if (aboveMaxTransactionAmount) {
          errorMessage = S.current.fundingAmountTooHigh;
        }
        notifyListeners();
        return;
      }

      VirtualCardWallet wallet = transferModel.from as VirtualCardWallet;
      LocalCardAccount cardAccount = wallet.cardAccount;
      num? cardMinBalance = cardAccount.cardUsageTerms?.minimumBalance;
      String cardCurrencySymbol =
          cardAccount.cardUsageTerms?.cardCurrencySymbol ?? "";

      if (cardMinBalance != null && _cardBalance != null) {
        double remainingBalance = _cardBalance! - valueAsDouble;
        if (remainingBalance < cardMinBalance) {
          errorMessage = S.current.cardWithdrawalMinimumBalanceWarning(
            cardMinBalance
                .toDouble()
                .currencyFormat(symbol: cardCurrencySymbol),
          );
          buttonEnabled = false;
          notifyListeners();

          return;
        }
      }
    }

    errorMessage = null;
    buttonEnabled = true;
    notifyListeners();
  }

  void onModelReady({required WalletType from, WalletType? to}) {
    _setFromWallet(from);
    transferModel.to = _getToWallet(
      from: from,
      to: to,
    );
    _updateTokenCodeForVirtualCard();

    if (fromCard || toCard) {
      LocalCardAccount? account;
      if (fromCard) {
        account = (transferModel.from as VirtualCardWallet).cardAccount;
      } else {
        account = (transferModel.to as VirtualCardWallet).cardAccount;
      }
      _getCardFundingCryptoRate(account);
      _getCardWithdrawalCryptoRate(account);
    }

    if (transferModel.to.walletType == WalletType.virtualCard) {
      switchInputMode();
    }
    _listenToCardAccountUpdate();
  }

  void _setFromWallet(WalletType from) {
    final activeCardAccounts = _cardService.activeCardAccounts;
    activeCardAccounts
        .sort((a, b) => _cardService.sortCardAccountByPosition(a: a, b: b));
    if (from == WalletType.virtualCard && activeCardAccounts.isNotEmpty) {
      transferModel.from = VirtualCardWallet(activeCardAccounts.first);
    } else if (from == WalletType.trading) {
      transferModel.from = MerchantWallet();
    } else {
      transferModel.from = SpotWallet();
    }
  }

  OnboardWalletType _getToWallet({
    required WalletType from,
    WalletType? to,
  }) {
    final activeCardAccounts = _cardService.transferableCardAccounts;
    activeCardAccounts
        .sort((a, b) => _cardService.sortCardAccountByPosition(a: a, b: b));
    if (to == WalletType.trading && from != WalletType.trading) {
      return MerchantWallet();
    } else if (to == WalletType.virtualCard && activeCardAccounts.isNotEmpty) {
      if (transferModel.from.walletType.isCard &&
          activeCardAccounts.length > 1) {
        final fromCardAccount = transferModel.from as VirtualCardWallet;
        return VirtualCardWallet(activeCardAccounts
            .firstWhere((e) => fromCardAccount.cardAccount != e));
      } else {
        return VirtualCardWallet(activeCardAccounts.first);
      }
    } else if (to == WalletType.spot) {
      return SpotWallet();
    } else if (transferModel.from is SpotWallet &&
        _hasTradingWalletOnActiveChain) {
      return MerchantWallet();
    } else if (transferModel.from is SpotWallet &&
        activeCardAccounts.isNotEmpty) {
      return VirtualCardWallet(activeCardAccounts.first);
    } else {
      return SpotWallet();
    }
  }

  toggleWallet() {
    if (isToggleDisabled) return;
    var type = transferModel.from;
    transferModel.from = transferModel.to;
    transferModel.to = type;
    // Find equivalent of current token in current 'from' wallet
    if (transferModel.from.walletType.isCard) {
      inputMode = InputMode.fiat;
    }
    _updateToken();
    _reset();
  }

  _updateToken() {
    if (fromCard || toCard) {
      _updateTokenCodeForVirtualCard();
    } else if (transferModel.from.walletType == WalletType.trading) {
      _updateTradingToken();
    } else if (transferModel.from.walletType == WalletType.spot) {
      _updateSpotToken();
    }
    notifyListeners();
  }

  @override
  String? getMaxValue() {
    if (fromCard) {
      final virtualCardWallet = transferModel.from as VirtualCardWallet;
      String cardCurrencySymbol =
          virtualCardWallet.cardAccount.cardUsageTerms?.cardCurrencySymbol ??
              "";
      return _cardBalance?.currencyFormat(symbol: cardCurrencySymbol);
    }
    switch (inputMode) {
      case InputMode.fiat:
        return balanceAsDouble?.currencyFormat();
      case InputMode.crypto:
        final value =
            balanceAsDouble?.truncateToDecimalPlaces(4).removeTrailingZero();
        return '$value ${localToken.symbol ?? ''}';
    }
  }

  _updateTradingToken() {
    final tradingToken =
        _partnerService.tradingBalances.firstWhereOrNull((element) {
      final tokenNetworkId = findKeyByValue(CoreNetworkId.exchangeNetworkIdMap,
          tokenNetworkBalance.id?.toLowerCase());
      final elementNetworkId = findKeyByValue(
          CoreNetworkId.exchangeNetworkIdMap,
          element.network?.id?.toLowerCase());
      bool sameAddress = tokenNetworkBalance.contractAddress?.toLowerCase() ==
          element.contractAddress?.toLowerCase();
      bool sameId =
          tokenNetworkId?.toLowerCase() == elementNetworkId?.toLowerCase();
      return sameId && sameAddress;
    });
    if (tradingToken == null) {
      tokenNetworkBalance.balance = '0';
      localToken.total = '0';
    } else {
      localToken = LocalAddressTokenBalance.fromLocalToken(tradingToken);
      tokenNetworkBalance = localToken.networks!.first;
    }
  }

  _updateSpotToken() {
    final networkId = tokenNetworkBalance.coreNetworkId;

    var token = _tokenService.getTokenInUserWalletByContractAddressAndNetwork(
      tokenContractAddress: tokenNetworkBalance.contractAddress,
      networkId: networkId,
    );

    final balance = token?.networks?.firstWhereOrNull(
        (e) => e.id?.toLowerCase() == networkId?.toLowerCase());

    if (token != null) {
      localToken = token;
    } else {
      localToken.total = '0';
    }
    if (balance != null) {
      tokenNetworkBalance = balance;
    } else {
      tokenNetworkBalance.balance = '0';
    }
  }

  void onContinuePressed() {
    if (!buttonEnabled) return;
    if (fromCard) {
      final fromCardAccount =
          (transferModel.from as VirtualCardWallet).cardAccount;
      bool accountIsActive = fromCardAccount.localCardAccountStatus ==
              LocalCardAccountStatus.active &&
          fromCardAccount.card?.status == LocalCardStatus.active;
      if (accountIsActive == false) {
        _routeToActivateAccountScreen(fromCardAccount);
        return;
      }
    }
    if (toCard) {
      final toCardAccount = (transferModel.to as VirtualCardWallet).cardAccount;

      bool accountIsActive = toCardAccount.localCardAccountStatus ==
              LocalCardAccountStatus.active &&
          toCardAccount.card?.status == LocalCardStatus.active;
      if (accountIsActive == false) {
        _routeToActivateAccountScreen(toCardAccount);
        return;
      }
    }
    if (fromCard && toCard) {
      final fromCardAccount =
          (transferModel.from as VirtualCardWallet).cardAccount;
      final toCardAccount = (transferModel.to as VirtualCardWallet).cardAccount;
      if (toCardAccount.cardCurrency != fromCardAccount.cardCurrency) {
        _toastManager.showErrorToast(
            text: "Please ensure cards are in the same currency");
        return;
      }
      final amountAsNum = num.tryParse(amount ?? "");
      if (amountAsNum == null) return;

      _navigationService.navigateToCardTransferPreviewView(
        fromCardAccount: fromCardAccount,
        toCardAccount: toCardAccount,
        amount: amountAsNum,
      );
    } else if (fromCard) {
      VirtualCardWallet virtualCardWallet =
          transferModel.from as VirtualCardWallet;
      _sendFromCard(virtualCardWallet.cardAccount);
    } else if (toCard) {
      VirtualCardWallet virtualCardWallet =
          transferModel.to as VirtualCardWallet;
      _sendToCard(virtualCardWallet.cardAccount);
    } else {
      if (!_isValidAmount()) return;
      _fromSpotOrTradingWallet();
    }
  }

  _fromSpotOrTradingWallet() {
    if (amountInToken == null) return;
    transferModel.amount = amountInToken!;
    transferModel.token = localToken;
    String? spotWalletAddress = _walletService.getWalletAddress;
    String? networkId =
        CoreNetworkId.exchangeNetworkIdMap[tokenNetworkBalance.id] ??
            tokenNetworkBalance.id;
    String? tradingWalletAddress =
        _partnerService.tradingAccountForNetworkId(networkId)?.address;
    if (spotWalletAddress == null || tradingWalletAddress == null) {
      return;
    }
    transferModel.tokenNetwork = tokenNetworkBalance;
    if (transferModel.from.walletType == WalletType.spot) {
      transferModel.fromAddress = spotWalletAddress;
      transferModel.toAddress = tradingWalletAddress;
    } else {
      transferModel.fromAddress = tradingWalletAddress;
      transferModel.toAddress = spotWalletAddress;
    }

    SendAssetModel sendAssetModel =
        SendAssetModel.fromInternalTransferModel(transferModel);

    _navigationService.navigateToTransferPreviewView(
      transferModel: sendAssetModel,
    );
  }

  @override
  void dispose() {
    tokenTextEditingController.dispose();
    super.dispose();
  }

  void showFromWallets() {
    List<OnboardWalletType> wallets = [];

    ///only show trading wallets when the receiving wallet is not a virtual card
    ///the user has trading wallet on the active chain.
    ///and the receiving wallet is not a trading wallet.
    if (transferModel.to.walletType != WalletType.trading &&
        _hasTradingWalletOnActiveChain &&
        transferModel.to.walletType != WalletType.virtualCard) {
      wallets.add(MerchantWallet());
    }

    ///if the receiving wallet is card, show spot wallet if the token is supported for card funding.]
    ///else if the receiving wallet is trading wallet show spot wallet.
    if (transferModel.to.walletType.isCard && _canFundCardWithToken) {
      wallets.add(SpotWallet());
    } else if (transferModel.to.walletType.isTrading) {
      wallets.add(SpotWallet());
    }

    /// run if the user has active cards
    final activeCards = _cardService.transferableCardAccounts;

    if (activeCards.isNotEmpty && (_canWithdrawFromCardToToken || toCard)) {
      for (var e in activeCards) {
        ///if the receiving wallet is a card, show OTHER cards of the SAME CURRENCY.
        if (toCard) {
          final toCardAccount =
              (transferModel.to as VirtualCardWallet).cardAccount;
          if (toCardAccount != e) {
            wallets.add(VirtualCardWallet(e));
          }
        } else if (_canWithdrawFromCardToToken) {
          ///otherwise only show card if the user can withdraw from cards in that token.
          wallets.add(VirtualCardWallet(e));
        }
      }
    }

    BuildContext? context = StackedService.navigatorKey?.currentContext;
    if (context == null) return;
    ExternalCloseSheet.showModal(
      context,
      child: WalletSelectionSheet(
        title: S.current.sendFrom,
        selectedWallet: transferModel.from,
        walletList: wallets,
        onSelectWallet: onSelectFromWallet,
      ),
    );
  }

  bool tokenSupportedByTradingWallet() {
    final configService = locator<ConfigService>();
    final exchangeTokens = configService.getActiveNetworkTokensAndMerge(
      networkId: CoreNetworkId.exchangeNetworkIdMap[tokenNetworkBalance.id] ??
          tokenNetworkBalance.id,
    );
    return exchangeTokens.firstWhereOrNull(
            (element) => _checkTradingTokenIsSameAsWalletToken(element)) !=
        null;
  }

  bool _checkTradingTokenIsSameAsWalletToken(LocalToken element) {
    bool sameAddress = element.contractAddress?.toLowerCase() ==
        tokenNetworkBalance.contractAddress?.toLowerCase();
    final tokenNetworkId = findKeyByValue(CoreNetworkId.exchangeNetworkIdMap,
        tokenNetworkBalance.id?.toLowerCase());
    final exchangeTokenId = findKeyByValue(
        CoreNetworkId.exchangeNetworkIdMap, element.network?.id?.toLowerCase());
    bool sameNetwork = tokenNetworkId == exchangeTokenId;

    return sameAddress && sameNetwork;
  }

  bool get _canWithdrawFromCardToToken =>
      _cardService.checkTokenBalanceAvailableForWithdrawal(tokenNetworkBalance);

  bool get _canFundCardWithToken =>
      _cardService.checkTokenBalanceAvailableForFunding(tokenNetworkBalance);

  bool canWithdrawToTradingWallet() {
    return _partnerService
        .getTradingAccountNetworks()
        .where((element) =>
            _cardService.cardWithdrawalConfigBox.values.firstOrNull?.networks
                ?.contains(element) ==
            true)
        .toList()
        .isNotEmpty;
  }

  void showToWallets() {
    List<OnboardWalletType> wallets = [];

    if (_hasTradingWalletOnActiveChain) {
      if (transferModel.from.walletType == WalletType.virtualCard &&
          canWithdrawToTradingWallet()) {
        wallets.add(MerchantWallet());
      } else if (transferModel.from.walletType == WalletType.spot) {
        wallets.add(MerchantWallet());
      }
    }

    if (transferModel.from.walletType.isCard && _canFundCardWithToken) {
      wallets.add(SpotWallet());
    } else if (transferModel.from.walletType.isTrading) {
      wallets.add(SpotWallet());
    }

    if (transferModel.from.walletType != WalletType.trading) {
      /// run if the user has active cards
      final activeCards = _cardService.transferableCardAccounts;
      for (var e in activeCards) {
        if (fromCard) {
          final fromCardAccount =
              (transferModel.from as VirtualCardWallet).cardAccount;
          if (fromCardAccount != e) {
            wallets.add(VirtualCardWallet(e));
          }
        } else if (_canFundCardWithToken) {
          wallets.add(VirtualCardWallet(e));
        }
      }
    }

    BuildContext? context = StackedService.navigatorKey?.currentContext;
    if (context == null) return;
    ExternalCloseSheet.showModal(
      context,
      child: WalletSelectionSheet(
        title: S.current.sendTo,
        selectedWallet: transferModel.to,
        walletList: wallets,
        onSelectWallet: onSelectToWallet,
      ),
    );
  }

  void onSelectToWallet(OnboardWalletType wallet) {
    _navigationService.back();
    transferModel.to = wallet;
    notifyListeners();
    if (wallet.walletType == WalletType.virtualCard) {
      _updateTokenCodeForVirtualCard();
    }
    if (toCard) {
      final account = (transferModel.to as VirtualCardWallet).cardAccount;
      if (account.cardCurrency != withdrawalCryptoRateInfo?.cardCurrency) {
        _getCardFundingCryptoRate(account);
      }
    }
    onTokenTextChanged(tokenTextEditingController.text);
  }

  void onSelectFromWallet(OnboardWalletType wallet) {
    _navigationService.back();
    transferModel.from = wallet;
    notifyListeners();
    if (fromCard) {
      final account = (transferModel.from as VirtualCardWallet).cardAccount;
      if (account.cardCurrency != withdrawalCryptoRateInfo?.cardCurrency) {
        _getCardWithdrawalCryptoRate(account);
      }
    }
    onTokenTextChanged(tokenTextEditingController.text);
  }

  void _updateTokenCodeForVirtualCard() {
    final token =
        cardTransactionConfig?.localTokens?.firstWhereOrNull((element) {
      return element.uniqueKey.toLowerCase() ==
          tokenNetworkBalance.uniqueKey.toLowerCase();
    });
    if (token != null) {
      localToken.tokenCode = token.tokenCode;
    } else {
      //Token not available for card transaction.
      localToken.tokenCode = null;
    }
  }

  Future<void> _sendFromCard(LocalCardAccount cardAccount) async {
    EasyLoading.show();
    String? tokenCode = localToken.tokenCode;
    String? cardCurrency = cardAccount.card?.currency;

    if (tokenCode == null || cardCurrency == null) {
      EasyLoading.dismiss();
      return;
    }
    final rateResponse = await _cardService.getWithdrawalCryptoRates(
      tokenCode: tokenCode,
      cardCurrency: cardCurrency,
    );
    rateResponse.when(success: (cryptoRateInfo) {
      if (cryptoRateInfo != null) {
        _toCardWithdrawalPreview(cryptoRateInfo, cardAccount);
      }
    }, failure: (failure) {
      _toastManager.showErrorToast(text: failure.message);
    });
    EasyLoading.dismiss();
  }

  _getCardWithdrawalCryptoRate(LocalCardAccount cardAccount) async {
    String? tokenCode = localToken.tokenCode;
    String? cardCurrency = cardAccount.cardCurrency;
    if (tokenCode == null || cardCurrency == null) {
      return;
    }
    final response = await _cardService.getWithdrawalCryptoRates(
      tokenCode: tokenCode,
      cardCurrency: cardCurrency,
    );

    response.when(
        success: (success) {
          withdrawalCryptoRateInfo = success;
        },
        failure: (failure) {});
  }

  _getCardFundingCryptoRate(LocalCardAccount cardAccount) async {
    String? tokenCode = localToken.tokenCode;
    String? cardCurrency = cardAccount.cardCurrency;
    if (tokenCode == null || cardCurrency == null) {
      return;
    }
    final response = await _cardService.getFundingRateForCryptoToken(
      tokenCode: tokenCode,
      cardCurrency: cardCurrency,
      initialFunding: false,
    );

    response.when(
        success: (success) {
          fundingCryptoRateInfo = success;
        },
        failure: (failure) {});
  }

  WalletCardsSvcCryptoRateInfo? fundingCryptoRateInfo;

  WalletCardsSvcCryptoRateInfo? withdrawalCryptoRateInfo;

  Future<void> _sendToCard(LocalCardAccount cardAccount) async {
    EasyLoading.show();
    String? tokenCode = localToken.tokenCode;
    String? cardCurrency = cardAccount.card?.currency;
    String cardCurrencySymbol =
        cardAccount.cardUsageTerms?.cardCurrencySymbol ?? "";

    if (transferModel.from.walletType != WalletType.spot ||
        tokenCode == null ||
        cardCurrency == null) {
      EasyLoading.dismiss();
      return;
    }
    final rateResponse = await _cardService.getFundingRateForCryptoToken(
      tokenCode: tokenCode,
      cardCurrency: cardCurrency,
      initialFunding: false,
    );
    await rateResponse.when(success: (cryptoRateInfo) async {
      double? amountAsDouble = double.tryParse(tokenTextEditingController.text
          .removeSymbol(cardCurrencySymbol)
          .removeSymbol(inputCurrency.symbol)
          .removeCommas());
      amountAsDouble = inputMode == InputMode.crypto
          ? convertTokenAmountToFiat(amount) ?? amountAsDouble
          : amountAsDouble;

      if (cryptoRateInfo != null && amountAsDouble != null) {
        LocalTxChannel? channel = cardTransactionConfig?.txChannels
            ?.firstWhereOrNull((element) =>
                element.optionsEnum ==
                CardTransactionOptionsEnum.onboardWallet);
        String? channelId = channel?.channelId;

        final fundingRequest = WalletCardsSvcFundingRequest((b) {
          b.amount = amountAsDouble;
          b.channelId = channelId;
          b.rateReference = cryptoRateInfo.reference;
          b.assetCode = tokenCode;
        });

        await _topUpCard(
          fundingRequest: fundingRequest,
          cardCurrency: cardCurrency,
          cryptoRateInfo: cryptoRateInfo,
          amountAsDouble: amountAsDouble,
          cardAccount: cardAccount,
        );
      }
    }, failure: (failure) {
      _toastManager.showErrorToast(text: failure.message);
    });
    EasyLoading.dismiss();
  }

  _topUpCard(
      {required WalletCardsSvcFundingRequest fundingRequest,
      required WalletCardsSvcCryptoRateInfo cryptoRateInfo,
      required String cardCurrency,
      required double amountAsDouble,
      required LocalCardAccount cardAccount}) async {
    String? accountId = cardAccount.id;
    if (accountId == null) return;
    final topUpResponse = await _cardService.initiateFundingRequest(
      accountId: accountId,
      fundingRequest: fundingRequest,
    );
    topUpResponse.when(success: (success) {
      String? fundingAddress = success?.fundingAddress?.address;
      if (fundingAddress == null) return;
      toCardFundAccountPreview(
        fundingAddress: fundingAddress,
        cryptoRateInfo: cryptoRateInfo,
        cardCurrency: cardCurrency,
        amountAsDouble: amountAsDouble,
        cardAccount: cardAccount,
        assetAmount: success?.assetAmount,
      );
    }, failure: (failure) {
      _toastManager.showErrorToast(
        text: failure.message,
      );
    });
  }

  toCardFundAccountPreview(
      {required String fundingAddress,
      required WalletCardsSvcCryptoRateInfo cryptoRateInfo,
      required String cardCurrency,
      required double amountAsDouble,
      num? assetAmount,
      required LocalCardAccount cardAccount}) {
    num rate = cryptoRateInfo.rate;
    String cardCurrencySymbol =
        cardAccount.cardUsageTerms?.cardCurrencySymbol ?? "";

    double processingFee = cryptoRateInfo.processingFee?.toDouble() ?? 0;
    double total = processingFee + amountAsDouble;

    var arguments = CryptoFundPreviewArgs(
        processingFee: processingFee,
        rate: rate,
        fundingAmount: amountAsDouble,
        cardCurrency: cardCurrency,
        fiatTotal: total,
        tokenTotal: assetAmount ?? (amountAsDouble * rate),
        cardFundingType: CardFundingType.subsequent,
        localToken: localToken,
        cardAccount: cardAccount,
        fundingAddress: fundingAddress,
        tokenNetworkBalance: tokenNetworkBalance,
        cardCurrencySymbol: cardCurrencySymbol);
    _navigationService.navigateToConfirmCardPurchaseView(
        confirmCardCardPurchaseViewArgs: arguments);
  }

  _toCardWithdrawalPreview(WalletCardsSvcCryptoRateInfo cryptoRateInfo,
      LocalCardAccount cardAccount) {
    String cardCurrencySymbol =
        cardAccount.cardUsageTerms?.cardCurrencySymbol ?? "";
    String? toAddress;
    if (transferModel.to is SpotWallet) {
      toAddress = _walletService.getWalletAddress;
    } else if (transferModel.to is MerchantWallet) {
      toAddress = _partnerService
          .tradingAccountForNetworkId(tokenNetworkBalance.exchangeNetworkId)
          ?.address;
    }

    num? amountAsNum = num.tryParse(tokenTextEditingController.text
        .removeSymbol(cardCurrencySymbol)
        .removeSymbol(inputCurrency.symbol)
        .removeCommas());
    amountAsNum = inputMode == InputMode.crypto
        ? convertTokenAmountToFiat(amount) ?? amountAsNum
        : amountAsNum;

    String currencyName = cardAccount.card?.currency ?? '';

    String? accountId = cardAccount.id;
    if (toAddress == null || amountAsNum == null || accountId == null) return;
    LocalToken token = LocalToken.fromAddressAndNetworkBalance(
        localToken, tokenNetworkBalance);
    _navigationService.navigateToCardWithdrawalPreviewView(
      cardWithdrawalPreviewArgs: CardWithdrawalPreviewArgs(
        toAddress: toAddress,
        withdrawalAmount: amountAsNum,
        token: token,
        toWalletType: transferModel.to,
        cardCurrencySymbol: cardCurrencySymbol,
        cardCurrencyName: currencyName,
        accountId: accountId,
        withdrawalCryptoRate: cryptoRateInfo,
      ),
    );
  }

  void _routeToActivateAccountScreen(LocalCardAccount cardAccount) {
    bool cardIsFrozen = cardAccount.cardIsFrozen == true;
    bool cardIsActiveOverdue = cardAccount.isOverdue;
    if (cardIsFrozen) {
      _unfreeze(cardAccount);
    } else if (cardIsActiveOverdue) {
      _navigationService.navigateToCardOverdueFeesView(
          cardAccount: cardAccount);
    } else if (cardAccount.cardIsSuspended == true) {
      _navigationService.navigateToCardSuspendedView(cardAccount: cardAccount);
    } else {
      ///fail-safe.
      _toastManager.showErrorToast(text: 'Activate card to continue');
    }
  }

  void _unfreeze(LocalCardAccount cardAccount) async {
    String? accountId = cardAccount.id;
    if (accountId == null) {
      return;
    }
    EasyLoading.show();
    await _requestOtp(accountId);
    EasyLoading.dismiss();
  }

  Future<void> _requestOtp(String accountId) async {
    WalletCardsSvcOtpRequest otpRequest = WalletCardsSvcOtpRequest((b) {
      b.accountId = accountId;
      b.type = WalletCardsSvcOtpRequestType.OP_UNFREEZE;
    });
    final otpResponse = await _cardService.requestActionOtp(otpRequest);

    final String email = locator<UserService>().getCurrentUser()?.email ?? '';

    otpResponse.when(success: (success) {
      _navigationService.navigateToVerifyOtpView(
        appBarTitle: S.current.confirmTransaction,
        resendOtp: () => onResendOtp(accountId),
        onCompleteAction: (String code) async {
          return await onCompleteAction(otpCode: code, accountId: accountId);
        },
        loaderMessage: S.current.confirmingYourTransaction,
        onVerifySuccess: (success) => onVerifySuccess(success),
        emailAddress: email,
      );
    }, failure: (failure) {
      _toastManager.showErrorToast(
        text: failure.message,
      );
    });
  }

  Future<void> onVerifySuccess(dynamic updatedCardAccount) async {
    _navigationService.back();
    _toastManager.showToast(
      text: "${S.current.cardReactivated}!",
    );
  }

  Future<Result> onCompleteAction(
          {required String otpCode, required String accountId}) =>
      _makeUnfreezeRequest(otpCode: otpCode, accountId: accountId);

  Future<Result> _makeUnfreezeRequest(
      {required String otpCode, required String accountId}) async {
    late Result<LocalCardAccount?> result;
    final response = await _cardService.unFreezeCardAccount(
      accountId: accountId,
      cardOpRequest: CardOpRequest((b) {
        b.otp = otpCode;
      }),
    );
    response.when(success: (success) {
      result = Result.success(data: success);
    }, failure: (failure) {
      result = Result.failure(error: failure);
    });

    return result;
  }

  Future<Result<StatusDto?>> onResendOtp(String accountId) =>
      _cardService.requestActionOtp(
        WalletCardsSvcOtpRequest(
          (b) {
            b.accountId = accountId;
            b.type = WalletCardsSvcOtpRequestType.OP_UNFREEZE;
          },
        ),
      );

  void _listenToCardAccountUpdate() {
    _cardService.cardAccountBox.watch().listen((v) {
      if (v.deleted == false) {
        LocalCardAccount account = v.value as LocalCardAccount;

        if (fromCard) {
          LocalCardAccount fromCardAccount =
              (transferModel.from as VirtualCardWallet).cardAccount;
          if (account.id?.toLowerCase() == fromCardAccount.id?.toLowerCase()) {
            (transferModel.from as VirtualCardWallet).cardAccount = account;
            notifyListeners();
          }
        }
        if (toCard) {
          LocalCardAccount toCardAccount =
              (transferModel.to as VirtualCardWallet).cardAccount;
          if (account.id?.toLowerCase() == toCardAccount.id?.toLowerCase()) {
            (transferModel.to as VirtualCardWallet).cardAccount = account;
            notifyListeners();
          }
        }
      }
    });
  }
}
