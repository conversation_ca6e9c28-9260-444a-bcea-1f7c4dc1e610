import 'package:flutter/material.dart';
import 'package:onboard_wallet/app/app.locator.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/models/token/wallet_type.dart';
import 'package:onboard_wallet/services/partners_service.dart';
import 'package:onboard_wallet/ui/widgets/images.dart';

import 'onboard_wallet_type.dart';

class MerchantWallet extends OnboardWalletType {
  @override
  String? address({String? networkId}) {
    return locator<PartnersService>()
        .getAccountForNetwork(networkId: networkId)
        ?.address;
  }

  @override
  Widget icon({Color? color}) {
    return TradingWalletIcon(
      bgColor: color,
    );
  }

  @override
  String get name => S.current.merchantWallet;

  @override
  WalletType get walletType => WalletType.trading;

  @override
  bool operator ==(Object other) {
    return other is MerchantWallet && walletType == other.walletType;
  }

  @override
  int get hashCode {
    return walletType.hashCode;
  }
}
