import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:just_the_tooltip/just_the_tooltip.dart';
import 'package:onboard_wallet/app/index.dart';
import 'package:onboard_wallet/constants/hive_box_name.dart';
import 'package:onboard_wallet/extensions/string.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/models/models.dart';
import 'package:onboard_wallet/models/wallet_address/local_wallet_address.dart';
import 'package:onboard_wallet/models/wallet_address/local_wallet_address_type.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:onboard_wallet/ui/views/send/data/data.dart';
import 'package:onboard_wallet/ui/views/transfer/base_view_model/transfer_base_viewmodel.dart';
import 'package:onboard_wallet/ui/views/transfer/internal/data/data.dart';
import 'package:onboard_wallet/ui/views/transfer/internal/data/external_wallet.dart';
import 'package:onboard_wallet/ui/views/transfer/internal/data/spot_wallet.dart';
import 'package:onboard_wallet/ui/views/transfer/internal/data/trading_wallet.dart';
import 'package:stacked_services/stacked_services.dart';

class ExternalTransferAmountViewModel extends TransferBaseViewModel {
  final _navigationService = locator<NavigationService>();
  final _networkService = locator<NetworkService>();
  final WalletService _walletService = locator<WalletService>();
  final PartnersService _partnerService = locator<PartnersService>();
  final _appSettingService = locator<AppSettingsService>();

  final tooltipController = JustTheController();

  bool get isMerchant => locator<UserService>().userIsMerchant;

  bool buttonEnabled = false;
  String _amountErrorMessage = '';
  String _addressErrorMessage = '';
  String? _toAddress;

  String get errorMessage =>
      addressErrorMessage.isNotEmpty ? addressErrorMessage : amountErrorMessage;

  String get amountErrorMessage => _amountErrorMessage;

  String get addressErrorMessage => _addressErrorMessage;

  LocalWalletAddress? walletAddress;

  StreamSubscription? walletAddressSub;

  String? get addressName {
    if (addressTextEditingController.text.trim() ==
        walletAddress?.walletAddress) {
      return walletAddress?.name;
    } else {
      return null;
    }
  }

  final addressTextEditingController = TextEditingController();
  InternalTransferModel transferModel = InternalTransferModel();

  ExternalTransferAmountViewModel(
    super.token,
    super.tokenNetworkBalance,
    super.localNFTBrief,
    super.localNFTData,
  );

  bool get isValidAddress =>
      _walletService.isValidAddress(address: _toAddress ?? '');

  Box<LocalWalletAddress> get _localWalletAddressBox =>
      Hive.box(HiveBoxName.localWalletAddressBoxName);

  bool get hasSavedAddress => localNFTBrief != null
      ? false
      : _localWalletAddressBox.values.where((element) {
          bool sameToken = element.tokenId?.toLowerCase() ==
              localToken.uniqueKey.toLowerCase();
          return sameToken ||
              element.walletAddressType == LocalWalletAddressType.universal;
        }).isNotEmpty;

  set amountErrorMessage(String value) {
    _amountErrorMessage = value;
    notifyListeners();
  }

  set addressErrorMessage(String value) {
    _addressErrorMessage = value;
    notifyListeners();
  }

  bool get isTradingWallet {
    return _partnerService
            .tradingAccountForNetworkId(tokenNetworkBalance.exchangeNetworkId)
            ?.address
            ?.toLowerCase() ==
        addressTextEditingController.text.toLowerCase();
  }

  bool _isValidAmount() {
    if (localNFTBrief != null) return true;
    double? valueAsDouble = double.tryParse(amount ?? '');
    double? balance = balanceAsDouble;
    if (valueAsDouble == null || balance == null) {
      return false;
    }
    if (valueAsDouble > balance || valueAsDouble == 0) {
      return false;
    }
    return true;
  }

  @override
  onTokenTextChanged(String? value) {
    amount = value
        ?.removeCommas()
        .replaceAll(inputCurrency.symbol, '')
        .removeSymbol(localToken.symbol ?? '');
    if (value?.isEmpty == true) {
      buttonEnabled = false;
      amountErrorMessage = '';
      return;
    }
    double? valueAsDouble = double.tryParse(amount ?? '');
    double? balance = balanceAsDouble;
    if (valueAsDouble == null || balance == null) {
      buttonEnabled = false;
      amountErrorMessage = S.current.invalidAmount;
      return;
    }
    if (valueAsDouble > balance) {
      buttonEnabled = true;
      amountErrorMessage = S.current.insufficientBalance;
      return;
    }
    if (valueAsDouble <= 0) {
      buttonEnabled = false;
      amountErrorMessage = '';

      return;
    }
    buttonEnabled = isValidAddress;
    amountErrorMessage = '';
  }

  @override
  onNetworkDropDownTap() {
    showNetworkDropDown(
      onSelectNetwork: (n) {
        tokenNetworkBalance = n;
        notifyListeners();
        if (amount != null) {
          onTokenTextChanged(amount);
        }
      },
    );
  }

  Future<void> onModelReady() async {
    transferModel.from = SpotWallet();
    walletAddressSub = _localWalletAddressBox.watch().listen((event) {
      ///user has deleted this address.
      if (_localWalletAddressBox.values.contains(walletAddress) == false) {
        walletAddress = null;
      }
      notifyListeners();
    });
    final showToolTip = await _appSettingService.showSwitchNetworkToolTip();
    if (showToolTip) {
      _showTooltip();
    }
  }

  _showTooltip() async {
    tooltipController.showTooltip();
    await Future.delayed(const Duration(seconds: 10));
    tooltipController.hideTooltip();
  }

  onAddressChanged(String? value) {
    _toAddress = value;
    buttonEnabled = isValidAddress && _isValidAmount();
    if (value?.isEmpty == true) {
      addressErrorMessage = '';
      buttonEnabled = false;
    } else if (!isValidAddress) {
      buttonEnabled = true;
      addressErrorMessage = S.current.invalidAddress;
    } else {
      buttonEnabled = _isValidAmount();
      addressErrorMessage = "";
    }
  }

  onPastePress() {
    Clipboard.getData(Clipboard.kTextPlain).then((value) {
      if (value?.text != null) {
        pasteAddress(value!.text!);
      }
    });
  }

  void pasteAddress(String value) {
    addressTextEditingController.text = value;
    addressTextEditingController.selection = TextSelection.fromPosition(
      TextPosition(offset: addressTextEditingController.text.length),
    );
    onAddressChanged(value);
  }

  showScanner() {
    _navigationService.navigateToQrCodeScanner(onCompleteScanning: (code) {
      pasteAddress(code);
    });
  }

  void onContinuePressed() {
    if (!_isValidAmount()) return;
    if (!isValidAddress) return;
    if (!buttonEnabled) return;
    if (amountInToken == null) return;
    transferModel.amount = amountInToken!;
    transferModel.from = SpotWallet();
    transferModel.token = localToken;
    transferModel.toAddress = _toAddress!;
    String? spotWalletAddress = _walletService.getWalletAddress;
    LocalNetwork? network = _networkService.currentChain;

    if (spotWalletAddress == null || network == null) {
      return;
    }
    transferModel.tokenNetwork = tokenNetworkBalance;
    transferModel.fromAddress = spotWalletAddress;
    transferModel.to =
        isTradingWallet ? MerchantWallet() : ExternalWallet(_toAddress!);
    SendAssetModel sendAssetModel =
        SendAssetModel.fromInternalTransferModel(transferModel);
    sendAssetModel.localNFTBrief = localNFTBrief;
    sendAssetModel.localNFTData = localNFTData;
    _navigationService.navigateToTransferPreviewView(
      transferModel: sendAssetModel,
    );
  }

  @override
  void dispose() {
    tokenTextEditingController.dispose();
    addressTextEditingController.dispose();
    walletAddressSub?.cancel();
    super.dispose();
  }

  void onAddressWidgetTapped() {
    if (hasSavedAddress) {
      _navigationService.navigateToSelectWalletAddressView(
        onAddressSelected: (walletAddress) => onAddressSelected(walletAddress),
        tokenBalance: localToken,
        tokenNetworkBalance: tokenNetworkBalance,
      );
    } else {
      LocalWalletAddress address = LocalWalletAddress(
        name: '',
        walletAddressType: LocalWalletAddressType.token,
        walletAddress: addressTextEditingController.text,
        tokenNetworkBalance: tokenNetworkBalance,
        tokenId: localToken.uniqueKey,
      );
      _navigationService.navigateToAddWalletAddressView(
        onAddressSaved: onAddressSelected,
        localWalletAddress: address,
        tokenNetworkBalance: tokenNetworkBalance,
        tokenBalance: localToken,
      );
    }
  }

  onAddressSelected(LocalWalletAddress walletAddress) {
    this.walletAddress = walletAddress;
    if (walletAddress.walletAddressType == LocalWalletAddressType.token &&
        walletAddress.tokenNetworkBalance != null) {
      tokenNetworkBalance = walletAddress.tokenNetworkBalance!;

      bool containsNetwork =
          localToken.networks?.contains(tokenNetworkBalance) == true;
      if (containsNetwork == false) {
        localToken.networks?.add(tokenNetworkBalance);
      }
    }
    addressTextEditingController.text = walletAddress.walletAddress;
    onAddressChanged(addressTextEditingController.text);
    notifyListeners();
  }

  void onClearTapped() {
    addressTextEditingController.clear();
    onAddressChanged(addressTextEditingController.text);
  }
}
