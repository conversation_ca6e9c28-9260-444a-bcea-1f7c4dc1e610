import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:nestcoinco_onboard_api_gateway/nestcoinco_onboard_api_gateway.dart';
import 'package:onboard_wallet/constants/action_target.dart';
import 'package:onboard_wallet/enums/auth_session_result.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/manager/toast.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:onboard_wallet/ui/views/passcode/passcode_viewmodel.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';

import '../../../app/index.dart';

class PasscodeUnlockViewModel extends BaseViewModel {
  final _userService = locator<UserService>();
  final _navigationService = locator<NavigationService>();
  final _cloudBackupService = locator<CloudBackupService>();
  final userService = locator<UserService>();
  final _biometricService = locator<BiometricService>();
  final _passcodeService = locator<PasscodeService>();
  final _toastManager = locator<ToastManager>();

  String errorMessage = '';
  final TextEditingController controller = TextEditingController();
  final _authenticationService = locator<AuthenticationService>();
  final FocusNode focusNode = FocusNode();
  Timer? _countdownTimer;
  Duration timerDuration = const Duration(seconds: 5);

  String get email => _userService.getCurrentUser()?.email ?? '';

  bool get isTimerActive => _countdownTimer?.isActive ?? false;

  num get timerDigit => timerDuration.inSeconds;

  int _userAttempts = 0;

  bool obscureText = true;

  final int _showAttemptsCount = 3;

  final int _maxAttempts = 8;

  bool isBiometricSetup = false;

  final Function()? onUnlock;

  PasscodeUnlockViewModel(this.onUnlock);

  onModelReady() {
    isBiometricSetup = _biometricService.isBiometricLockEnabled();
    final hasOnlinePinSetUp = _passcodeService.hasOnlinePinSetup();
    if (hasOnlinePinSetUp == false) {
      ///force the user to use passcode unlock, so we can set up online pin.
      isBiometricSetup = false;
    }
    if (isBiometricSetup) {
      biometricUnlock();
    }
    notifyListeners();
  }

  void onCodeChanged(String value) {
    if (!obscureText) {
      obscureText = true;
      notifyListeners();
    }
  }

  void onCodeComplete(String value) async {
    if (_userAttempts < _maxAttempts) {
      _userAttempts += 1;
      _verifyAccess(value);
    }
  }

  Future<void> _verifyAccess(String value) async {
    try {
      // First verify locally regardless of online status
      final isLocallyValid = await _passcodeService.verifyPasscode(value);

      if (isLocallyValid) {
        await _handleValidLocalPasscode(value);
      } else {
        await _handleInvalidLocalPasscode(value);
      }
    } catch (e) {
      _handleErrorMessage();
      getLogger(toString()).e("Verification error: $e");
    } finally {
      EasyLoading.dismiss();
    }
  }

  // Handle case when local passcode is valid
  Future<void> _handleValidLocalPasscode(String passcode) async {
    // Check if the user has already set up an online PIN
    final hasOnlinePinSetup = _passcodeService.hasOnlinePinSetup();

    if (hasOnlinePinSetup) {
      // User has already registered their PIN online, proceed normally
      await _handleSuccessfulAuthentication();
    } else {
      // User has a valid local passcode but hasn't set up online PIN
      EasyLoading.show();
      await _syncLocalPasscodeWithBackend(passcode);
    }
  }

  // Sync local passcode with backend
  Future<void> _syncLocalPasscodeWithBackend(String passcode) async {
    // Check if an online PIN exists
    final onlinePinResult = await _passcodeService.checkPinExists();

    onlinePinResult.when(success: (status) async {
      if (status?.success == true) {
        await _verifyWithExistingOnlinePin(passcode);
      } else {
        // User has no online PIN yet, register their existing passcode
        await _registerExistingPasscodeAsOnlinePin(passcode);
      }
    }, failure: (error) async {
      // Error checking online PIN status - since local verification succeeded, let the user in
      await _handleSuccessfulAuthentication();
      getLogger(toString()).e("Failed to check online PIN: ${error.message}");
    });
  }

  // Verify with existing online PIN
  Future<void> _verifyWithExistingOnlinePin(String passcode) async {
    final verifyResult = await _passcodeService.verifyAccountPin(pin: passcode);

    verifyResult.when(success: (status) async {
      if (status?.success == true) {
        // If verification succeeds, mark that they have the online PIN set up
        await _passcodeService.markOnlinePinSetup();
        await _handleSuccessfulAuthentication();
      } else {
        // Local passcode doesn't match online PIN, register local one as new
        await _registerExistingPasscodeAsOnlinePin(passcode);
      }
    }, failure: (error) async {
      // Error verifying - prioritize user experience by registering local passcode
      await _registerExistingPasscodeAsOnlinePin(passcode);
      getLogger(toString()).e(error.message);
    });
  }

  // Helper method to register existing passcode as transaction PIN
  Future<void> _registerExistingPasscodeAsOnlinePin(String passcode) async {
    try {
      // Create online PIN using existing passcode
      final createResult =
          await _passcodeService.createAccountPin(pin: passcode);

      createResult.when(success: (status) async {
        if (status?.success == true) {
          // Successfully registered existing passcode as online PIN
          await _passcodeService.markOnlinePinSetup();
          await _handleSuccessfulAuthentication();
        } else {
          // Registration failed but local passcode is valid, so let them in
          // They'll be prompted to set up online PIN next time
          await _handleSuccessfulAuthentication();
          getLogger(toString())
              .e("Failed to register existing passcode: ${status?.message}");
        }
      }, failure: (error) async {
        // Registration failed but local passcode is valid, so let them in
        // They'll be prompted to set up online PIN next time
        await _handleSuccessfulAuthentication();
        getLogger(toString())
            .e("Failed to register existing passcode: ${error.message}");
      });
    } catch (e) {
      // Error registering passcode, but since local verification succeeded,
      // allow the user to proceed
      await _handleSuccessfulAuthentication();
      getLogger(toString()).e("Error registering existing passcode: $e");
    }
  }

  // Handle case when local passcode is invalid
  Future<void> _handleInvalidLocalPasscode(String value) async {
    EasyLoading.show();
    final onlinePinResult = await _passcodeService.checkPinExists();

    onlinePinResult.when(success: (status) async {
      if (status?.success == true) {
        await _verifyOnlinePinOnly(value);
      } else {
        // No valid local passcode, no online PIN - take them to setup
        _navigationService.navigateToPasscodeView(
            passcodeType: PasscodeType.create,
            passCodeEntryPoint: PassCodeEntryPoint.secureWallet);
      }
    }, failure: (error) async {
      _handleErrorMessage();
      getLogger(toString()).e(error.message);
    });
  }

  // Verify with online PIN only (when local verification failed)
  Future<void> _verifyOnlinePinOnly(String value) async {
    final verifyResult = await _passcodeService.verifyAccountPin(pin: value);

    verifyResult.when(success: (status) async {
      if (status?.success == true) {
        // Their online PIN is valid, update local storage
        await _passcodeService.savePasscode(value);
        await _passcodeService.markOnlinePinSetup();
        await _handleSuccessfulAuthentication();
      } else {
        _handleErrorMessage();
      }
    }, failure: (error) {
      _handleErrorMessage();
      getLogger(toString()).e(error.message);
    });
  }

  Future<void> _handleSuccessfulAuthentication() async {
    await locator<WalletService>().initializeWallet(useBiometric: false);
    if (onUnlock == null) {
      _navigationService.clearStackAndShow(Routes.homeView);
    } else {
      onUnlock!.call();
    }
  }

  void logout() => _checkLogoutNudgeIsRequired();

  Future biometricUnlock() async {
    bool unlock = await _biometricService.unlock() ?? false;
    if (unlock) {
      if (onUnlock == null) {
        _onSuccessfulBiometricUnlock();
      } else {
        onUnlock!.call();
      }
    }
  }

  _onSuccessfulBiometricUnlock() async {
    bool isLogged = await _authenticationService.isLoggedIn();
    if (isLogged) {
      if (locator<CloudBackupService>().requiresBackUp) {
        _navigationService.replaceWith(Routes.walletSecurityView);
      } else {
        _navigationService.clearStackAndShow(Routes.homeView);
      }
    } else {
      _navigationService.clearStackAndShow(Routes.authView);
    }
  }

  void _startTimer() {
    timerDuration = const Duration(seconds: 5);
    _countdownTimer = Timer.periodic(
      const Duration(seconds: 1),
      (_) => _setCountDown(),
    );
  }

  void _setCountDown() {
    final seconds = timerDuration.inSeconds - 1;
    if (seconds < 0) {
      _countdownTimer?.cancel();
      _checkLogoutNudgeIsRequired().then((_) {
        ///change error message if user cancelled backup.
        errorMessage = S.current.youHaveNoMoreAttemptsLeft;
        notifyListeners();
      });
    } else {
      timerDuration = Duration(seconds: seconds);
      errorMessage = S.current.noAttemptsLeftLoggingOutInXSeconds(timerDigit);
    }
    notifyListeners();
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    super.dispose();
  }

  logUserOut() async {
    EasyLoading.show();
    await _authenticationService.logout();
    try {
      await locator<SecureStorageService>().deleteAll();
    } catch (_) {}
    EasyLoading.dismiss();
    locator<DatabaseService>().clearDb();
    locator<PreferenceService>().clear();
    _navigationService.clearStackAndShow(Routes.authView);
  }

  Future<void> _checkLogoutNudgeIsRequired() async {
    if (_cloudBackupService.requiresBackUp) {
      await _nudgeWalletBackup();
    } else {
      logUserOut();
    }
  }

  _nudgeWalletBackup() async {
    _cloudBackupService.setCallBackToCurrentRoute();
    await _navigationService.navigateToWalletSecurityView();
    if (_cloudBackupService.hasBackUp) {
      logUserOut();
    }
  }

  void _handleErrorMessage() {
    obscureText = false;

    if (_userAttempts < _showAttemptsCount) {
      errorMessage = S.current.incorrectPasscode;
    } else if (_userAttempts < _maxAttempts) {
      errorMessage = S.current
          .incorrectPasscodeXAttemptsLeft(_maxAttempts - _userAttempts);
    } else {
      _startTimer();
    }

    setError(true);
    focusNode.unfocus();
  }

  void resetPasscodePressed() async {
    clearErrors();

    final authorizationService = locator<AuthorizationService>();
    final currentUser = locator<DatabaseService>().getCurrentUser();

    final authorizationSessionRequest = AuthorizationSessionRequest((b) => b
      ..actionTarget = ActionTarget.authorizations
      ..action = AuthorizationSessionAction.RESET_CREDENTIALS
      ..details = AuthorizationSessionData((a) => a
        ..credentialType = AuthenticationMethod.ACCOUNT_PIN
        ..accountId = currentUser?.userId).toBuilder());

    final response = await authorizationService.startAuthFlow(
        authorizationSessionRequest: authorizationSessionRequest);

    response.when(success: (result) {
      if (result.status == AuthSessionResult.completed) {
        _navigationService.navigateToPasscodeView(
            preventDuplicates: false,
            passcodeType: PasscodeType.createNewPasscode,
            resetPinToken: result.sessionId,
            passCodeEntryPoint: PassCodeEntryPoint.secureWallet,
            onConfirmPasscode: () {
              _handleSuccessfulAuthentication();
            });
      } else {
        _toastManager.showErrorToast(text: S.current.verificationFailed);
      }
    }, failure: (error) {
      getLogger(toString()).e(error.message);
      _toastManager.showErrorToast(text: error.message);
    });
  }
}
