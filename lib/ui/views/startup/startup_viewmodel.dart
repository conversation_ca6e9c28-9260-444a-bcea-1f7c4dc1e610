import 'package:collection/collection.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:onboard_wallet/app/app.locator.dart';
import 'package:onboard_wallet/app/app.logger.dart';
import 'package:onboard_wallet/app/app.router.dart';
import 'package:onboard_wallet/constants/string_constants.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';

import '../../../manager/manager.dart';
import 'package:onboard_wallet/ui/views/passcode/passcode_viewmodel.dart'
    show PasscodeType, PassCodeEntryPoint;

class StartupViewModel extends BaseViewModel {
  final _navigationService = locator<NavigationService>();
  final _authenticationService = locator<AuthenticationService>();
  final _analyticsService = locator<AnalyticsService>();
  final _remoteConfigService = locator<RemoteConfigService>();
  final _passkeyService = locator<PasskeyService>();
  final passcodeService = locator<PasscodeService>();
  final _userService = locator<UserService>();

  bool showWelcomeText = false;

  StartupViewModel();

  // Place anything here that needs to happen before we get into the application
  Future runStartupLogic() async {
    FlutterNativeSplash.remove();
    bool loginState = await _authenticationService.isLoggedIn();
    _authenticationService.firebaseAnonymousSignIn();
    showWelcomeText = loginState;
    notifyListeners();
    locator<ToastManager>().init();
    await _remoteConfigService.setup();
    await _remoteConfigService.showCard();
    _analyticsService.setupAnalytics();
    toAuthView(loginState);

    _loadConfig();
  }

  Future _handleCountrySetupIfNull() async {
    bool hasCountryCode = _userService.getCurrentUser()?.country != null;
    if (!hasCountryCode) {
      await _navigationService.navigateToCountryOfResidenceAuthView(
          onContinue: (country) async {
            await _userService.saveUserCountryFeature(country);
            handleLoginStates(true);
          },
          progress: 0.0);
    }
  }

  handleLoginStates(bool isLoggedIn) async {
    final biometricService = locator<BiometricService>();
    final isBiometricBackUp = biometricService.isBiometricLockEnabled();
    final hasOnlinePinSetUp = passcodeService.hasOnlinePinSetup();
    final passcodeBackUp = passcodeService.hasLocalPinSetup();
    await _passkeyService.getRegisteredPasskeys();
    final hasTokenExpired = await _authenticationService.hasTokenExpired();
    bool hasCountryCode =
        locator<UserService>().getCurrentUser()?.country != null;
    if (isLoggedIn && hasTokenExpired == false && !hasCountryCode) {
      _handleCountrySetupIfNull();
      return;
    }
    if (isLoggedIn) {
      locator<PreferenceService>().setBool(key: kReturningUser, value: true);
      if (hasTokenExpired == true) {
        final userService = locator<UserService>();
        String? email = userService.getCurrentUser()?.email;
        if (email != null) {
          _navigationService.replaceWith(
            Routes.setEmailView,
            arguments: SetEmailViewArguments(
              initialEmail: email,
            ),
          );
        } else {
          _navigationService.replaceWith(Routes.authView);
        }
      } else if (hasOnlinePinSetUp == false && passcodeBackUp) {
        ///has NO online pin setup but has a local pin setup.
        ///redirect to passcode unlock and then use local pin to set up online pin.
        _navigationService.replaceWith(Routes.passcodeUnlockView);
      } else if (isBiometricBackUp) {
        ///this view will handle a case where the user has no pin set up,
        ///after successful biometric verification.
        _navigationService.replaceWith(Routes.lockView);
      } else if (passcodeBackUp) {
        _navigationService.replaceWith(Routes.passcodeUnlockView);
      } else {
        noBiometricLogin(isLoggedIn);
      }
    } else {
      _navigationService.replaceWith(Routes.authView);
    }
  }

  noBiometricLogin(bool isLogged) async {
    if (isLogged) {
      final hasOnlinePinSetUp = await passcodeService.checkPinExists();
      hasOnlinePinSetUp.when(success: (success) {
        _onPinSetup(hasOnlinePin: success?.success == true);
      }, failure: (failure) {
        _onPinSetup(hasOnlinePin: false);
      });
    } else {
      _navigationService.replaceWith(Routes.authView);
    }
  }

  Future<void> _onPinSetup({required bool hasOnlinePin}) async {
    _navigationService.navigateToPasscodeView(
      passcodeType:
          hasOnlinePin ? PasscodeType.verifyAccess : PasscodeType.create,
      passCodeEntryPoint: PassCodeEntryPoint.startUpView,
    );
  }

  void init() {
    locator<WalletAuthService>().init();
    locator<LocationService>().getDeviceCountry();
    locator<NetworkService>().getActiveChain();
    locator<NetworkService>().getLastSelectedChain();
    _initUserCountryFeatureIfNull();
  }

  void _loadConfig() {
    final cryptoService = locator<CryptoService>();
    cryptoService.fetchExchangeNetworks();
    cryptoService.fetchNetwork();
  }

  Future<void> toAuthView(bool loginState) async {
    try {
      handleLoginStates(loginState);
      init();
    } catch (e) {
      getLogger(toString()).e(e);
      _authenticationService.logout();
      locator<SecureStorageService>().deleteAll();
      locator<DatabaseService>().clearDb();
      locator<PreferenceService>().clear();
      _navigationService.replaceWith(Routes.authView);
    }
  }

  Future<void> _initUserCountryFeatureIfNull() async {
    final userService = locator<UserService>();
    final userCountryFeature = userService.getUserCountryFeature();
    if (userCountryFeature == null) {
      final countryCode = userService.getUserKycCountryCode();
      final result = await locator<ConfigService>()
          .getCountryFeatures(countryCode: countryCode);
      result.when(
          success: (success) {
            final userKycCountry = userService.getCurrentUser()?.country;
            if (userKycCountry != null) {
              final countryFeature = success?.countries.firstWhereOrNull((e) =>
                  e.country.code.toLowerCase() == userKycCountry.toLowerCase());
              if (countryFeature != null) {
                userService.saveUserCountryFeature(countryFeature);
              }
            }
          },
          failure: (failure) {});
    }
  }
}
