import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:just_the_tooltip/just_the_tooltip.dart';
import 'package:onboard_wallet/app/index.dart';
import 'package:onboard_wallet/constants/constants.dart';
import 'package:onboard_wallet/extensions/extensions.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/manager/manager.dart';
import 'package:onboard_wallet/models/models.dart';
import 'package:onboard_wallet/models/token/wallet_type.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';
import 'package:onboard_wallet/ui/dialogs/info_alert/external_close_dialog.dart';
import 'package:onboard_wallet/ui/views/choose_crypto/widgets/app_networks_list_sheet.dart';
import 'package:onboard_wallet/ui/views/choose_tokens/choose_tokens_viewmodel.dart';
import 'package:onboard_wallet/ui/views/dashboard/crypto_dashboard/customer_dashboard/widgets/widgets.dart';
import 'package:onboard_wallet/ui/views/dashboard/main_dashboard/widgets/hide_balance_alert/hide_balance_alert_view.dart';
import 'package:onboard_wallet/ui/views/dashboard/widgets/widgets.dart';
import 'package:onboard_wallet/ui/views/fund_options/fund_options_viewmodel.dart';
import 'package:onboard_wallet/ui/views/token_details/subviews/low_reputation_modal.dart';
import 'package:onboard_wallet/ui/views/wallet_connect/wallet_connect_intro/wallet_connect_intro_view.dart';
import 'package:onboard_wallet/utils/mixins/mixins.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';

import '../../../../../constants/string_constants.dart';
import '../../../../widgets/widgets.dart';

class CustomerDashboardViewModel extends ReactiveViewModel
    with CopyToClipboardMixin, TransactionsMixin {
  final _tokenService = locator<TokenService>();
  final _appSettingsService = locator<AppSettingsService>();
  final _nftsService = locator<NftsService>();
  final _navigationService = locator<NavigationService>();
  final _walletNavigationService = locator<WalletNavigationService>();
  final _analyticsService = locator<AnalyticsService>();
  final _walletService = locator<WalletService>();
  final _walletConnectService = locator<WalletConnectService>();
  final _fireStoreService = locator<FireStoreService>();
  final _userService = locator<UserService>();
  final _featureService = locator<FeatureService>();
  final _networkService = locator<NetworkService>();
  final _prefService = locator<PreferenceService>();
  num _cryptoBalance = 0;
  int selectedTabIndex = 0;

  bool get showSwap => !_featureService.isSwapOff;
  bool showNFTNewIndicator = false;
  NFTsSorting nftSorting = NFTsSorting.recent;

  bool get hasSeenWalletConnectIntro =>
      _appSettingsService.hasSeenWalletConnectIntro.value;

  bool get isBalanceHidden => _appSettingsService.balanceHidden;

  bool get userIsMerchant => _userService.userIsMerchant;
  JustTheController nftTooltipController = JustTheController();

  List<LocalAddressTokenBalance> get tokens {
    List<LocalAddressTokenBalance> addressTokens = _tokenService
        .myPortfolioBoxName.values
        .where((element) =>
            element.isHidden == false &&
            (lastSelectedNetwork == null ||
                element.containsNetwork(lastSelectedNetwork?.coreNetworkId)))
        .toList();
    if (lastSelectedNetwork == null) {
      addressTokens.sort((a, b) => b.compareFiatValue(a));
    } else {
      String? coreNetworkId = lastSelectedNetwork?.coreNetworkId;
      addressTokens.sort((a, b) {
        return b
            .getFiatValueOfNetwork(coreNetworkId)
            .compareTo(a.getFiatValueOfNetwork(coreNetworkId));
      });
    }
    return addressTokens;
  }

  bool get emptyPortfolio =>
      _walletService.isDefiWalletSetUp == false ||
      _tokenService.myPortfolioBoxName.isEmpty;

  bool get hasVerifiedAddress =>
      _userService.getCurrentUser()?.hasVerifiedWalletAddress == true;

  String get emptyPortfolioLabel {
    if (_walletService.isDefiWalletSetUp == false && hasVerifiedAddress) {
      return S.current.restoreYourWallet;
    } else {
      return S.current.getCryptoPortfolio;
    }
  }

  String get emptyPortfolioSubtitle {
    if (_walletService.isDefiWalletSetUp == false && hasVerifiedAddress) {
      return S.current.thisWalletIsInWatchModeSubCopy;
    } else {
      return S.current.startByBuying;
    }
  }

  void toggleShowBalance() {
    final hide = !isBalanceHidden;
    _appSettingsService.balanceHiddenBox.put("isHidden", hide);
    if (hide) {
      _analyticsService.logEvent(eventName: AnalyticsEvent.hidesAppBalance);
      _showHideBalanceInfoCard();
    } else {
      _analyticsService.logEvent(eventName: AnalyticsEvent.unHidesAppBalance);
    }
  }

  _showHideBalanceInfoCard() {
    final showHideBalanceInfo =
        _prefService.getBool(key: kShowHiddenBalanceInfoCard) ?? true;
    if (showHideBalanceInfo) {
      final context = StackedService.navigatorKey?.currentContext;
      ExternalCloseDialog.showExternalDialog(
        context!,
        child: const HideBalanceAlert(),
      );
    }
  }

  List<LocalNFTBrief> get getNFTsForNetwork {
    final coreNetworkId = lastSelectedNFTNetwork?.coreNetworkId;
    if (coreNetworkId == null) {
      List<LocalNFTBrief> allCollections = [];
      for (var network in _networkService.networks) {
        final collections = _nftsService.nftsBox.values
            .where((element) => element.network?.id == network.coreNetworkId)
            .toList();
        collections.removeWhere((collection) => _nftsService.isCollectionHidden(
            collectionUniqueId: collection.uniqueId,
            allUniqueNFTIds: collection.allNFTDataIds));
        allCollections.addAll(collections);
      }

      return allCollections;
    }
    final collections = _nftsService.nftsBox.values
        .where((element) => element.network?.id == coreNetworkId)
        .toList();
    collections.removeWhere((collection) => _nftsService.isCollectionHidden(
        collectionUniqueId: collection.uniqueId,
        allUniqueNFTIds: collection.allNFTDataIds));
    return collections;
  }

  bool showNFTsLoader = false;

  String get getCryptoBalance {
    if (isBalanceHidden) {
      return '**';
    } else {
      if (lastSelectedNetwork == null) {
        return _cryptoBalance.shortBalance();
      } else {
        return _tokenService
            .getPortfolioBalanceOnNetwork(lastSelectedNetwork?.coreNetworkId)
            .currencyFormat();
      }
    }
  }

  CustomerDashboardViewModel() {
    _updateCryptoBalance();
    _tokenService.balanceRefreshed.listen((v) {
      _updateCryptoBalance();
      notifyListeners();
    });

    _appSettingsService.balanceHiddenBox.listenable().addListener(() {
      notifyListeners();
    });
  }

  LocalNetwork? get lastSelectedNetwork => _networkService.lastSelectedChain;

  LocalNetwork? get lastSelectedNFTNetwork =>
      _networkService.lastSelectedNFTChain;

  _updateCryptoBalance() {
    if (_tokenService.myPortfolioBoxName.isEmpty) return;

    _cryptoBalance = _tokenService.getPortfolioBalance();
  }

  setSelectedIndexTab(int index) {
    selectedTabIndex = index;
    notifyListeners();
  }

  onAddPressed() {
    if (userIsMerchant) {
      _analyticsService.logEvent(
          eventName: AnalyticsEvent.tapsDepositOnSpotWallet);
    } else {
      _analyticsService.logEvent(eventName: AnalyticsEvent.tapDepositOnHome);
    }

    if (_walletService.isDefiWalletSetUp) {
      _onAddPressed();
    } else {
      _setUpDefiWallet(onCompleteSetup: _onAddPressed);
    }
  }

  _onAddPressed() {
    _navigationService.navigateToChooseCryptoView(
      onTokenSelect: (token, tokenNetworkBalance) {
        _walletNavigationService.navigateToFundingOption(
          tokenBalance: token,
          walletType: token.walletType ?? WalletType.spot,
          fundOptionType: FundOptionFLowType.add,
          tokenNetworkBalance: tokenNetworkBalance,
        );
      },
      fundOptionFLowType: FundOptionFLowType.add,
    );
  }

  void startBuyingPressed() {
    _analyticsService.logEvent(
        eventName: AnalyticsEvent.startBuyingCryptoOnDashBoard);
    if (_walletService.isDefiWalletSetUp) {
      _navigationService.navigateToChooseTokensView(
        type: DiscoverCryptoType.choose,
        onTokenSelect: onSelectToken,
      );
    } else {
      _setUpDefiWallet(onCompleteSetup: () {});
    }
  }

  void onSelectToken(LocalAddressTokenBalance token) {
    _navigationService.navigateToTokenDetailsView(
      token: token,
    );

    _analyticsService.logEvent(
        eventName: AnalyticsEvent.tapsHomePageAsset,
        parameters: {EventParameterKey.tokenSymbol: token.symbol});
  }

  onSendPressed() {
    if (userIsMerchant) {
      _analyticsService.logEvent(
          eventName: AnalyticsEvent.tapsSendOnSpotWallet);
    } else {
      _analyticsService.logEvent(eventName: AnalyticsEvent.tapsSendOnHome);
    }
    if (_walletService.isDefiWalletSetUp) {
      _onSendPressed();
    } else {
      _setUpDefiWallet(onCompleteSetup: _onSendPressed);
    }
  }

  void _onSendPressed() {
    _navigationService.navigateToChooseCryptoView(
      onTokenSelect: (token, tokenNetworkBalance) {
        _walletNavigationService.navigateToFundingOption(
          tokenBalance: token,
          walletType: token.walletType ?? WalletType.spot,
          fundOptionType: FundOptionFLowType.transfer,
          tokenNetworkBalance: tokenNetworkBalance,
        );
      },
      fundOptionFLowType: FundOptionFLowType.transfer,
    );
  }

  Future<void> refreshWallet() async {
    await Future.wait([
      _tokenService.getTokenBalances(forceRefresh: true, clearDb: false),
    ]);
  }

  onSwapPressed() async {
    _logSwapTapEvent();
    if (_walletService.isDefiWalletSetUp) {
      _onSwapPressed();
    } else {
      _setUpDefiWallet(onCompleteSetup: _onSwapPressed);
    }
  }

  void _onSwapPressed() {
    _navigationService.navigateToSwapView();
  }

  void _logSwapTapEvent() {
    if (userIsMerchant) {
      _analyticsService.logEvent(
          eventName: AnalyticsEvent.tapsTradeOnSpotWallet);
    } else {
      _analyticsService.logEvent(eventName: AnalyticsEvent.tapsTradeOnHome);
    }
  }

  void onMorePressed() {
    _analyticsService.logEvent(
      eventName: AnalyticsEvent.clickMoreOnDashBoard,
    );
    BuildContext? context = StackedService.navigatorKey?.currentContext;
    if (context == null) return;
    final address = _userService.getCurrentUser()?.verifiedAddress;
    if (address == null) {
      _setUpDefiWallet(onCompleteSetup: () {
        _navigationService.clearStackAndShow(Routes.homeView);
      });
    } else {
      ExternalCloseSheet.showModal(
        context,
        backgroundColor: Grey.grey25,
        child: MoreDashboardOptions(
          walletAddress: address,
          onCopyAddressPressed: () {
            _logCopyAddressEvents();
            copyWalletAddress(address: address);
            _navigationService.back();
          },
          onWalletConnectPressed: () {
            _navigationService.back();
            if (_walletService.isDefiWalletSetUp) {
              onScanPressed();
            } else {
              _setUpDefiWallet(onCompleteSetup: () {
                onScanPressed();
              });
            }
          },
        ),
      );
    }
  }

  void _logCopyAddressEvents() {
    if (userIsMerchant) {
      _analyticsService.logEvent(
          eventName: AnalyticsEvent.copiesSpotWalletAddress);
    } else {
      _analyticsService.logEvent(
          eventName: AnalyticsEvent.copiesWalletOnAddressOnHome);
    }
  }

  onScanPressed() {
    if (_walletService.isDefiWalletSetUp) {
      _scanPressed();
    } else {
      _walletService.setUpOrRecoverDefiWallet(onCompleteSetup: () {
        _scanPressed();
      });
    }
  }

  _scanPressed() {
    if (!hasSeenWalletConnectIntro) {
      _showWalletConnectIntro();
    } else {
      _showWalletConnectScanner();
    }
    _analyticsService.logEvent(eventName: AnalyticsEvent.walletConnectStart);
  }

  _showWalletConnectIntro() {
    _navigationService.navigateToWalletConnectIntroView(
      preventDuplicates: false,
      onContinue: () {
        _navigationService.back();
        _navigationService.navigateToWalletConnectIntroView(
          preventDuplicates: false,
          onContinue: () {
            _navigationService.back();
            _showWalletConnectScanner();
          },
          page: WalletConnectIntroPage.second,
        );
      },
      page: WalletConnectIntroPage.first,
    );
  }

  _showWalletConnectScanner() async {
    var result = await _navigationService.navigateToWalletConnectQrCodeScanner(
      onInfoPressed: () => _showWalletConnectIntro(),
    );
    if (result is String) {
      _initiateWallConnectSession(url: result);
    }
  }

  Future _initiateWallConnectSession({required String url}) async {
    try {
      _walletConnectService.showConnecting();
      await _walletConnectService.connectWithUri(url);
    } catch (e) {
      locator<ToastManager>().showErrorToast(
        text: S.current.invalidWalletConnectUri,
      );
    }
  }

  void onTokenSelected(LocalAddressTokenBalance token) {
    if (_walletService.isDefiWalletSetUp) {
      _navigationService.navigateToTokenDetailsView(token: token);
    } else {
      _setUpDefiWallet(onCompleteSetup: () {
        _navigationService.navigateToTokenDetailsView(token: token);
      });
    }
  }

  onNFTCollectionSelected(LocalNFTBrief nftBrief) {
    _navigationService.navigateToNftCollectionView(
        nftBrief: nftBrief, isHiddenView: false);
  }

  onNFTsTransactionsPressed() {
    _navigationService.navigateToNftTransactionsView();
  }

  void onManagePressed(bool isNFT) {
    if (isNFT) {
      _navigationService.navigateToManageNftsView();
    } else {
      _analyticsService.logEvent(eventName: AnalyticsEvent.manageAssetsClick);
      _navigationService.navigateToManageAssetsView();
    }
  }

  void onSearchPressed() {
    _analyticsService.logEvent(
        eventName: AnalyticsEvent.clickSearchOnCryptDashBoard);
    if (_walletService.isDefiWalletSetUp) {
      _toPortfolio();
    } else {
      _setUpDefiWallet(onCompleteSetup: _toPortfolio);
    }
  }

  void _toPortfolio() {
    _navigationService.navigateToMyPortfolioView(onTokenSelect: (token) {
      _navigationService.navigateToTokenDetailsView(token: token);
    });
  }

  @override
  List<ListenableServiceMixin> get listenableServices => [
        _appSettingsService,
        _fireStoreService,
        _tokenService,
        _networkService,
        _nftsService
      ];

  onViewModelReady({required bool startSetup}) async {
    final val = _prefService.getInt(key: kNftSortingKey) ?? 0;
    nftSorting = NFTsSorting.values[val];
    notifyListeners();
    if (userIsMerchant == false) {
      _analyticsService.logEvent(
          eventName: AnalyticsEvent.viewCustomerDashBoard);
    }
    onSelectNFTNetwork(lastSelectedNFTNetwork);
    showNftTooltip();
  }

  void onTapNetworkPill(bool isNFT) {
    BuildContext? context = StackedService.navigatorKey?.currentContext;
    final appNetworks = _networkService.getAppNetworks();

    if (context == null) return;

    appNetworks.sort((a, b) => _networkService.sortLocalNetwork(a, b));
    if (lastSelectedNetwork != null) {
      appNetworks.remove(lastSelectedNetwork);
      appNetworks.insert(0, lastSelectedNetwork!);
    }

    ExternalCloseSheet.showModal(
      context,
      child: AppNetworksListSheet(
        appNetworks: appNetworks,
        onSelectNetwork: (network) {
          if (isNFT) {
            onSelectNFTNetwork(network);
          } else {
            _networkService.setLastSelectedChain(network);
          }
          _navigationService.back();
        },
        selectedNetwork: isNFT ? lastSelectedNFTNetwork : lastSelectedNetwork,
        onSelectAllNetwork: () {
          if (isNFT) {
            onSelectNFTNetwork(null);
          } else {
            _networkService.clearLastSelectedChain();
          }
          _navigationService.back();
        },
      ),
    );
  }

  void onTapSorting() {
    BuildContext? context = StackedService.navigatorKey?.currentContext;
    if (context == null) return;
    ExternalCloseSheet.showModal(
      context,
      backgroundColor: Black.black25,
      child: NFTsSortingSheet(
        selectedSort: nftSorting,
        onSelectSorting: (sorting) {
          nftSorting = sorting;
          _navigationService.back();
          notifyListeners();
          _prefService.setInt(key: kNftSortingKey, value: sorting.val);
        },
      ),
    );
  }

  Future<void> showNftTooltip() async {
    await Future.delayed(const Duration(seconds: 2));
    bool show = _prefService.getBool(key: kShowNftsTooltip) ?? true;

    if (show) {
      showNFTNewIndicator = true;
      notifyListeners();
      nftTooltipController.showTooltip();
      _prefService.setBool(key: kShowNftsTooltip, value: false);
    }
  }

  void toolTipCtaPressed() {
    nftTooltipController.hideTooltip();
    setSelectedIndexTab(1);
    showNFTNewIndicator = false;
    notifyListeners();
  }

  void exploreNftShops() async {
    _analyticsService.logEvent(
        eventName: AnalyticsEvent.openDappCategoryDetail);
    final category =
        await locator<FireStoreService>().getCategoryWithName("collectibles");
    if (category == null) return;
    _navigationService.navigateToDappCategoryDetailView(
      dappCategory: category,
    );
  }

  void onSelectNFTNetwork(LocalNetwork? network) async {
    _networkService.setLastSelectedNFTChain(network);
    if (getNFTsForNetwork.isEmpty) {
      showNFTsLoader = true;
    }
    notifyListeners();
    try {
      if (network != null) {
        await _nftsService.getNftForNetwork(networkId: network.coreNetworkId!);
      } else {
        for (var network in _networkService.networks) {
          final response = await _nftsService.getNftForNetwork(
              networkId: network.coreNetworkId!);
          if (response.isNotEmpty) {
            showNFTsLoader = false;
            notifyListeners();
          }
        }
      }
    } catch (_) {}
    showNFTsLoader = false;
    notifyListeners();
  }

  void lowReputationWarningTap() {
    final context = StackedService.navigatorKey?.currentContext;
    if (context != null) {
      ExternalCloseDialog.showExternalDialog(
        context,
        child: LowReputationDialog(
          manageTokenTap: () {
            _navigationService.back();
            _navigationService.navigateToManageTokensView();
          },
        ),
      );
    }
  }

  @override
  void dispose() {
    nftTooltipController.dispose();
    super.dispose();
  }

  _setUpDefiWallet({required Function() onCompleteSetup}) {
    _walletService.setUpOrRecoverDefiWallet(onCompleteSetup: onCompleteSetup);
  }
}
