import 'package:carousel_slider/carousel_slider.dart';
import 'package:figma_squircle_updated/figma_squircle.dart';
import 'package:flutter/material.dart';
import 'package:inner_shadow_widget/inner_shadow_widget.dart';
import 'package:just_the_tooltip/just_the_tooltip.dart';
import 'package:onboard_wallet/app/index.dart';
import 'package:onboard_wallet/extensions/num.dart';
import 'package:onboard_wallet/gen/assets.gen.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/models/onboard_accounts_enum.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:onboard_wallet/theme_typography.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';
import 'package:onboard_wallet/ui/common/ui_helpers.dart';
import 'package:onboard_wallet/ui/views/dashboard/main_dashboard/main_dashboard_viewmodel.dart';
import 'package:onboard_wallet/ui/views/dashboard/widgets/tooltip_widget.dart';
import 'package:onboard_wallet/ui/widgets/onbw_shimmer.dart';
import 'package:stacked/stacked.dart';

import '../../../../widgets/buttons/column_transaction_button.dart';
import '../../../wallet/subviews/home_banner_indicator.dart'
    show HomeAccountsIndicator;

class MainBalanceCard extends ViewModelWidget<MainDashboardViewModel> {
  const MainBalanceCard({super.key});

  @override
  Widget build(BuildContext context, MainDashboardViewModel viewModel) {
    final featureService = locator<FeatureService>();

    return SliverToBoxAdapter(
      child: Column(
        children: [
          CarouselSlider(
              options: CarouselOptions(
                height: 195,
                viewportFraction: 0.92,
                initialPage: 0,
                enableInfiniteScroll: false,
                reverse: false,
                autoPlay: false,
                autoPlayInterval: const Duration(milliseconds: 1500),
                scrollDirection: Axis.horizontal,
                onPageChanged: (index, _) => viewModel.onChangeAccount(index),
              ),
              items: viewModel.accountsEnumList.map<Widget>((e) {
                switch (e) {
                  case OnboardAccountsEnum.usdAccount:
                    return _buildCard(
                      context: context,
                      viewModel: viewModel,
                      details: _CardDetails(
                        title: S.current.usdBalance,
                        balance: viewModel.getUSDAccountBalance,
                        background: kcBaseWhite,
                        balanceTextColor: Colors.black,
                        titleTextColor: Grey.grey400,
                        eyeIconColor: Grey.grey300,
                        onTap: viewModel.onVirtualAccountPressed,
                        isVirtualAccount: true,
                        actions: _buildVirtualAccountActions(
                            context, viewModel, featureService),
                      ),
                    );
                  case OnboardAccountsEnum.card:
                    return _buildCard(
                      context: context,
                      viewModel: viewModel,
                      details: _CardDetails(
                          title: S.current.cardBalance,
                          balance: viewModel.getCardBalance,
                          titleTextColor: Grey.grey200,
                          background: const Color(0xFF13004B),
                          onTap: viewModel.toCardDashboard,
                          actions: _buildCardActions(
                              context, viewModel, featureService)),
                    );
                  case OnboardAccountsEnum.defiWallet:
                    return _buildCard(
                      context: context,
                      viewModel: viewModel,
                      details: _CardDetails(
                        title: S.current.walletBalance,
                        titleTextColor: kcSteamWhite,
                        balance: viewModel.getDefiWalletBalance,
                        isCryptoAccount: true,
                        background:
                            Assets.png.cryptoBalanceBackground.image().image,
                        onTap: viewModel.onCryptoCardPressed,
                        actions: _buildCryptoActions(
                            context, viewModel, featureService),
                      ),
                    );
                  case OnboardAccountsEnum.tradingWallet:
                    return _buildCard(
                      context: context,
                      viewModel: viewModel,
                      details: _CardDetails(
                        title: S.current.tradingBalance,
                        titleTextColor: kcSteamWhite,
                        balance: viewModel.getTradingWalletBalance,
                        isTradingAccount: true,
                        background:
                            Assets.png.tradingBalanceBackground.image().image,
                        onTap: viewModel.onTradingWalletPressed,
                        actions: _buildTradingWalletActions(
                            context, viewModel, featureService),
                      ),
                    );
                  case OnboardAccountsEnum.merchantWallet:
                    return _buildCard(
                      context: context,
                      viewModel: viewModel,
                      details: _CardDetails(
                        title: S.current.merchantBalance,
                        titleTextColor: kcSteamWhite,
                        balance: viewModel.getMerchantWalletBalance,
                        background:
                            Assets.png.cryptoBalanceBackground.image().image,
                        onTap: viewModel.onMerchantWalletPressed,
                        actions: _buildMerchantWalletActions(
                            context, viewModel, featureService),
                      ),
                    );
                }
              }).toList()),
          JustTheTooltip(
            onDismiss: viewModel.nextTooltip,
            barrierDismissible: false,
            controller: viewModel.switchAccountTooltipController,
            preferredDirection: AxisDirection.down,
            backgroundColor: kcBaseWhite,
            tailLength: 16,
            tailBaseWidth: 16,
            elevation: 20,
            borderRadius: BorderRadius.circular(24),
            content: ToolTipWidget(
              buttonText: S.current.next,
              title: S.current.switchBetweenAccount,
              subtitle: S.current.aQuickSwipeAccount,
              count: "2",
              onNextPressed: viewModel.nextTooltip,
              icon: Assets.svg.swipeLeft.svg(),
            ),
            child: Padding(
              padding: const EdgeInsets.only(
                top: 12,
              ),
              child: HomeAccountsIndicator(
                length: viewModel.accountsEnumList.length,
                currentIndex: viewModel.selectedOnboardAccountEnum != null
                    ? viewModel.accountsEnumList
                        .indexOf(viewModel.selectedOnboardAccountEnum!)
                    : 0,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCard({
    required BuildContext context,
    required MainDashboardViewModel viewModel,
    required _CardDetails details,
  }) {
    final theme = Theme.of(context).textTheme;

    return GestureDetector(
      onTap: details.onTap,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: InnerShadow(
          blur: 5,
          color: kcBaseWhite.withOpacity(0.1),
          offset: const Offset(5, 5),
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 8),
            decoration: ShapeDecoration(
              image: details.background is ImageProvider
                  ? DecorationImage(
                      image: details.background as ImageProvider,
                      fit: BoxFit.cover,
                    )
                  : null,
              color: details.background is Color
                  ? details.background as Color
                  : null,
              shape: SmoothRectangleBorder(
                borderRadius: SmoothBorderRadius(
                  cornerRadius: 16,
                  cornerSmoothing: 16,
                ),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 20, 16, 13),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        details.title,
                        style: theme.body12Medium.copyWith(
                          color: details.titleTextColor,
                          fontSize: 11.fontSize,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const horizontalSpace(2),
                      SizedBox(
                        height: 14,
                        width: 14,
                        child: GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: viewModel.toggleShowBalance,
                          child: viewModel.isHidden
                              ? Assets.svg.hideGrey.svg(
                                  height: 14,
                                  width: 14,
                                  colorFilter: ColorFilter.mode(
                                    details.eyeIconColor,
                                    BlendMode.srcIn,
                                  ),
                                )
                              : Assets.svg.showGrey.svg(
                                  height: 14,
                                  width: 14,
                                  colorFilter: ColorFilter.mode(
                                    details.eyeIconColor,
                                    BlendMode.srcIn,
                                  ),
                                ),
                        ),
                      ),
                    ],
                  ),
                  const verticalSpace(5),
                  if ((viewModel.updatingCryptoBalance &&
                          details.isCryptoAccount) ||
                      (details.isTradingAccount &&
                          viewModel.updatingCryptoBalance)) ...[
                    OnbwShimmer(
                      height: 15,
                      width: 40,
                      padding: EdgeInsets.zero,
                      borderRadius: BorderRadius.circular(4),
                      shimmerBorderRadius: BorderRadius.circular(4),
                      shimmerBaseColor: kcBrandPurple,
                    ),
                  ] else ...[
                    Text(
                      details.balance,
                      style: theme.body26BoldSatoshi.copyWith(
                        color: details.balanceTextColor,
                      ),
                    ),
                  ],
                  const SizedBox(
                    height: 22,
                  ),
                  if (details.actions != null) details.actions!,
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCryptoActions(
    BuildContext context,
    MainDashboardViewModel viewModel,
    FeatureService featureService,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        ColumnTransactButton.add(
          backgroundColor: Colors.white.withOpacity(0.3),
          iconColor: Colors.white,
          textColor: Colors.white,
          onTap: viewModel.onAddPressed,
          iconSize: 22,
        ),
        ColumnTransactButton.transfer(
          backgroundColor: Colors.white.withOpacity(0.3),
          iconColor: Colors.white,
          textColor: Colors.white,
          onTap: viewModel.onSendPressed,
          iconSize: 23,
        ),
        if (!featureService.isSwapOff)
          ColumnTransactButton.swap(
            backgroundColor: Colors.white.withOpacity(0.3),
            iconColor: Colors.white,
            textColor: Colors.white,
            onTap: viewModel.onSwapPressed,
            iconSize: 22,
          ),
        ColumnTransactButton.more(
          backgroundColor: Colors.white.withOpacity(0.3),
          iconColor: Colors.white,
          textColor: Colors.white,
          onTap: viewModel.onMorePressed,
          iconSize: 22,
        ),
      ],
    );
  }

  Widget _buildTradingWalletActions(
    BuildContext context,
    MainDashboardViewModel viewModel,
    FeatureService featureService,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        ColumnTransactButton.add(
          backgroundColor: Colors.white.withOpacity(0.3),
          iconColor: Colors.white,
          textColor: Colors.white,
          onTap: viewModel.onTradingWalletAdd,
          iconSize: 22,
        ),
        ColumnTransactButton.transfer(
          backgroundColor: Colors.white.withOpacity(0.3),
          iconColor: Colors.white,
          textColor: Colors.white,
          onTap: viewModel.onTradingWalletTransfer,
          iconSize: 23,
        ),
        if (!featureService.isSwapOff)
          ColumnTransactButton.swap(
            backgroundColor: Colors.white.withOpacity(0.3),
            iconColor: Colors.white,
            textColor: Colors.white,
            onTap: viewModel.onTradingWalletSwap,
            iconSize: 22,
          ),
      ],
    );
  }

  Widget _buildMerchantWalletActions(
    BuildContext context,
    MainDashboardViewModel viewModel,
    FeatureService featureService,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        ColumnTransactButton.add(
          backgroundColor: Colors.white.withOpacity(0.3),
          iconColor: Colors.white,
          textColor: Colors.white,
          onTap: viewModel.onMerchantAddPressed,
          iconSize: 22,
        ),
        ColumnTransactButton.transfer(
          backgroundColor: Colors.white.withOpacity(0.3),
          iconColor: Colors.white,
          textColor: Colors.white,
          onTap: viewModel.onMerchantSendPressed,
          iconSize: 23,
        ),
        ColumnTransactButton.more(
          backgroundColor: Colors.white.withOpacity(0.3),
          iconColor: Colors.white,
          textColor: Colors.white,
          onTap: viewModel.onMerchantMorePressed,
          iconSize: 22,
        ),
      ],
    );
  }

  Widget _buildVirtualAccountActions(
    BuildContext context,
    MainDashboardViewModel viewModel,
    FeatureService featureService,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        ColumnTransactButton.add(
          backgroundColor: Purple.purple50,
          iconColor: kcBrandPurple,
          textColor: Colors.black,
          onTap: viewModel.onVirtualAccountAddPressed,
          iconSize: 22,
        ),
        ColumnTransactButton.transfer(
          backgroundColor: Purple.purple50,
          iconColor: kcBrandPurple,
          textColor: Colors.black,
          onTap: viewModel.onVirtualAccountTransferPressed,
          iconSize: 23,
        ),
        ColumnTransactButton.details(
          backgroundColor: Purple.purple50,
          iconColor: kcBrandPurple,
          textColor: Colors.black,
          onTap: viewModel.onVirtualAccountDetailPressed,
          iconSize: 23,
        ),
      ],
    );
  }
}

Widget _buildCardActions(
  BuildContext context,
  MainDashboardViewModel viewModel,
  FeatureService featureService,
) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      ColumnTransactButton.add(
        backgroundColor: Colors.white.withOpacity(0.22),
        iconColor: Colors.white,
        textColor: Colors.white,
        onTap: viewModel.onCardAddPressed,
        title: S.current.fund,
        iconSize: 22,
      ),
      ColumnTransactButton(
        icon: Assets.svg.showGrey.svg(
          colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
          height: 22,
          width: 22,
        ),
        text: S.current.details,
        onTap: viewModel.onCardDetailPressed,
        backgroundColor: Colors.white.withOpacity(0.22),
        textColor: Colors.white,
      ),
      ColumnTransactButton.more(
        backgroundColor: Colors.white.withOpacity(0.22),
        iconColor: Colors.white,
        textColor: Colors.white,
        onTap: viewModel.onCardMorePressed,
        iconSize: 22,
      ),
    ],
  );
}

class _CardDetails {
  final String title;
  final String balance;
  final dynamic background; // Can be ImageProvider or Color
  final VoidCallback onTap;
  final Widget? actions;
  final bool isVirtualAccount;
  final bool isTradingAccount;
  final Color titleTextColor;
  final bool isCryptoAccount;
  final Color eyeIconColor;
  final Color balanceTextColor;

  _CardDetails({
    required this.title,
    required this.balance,
    required this.background,
    required this.onTap,
    this.actions,
    required this.titleTextColor,
    this.isVirtualAccount = false,
    this.isCryptoAccount = false,
    this.isTradingAccount = false,
    this.eyeIconColor = kcSteamWhite,
    this.balanceTextColor = Colors.white,
  });
}
