import 'dart:async';
import 'dart:convert';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:collection/collection.dart';
import 'package:debounce_throttle/debounce_throttle.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:just_the_tooltip/just_the_tooltip.dart';
import 'package:nestcoinco_core_crypto_public_api/nestcoinco_core_crypto_public_api.dart';
import 'package:nestcoinco_onboard_api_gateway/nestcoinco_onboard_api_gateway.dart';
import 'package:nestcoinco_onboard_api_gateway_lite/nestcoinco_onboard_api_gateway_lite.dart'
    as lite;
import 'package:onboard_wallet/app/index.dart';
import 'package:onboard_wallet/constants/constants.dart';
import 'package:onboard_wallet/constants/string_constants.dart';
import 'package:onboard_wallet/enums/card_funding_type.dart';
import 'package:onboard_wallet/enums/onboarding_state.dart';
import 'package:onboard_wallet/extensions/extensions.dart';
import 'package:onboard_wallet/gen/assets.gen.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/haven/services/services.dart';
import 'package:onboard_wallet/haven/services/trading_activation_service.dart';
import 'package:onboard_wallet/haven/services/trading_portfolio_service.dart';
import 'package:onboard_wallet/manager/manager.dart';
import 'package:onboard_wallet/models/card/local_card_account/index.dart';
import 'package:onboard_wallet/models/local/checklist_dto/local_checklist_dto.dart';
import 'package:onboard_wallet/models/models.dart';
import 'package:onboard_wallet/models/onboard_accounts_enum.dart';
import 'package:onboard_wallet/models/token/wallet_type.dart';
import 'package:onboard_wallet/models/virtual_account/local_virtual_account/local_virtual_account.dart';
import 'package:onboard_wallet/models/virtual_account/local_virtual_account/local_virtual_account_status.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';
import 'package:onboard_wallet/ui/dialogs/info_alert/external_close_dialog.dart';
import 'package:onboard_wallet/ui/views/dashboard/crypto_dashboard/defi_dashboard/defi_dashboard.dart';
import 'package:onboard_wallet/ui/views/dashboard/main_dashboard/widgets/hide_balance_alert/hide_balance_alert_view.dart';
import 'package:onboard_wallet/ui/views/dashboard/main_dashboard/widgets/select_cards_bottom_sheet.dart';
import 'package:onboard_wallet/ui/views/dashboard/widgets/widgets.dart';
import 'package:onboard_wallet/ui/views/fund_options/fund_options_viewmodel.dart';
import 'package:onboard_wallet/ui/views/home/<USER>';
import 'package:onboard_wallet/ui/views/hub/widgets/marketing_banner.dart';
import 'package:onboard_wallet/ui/views/refer_and_earn/refer_and_earn_view.dart';
import 'package:onboard_wallet/ui/views/views.dart';
import 'package:onboard_wallet/ui/views/wallet/subviews/ongoing_order_card.dart';
import 'package:onboard_wallet/ui/views/wallet/subviews/subviews.dart';
import 'package:onboard_wallet/ui/views/wallet_connect/scanner/wallet_connect_scanner.dart';
import 'package:onboard_wallet/ui/views/wallet_connect/wallet_connect_intro/wallet_connect_intro_view.dart';
import 'package:onboard_wallet/ui/widgets/bottomsheet/bottomsheet.dart';
import 'package:onboard_wallet/ui/widgets/cards/onbw_checklist_card.dart';
import 'package:onboard_wallet/ui/widgets/deposit/deposit_option.dart';
import 'package:onboard_wallet/utils/mixins/mixins.dart';
import 'package:onboard_wallet/utils/utils.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../cards/more_options/more_options.dart' show MoreCardOptions;

class MainDashboardViewModel extends ReactiveViewModel
    with CopyToClipboardMixin, TransactionsMixin {
  final _navigationService = locator<NavigationService>();
  final _walletNavigationService = locator<WalletNavigationService>();
  final _walletService = locator<WalletService>();
  final _cryptoService = locator<CryptoService>();
  final _networkService = locator<NetworkService>();
  final _ethereumService = locator<EthereumService>();
  OnboardExchangeService onboardExchangeService =
      locator<OnboardExchangeService>();
  final _cardService = locator<CardService>();
  final _virtualAccountService = locator<VirtualAccountService>();
  final _transactionHashMonitoringService =
      locator<TransactionHashMonitoringService>();

  final _onboardProviderService = locator<OnboardProviderService>();
  final _configService = locator<ConfigService>();
  final _userService = locator<UserService>();
  final _analyticsService = locator<AnalyticsService>();
  final _walletConnectService = locator<WalletConnectService>();
  final _remoteConfigService = locator<RemoteConfigService>();
  final _prefService = locator<PreferenceService>();
  final _tokenService = locator<TokenService>();
  final _appSettingsService = locator<AppSettingsService>();
  final _partnersService = locator<PartnersService>();
  final _featureService = locator<FeatureService>();
  final _ongoingOrderService = locator<OngoingOrderService>();
  final _nftsService = locator<NftsService>();
  final _tradingPortfolioService = locator<TradingPortfolioService>();
  final _tradingActivationService = locator<TradingActivationService>();
  final _passkeyService = locator<PasskeyService>();

  final tooltipController = JustTheController();
  final switchAccountTooltipController = JustTheController();
  final sendMoneyTooltipController = JustTheController();

  int _currentTooltipIndex = 0;

  int get currentTooltipIndex => _currentTooltipIndex;

  Future<void> nextTooltip() async {
    try {
      final shownToolTip = _prefService.getBool(key: kShowDashboardTooltip);
      if (shownToolTip == true) return;
      _analyticsService.logEvent(
        eventName: AnalyticsEvent.showDashboardTooltip,
        parameters: {'currentTooltip': _currentTooltipIndex},
      );
      if (_currentTooltipIndex == 0) {
        tooltipController.showTooltip();
      } else if (_currentTooltipIndex == 1) {
        tooltipController.hideTooltip();
        await Future.delayed(const Duration(milliseconds: 100));
        switchAccountTooltipController.showTooltip();
      } else if (_currentTooltipIndex == 2) {
        switchAccountTooltipController.hideTooltip();
        await Future.delayed(const Duration(milliseconds: 100));
        sendMoneyTooltipController.showTooltip();
      } else {
        sendMoneyTooltipController.hideTooltip();
        _prefService.setBool(key: kShowDashboardTooltip, value: true);
      }
      _currentTooltipIndex++;
      notifyListeners();
    } on Exception catch (e) {
      getLogger(toString()).e(e);
    }
  }

  List<MarketingBannerUiModel> marketingBanners = [];

  bool get userHasUSDCard => _cardService.userHasActiveCard;

  bool get userIsMerchant => _userService.userIsMerchant;

  bool get hasSeenWalletConnectIntro =>
      _appSettingsService.hasSeenWalletConnectIntro.value;

  bool get isAppStoreBuild => locator<FeatureService>().isAppStoreBuild;

  Box<LocalChecklistDto> get _checkListBox =>
      Hive.box<LocalChecklistDto>(HiveBoxName.localCheckListBox);

  LocalChecklistDto? localChecklist;
  List<OnbwCheckListItemsUIModel> _onboardingCheckListItems = [];

  List<OnbwCheckListItemsUIModel> get onboardingCheckListItems =>
      _onboardingCheckListItems;

  OnbwCheckListItemsUIModel? get nextUncompletedChecklist =>
      _onboardingCheckListItems
          .firstWhereOrNull((element) => !element.completed);

  OnboardingChecklistState onboardingChecklistState =
      OnboardingChecklistState.completed;

  bool get completedOnboardingCheckList =>
      localChecklist?.isCompletedForCustomer() == true;

  StreamSubscription? networksStreamSubscription;

  StreamSubscription? _checkListSubscription;

  StreamSubscription? topTokensSubscription;
  StreamSubscription? _virtualAccountSubscription;

  int _currentIndex = 0;

  int get currentIndex => _currentIndex;

  int? _nextPage;

  bool autoPlayAdvert = true;

  bool get hasReachedMax => _nextPage == null;

  LocalNetwork? get activeNetwork => _networkService.currentChain;

  String get cardCurrency => _remoteConfigService.cardCurrency;

  int _currentAdvertPage = 0;

  int get currentBannerPage => _currentAdvertPage;

  set currentIndex(int index) {
    _currentIndex = index;
    notifyListeners();
  }

  double get onBoardingProgress {
    int completedItemsLength =
        onboardingCheckListItems.where((element) => element.completed).length;
    return completedItemsLength / onboardingCheckListItems.length;
  }

  String? get address => _walletService.getWalletAddress;

  LocalNetwork? get network => _networkService.currentChain;

  String countryCode = 'NG';

  bool get hasVirtualAccount => _virtualAccountService.hasAccount;

  List<LocalVirtualAccount> get virtualAccounts =>
      _virtualAccountService.virtualAccountBox.values.toList();

  List<LocalCardAccount> get cardAccounts =>
      _cardService.transferableCardAccounts.toList();

  bool get showAddNewCard =>
      cardAccounts.length < (_cardService.activeCardAccountLimit.value ?? 0);

  OnboardUser? get currentUser => _userService.getCurrentUser();

  lite.AssetBalance? _tradeBalance;

  lite.AssetBalance? get tradeBalance =>
      _tradingPortfolioService.lastUpdatedBalance?.balance ?? _tradeBalance;

  set tradeBalance(lite.AssetBalance? value) {
    _tradeBalance = value;
    notifyListeners();
  }

  MainDashboardViewModel() {
    _transactionHashMonitoringService.pendingLocalTransaction.listen((v) {
      if (v?.localTransaction.status == LocalTransactionStatus.successful) {
        _fetchToken(true);
      }
    });
    _cardService.cardAccountBox.listenable().addListener(() {
      _updateCardBalance();
      notifyListeners();
    });
    _appSettingsService.balanceHiddenBox.listenable().addListener(() async {
      notifyListeners();
      final checkList = _checkListBox.values.firstOrNull;
      if (checkList != null) {
        checkList.copyWith(
          hasVirtualCard: _cardService.userHasActiveCard,
        );
        await _checkListBox.clear();
        await _checkListBox.add(checkList);
        notifyListeners();
      }
    });

    topTokensSubscription = _tokenService.balanceRefreshed.listen((_) {
      notifyListeners();
    });

    _virtualAccountSubscription =
        _virtualAccountService.virtualAccountRefreshed.listen((v) async {
      notifyListeners();
      if (localChecklist != null &&
          completedOnboardingCheckList == false &&
          localChecklist?.hasVirtualAccount != true) {
        final newCheckList = localChecklist!.copyWith(
            hasVirtualAccount: _virtualAccountService.hasNonTerminatedAccount);
        await _checkListBox.clear();
        await _checkListBox.add(newCheckList);
      }
    });

    _tradingPortfolioService.getUserBalance();
  }

  num _cardBalance = 0;

  num _cryptoBalance = 0;

  bool updatingCryptoBalance = false;

  List<OnboardAccountsEnum> accountsEnumList = [];

  num get _merchantWalletBalance =>
      _partnersService.allTradingWalletBalance().item1;

  String get getDefiWalletBalance =>
      isHidden ? "**" : _cryptoBalance.shortBalance();

  String get getMerchantWalletBalance =>
      isHidden ? "**" : _merchantWalletBalance.shortBalance();

  String get getTradingWalletBalance =>
      isHidden ? "**" : (tradeBalance?.availableBalance ?? 0).shortBalance();

  String get getCardBalance => isHidden ? "**" : _cardBalance.shortBalance();

  bool get isMerchant => _userService.getCurrentUser()?.isMerchant == true;

  num get _cashBalance => _virtualAccountService.availableBalance ?? 0;

  bool get isTradingWalletSetUp =>
      _tradingPortfolioService.doesTradingWalletExist();

  bool get isDeFiWalletSetup => _walletService.isDefiWalletSetUp;

  bool get isMerchantWalletSetup =>
      userIsMerchant && _partnersService.allAccounts.isNotEmpty;

  num get _allAccountBalance =>
      _cardBalance +
      _cashBalance +
      _cryptoBalance +
      _merchantWalletBalance +
      (tradeBalance?.availableBalance ?? 0);

  String get getTotalBalance =>
      isHidden ? "**" : _allAccountBalance.shortBalance();

  OnboardAccountsEnum? selectedOnboardAccountEnum;

  bool get virtualAccountSupported {
    return accountsEnumList.contains(OnboardAccountsEnum.usdAccount);
  }

  bool get defiWalletSupprted {
    return accountsEnumList.contains(OnboardAccountsEnum.defiWallet);
  }

  bool get tradingWalletSupported {
    return accountsEnumList.contains(OnboardAccountsEnum.tradingWallet);
  }

  bool get cardSupported {
    return accountsEnumList.contains(OnboardAccountsEnum.card);
  }

  bool get merchantWalletSupported {
    return accountsEnumList.contains(OnboardAccountsEnum.merchantWallet);
  }

  String get getUSDAccountBalance => isHidden
      ? "**"
      : _cashBalance.shortBalance(
          symbol:
              _virtualAccountService.virtualAccount?.terms?.currencySymbol ??
                  '\$',
        );

  bool get virtualAccountIsPending =>
      _virtualAccountService.virtualAccount?.accountStatus ==
      LocalVirtualAccountStatus.pending;

  _updateCardBalance() {
    num balance = 0;
    for (var account in _cardService.cardAccountBox.values.where(
        (e) => e.localCardAccountStatus == LocalCardAccountStatus.active)) {
      balance += (account.card?.balance ?? 0);
    }
    _cardBalance = balance;
    notifyListeners();
  }

  Future _fetchToken(bool forceRefresh) async {
    if (_tokenService.addressTokenBox.isEmpty) {
      setBusy(true);
    }

    initEthereumProvider();
    await _passkeyService.getRegisteredPasskeys();
    localChecklist = _checkListBox.values.firstOrNull;
    if (localChecklist != null) {
      _parseLocalChecklistDto(localChecklist!);
    }
    final responses = await Future.wait([
      _tokenService.getTokenBalances(
          forceRefresh: forceRefresh, clearDb: false),
      _cardService.getCardAccount(),
      _virtualAccountService.getVirtualAccounts(),
      _getTopTokens(),
      if (completedOnboardingCheckList == false) ...[
        _userService.getOnboardingCheckListItems(currentUser?.userId),
      ]
    ]);

    if (isMerchant) {
      await _fetchTradingWallet();
    }

    _checkListSubscription = _checkListBox.watch().listen((event) {
      LocalChecklistDto? updatedValue = event.value;
      if (updatedValue != null) {
        _logChecklistCompletedEvents(updatedValue);
        _parseLocalChecklistDto(updatedValue);
        if (updatedValue.isCompletedForCustomer()) {
          _analyticsService.logEvent(
            eventName: AnalyticsEvent.customerCompletedChecklist,
            parameters: {
              EventParameterKey.emailAddress:
                  _userService.getCurrentUser()?.email,
            },
          );
        }
      }
    });

    if (responses.length > 3) {
      Result<ChecklistDto?>? checkListResponse = responses
          .firstWhereOrNull((element) => element is Result<ChecklistDto?>);
      await checkListResponse?.when(
        success: (success) async {
          bool walletProtected = await _walletService.hasWalletProtected();

          bool hasCard = _cardService.userHasActiveCard;

          bool hasVirtualAccount = _virtualAccountService.hasAccount;

          final LocalChecklistDto newChecklistDto =
              LocalChecklistDto.fromRemoteDto(
            success,
            hasProtectedWallet: walletProtected,
            hasAddedPhoneNumber:
                _userService.getCurrentUser()?.phoneNumberVerified ?? false,
            hasVirtualCard: hasCard,
            hasVirtualAccount: hasVirtualAccount,
          );
          await _checkListBox.clear();
          await _checkListBox.add(newChecklistDto);
        },
        failure: (_) {},
      );
    }

    if (isBusy) {
      setBusy(false);
    } else {
      notifyListeners();
    }

    final NetworkDatabaseService networkDatabaseService =
        locator<NetworkDatabaseService>();
    final networks = networkDatabaseService.getAllNetworks();
    _walletConnectService.registerAccountAndEventHandler(networks: networks);

    _updateOrderStatus();
  }

  Future _getTopTokens() async {
    await _tokenService.getTopTokens(page: 1);
    await _tokenService.getTopTokens(
        page: 1, category: TopTokenOrderCategory.GAINERS);
    return true;
  }

  initEthereumProvider() async {
    _ethereumService.init();
  }

  onSendPressed() {
    _analyticsService.logEvent(eventName: AnalyticsEvent.tapsSendOnHome);

    if (isDeFiWalletSetup == false) {
      _toCryptoDashboard();
    } else {
      _navigationService.navigateToChooseCryptoView(
        onTokenSelect: (token, tokenNetworkBalance) {
          _walletNavigationService.navigateToFundingOption(
            tokenBalance: token,
            walletType: token.walletType ?? WalletType.spot,
            fundOptionType: FundOptionFLowType.transfer,
            tokenNetworkBalance: tokenNetworkBalance,
          );
        },
        fundOptionFLowType: FundOptionFLowType.transfer,
      );
    }
  }

  onVirtualAccountTransferPressed() {
    final virtualAccount = _virtualAccountService.virtualAccount;
    if (virtualAccount != null) {
      _navigationService.navigateToVirtualAccountTransferOptionsView(
        virtualAccount: virtualAccount,
      );
    } else {
      _toCashDashboard();
    }
  }

  Future setUp() async {
    await _cryptoService.fetchNetwork();
    await _fetchToken(false);
    await locator<FireStoreService>().setup();
    _cryptoService.fetchExchangeNetworks();
    _networkService.getActiveChain();
    _walletService.getNestcoinRpc();
    await _fetchNFTs();
  }

  Future refreshWallet() async {
    locator<NotificationsViewModel>().refresh();
    await Future.wait([
      _fetchToken(true),
      _ongoingOrderService.fetchOngoingOrders(),
      _fetchNFTs(),
    ]);
    notifyListeners();
  }

  Future _fetchNFTs() async {
    _networkService.getLastSelectedChain();
    final network =
        _networkService.lastSelectedChain ?? LocalNetwork.defaultNetwork();
    final address = _walletService.getWalletAddress;
    final networkId = network?.coreNetworkId;
    if (networkId != null && address != null) {
      _nftsService.getNftForNetwork(networkId: networkId);
    } else {
      for (var network in _networkService.networks) {
        _nftsService.getNftForNetwork(networkId: network.coreNetworkId!);
      }
    }
  }

  @override
  List<ListenableServiceMixin> get listenableServices => [
        _cryptoService,
        _tokenService,
        _partnersService,
        _appSettingsService,
        _virtualAccountService,
        _tradingPortfolioService,
      ];

  onAddPressed() {
    _analyticsService.logEvent(eventName: AnalyticsEvent.tapDepositOnHome);
    if (isDeFiWalletSetup == false) {
      _toCryptoDashboard();
    } else {
      _navigationService.navigateToChooseCryptoView(
        onTokenSelect: (token, tokenNetworkBalance) {
          _walletNavigationService.navigateToFundingOption(
            tokenBalance: token,
            walletType: token.walletType ?? WalletType.spot,
            fundOptionType: FundOptionFLowType.add,
            tokenNetworkBalance: tokenNetworkBalance,
          );
        },
        fundOptionFLowType: FundOptionFLowType.add,
      );
    }
  }

  onVirtualAccountAddPressed() {
    _analyticsService.logEvent(
      eventName: AnalyticsEvent.clickFundVirtualAccount,
    );
    if (hasVirtualAccount) {
      _navigationService.navigateToVirtualAccountFundingOptionView(
          account: _virtualAccountService.virtualAccount!);
    } else {
      _toCashDashboard();
    }
  }

  onVirtualAccountDetailPressed() {
    _analyticsService.logEvent(
      eventName: AnalyticsEvent.clickFundVirtualAccount,
    );
    if (hasVirtualAccount) {
      _navigationService.navigateToVirtualAccountDetailsView(
          virtualAccount: _virtualAccountService.virtualAccount!);
    } else {
      _toCashDashboard();
    }
  }

  void _initDashboardCards() {
    final userSupportedFeatures =
        _userService.getUserCountryFeature()?.features ?? [];

    List<OnboardAccountsEnum> supportedFeatures = List.from(
      OnboardAccountsEnum.values.where(
        (e) => userSupportedFeatures.contains(e.id),
      ),
    );

    ///fail safe.
    if (supportedFeatures.isEmpty) {
      supportedFeatures = List.from([OnboardAccountsEnum.defiWallet]);
      if (!_userService.userIsMerchant) {
        supportedFeatures.add(OnboardAccountsEnum.merchantWallet);
      }
    }
    accountsEnumList = supportedFeatures.toSet().toList();
    if (!isMerchant) {
      accountsEnumList.remove(OnboardAccountsEnum.merchantWallet);
    }
    selectedOnboardAccountEnum = accountsEnumList.first;

    final lastSelectedAccount =
        _appSettingsService.getLastSelectedOnboardAccount();

    if (lastSelectedAccount != null) {
      List<OnboardAccountsEnum> modifiableList = List.from(accountsEnumList);
      modifiableList.remove(lastSelectedAccount);
      modifiableList.insert(0, lastSelectedAccount);
      if (!isMerchant) {
        modifiableList.remove(OnboardAccountsEnum.merchantWallet);
      }
      selectedOnboardAccountEnum = modifiableList.first;
      accountsEnumList = modifiableList;
    }
  }

  StreamSubscription? _userFeatureBoxSub;

  void _listenToUserFeatureBox() {
    _userFeatureBoxSub = _userService.userCountryFeatureBox.watch().listen((e) {
      _initDashboardCards();
    });
  }

  onReady() {
    ///fail safe, for where initializing was for any reason not called during start up.
    locator<WalletService>().initializeWallet(useBiometric: false);
    _listenToUserFeatureBox();
    _initDashboardCards();
    notifyListeners();
    _fetchTradingPortfolioBalance();
    locator<TradingMarketService>().getMarketAssetPrices();
    setUp();

    _cryptoBalance = _tokenService.getPortfolioBalance();
    locator<TradingActivationService>().setup();

    _updateCardBalance();
    _getConfigs();
    _initCountryCode();
    _networkService.getActiveChain();
    _initMarketingBanners();
    _configService.getAssetConfig();
    _analyticsService.logEvent(
      eventName: AnalyticsEvent.viewHomeTab,
    );
    listenToOrderEvent();
    locator<NotificationsViewModel>().onReady();
    locator<NotificationsViewModel>().refresh();
    listenToPortfolioChanges();
    SchedulerBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(seconds: 3)).then((_) {
        nextTooltip();
      });
    });

    locator<TradingAssetsService>().getAssetsConfig();
  }

  _getConfigs() async {
    List<LocalCardAccount> cardAccounts =
        _cardService.cardAccountBox.values.toList();
    String? userCountry = await _userService.getUserTransactionCountryCode();
    if (userCountry == null) return;
    for (var element in cardAccounts) {
      _cardService.getRemoteFundingConfig(
        country: userCountry,
        cardCurrency: element.cardCurrency,
        packageId: element.cardPackageId,
      );
      _cardService.getRemoteWithdrawalConfig(
        country: userCountry,
        cardCurrency: element.cardCurrency,
        packageId: element.cardPackageId,
      );
    }
  }

  @override
  void dispose() {
    networksStreamSubscription?.cancel();
    _checkListSubscription?.cancel();
    _virtualAccountSubscription?.cancel();
    topTokensSubscription?.cancel();
    _userFeatureBoxSub?.cancel();
    _cardService.cardAccountBox.listenable().removeListener(notifyListeners);
    _tokenService.myPortfolioBoxName
        .listenable()
        .removeListener(notifyListeners);
    super.dispose();
  }

  onSwapPressed() async {
    _analyticsService.logEvent(eventName: AnalyticsEvent.tapsTradeOnHome);
    if (isDeFiWalletSetup == false) {
      _toCryptoDashboard();
    } else {
      _navigationService.navigateToSwapView();
    }
  }

  listenToOrderEvent() {
    onboardExchangeService.orderEventsStream?.listen((event) async {
      _updateOrderStatus();
    });
    _onboardProviderService.orderWebEventsStream?.listen((event) {
      _updateOrderStatus();
    });
  }

  final deBouncer = Debouncer<String?>(const Duration(seconds: 1),
      checkEquality: false, initialValue: null);

  StreamSubscription? debounceStreamSub;

  listenToPortfolioChanges() {
    _tokenService.myPortfolioBoxName.watch().listen((event) {
      LocalAddressTokenBalance? token =
          event.value as LocalAddressTokenBalance?;
      deBouncer.setValue(token?.uniqueKey);
    });

    debounceStreamSub = deBouncer.values.listen((_) {
      final newBalance = _tokenService.getPortfolioBalance();
      if (newBalance != _cryptoBalance) {
        //play a small shimmer animation before the balance updates,
        // so it doesn't feel buggy.
        updatingCryptoBalance = true;
        notifyListeners();
        Future.delayed(const Duration(milliseconds: 300)).whenComplete(() {
          updatingCryptoBalance = false;
          _cryptoBalance = newBalance;
          notifyListeners();
        });
      } else {
        _cryptoBalance = newBalance;
        notifyListeners();
      }
    });
  }

  Future<void> _updateOrderStatus() async {
    await _ongoingOrderService.fetchOngoingOrders();
    notifyListeners();
  }

  void showReferAndEarn() {
    _navigationService.navigateToView(const ReferAndEarnView());
  }

  toCardView() {
    _analyticsService.logEvent(eventName: AnalyticsEvent.getUsdCardViaBanner);
    toCardDashboard();
  }

  onSellCardClicked() {
    _analyticsService.logEvent(eventName: AnalyticsEvent.sellCryptoViaBanner);

    _navigationService.navigateToChooseCryptoView(
      onTokenSelect: (token, tokenNetworkBalance) {
        _walletNavigationService.navigateToFundingOption(
          tokenBalance: token,
          walletType: token.walletType ?? WalletType.spot,
          fundOptionType: FundOptionFLowType.sell,
          tokenNetworkBalance: tokenNetworkBalance,
        );
      },
      fundOptionFLowType: FundOptionFLowType.sell,
    );
  }

  _parseLocalChecklistDto(LocalChecklistDto localChecklistDto) async {
    _onboardingCheckListItems =
        _userService.getOnboardingUiModels(localChecklistDto);

    if (localChecklistDto.isCompletedForCustomer()) {
      onboardingChecklistState = OnboardingChecklistState.completed;
    } else {
      onboardingChecklistState = OnboardingChecklistState.showFullChecklist;
    }
    notifyListeners();
  }

  void _initCountryCode() {
    final user = locator<UserService>().getCurrentUser();
    String? code =
        user?.country ?? _prefService.getString(key: kDeviceCountryCode);
    if (code != null) {
      countryCode = code;
    }
  }

  onAdvertPageChanged(int index) {
    _currentAdvertPage = index;
    notifyListeners();
    if (index == 0 && autoPlayAdvert) {
      autoPlayAdvert = false;
      notifyListeners();
    }
  }

  void _initMarketingBanners() {
    try {
      final marketingBannerJson = _remoteConfigService.homeBannerJson;
      if (marketingBannerJson.isEmpty) return;
      List<Map<String, dynamic>> jsonList =
          List.from(jsonDecode(marketingBannerJson))
              .cast<Map<String, dynamic>>();
      List<MarketingBannerUiModel> remoteBanners = [];

      for (int i = 0; i < jsonList.length; i++) {
        remoteBanners
            .add(MarketingBannerUiModel.fromJson(json: jsonList[i], index: i));
      }
      remoteBanners.removeWhere((element) => isNullOrEmpty(element.header));
      marketingBanners = remoteBanners;
      notifyListeners();
    } catch (e) {
      getLogger(toString()).e(e);
    }
  }

  onMarketingBannerClicked(String? url) {
    _analyticsService.logEvent(
      eventName: AnalyticsEvent.clickMarketingBannerOnHome,
      parameters: {
        EventParameterKey.url: url,
      },
    );

    if (url == null) return;

    var dynamicLinkService = locator<DynamicLinkService>();
    final uri = Uri.tryParse(url);
    if (uri == null) return;
    dynamicLinkService.navigate(uri);
  }

  List<Widget> get bannerItems {
    return [
      if (_ongoingOrderService.hasActionRequiredCustomerOrder) ...[
        DisputeOrderCard(
          subtitle: _ongoingOrderService.customerDisputeOrderSubtitle,
          title: S.current.yourOrderNeedsAttention(
              _ongoingOrderService.customerDisputeOrdersLength),
          onTap: () => _ongoingOrderService.toCustomerDisputeOrderWebView(),
        )
      ],
      if (_ongoingOrderService.hasActionRequiredMerchantOrder) ...[
        DisputeOrderCard(
          subtitle: _ongoingOrderService.merchantDisputeOrderSubtitle,
          title: S.current.yourOrderNeedsAttention(
              _ongoingOrderService.merchantDisputeOrdersLength),
          onTap: () => _ongoingOrderService.toMerchantDisputeOrderWebView(),
        )
      ],
      if (_ongoingOrderService.hasOngoingMerchantOrder) ...[
        OngoingOrderCard(
          subtitle: _ongoingOrderService.merchantOngoingOrderSubtitle,
          onTap: () => _ongoingOrderService.toMerchantOngoingOrderWebView(),
          progress: _ongoingOrderService.merchantOrderProgress,
        )
      ],
      if (_ongoingOrderService.hasOngoingCustomerOrder) ...[
        OngoingOrderCard(
          subtitle: _ongoingOrderService.customerOngoingOrderSubtitle,
          onTap: () => _ongoingOrderService.toCustomerOngoingOrderWebView(),
          progress: _ongoingOrderService.totalOrderProgress,
        )
      ],
      ...marketingBanners.map(
        (e) => Center(
          child: MarketingBanner(
            marketingBannerDecorationType: MarketingBannerDecorationType.home,
            height: 108,
            onTap: () => onMarketingBannerClicked(e.redirectUrl),
            title: e.header ?? '',
            textColor: kcMidnightBlue,
            bgColor: e.backgroundColorHexCode,
            icon: CachedNetworkImage(
              imageUrl: e.iconUrl ?? '',
              errorWidget: (_, __, ___) => Assets.svg.marketingBannerIcon.svg(),
            ),
            subtitle: e.hubPromotionText ?? '',
            iconHeight: 72,
            iconWidth: 72,
          ),
        ),
      ),
      if (userHasUSDCard == false) ...[
        Center(
          child: AdvertCard(
            subtitle: S.current.makePaymentGlobally,
            onTap: toCardView,
            title: S.current.getAUsdCard(
              cardCurrency,
            ),
            bgColor: const Color(0xffB399FF).withOpacity(0.1),
            icon: Assets.svg.getUsdCardIlos.svg(),
          ),
        ),
      ],
      Center(
        child: AdvertCard(
          subtitle: S.current.getYourFriendsToSignUpAndEarn,
          bgColor: const Color(0xFFAB0A60).withOpacity(0.06),
          onTap: () {
            _analyticsService.logEvent(
                eventName: AnalyticsEvent.clickReferAdvertBanner);
            showReferAndEarn();
          },
          title: S.current.earnForEveryInvitee,
          icon: Assets.svg.referAdvertIlos.svg(),
        ),
      ),
      if (!_featureService.isAppStoreBuild)
        Center(
          child: AdvertCard(
            title: S.current.sellAnyCrypto,
            onTap: onSellCardClicked,
            subtitle: S.current.goFromAnyCryptoTokenToCash,
            icon: Assets.svg.sellCryptoIlos.svg(),
            bgColor: const Color(0xFFAB0A60).withOpacity(0.06),
          ),
        ),
    ];
  }

  toCommunityWebView() async {
    String url = _remoteConfigService.communityBannerWebUrl;
    _analyticsService.logEvent(
      eventName: AnalyticsEvent.communityWebBannerClick,
    );
    final parsedUri = Uri.tryParse(url);
    if (parsedUri == null) return;
    if (await canLaunchUrl(parsedUri)) {
      launchUrl(parsedUri, mode: LaunchMode.externalApplication);
    }
  }

  bool get isHidden => _appSettingsService.balanceHidden;

  void toggleShowBalance() {
    final hide = !isHidden;
    _appSettingsService.balanceHiddenBox.put("isHidden", hide);
    if (hide) {
      _analyticsService.logEvent(eventName: AnalyticsEvent.hidesAppBalance);
      _showHideBalanceInfoCard();
    } else {
      _analyticsService.logEvent(eventName: AnalyticsEvent.unHidesAppBalance);
    }
  }

  int tabIndex = 0;

  onPageChanged(int index) {
    tabIndex = index;
  }

  void toCardDashboard(
      {bool isAccountList = false,
      bool isAccountSetup = false,
      LocalCardAccount? account}) {
    if (isAccountList) {
      _navigationService.back();
    }
    if (isAccountList && cardAccounts.isEmpty) {
      _analyticsService.logEvent(
          eventName: AnalyticsEvent.clickSetupVirtualCard);
    } else {
      _analyticsService.logEvent(
          eventName: AnalyticsEvent.clickIntoVirtualCards);
    }
    _appSettingsService
        .updateLastSelectedOnboardAccount(OnboardAccountsEnum.card);

    _navigationService.navigateTo(DashboardRouteViewRoutes.cardView,
        arguments: NestedCardViewArguments(
            startSetup: isAccountSetup, initialCardAccountId: account?.id),
        id: 1);
  }

  void onCryptoCardPressed() {
    _toCryptoDashboard();
  }

  /// this is is for clicking from accounts list view
  void onDefiWalletCardTap() {
    if (_walletService.isDefiWalletSetUp) {
      _navigationService.back();
      _toCryptoDashboard();
    } else {
      _analyticsService.logEvent(
          eventName: AnalyticsEvent.clickSetupDefiWallet);
      _walletService.setUpOrRecoverDefiWallet(
          onCompleteSetup: _toCryptoDashboard);
    }
  }

  void _toCryptoDashboard() {
    _appSettingsService
        .updateLastSelectedOnboardAccount(OnboardAccountsEnum.defiWallet);

    _analyticsService.logEvent(eventName: AnalyticsEvent.clickIntoDefiWallet);
    _navigationService.navigateToView(const DefiDashboard());
  }

  void onVirtualAccountPressed(
      {bool isAccountList = false,
      bool isAccountSetup = false,
      LocalVirtualAccount? account}) {

    _appSettingsService
        .updateLastSelectedOnboardAccount(OnboardAccountsEnum.usdAccount);
    if (isAccountList) {
      _navigationService.back();
    }
    _toCashDashboard(
        isAccountList: isAccountList, isAccountSetup: isAccountSetup);
  }

  void _toCashDashboard(
      {bool isAccountList = false, bool isAccountSetup = false}) {

    if (isAccountSetup == false) {
      _analyticsService.logEvent(
          eventName: AnalyticsEvent.clickSetupUsdAccount);
    } else {
      _analyticsService.logEvent(eventName: AnalyticsEvent.clickIntoUsdAccount);
    }

    _navigationService.navigateTo(DashboardRouteViewRoutes.cashDashboardView,
        arguments: NestedCashDashboardViewArguments(startSetup: isAccountSetup),
        id: 1);
  }

  void onMorePressed() {
    BuildContext? context = StackedService.navigatorKey?.currentContext;
    if (context == null) return;
    _analyticsService.logEvent(
      eventName: AnalyticsEvent.clickMoreOnDashBoard,
    );

    if (isDeFiWalletSetup == false) {
      _toCryptoDashboard();
    } else {
      final address = _walletService.getWalletAddress ?? "";
      ExternalCloseSheet.showModal(
        context,
        backgroundColor: Grey.grey25,
        child: MoreDashboardOptions(
          walletAddress: address,
          onCopyAddressPressed: () {
            _analyticsService.logEvent(
                eventName: AnalyticsEvent.copiesWalletOnAddressOnHome);
            copyWalletAddress(address: address);
            _navigationService.back();
          },
          onWalletConnectPressed: () {
            _navigationService.back();
            onScanPressed();
          },
        ),
      );
    }
  }

  _showHideBalanceInfoCard() {
    final showHideBalanceInfo =
        _prefService.getBool(key: kShowHiddenBalanceInfoCard) ?? true;
    if (showHideBalanceInfo) {
      final context = StackedService.navigatorKey?.currentContext;
      ExternalCloseDialog.showExternalDialog(
        context!,
        child: const HideBalanceAlert(),
      );
    }
  }

  onScanPressed() async {
    if (!hasSeenWalletConnectIntro) {
      _showWalletConnectIntro();
    } else {
      _showWalletConnectScanner();
    }
    _analyticsService.logEvent(eventName: AnalyticsEvent.walletConnectStart);
  }

  _showWalletConnectIntro() {
    _navigationService.navigateToView(
      WalletConnectIntroView(
        onContinue: () {
          _navigationService.back();
          _navigationService.navigateToView(
            WalletConnectIntroView(
              onContinue: () {
                _navigationService.back();
                _showWalletConnectScanner();
              },
              page: WalletConnectIntroPage.second,
            ),
          );
        },
        page: WalletConnectIntroPage.first,
      ),
    );
  }

  _showWalletConnectScanner() async {
    var result = await _navigationService.navigateToView(
      WalletConnectQrCodeScanner(
        onInfoPressed: () => _showWalletConnectIntro(),
      ),
    );
    if (result is String) {
      _initiateWallConnectSession(url: result);
    }
  }

  Future _initiateWallConnectSession({required String url}) async {
    try {
      _walletConnectService.showConnecting();
      await _walletConnectService.connectWithUri(url);
    } catch (e) {
      locator<ToastManager>().showErrorToast(
        text: S.current.invalidWalletConnectUri,
      );
    }
  }

  void toCompleteProfileView() {
    _analyticsService.logEvent(eventName: AnalyticsEvent.clickChecklistCard);
    _navigationService.navigateToCompleteProfileView(
      onbwChecklistUiModelList: onboardingCheckListItems,
    );
  }

  Future _fetchTradingWallet() async {
    _partnersService.loadLocalData();
    _partnersService.getPartnerTradingAccountBalance();
    _partnersService.getPartnerTradingAccount();
  }

  void _logChecklistCompletedEvents(LocalChecklistDto updatedValue) {
    if (updatedValue.hasProtectedWallet == true &&
        localChecklist?.hasProtectedWallet != true) {
      _analyticsService.logEvent(
          eventName: AnalyticsEvent.completesProtectYourWalletChecklist);
    }
    if (updatedValue.hasAddedPhoneNumber == true &&
        localChecklist?.hasAddedPhoneNumber != true) {
      _analyticsService.logEvent(
          eventName: AnalyticsEvent.completesEnable2faChecklist);
    }
    if (updatedValue.hasCreatedAd == true &&
        localChecklist?.hasCreatedAd != true) {
      _analyticsService.logEvent(
          eventName: AnalyticsEvent.completesCreateAdChecklist);
    }

    if (updatedValue.hasVirtualCard == true &&
        localChecklist?.hasVirtualCard != true) {
      _analyticsService.logEvent(
          eventName: AnalyticsEvent.completesGetAUsdCardChecklist);
    }

    localChecklist = updatedValue;
  }

  void onGiftIconTap() {
    _analyticsService.logEvent(
        eventName: AnalyticsEvent.clicksReferAndEarnFromAppBar);
    showReferAndEarn();
  }

  void hideOnboard2() {
    _appSettingsService.setHideOnboard2Tag();
  }

  void showAnnouncementTweet() async {
    final urlString = _remoteConfigService.onboard2TweetUrl;
    final tweetUrl = Uri.tryParse(urlString);

    if (tweetUrl != null && await canLaunchUrl(tweetUrl)) {
      launchUrl(tweetUrl);
    }
  }

  void onChangeAccount(int index) {
    selectedOnboardAccountEnum = accountsEnumList[index];
    notifyListeners();
  }

  void toAccountsListView() {
    _navigationService.navigateToAccountsListView();
  }

  void onTradingWalletSwap() async {
    if (isTradingWalletSetUp) {
      // _analyticsService.logEvent(eventName: AnalyticsEvent.dashboardConvertTap);
      final marketDataPriceList =
          locator<TradingMarketService>().marketDataPriceList;
      final portfolioAssetsBalances =
          _tradingPortfolioService.portfolioAssetsBalances;

      if (marketDataPriceList.isNotEmpty) {
        const usdAssetSymbol = "USD";
        final fromAsset = marketDataPriceList.firstWhereOrNull(
            (element) => element.assetInfo?.symbol == usdAssetSymbol);
        final fromAssetSymbol = fromAsset?.assetInfo?.symbol ??
            marketDataPriceList.last.assetInfo?.symbol;
        final fromAssetBalance = portfolioAssetsBalances
            .firstWhereOrNull((element) => element.asset == fromAssetSymbol);

        _navigationService.navigateToHavenConvertAssetView(
            fromAsset: fromAsset ?? marketDataPriceList.last,
            fromAssetBalance: fromAssetBalance?.balance ??
                lite.AssetBalance((b) => b
                  ..availableBalance = 0
                  ..totalBalance = 0));
      }
    } else {
      _tradingActivationService.startTradingWalletSetup();
    }
  }

  void onTradingWalletAdd() async {
    if (isTradingWalletSetUp) {
      _navigationService.navigateToHavenSelectAssetsView();
    } else {
      _tradingActivationService.startTradingWalletSetup();
    }
  }

  void onTradingWalletTransfer() async {
    if (isTradingWalletSetUp) {
      _navigationService.navigateToHavenSelectUserAssetsView();
    } else {
      _tradingActivationService.startTradingWalletSetup();
    }
  }

  void onReferFriend() {
    locator<HomeViewModel>().switchTab(3);
  }

  void onShareFeedbackTap() {
    launchUrl(
      Uri.parse(_remoteConfigService.feedbackUrl),
      mode: LaunchMode.externalApplication,
    );
  }

  void onSeeWhatsNew() {
    launchUrl(
      Uri.parse(_remoteConfigService.changeLogUrl),
      mode: LaunchMode.externalApplication,
    );
  }

  void onTradingWalletPressed({bool isAccountList = false}) {
    _appSettingsService
        .updateLastSelectedOnboardAccount(OnboardAccountsEnum.tradingWallet);

    if (isAccountList && isTradingWalletSetUp == false) {
      _analyticsService.logEvent(
          eventName: AnalyticsEvent.clickSetupTradingWallet);
    } else {
      _analyticsService.logEvent(
          eventName: AnalyticsEvent.clickIntoTradingWallet);
    }

    _navigationService.navigateToHavenHomeView();
  }

  void onMerchantWalletPressed({bool isAccountList = false}) {
    _appSettingsService
        .updateLastSelectedOnboardAccount(OnboardAccountsEnum.merchantWallet);

    _toMerchantDashboard(isAccountList: isAccountList);
  }

  void _toMerchantDashboard({bool isAccountList = false}) {
    if (isAccountList && isMerchantWalletSetup == false) {

      _analyticsService.logEvent(
          eventName: AnalyticsEvent.clickSetupMerchantWallet);
    } else {
      _analyticsService.logEvent(
          eventName: AnalyticsEvent.clickIntoMerchantWallet);
    }
    if (isAccountList) {
      _navigationService.back();
    }
    _navigationService
        .navigateTo(DashboardRouteViewRoutes.merchantDashboardView, id: 1);
  }

  bool get hasActiveCardAccount => _cardService.activeCardAccounts.isNotEmpty;

  String get defiButtonLabel {
    final user = _userService.getCurrentUser();
    if (user?.hasVerifiedWalletAddress == true) {
      return S.current.restore;
    } else {
      return S.current.setup;
    }
  }

  void onCardAddPressed() {
    if (hasActiveCardAccount) {
      _onCardOptions(onSelectCardAccount: (cardAccount) {
        _navigationService.navigateToCardFundOptionsView(
          cardFundingType: CardFundingType.subsequent,
          cardUsageTerms: cardAccount.cardUsageTerms,
          localCardAccount: cardAccount,
        );
      });
    } else {
      toCardDashboard();
    }
  }

  void _onCardOptions(
      {required Function(LocalCardAccount) onSelectCardAccount}) {
    BuildContext? context = StackedService.navigatorKey?.currentContext;
    if (context == null) return;
    ExternalCloseSheet.showModal(
      context,
      child: SelectCardsBottomSheet(
        onSelectCardAccount: (cardAccount) {
          _navigationService.back();
          _handleCardOptions(cardAccount, onSelectCardAccount);
        },
        cardAccounts: _cardService.transferableCardAccounts,
      ),
    );
  }

  void _handleCardOptions(LocalCardAccount cardAccount,
      Function(LocalCardAccount) onSelectCardAccount) {
    if (cardAccount.localCardAccountStatus == LocalCardAccountStatus.active &&
        cardAccount.card?.cardIsActive == true) {
      onSelectCardAccount(cardAccount);
    } else if (cardAccount.cardIsSuspended == true) {
      _navigationService.navigateToCardSuspendedView(
        cardAccount: cardAccount,
      );
    } else if (cardAccount.cardIsFrozen == true) {
      _navigationService.navigateToCardFrozenView();
    } else if (cardAccount.cardTerminated) {
      _navigationService.navigateToCardTerminatedView(
        cardAccount: cardAccount,
      );
    } else if (cardAccount.isOverdue) {
      _navigationService.navigateToCardOverdueFeesView(
        cardAccount: cardAccount,
      );
    } else {
      //go to card dashboard
      toCardDashboard();
    }
  }

  onCardDetailPressed() {
    if (hasActiveCardAccount == false) {
      toCardDashboard();
    } else {
      _onCardOptions(onSelectCardAccount: (cardAccount) {
        String? cardId = cardAccount.id;
        final card = cardAccount.card;
        if (card == null || cardId == null) {
          return;
        }
        _navigationService.navigateToPasscodeAndBiometricView(onUnlock: (_) {
          _navigationService.replaceWithCardDetailsView(
            accountId: cardId,
            localCard: card,
          );
        });
      });
    }
  }

  void onCardMorePressed() {
    if (hasActiveCardAccount == false) {
      toCardDashboard();
    } else {
      _onCardOptions(onSelectCardAccount: (cardAccount) {
        showMoreOptions(cardAccount);
      });
    }
  }

  void showMoreOptions(LocalCardAccount cardAccount) {
    BuildContext? context = StackedService.navigatorKey?.currentContext;
    if (context == null) return;
    ExternalCloseSheet.showModal(
      context,
      child: MoreCardOptions(
        localCardAccount: cardAccount,
        onDeleteCard: () => onDeleteCardPressed(cardAccount),
      ),
      backgroundColor: Grey.grey25,
    );
  }

  onDeleteCardPressed(LocalCardAccount cardAccount) {
    _analyticsService.logEvent(eventName: AnalyticsEvent.deleteCardOnMore);
    _navigationService.back();
    _navigationService.navigateToDeleteCardView(cardAccount: cardAccount);
  }

  _fetchTradingPortfolioBalance() async {
    if (tradeBalance == null) updatingCryptoBalance = true;

    final res = await _tradingPortfolioService
        .getUserBalance()
        .whenComplete(() => updatingCryptoBalance = false);

    res.when(success: (success) {
      tradeBalance = success?.balance;

      if (success?.accountExists == true) {
        _tradingPortfolioService.getUserPortfolio();
      }
    }, failure: (error) {
      getLogger(toString()).e(error.message);
    });
  }

  onNewCardTap() {
    _navigationService.navigateToCreateCardView();
  }

  void onMerchantAddPressed() {
    bool hasMerchantWalletSetup = _partnersService.hasTradingAccount;
    if (hasMerchantWalletSetup) {
      _showFundWalletOptions();
    } else {
      _toMerchantDashboard();
    }
  }

  void onMerchantSendPressed() {
    bool hasMerchantWalletSetup = _partnersService.hasTradingAccount;
    if (hasMerchantWalletSetup) {
      _merchantTransferPressed();
    } else {
      _toMerchantDashboard();
    }
  }

  void onMerchantMorePressed() {
    bool hasMerchantWalletSetup = _partnersService.hasTradingAccount;
    if (hasMerchantWalletSetup) {
      _onMerchantMorePressed();
    } else {
      _toMerchantDashboard();
    }
  }

  void _onMerchantMorePressed() {
    BuildContext? context = StackedService.navigatorKey?.currentContext;
    final address =
        locator<PartnersService>().tradingAccountForActiveChain()?.address;
    _analyticsService.logEvent(
        eventName: AnalyticsEvent.clickMoreOnTradingDashBoard);
    if (context == null || address == null) return;
    ExternalCloseSheet.showModal(
      context,
      backgroundColor: Grey.grey25,
      child: MoreDashboardOptions(
        walletAddress: address,
        showWalletConnect: false,
        onCopyAddressPressed: () {
          copyWalletAddress(address: address);
          _navigationService.back();
        },
        onWalletConnectPressed: () {},
      ),
    );
  }

  void _merchantTransferPressed() {
    toSelectTokenView(
      onSelectToken: (LocalToken token) {
        final localToken = LocalAddressTokenBalance.fromLocalToken(token);
        final tokenNetworkBalance = localToken.networks!.first;
        _navigationService.navigateToInternalTransferAmountView(
          from: WalletType.trading,
          localToken: localToken,
          tokenNetworkBalance: tokenNetworkBalance,
        );
      },
      fromWalletType: WalletType.trading,
    );
  }

  _showFundWalletOptions() {
    BuildContext? context = StackedService.navigatorKey?.currentContext;
    if (context == null) return;
    ExternalCloseSheet.showModal(
      context,
      backgroundColor: kcLightGrey25,
      child: FundWalletOptionView(
        onSelect: (fundType) {
          _navigationService.back();
          if (fundType == FundWalletType.internalTransfer) {
            toSelectTokenView(
              fromWalletType: WalletType.spot,
              toWalletType: WalletType.trading,
              onSelectToken: (token) {
                final localToken =
                    LocalAddressTokenBalance.fromLocalToken(token);
                final tokenNetworkBalance = localToken.networks?.firstOrNull;
                _navigationService.navigateToInternalTransferAmountView(
                  from: WalletType.spot,
                  localToken: localToken,
                  tokenNetworkBalance: tokenNetworkBalance!,
                );
              },
            );
          } else {
            toSelectTokenView(
              onSelectToken: (token) {
                _navigationService.navigateToDepositView(
                  walletType: WalletType.trading,
                  token: LocalAddressTokenBalance.fromLocalToken(token),
                  tokenNetworkBalance: LocalTokenNetworkBalance(
                    balance: token.balance,
                    contractAddress: token.contractAddress,
                    decimals: token.decimals,
                    name: token.network?.name,
                    id: token.network?.id,
                  ),
                );
              },
              fromWalletType: WalletType.trading,
              toWalletType: null,
            );
          }
        },
        fundWalletOptionsModelList: FundWalletOptionModel.fundTradingWalletList,
      ),
    );
  }

  toSelectTokenView({
    required Function(LocalToken token) onSelectToken,
    required WalletType fromWalletType,
    WalletType? toWalletType,
  }) {
    _navigationService.navigateToTransferSelectTokenView(
      fromWalletType: fromWalletType,
      toWalletType: toWalletType,
      showExternalWalletWarning: false,
      onSelectToken: (token) {
        onSelectToken.call(token);
      },
      shouldContinueToFundingOption: false,
    );
  }
}
