import 'package:email_validator/email_validator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:nestcoinco_onboard_api_gateway/nestcoinco_onboard_api_gateway.dart';
import 'package:onboard_wallet/app/app.locator.dart';
import 'package:onboard_wallet/app/app.logger.dart';
import 'package:onboard_wallet/app/app.router.dart';
import 'package:onboard_wallet/constants/analytic_event.dart';
import 'package:onboard_wallet/constants/string_constants.dart';
import 'package:onboard_wallet/enums/backup_type.dart';
import 'package:onboard_wallet/models/models.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:onboard_wallet/ui/views/passcode/passcode_viewmodel.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';

import '../../../generated/l10n.dart';

class GetStartedViewModel extends BaseViewModel {
  final _analyticsService = locator<AnalyticsService>();
  final _navigationService = locator<NavigationService>();
  final _authenticationService = locator<AuthenticationService>();
  final _passkeyService = locator<PasskeyService>();
  final _userService = locator<UserService>();
  final _passcodeService = locator<PasscodeService>();

  TextEditingController emailController = TextEditingController();

  String errorMessage = '';

  bool isPinExist = false;

  onContinuePressed() {
    // Validate returns true if the form is valid, or false otherwise.
    bool isValid = EmailValidator.validate(emailController.text);
    if (isValid) {
      _analyticsService.logEvent(eventName: AnalyticsEvent.verifyEmailAddress);
      _isPasskeyAvailable(emailController.text);
    } else {
      errorMessage = S.current.invalidEmailAddressPleaseCheckAndTryAgain;
      notifyListeners();
    }
  }

  Future _isPasskeyAvailable(String email) async {
    EasyLoading.show();
    bool isPasskeyAvailable = await _passkeyService.checkPasskeyAvailability();

    if (isPasskeyAvailable) {
      final result = await _passkeyService.generateLoginChallenge(email: email);

      result.when(success: (data) {
        if (data.challenge.allowCredentials.isEmpty) {
          _requestOtp(email);
        } else {
          _loginWithPasskey(data);
        }
      }, failure: (error) {
        _requestOtp(emailController.text);
      });
    } else {
      _analyticsService.logEvent(eventName: AnalyticsEvent.verifyEmailAddress);
      _requestOtp(emailController.text);
    }
  }

  Future _loginWithPasskey(PasskeyLoginDTO passkeyLoginDto) async {
    final response = await _passkeyService
        .authenticateWithPlatformAndLoginPK(passkeyLoginDto);

    response.when(success: (authOtpRes) async {
      if (authOtpRes != null) {
        try {
          await _authenticationService.processAuthOtpResponseDto(authOtpRes);
          await locator<PasskeyService>().getRegisteredPasskeys();
          locator<PreferenceService>()
              .setBool(key: kReturningUser, value: true);
          locator<AnalyticsService>()
              .setUserProperties(name: "is_returning_user", value: "true");
          final user = await locator<UserService>().getUpdatedUserInfo();
          EasyLoading.dismiss();
          _onVerifySuccess(user!);
        } catch (e) {
          EasyLoading.dismiss();
          EasyLoading.show(status: S.current.emailVerification);
          _requestOtp(emailController.text);
        }
      } else {
        EasyLoading.dismiss();
      }
    }, failure: (failure) {
      EasyLoading.dismiss();
      EasyLoading.show(status: S.current.emailVerification);
      _requestOtp(emailController.text);
    });
  }

  _onVerifySuccess(OnboardUser user) async {
    try {
      final hasOnlinePinSetup = _passcodeService.hasOnlinePinSetup();
      final hasLocalPinSetup = _passcodeService.hasLocalPinSetup();

      if (hasOnlinePinSetup && hasLocalPinSetup) {
        // User has pin setup local and online
        _onPinSetupExist(user);
      } else {
        EasyLoading.show();

        // User hasn't done online pin setup on device, confirm with backend
        final result = await _passcodeService.checkPinExists();

        result.when(success: (status) {
          if (status?.success == true) {
            // User has an online PIN but not set up locally
            // Navigate to verify access screen where they'll enter their PIN
            // Once verified with backend, it will be saved locally
            _onPinSetupDoNotExist(user, setupLocal: true);
          } else {
            // User has no online PIN - need to create a new one
            _onPinSetupDoNotExist(user, setupLocal: false);
          }
        }, failure: (error) {
          // Error checking PIN existence - default to creation flow
          _onPinSetupDoNotExist(user, setupLocal: false);
          getLogger(toString()).e(error.message);
        });
      }
    } catch (e) {
      getLogger(toString()).e(e.toString());
      // default to creation flow
      _onPinSetupDoNotExist(user, setupLocal: false);
    } finally {
      EasyLoading.dismiss();
    }
  }

  //user has pin setup already (saved locally and online)
  _onPinSetupExist(OnboardUser user) {
    final prefService = locator<PreferenceService>();
    final isReturningUser = prefService.getBool(key: kReturningUser);

    if (isReturningUser == true) {
      _navigationService.navigateToEnableNotifsView(authType: AuthType.login);
      return;
    }

    // For new users, check country settings
    if (user.country != null) {
      if (locator<PasskeyService>().passkeysList.isEmpty) {
        _navigationService.navigateToCreatePasskeyView();
      } else {
        _navigationService.navigateToEnableNotifsView(
            authType: AuthType.create);
      }
    } else {
      _navigationService.navigateToCountryOfResidenceAuthView(
        onContinue: (country) {
          _userService.saveUserCountryFeature(country);
          if (locator<PasskeyService>().passkeysList.isEmpty) {
            _navigationService.navigateToCreatePasskeyView();
          } else {
            _navigationService.navigateToEnableNotifsView(
                authType: AuthType.create);
          }
        },
        progress: 0.5,
      );
    }
  }

// Handle navigation when PIN setup doesn't exist
  void _onPinSetupDoNotExist(OnboardUser user, {bool setupLocal = false}) {
    if (user.country != null) {
      _navigationService.navigateToPasscodeView(
        // If setupLocal is true, we want to verify the existing online PIN
        // If false, we need to create a new PIN
        passcodeType:
            setupLocal ? PasscodeType.verifyAccess : PasscodeType.create,
        passCodeEntryPoint: PassCodeEntryPoint.secureWallet,
      );
    } else {
      _navigationService.navigateToCountryOfResidenceAuthView(
        onContinue: (country) {
          _userService.saveUserCountryFeature(country);
          _navigationService.navigateToPasscodeView(
            passcodeType:
                setupLocal ? PasscodeType.verifyAccess : PasscodeType.create,
            passCodeEntryPoint: PassCodeEntryPoint.secureWallet,
          );
        },
        progress: 0.5,
      );
    }
  }

  onChanged(String? value) {
    if (errorMessage.isNotEmpty) {
      errorMessage = '';
      notifyListeners();
    }
  }

  Future _requestOtp(String email) async {
    EasyLoading.show();
    final referralCode = await locator<DynamicLinkService>().getReferralLink();
    final response = await _authenticationService.onboardUserAuthentication(
      email: email,
      referredBy: referralCode,
    );
    EasyLoading.dismiss();
    response.when(success: (session) {
      if (session == null) {
        EasyLoading.showToast("Failed to get session");
        return;
      }
      _navigationService.navigateTo(
        Routes.verifyEmailView,
        arguments: VerifyEmailViewArguments(
          emailAddress: email,
          authSessionId: session,
          showProgress: false,
        ),
      );
    }, failure: (failure) {
      EasyLoading.showError(failure.message);
    });
  }
}
