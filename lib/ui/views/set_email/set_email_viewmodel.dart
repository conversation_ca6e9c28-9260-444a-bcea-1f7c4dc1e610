import 'package:email_validator/email_validator.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:nestcoinco_onboard_api_gateway/nestcoinco_onboard_api_gateway.dart';
import 'package:onboard_wallet/app/index.dart';
import 'package:onboard_wallet/constants/analytic_event.dart';
import 'package:onboard_wallet/constants/string_constants.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';

import '../../../generated/l10n.dart';

class SetEmailViewModel extends BaseViewModel {
  final _navigationService = locator<NavigationService>();
  final _authenticationService = locator<AuthenticationService>();
  final _analyticsService = locator<AnalyticsService>();
  final _cloudBackupService = locator<CloudBackupService>();
  final _passkeyService = locator<PasskeyService>();
  final _userService = locator<UserService>();
  final VoidCallback? onCompleteActionCallback;
  TextEditingController textEditingController = TextEditingController();
  final String? idToken;
  final String email;

  bool get buttonEnabled => EmailValidator.validate(textEditingController.text);

  String get title {
    return S.current.welcomeBackWithEmoji;
  }

  String get subtitle {
    return S.current.letsGetYouBackIn;
  }

  SetEmailViewModel(this.email, this.idToken, this.onCompleteActionCallback) {
    textEditingController.text = email;
  }

  void onChanged(String? value) {
    notifyListeners();
  }

  void onContinuePressed() async {
    await _isPasskeyAvailable(textEditingController.text);
  }

  Future _requestOtp() async {
    EasyLoading.show();
    final email = textEditingController.text;
    final response = await _authenticationService.onboardUserAuthentication(
      email: email,
    );
    EasyLoading.dismiss();
    response.when(success: (session) {
      if (session == null) {
        EasyLoading.showToast("Failed to get session");
      }
      _navigationService.navigateTo(
        Routes.verifyEmailView,
        arguments: VerifyEmailViewArguments(
          emailAddress: email,
          authSessionId: session!,
          showProgress: false,
          onCompleteAction: onCompleteActionCallback,
        ),
      );
    }, failure: (failure) {
      EasyLoading.showError(failure.message);
    });
  }

  void logout() {
    _checkLogoutNudgeIsRequired();
  }

  logUserOut() async {
    EasyLoading.show();
    await _authenticationService.logout();
    try {
      await locator<SecureStorageService>().deleteAll();
    } catch (_) {}
    locator<DatabaseService>().clearDb();
    locator<PreferenceService>().clear();
    EasyLoading.dismiss();
    _navigationService.clearStackAndShow(Routes.authView);
  }

  Future<void> _checkLogoutNudgeIsRequired() async {
    if (_cloudBackupService.requiresBackUp) {
      await _nudgeWalletBackup();
    } else {
      logUserOut();
    }
  }

  _nudgeWalletBackup() async {
    _cloudBackupService.setCallBackToCurrentRoute();
    await _navigationService.navigateToWalletSecurityView();
    if (_cloudBackupService.hasBackUp) {
      logUserOut();
    }
  }

  Future _isPasskeyAvailable(String email) async {
    EasyLoading.show();
    bool isPasskeyAvailable = await _passkeyService.checkPasskeyAvailability();
    final savedEmail = _userService.getCurrentUser()?.email;

    if (isPasskeyAvailable) {
      final result = await _passkeyService.generateLoginChallenge(
          email: savedEmail ?? email);

      result.when(success: (data) {
        if (data.challenge.allowCredentials.isEmpty) {
          _requestOtp();
        } else {
          _loginWithPasskey(data);
        }
      }, failure: (error) {
        _requestOtp();
      });
    } else {
      _analyticsService.logEvent(eventName: AnalyticsEvent.verifyEmailAddress);
      _requestOtp();
    }
  }

  Future _loginWithPasskey(PasskeyLoginDTO passkeyLoginDto) async {
    final response = await _passkeyService
        .authenticateWithPlatformAndLoginPK(passkeyLoginDto);

    response.when(success: (authOtpRes) async {
      if (authOtpRes != null) {
        try {
          await _authenticationService.processAuthOtpResponseDto(authOtpRes);
          locator<PreferenceService>()
              .setBool(key: kReturningUser, value: true);
          locator<AnalyticsService>()
              .setUserProperties(name: "is_returning_user", value: "true");
          await locator<UserService>().getUpdatedUserInfo();
          EasyLoading.dismiss();
          _navigationService.replaceWith(Routes.homeView);
        } catch (e) {
          EasyLoading.dismiss();
          EasyLoading.show(status: S.current.emailVerification);
          _requestOtp();
        }
      } else {
        EasyLoading.dismiss();
      }
    }, failure: (failure) {
      EasyLoading.dismiss();
      EasyLoading.show(status: S.current.emailVerification);
      _requestOtp();
    });
  }

  @override
  void dispose() {
    textEditingController.dispose();
    super.dispose();
  }
}
