import 'dart:async';

import 'package:collection/collection.dart';
import 'package:onboard_wallet/app/index.dart';
import 'package:onboard_wallet/constants/constants.dart';
import 'package:onboard_wallet/constants/string_constants.dart';
import 'package:onboard_wallet/enums/enums.dart';
import 'package:onboard_wallet/manager/toast.dart';
import 'package:onboard_wallet/models/card/initial_funding/tx_channel.dart';
import 'package:onboard_wallet/models/card/local_card_transaction_config/local_card_transaction_config.dart';
import 'package:onboard_wallet/models/country.dart';
import 'package:onboard_wallet/models/virtual_account/local_virtual_account/local_virtual_account.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';

class VirtualAccountFundingOptionViewModel extends BaseViewModel {
  final _navigationService = locator<NavigationService>();
  final _virtualAccountService = locator<VirtualAccountService>();
  final _analyticsService = locator<AnalyticsService>();
  final _featureService = locator<FeatureService>();
  final _cardService = locator<CardService>();
  final _appSettingsService = locator<AppSettingsService>();
  final ToastManager _toastManager = locator<ToastManager>();

  final LocalVirtualAccount account;

  StreamSubscription? countryStreamSubscription;
  LocalCardTransactionConfig? _localCardTransactionConfig;
  LocalCountry? country;

  Set<LocalCountry> _countries = {};

  VirtualAccountFundingOptionViewModel({
    required this.account,
  });

  bool get showNewOnCardFunding =>
      _appSettingsService.showNewOnCardFunding.value;

  List<LocalTxChannel> get fundOptions {
    List<LocalTxChannel> options =
        List.from(_localCardTransactionConfig?.txChannels ?? []);
    if (locator<WalletService>().isDefiWalletSetUp == false) {
      options.removeWhere((element) =>
          element.optionsEnum == CardTransactionOptionsEnum.onboardWallet);
    }
    options.insert(
        0, LocalTxChannel.virtualAccountBankTransfer(accountCurrency ?? "USD"));
    return options;
  }

  bool get hideCashFunding => _featureService.isOnRampOff;

  String? get accountCurrency {
    return account.currency;
  }

  onReady() {
    setBusy(true);
    _getCountries();
  }

  onFundOptionsClicked(LocalTxChannel localTransactionChannel) {
    saveCountry();
    switch (localTransactionChannel.optionsEnum) {
      case CardTransactionOptionsEnum.onboardWallet:
        _analyticsService.logEvent(
          eventName:
              AnalyticsEvent.virtualAccountFundingViaOnboardWalletSelected,
        );
        onOnboardWalletPressed();
        break;
      case CardTransactionOptionsEnum.externalWallet:
        _analyticsService.logEvent(
          eventName:
              AnalyticsEvent.virtualAccountFundingViaExternalWalletSelected,
        );
        onExternalWalletPressed();
        break;
      case CardTransactionOptionsEnum.cashTransfer:
        _analyticsService.logEvent(
          eventName:
              AnalyticsEvent.virtualAccountFundingViaCashTransferSelected,
        );
        onCashTransferPressed(localTransactionChannel);
        break;
      case CardTransactionOptionsEnum.cardToCard:
        break;
      case CardTransactionOptionsEnum.onboardPay:
        onOnboardPaySelected(localTransactionChannel);
        break;
      case CardTransactionOptionsEnum.virtualAccountBankTransfer:
        _navigationService.navigateToVirtualAccountDetailsView(
            virtualAccount: account);
        break;
      default:
        //do nothing.
        break;
    }
  }

  onOnboardWalletPressed() {
    if (_localCardTransactionConfig == null) return;
    _navigationService.navigateTo(
      Routes.cardFundSelectAssetView,
      arguments: CardFundSelectAssetViewArguments(
        localCardTransactionConfig: _localCardTransactionConfig!,
        cardFundOptionsEnum: CardTransactionOptionsEnum.onboardWallet,
        cardFundingType: CardFundingType.subsequent,
        cardUsageTerms: null,
        virtualAccount: account,
        virtualAccountUsageTerms: account.terms,
      ),
    );
  }

  onCashTransferPressed(LocalTxChannel localTransactionChannel) {
    String? assetCode =
        localTransactionChannel.supportedAssetCodes?.isNotEmpty == true
            ? localTransactionChannel.supportedAssetCodes?.first
            : null;
    if (assetCode == null) return;
    _navigationService.navigateToFundVirtualAccountViaCashTransferView(
      transactionChannel: localTransactionChannel,
      cardFundingType: CardFundingType.subsequent,
      usageTerms: account.terms,
      virtualAccount: account,
      assetCode: assetCode,
    );
  }

  onExternalWalletPressed() {
    if (_localCardTransactionConfig == null) return;
    _navigationService.navigateTo(
      Routes.cardFundSelectAssetView,
      arguments: CardFundSelectAssetViewArguments(
        localCardTransactionConfig: _localCardTransactionConfig!,
        cardFundOptionsEnum: CardTransactionOptionsEnum.externalWallet,
        cardFundingType: CardFundingType.subsequent,
        cardUsageTerms: null,
        virtualAccount: account,
        virtualAccountUsageTerms: account.terms,
      ),
    );
  }

  void onChangeCountry() => _showCountriesModal();

  Future<void> _showCountriesModal() async {
    final context = StackedService.navigatorKey?.currentContext;
    if (context == null) return;

    _navigationService.navigateToCountriesListView(
      countries: _countries.toList(),
      selectedCountry: country,
      onSelectCountry: (selectCountry) {
        if (selectCountry != country) {
          _localCardTransactionConfig = null;
          notifyListeners();
          _getFundingConfig(country: selectCountry.code);
        }
        country = selectCountry;

        notifyListeners();
        _navigationService.back();
      },
    );
  }

  Future _getFundingConfig({
    String? country,
  }) async {
    if (_localCardTransactionConfig == null) {
      setBusy(true);
    }
    _localCardTransactionConfig = null;

    final response = await _virtualAccountService.getFundingConfig(
      country: country,
      accountCurrency: accountCurrency,
    );
    response.when(success: (success) {
      _localCardTransactionConfig = success;
    }, failure: (failure) {
      _toastManager.showErrorToast(text: failure.message);
    });
    setBusy(false);
  }

  Future<void> _getCountries() async {
    final userService = locator<UserService>();
    String? countryCode = await userService.getUserTransactionCountryCode();
    countryStreamSubscription =
        _cardService.getCardCountries().take(1).listen((response) {
      response.when(
        success: (cardCountryList) {
          if (_countries.isEmpty) {
            _countries = cardCountryList.toSet();
            _initCountry(cardCountryList, countryCode);
          } else {
            // check if country list changed
            if (!const ListEquality()
                .equals(_countries.toList(), cardCountryList)) {
              _countries = cardCountryList.toSet();
              _initCountry(cardCountryList, countryCode);
            }
          }
        },
        failure: (failure) {},
      );
    });
  }

  Future<void> _initCountry(
      List<LocalCountry> countries, String? countryCode) async {
    country = countries.firstWhereOrNull(
        (element) => element.code.toLowerCase() == countryCode?.toLowerCase());
    if (country == null && countries.isNotEmpty) {
      country = countries.first;
    }
    _getFundingConfig(country: country?.code ?? countryCode);
    notifyListeners();
  }

  void onOnboardPaySelected(LocalTxChannel localTransactionChannel) {
    String? assetCode =
        localTransactionChannel.supportedAssetCodes?.isNotEmpty == true
            ? localTransactionChannel.supportedAssetCodes?.first
            : null;
    if (assetCode == null) return;
    _navigationService.navigateToFundVirtualAccountViaCashTransferView(
      transactionChannel: localTransactionChannel,
      cardFundingType: CardFundingType.subsequent,
      usageTerms: account.terms,
      virtualAccount: account,
      assetCode: assetCode,
    );
  }

  void saveCountry() {
    if (country == null) {
      return;
    }
    final prefService = locator<PreferenceService>();
    prefService.setString(
        key: kLastTransactionCountryCode, value: country!.code);
  }
}
