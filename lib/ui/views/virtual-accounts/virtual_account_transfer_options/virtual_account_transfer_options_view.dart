import 'package:flutter/material.dart';
import 'package:onboard_wallet/enums/card_fund_options_enum.dart';
import 'package:onboard_wallet/extensions/num.dart';
import 'package:onboard_wallet/gen/assets.gen.dart';
import 'package:onboard_wallet/gen/fonts.gen.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/models/card/initial_funding/tx_channel.dart';
import 'package:onboard_wallet/models/virtual_account/local_virtual_account/local_virtual_account.dart';
import 'package:onboard_wallet/theme_typography.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';
import 'package:onboard_wallet/ui/common/ui_helpers.dart';
import 'package:onboard_wallet/ui/views/cards/fund/card_fund_options/widgets/fund_options_skeleton.dart';
import 'package:onboard_wallet/ui/views/fund_options/widgets/country_empty_state.dart';
import 'package:onboard_wallet/ui/views/fund_options/widgets/country_pill.dart';
import 'package:onboard_wallet/ui/widgets/app_bar/transparent_app_bar.dart';
import 'package:onboard_wallet/ui/widgets/asset_error_icon.dart';
import 'package:onboard_wallet/ui/widgets/cards/card_surface.dart';
import 'package:onboard_wallet/ui/widgets/container/purple_container.dart';
import 'package:stacked/stacked.dart';

import 'virtual_account_transfer_options_view_model.dart';

class VirtualAccountTransferOptionsView
    extends StackedView<VirtualAccountTransferOptionsViewModel> {
  final LocalVirtualAccount virtualAccount;

  const VirtualAccountTransferOptionsView({
    super.key,
    required this.virtualAccount,
  });

  @override
  Widget builder(BuildContext context,
      VirtualAccountTransferOptionsViewModel viewModel, Widget? child) {
    final textTheme = Theme.of(context).textTheme;
    final channels = viewModel.withdrawalChannels;
    return Scaffold(
      appBar: TransparentAppBar(
        actions: [
          UnconstrainedBox(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.7),
              child: CountryPill(
                country: viewModel.country,
                onTap: viewModel.onChangeCountry,
              ),
            ),
          ),
          const SizedBox(width: 20),
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.only(
                  right: 70.0,
                  top: 18,
                ),
                child: Text(
                  S.current.whereWouldYouLikeToTransferYourFundsTo,
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        color: kcBrandBlue,
                        fontSize: 25.fontSize,
                        letterSpacing: -0.5,
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ),
              const verticalSpace(28),
              if (viewModel.isBusy == false) ...[
                if (channels.isNotEmpty) ...[
                  CardSurface(
                    padding: const EdgeInsets.fromLTRB(18, 10, 24, 12),
                    child: ListView.separated(
                      shrinkWrap: true,
                      itemBuilder: (_, index) {
                        final channel = channels[index];
                        return ListTile(
                          contentPadding: EdgeInsets.zero,
                          onTap: () => viewModel.onOptionsClicked(channel),
                          title: Text(
                            channel.name ?? '',
                            style: textTheme.bodyLarge?.copyWith(
                              fontWeight: FontWeight.w500,
                              color: kcBaseBlack,
                            ),
                          ),
                          subtitle: Text(
                            channel.description ?? '',
                            style: textTheme.body12Regular.copyWith(
                              color: Grey.grey400,
                              fontFamily: FontFamily.satoshi,
                            ),
                          ),
                          leading: PurpleContainer(
                            boxShape: BoxShape.circle,
                            size: 40,
                            bgColor: kcBaseWhite,
                            child: channel.optionsEnum?.icon ??
                                const AssetErrorIconSvg(),
                          ),
                          trailing:
                              Assets.svg.arrowRight.svg(color: Grey.grey400),
                        );
                      },
                      separatorBuilder: (_, __) {
                        return const Divider(
                          height: 3,
                          color: Grey.grey100,
                        );
                      },
                      itemCount: channels.length,
                    ),
                  ),
                ] else ...[
                  const verticalSpace(20),
                  CountryEmptyState(
                    subtitle: S.current
                        .withdrawalMethodIsCurrentlyNotAvailableForThisCountry,
                    onChangeCountry: viewModel.onChangeCountry,
                    country: viewModel.country,
                  )
                ]
              ] else ...[
                const FundOptionsSkeleton(),
              ]
            ],
          ),
        ),
      ),
    );
  }

  @override
  VirtualAccountTransferOptionsViewModel viewModelBuilder(
          BuildContext context) =>
      VirtualAccountTransferOptionsViewModel(virtualAccount);

  @override
  void onViewModelReady(VirtualAccountTransferOptionsViewModel viewModel) =>
      viewModel.onReady();
}
