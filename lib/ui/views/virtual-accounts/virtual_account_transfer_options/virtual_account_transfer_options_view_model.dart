import 'dart:async';

import 'package:collection/collection.dart';
import 'package:onboard_wallet/app/index.dart';
import 'package:onboard_wallet/constants/analytic_event.dart';
import 'package:onboard_wallet/constants/string_constants.dart';
import 'package:onboard_wallet/enums/card_fund_options_enum.dart';
import 'package:onboard_wallet/manager/toast.dart';
import 'package:onboard_wallet/models/card/initial_funding/tx_channel.dart';
import 'package:onboard_wallet/models/country.dart';
import 'package:onboard_wallet/models/local/network/local_network.dart';
import 'package:onboard_wallet/models/virtual_account/local_virtual_account/local_virtual_account.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';

import '../../../../models/card/local_card_transaction_config/local_card_transaction_config.dart';

class VirtualAccountTransferOptionsViewModel extends BaseViewModel {
  final _navigationService = locator<NavigationService>();
  final _virtualAccountService = locator<VirtualAccountService>();
  final _featureService = locator<FeatureService>();
  final _cardService = locator<CardService>();
  final _analyticsService = locator<AnalyticsService>();
  final LocalVirtualAccount virtualAccount;

  VirtualAccountTransferOptionsViewModel(this.virtualAccount);

  LocalNetwork? get activeNetwork => locator<NetworkService>().currentChain;

  LocalCardTransactionConfig? _localCardTransactionConfig;

  List<LocalTxChannel> get withdrawalChannels {
    final List<LocalTxChannel> options =
        List.from(_localCardTransactionConfig?.txChannels ?? []);
    if (locator<WalletService>().isDefiWalletSetUp == false) {
      options.removeWhere((element) =>
          element.optionsEnum == CardTransactionOptionsEnum.onboardWallet);
    }
    return options;
  }

  bool get hideCashWithdrawal => _featureService.isOffRampOff;

  String? get cardCurrency {
    return virtualAccount.currency;
  }

  LocalCountry? country;

  Set<LocalCountry> _countries = {};

  void onReady() {
    setBusy(true);
    _getCountries();
    _analyticsService.logEvent(
      eventName: AnalyticsEvent.viewVirtualAccountTransferOptions,
    );
  }

  @override
  void dispose() {
    countryStreamSubscription?.cancel();
    super.dispose();
  }

  onOptionsClicked(LocalTxChannel channel) {
    if (_localCardTransactionConfig == null) {
      return;
    }
    _analyticsService.logEvent(
      eventName: AnalyticsEvent.selectVirtualAccountTransferOption,
      parameters: {
        "channel": channel.channelId,
      },
    );
    saveCountry();
    switch (channel.optionsEnum) {
      case CardTransactionOptionsEnum.onboardWallet:
        if (_navigationService.previousRoute ==
            Routes.virtualAccountTransferViaOnboardWalletView) {
          _navigationService.back();
          return;
        }
        _navigationService.navigateToVirtualAccountSelectAssetView(
          virtualAccount: virtualAccount,
          transactionConfig: _localCardTransactionConfig!,
          cardTransactionOptionsEnum: CardTransactionOptionsEnum.onboardWallet,
        );

      case CardTransactionOptionsEnum.externalWallet:
        if (_navigationService.previousRoute ==
            Routes.virtualAccountTransferViaExternalWalletView) {
          _navigationService.back();
          return;
        }
        _navigationService.navigateToVirtualAccountSelectAssetView(
          virtualAccount: virtualAccount,
          transactionConfig: _localCardTransactionConfig!,
          cardTransactionOptionsEnum: CardTransactionOptionsEnum.externalWallet,
        );
      case CardTransactionOptionsEnum.onboardFast:
      case CardTransactionOptionsEnum.onboardPay:
      case CardTransactionOptionsEnum.cashTransfer:
        if (_navigationService.previousRoute ==
            Routes.virtualAccountTransferViaLocalCurrencyView) {
          _navigationService.back();
          return;
        }
        _navigationService.navigateToVirtualAccountTransferViaLocalCurrencyView(
          virtualAccount: virtualAccount,
          channel: channel,
          configMinimumAmount: _localCardTransactionConfig?.minAmount,
          configMaximumAmount: _localCardTransactionConfig?.maxAmount,
          fiatCurrencies: _localCardTransactionConfig?.fiatCurrencies ?? [],
        );
      default:
    }
  }

  void onChangeCountry() => _showCountriesModal();

  Future<void> _showCountriesModal() async {
    final context = StackedService.navigatorKey?.currentContext;
    if (context == null) return;

    _navigationService.navigateToCountriesListView(
      countries: _countries.toList(),
      selectedCountry: country,
      onSelectCountry: (selectCountry) {
        if (selectCountry != country) {
          setBusy(true);
          _localCardTransactionConfig = null;
          _initWithdrawalConfigs(selectCountry.code);
        }
        country = selectCountry;
        notifyListeners();
        _navigationService.back();
      },
    );
  }

  StreamSubscription? countryStreamSubscription;

  Future<void> _getCountries() async {
    final userService = locator<UserService>();

    String countryCode =
        await userService.getUserTransactionCountryCode() ?? "NG";
    countryStreamSubscription =
        _cardService.getCardCountries().listen((response) {
      //stop loader, when we receive any item.
      response.when(
        success: (cardCountryList) {
          _countries = cardCountryList.toSet();
          _initCountry(cardCountryList, countryCode);
        },
        failure: (failure) {},
      );
    });
  }

  Future<void> _initCountry(
      List<LocalCountry> countries, String countryCode) async {
    country = countries.firstWhereOrNull(
        (element) => element.code.toLowerCase() == countryCode.toLowerCase());
    if (country == null && countries.isNotEmpty) {
      country = countries.first;
    }
    _initWithdrawalConfigs(country?.code ?? countryCode);
    notifyListeners();
  }

  _initWithdrawalConfigs(String userCountry) async {
    String? accountCurrency = virtualAccount.currency;
    if (accountCurrency == null) {
      setBusy(false);
      return;
    }
    final response = await _virtualAccountService.getRemoteWithdrawalConfig(
        country: userCountry, accountCurrency: accountCurrency);
    response.when(success: (success) {
      if (success != null) {
        _localCardTransactionConfig =
            LocalCardTransactionConfig.fromVirtualAccountTransactionConfig(
                success);
      }
    }, failure: (failure) {
      locator<ToastManager>().showErrorToast(text: failure.message);
    });
    setBusy(false);
  }

  void saveCountry() {
    if (country == null) {
      return;
    }
    final prefService = locator<PreferenceService>();
    prefService.setString(
        key: kLastTransactionCountryCode, value: country!.code);
  }
}
