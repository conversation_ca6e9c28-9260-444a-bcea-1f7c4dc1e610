import 'package:flutter/material.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';
import 'package:onboard_wallet/ui/views/send/data/send_destination_enum.dart';

import 'package:onboard_wallet/ui/views/send/widgets/send_input_field.dart';

import 'package:stacked/stacked.dart';

import '../send_from_defi_viewmodel.dart';

class SendFromDefiInputField extends ViewModelWidget<SendFromDefiViewmodel> {
  const SendFromDefiInputField({super.key});

  @override
  Widget build(BuildContext context, SendFromDefiViewmodel viewModel) {
    String? toAssetCode = viewModel.toAssetCode;
    if (toAssetCode == null) {
      if (viewModel.sendDestinationEnum == SendDestinationEnum.crypto) {
        toAssetCode = S.current.chooseCrypto;
      } else {
        toAssetCode = S.current.chooseCurrency;
      }
    }
    return SendInputField(
      fromTextEditingController: viewModel.fromTextEditingController,
      toTextEditingController: viewModel.toTextEditingController,
      toInputFormatters: viewModel.toInputFormatters,
      fromInputFormatters: viewModel.fromInputFormatters,
      onFromInputChanged: viewModel.onFromInputChanged,
      onToInputChanged: viewModel.onToInputChanged,
      toAssetCode: toAssetCode,
      toAssetLogo: viewModel.toAssetLogo,
      fromAssetCode: viewModel.fromAssetCode ?? S.current.chooseCrypto,
      hasNoFee: viewModel.hasNoFee,
      showColoredDropDown: viewModel.fromAssetCode == null,
      amountToBeConverted: viewModel.amountToBeConverted,
      onChangeFromAsset: viewModel.changeFromAsset,
      onChangeToAsset: viewModel.changeToAsset,
      showFeeExplainer: viewModel.showFeeExplainer,
      formattedFee: viewModel.formattedFee,
      fetchingRate: viewModel.fetchingRate || viewModel.fetchingSwapQuote,
      formattedRate: viewModel.formattedRate,
      fromAssetLogo: viewModel.fromAssetLogo,
      availableBalanceColor:
          viewModel.insufficientBalance ? ErrorColor.error500 : Grey.grey400,
      onMaxSelected: viewModel.onUseMaxPressed,
      availableBalance: viewModel.fromTokenBalance,
      fetchingToAsset: viewModel.fetchingTargetAsset,
      disabledToDropDown: viewModel.fromAssetCode == null,
      showColoredToDropDown: viewModel.fetchingTargetAsset == false &&
          viewModel.toAssetCode == null && viewModel.fromAssetCode != null,
    );
  }
}
