import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:onboard_wallet/extensions/extensions.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/haven/ui/widgets/container/token_logo_container.dart';
import 'package:onboard_wallet/haven/ui/widgets/resizableSpacer.dart';
import 'package:onboard_wallet/models/token_balance/local_address_token_balance.dart';
import 'package:onboard_wallet/models/token_balance/local_token_network_balance.dart';
import 'package:onboard_wallet/theme_typography.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';
import 'package:onboard_wallet/ui/common/ui_helpers.dart';
import 'package:onboard_wallet/ui/widgets/onbw_checkbox.dart';

class ChooseTokenNetworkModalView extends StatelessWidget {
  final LocalAddressTokenBalance token;
  final LocalTokenNetworkBalance? selectedTokenNetworkBalance;
  final void Function(LocalAddressTokenBalance, LocalTokenNetworkBalance)
      onSelectToken;

  const ChooseTokenNetworkModalView({
    super.key,
    required this.token,
    required this.onSelectToken,
    this.selectedTokenNetworkBalance,
  });

  @override
  Widget build(BuildContext context) {
    final tokenNetworks = token.networks?.where((e) => e.balanceAsDouble() > 0).toList() ?? [];
    tokenNetworks.sort((a, b) => b.compareBalances(a));
    return Container(
      height: MediaQuery.of(context).size.height * 0.5,
      padding: const EdgeInsets.only(
        top: 10,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Text(
              S.current.chooseNetwork,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w500,
                    fontSize: 18.fontSize,
                    color: kcBaseBlack,
                  ),
            ),
          ),
          const verticalSpace(23),
          Padding(
            padding: const EdgeInsets.only(left: 18.0),
            child: Text(
              "${S.current.my} ${token.symbol ?? ""}",
              style: Theme.of(context).textTheme.body14Medium.copyWith(
                    color: Grey.grey400,
                  ),
            ),
          ),
          const verticalSpace(12),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: ListView.separated(
                itemBuilder: (context, index) {
                  final e = tokenNetworks[index];
                  bool isSelected = e == selectedTokenNetworkBalance;
                  return InkWell(
                    onTap: () => onSelectToken.call(token, e),
                    child: Stack(
                      alignment: Alignment.topRight,
                      clipBehavior: Clip.none,
                      children: [
                        Container(
                          padding: const EdgeInsets.fromLTRB(12, 10, 12, 10),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(14),
                              border: Border.all(
                                color:
                                    isSelected ? kcBrandPurple : Colors.white,
                                width: isSelected ? 1.5 : 0,
                              )),
                          child: Row(
                            children: [
                              Center(
                                child: Stack(
                                  alignment: Alignment.bottomRight,
                                  children: [
                                    TokenLogoContainer(
                                      size: 30.78,
                                      imageUrl: token.getLogoUrl() ?? '',
                                    ),
                                    CachedNetworkImage(
                                      height: 13,
                                      width: 13,
                                      imageUrl: e.logoUrl() ?? '',
                                      errorWidget: (_, __, ___) =>
                                          const SizedBox(),
                                    )
                                  ],
                                ),
                              ),
                              const horizontalSpace(12),
                              Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 3,
                                      horizontal: 6,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Grey.grey50,
                                      borderRadius: BorderRadius.circular(5),
                                    ),
                                    child: Text(
                                      token.symbol ?? '',
                                      style: Theme.of(context)
                                          .textTheme
                                          .body14Medium
                                          .copyWith(
                                            color: Grey.grey500,
                                            fontSize: 9,
                                            fontWeight: FontWeight.w400,
                                          ),
                                    ),
                                  ),
                                  const verticalSpace(4),
                                  Text(
                                    '${S.current.on} ${e.shortName ?? ''}',
                                    style: Theme.of(context)
                                        .textTheme
                                        .body14Medium
                                        .copyWith(
                                          color: Grey.grey900,
                                        ),
                                  )
                                ],
                              ),
                              const ResizableSpacer(
                                minWidth: 8,
                              ),
                              Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    token
                                        .getFiatValueOfNetworkBalance(e)
                                        .currencyFormat(),
                                    style: Theme.of(context)
                                        .textTheme
                                        .body14Medium
                                        .copyWith(
                                          color: Grey.grey900,
                                        ),
                                  ),
                                  const verticalSpace(4),
                                  Text(
                                    '${e.balanceAsDouble().currencyFormat(decimalDigits: 4, symbol: "").removeTrailingZero()} ${token.symbol ?? ''}',
                                    style: Theme.of(context)
                                        .textTheme
                                        .body12Light
                                        .copyWith(
                                          fontSize: 11.fontSize,
                                          color: Grey.grey400,
                                          fontWeight: FontWeight.w300,
                                        ),
                                  )
                                ],
                              )
                            ],
                          ),
                        ),
                        if (isSelected) ...[
                          Positioned(
                            bottom: 47,
                            right: 10,
                            child: Container(
                              decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: Colors.white,
                                    width: 2.3,
                                  )),
                              child: OnbwCheckBox(
                                padding: EdgeInsets.zero,
                                value: isSelected,
                              ),
                            ),
                          ),
                        ]
                      ],
                    ),
                  );
                },
                itemCount: tokenNetworks.length,
                separatorBuilder: (_, __) {
                  return const verticalSpace(12);
                },
              ),
            ),
          ),
          const verticalSpace(12),
        ],
      ),
    );
  }
}
