import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:onboard_wallet/extensions/extensions.dart';
import 'package:onboard_wallet/gen/assets.gen.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/haven/ui/widgets/app_bar/transparent_app_bar.dart';
import 'package:onboard_wallet/haven/ui/widgets/cards/card_surface.dart';
import 'package:onboard_wallet/models/models.dart';
import 'package:onboard_wallet/theme_typography.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';
import 'package:onboard_wallet/ui/common/ui_helpers.dart';
import 'package:onboard_wallet/ui/views/my_assets/widgets/search_field.dart';
import 'package:onboard_wallet/ui/widgets/asset_error_icon.dart';
import 'package:stacked/stacked.dart';
import 'choose_defi_wallet_token_view_model.dart';

class ChooseDefiWalletTokenView
    extends StackedView<ChooseDefiWalletTokenViewModel> {
  final LocalTokenNetworkBalance? selectedTokenNetworkBalance;
  final Function(LocalAddressTokenBalance, LocalTokenNetworkBalance)
      onSelectToken;

  const ChooseDefiWalletTokenView({
    super.key,
    required this.onSelectToken,
    this.selectedTokenNetworkBalance,
  });

  @override
  Widget builder(BuildContext context, ChooseDefiWalletTokenViewModel viewModel,
      Widget? child) {
    return Scaffold(
      appBar: TransparentAppBar(
        centerTitle: true,
        title: Text(
          S.current.currency,
          style: Theme.of(context).textTheme.body18Medium.copyWith(
                color: kcBaseBlack,
              ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: CustomScrollView(
          slivers: [
            const SliverToBoxAdapter(
              child: verticalSpace(8),
            ),
            SliverToBoxAdapter(
              child: Center(
                child: Text(
                  S.current.chooseYourPreferredAsset,
                  style: Theme.of(context).textTheme.body14Regular.copyWith(
                        color: Grey.grey500,
                      ),
                ),
              ),
            ),
            const SliverToBoxAdapter(
              child: verticalSpace(32),
            ),
            if (viewModel.userTokens.isEmpty) ...[
              SliverToBoxAdapter(
                child: Column(
                  children: [
                    const verticalSpace(46),
                    Assets.svg.searchEmptyStateIlos.svg(),
                    const verticalSpace(18),
                    Text(
                      S.current.noBalancesAvailable,
                      style: Theme.of(context).textTheme.body13Light.copyWith(
                            color: Grey.grey600,
                          ),
                    )
                  ],
                ),
              )
            ] else ...[
              SliverSearchField(
                padding: EdgeInsets.zero,
                searchController: viewModel.searchController,
                showClearIcon: viewModel.searchController.text.isNotEmpty,
                onChanged: viewModel.onInputChanged,
                onClearField: () => viewModel.clearField(),
              ),
              const SliverToBoxAdapter(
                child: verticalSpace(13),
              ),
              if(viewModel.tokens.isEmpty) ...[
                SliverToBoxAdapter(
                  child: Column(
                    children: [
                      const verticalSpace(46),
                      Assets.svg.searchEmptyStateIlos.svg(),
                      const verticalSpace(18),
                      Text(
                        S.current.noBalancesAvailable,
                        style: Theme.of(context).textTheme.body13Light.copyWith(
                          color: Grey.grey600,
                        ),
                      )
                    ],
                  ),
                )
              ] else ...[
                SliverToBoxAdapter(
                  child: CardSurface(
                    child: Column(
                      children: [
                        const SizedBox(
                          height: 10,
                        ),
                        ListView.separated(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: viewModel.tokens.length,
                          itemBuilder: (context, index) {
                            final asset = viewModel.tokens[index];
                            return InkWell(
                              onTap: () => viewModel.onTapToken(asset),
                              child: Row(
                                children: [
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(20),
                                    child: CachedNetworkImage(
                                      imageUrl: asset.getLogoUrl() ?? "",
                                      height: 34,
                                      width: 34,
                                      fit: BoxFit.fitHeight,
                                      errorWidget: (_, __, ___) =>
                                      const AssetErrorIconSvg(),
                                    ),
                                  ),
                                  const horizontalSpace(8),
                                  Text(
                                    asset.name ?? "",
                                    style: Theme.of(context)
                                        .textTheme
                                        .body14Medium
                                        .copyWith(
                                      color: Grey.grey900,
                                    ),
                                  ),
                                  const Spacer(),
                                  Text(
                                    asset
                                        .getFiatValueInDouble()
                                        .currencyFormat(),
                                    style: Theme.of(context)
                                        .textTheme
                                        .body14Medium
                                        .copyWith(
                                      color: Grey.grey900,
                                    ),
                                  ),
                                  const horizontalSpace(8),
                                  Assets.svg.chevronDown.svg(),
                                ],
                              ),
                            );
                          },
                          separatorBuilder: (BuildContext context, int index) {
                            return const SizedBox(
                              height: 25,
                            );
                          },
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                      ],
                    ),
                  ),
                ),
              ]

            ]
          ],
        ),
      ),
    );
  }

  @override
  ChooseDefiWalletTokenViewModel viewModelBuilder(BuildContext context) =>
      ChooseDefiWalletTokenViewModel(onSelectToken);

  @override
  void onViewModelReady(ChooseDefiWalletTokenViewModel viewModel) =>
      viewModel.onViewModelReady();
}
