import 'package:flutter/material.dart';
import 'package:onboard_wallet/app/app.locator.dart';
import 'package:onboard_wallet/haven/ui/widgets/bottomsheet/bottomsheet.dart';
import 'package:onboard_wallet/models/models.dart';
import 'package:onboard_wallet/services/token_service.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';
import 'package:onboard_wallet/ui/views/send/choose_defi_wallet_token/widgets/choose_token_network_modal_view.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';

class ChooseDefiWalletTokenViewModel extends BaseViewModel {
  final _tokenService = locator<TokenService>();
  final _navigationService = locator<NavigationService>();

  final void Function(LocalAddressTokenBalance, LocalTokenNetworkBalance)
      onSelectToken;

  ChooseDefiWalletTokenViewModel(this.onSelectToken);

  TextEditingController searchController = TextEditingController();

  List<LocalAddressTokenBalance> userTokens = [];

  List<LocalAddressTokenBalance> tokens = [];

  void onInputChanged(String value) {
    if (value.isEmpty) {
      tokens = userTokens;
      notifyListeners();
      return;
    }
    tokens = userTokens
        .where((e) =>
            e.symbol?.toLowerCase().contains(value.toLowerCase()) == true)
        .toList();
    notifyListeners();
  }

  void clearField() {
    searchController.clear();
    onInputChanged(searchController.text);
  }

  void onViewModelReady() {
    userTokens = _tokenService.myPortfolioBoxName.values
        .where(
            (e) => e.isHidden == false && e.tokenBalance?.assetRateId != null)
        .toList()
      ..sort((a, b) => b.compareFiatValue(a));
    tokens = userTokens;
    notifyListeners();
  }

  void onTapToken(LocalAddressTokenBalance asset) {
    BuildContext? context = StackedService.navigatorKey?.currentContext;
    if (context == null) return;

    ExternalCloseSheet.showModal(
      context,
      backgroundColor: Grey.grey25,
      child: ChooseTokenNetworkModalView(
        token: asset,
        onSelectToken: (token, tokenNetwork) {
          _navigationService.back();
          _navigationService.back();
          onSelectToken.call(token, tokenNetwork);
        },
      ),
    );
  }
}
