import 'package:cached_network_image/cached_network_image.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:nestcoinco_onboard_api_gateway/nestcoinco_onboard_api_gateway.dart';
import 'package:nestcoinco_onboard_api_gateway_lite/nestcoinco_onboard_api_gateway_lite.dart'
    as haven;
import 'package:nestcoinco_onboard_bridge_integration/nestcoinco_onboard_bridge_integration.dart';
import 'package:onboard_wallet/app/app.locator.dart';
import 'package:onboard_wallet/app/app.router.dart';
import 'package:onboard_wallet/constants/analytic_event.dart';
import 'package:onboard_wallet/extensions/extensions.dart';
import 'package:onboard_wallet/haven/enums/currency_enum.dart' as haven_enums;
import 'package:onboard_wallet/haven/models/asset/local_asset_info.dart';
import 'package:onboard_wallet/haven/models/transfer/crypto_transfer_model.dart';
import 'package:onboard_wallet/haven/services/services.dart';
import 'package:onboard_wallet/services/analytics_service.dart';
import 'package:onboard_wallet/ui/dialogs/info_alert/external_close_dialog.dart';
import 'package:onboard_wallet/ui/views/send/base_send_view_model.dart';
import 'package:onboard_wallet/ui/views/send/data/send_destination_enum.dart';
import 'package:onboard_wallet/ui/widgets/onboard_fee_explainer.dart';
import 'package:onboard_wallet/utils/input_formatters.dart';
import 'package:stacked_services/stacked_services.dart';
import '../../../../utils/flag_icon.dart';
import '../../../widgets/asset_error_icon.dart';
import 'confirm_send_via_onboard_pay/confirm_send_via_onb_pay_view.dart';

class SendFromTradingViewModel extends BaseSendViewModel {
  SendFromTradingViewModel(
      {required super.transferAccountType, required super.sendDestinationEnum});

  final _navigationService = locator<NavigationService>();
  final _analyticsService = locator<AnalyticsService>();

  List<TextInputFormatter> get fromInputFormatters =>
      [AnyDpDecimalTextInputFormatter()];

  List<TextInputFormatter> get toInputFormatters => [
        if (sendDestinationEnum == SendDestinationEnum.bankAccount) ...[
          AnyDpDecimalTextInputFormatter()
        ] else ...[
          AnyDpDecimalTextInputFormatter()
        ]
      ];

  FiatTransferAsset? sourceFiatTransferAsset;
  num? fromAssetBalance;
  LocalAssetInfo? localAsset;

  String? get fromAssetBalanceString => fromAssetBalance
      ?.currencyFormat(decimalDigits: fromAssetDecimalDigits, symbol: "")
      .removeTrailingZero();

  int get fromAssetDecimalDigits => localAsset?.displayDecimals ?? 8;

  @override
  onViewModelReady() {
    super.onViewModelReady();

    //Set a default Crypto
    final userBalances =
        locator<TradingPortfolioService>().portfolioAssetsBalances;

    if (userBalances.isNotEmpty) {
      final defaultBalance = userBalances.firstWhere(
          (e) => e.asset.toLowerCase() == haven_enums.CurrencyFiatEnum.usd.name,
          orElse: () => userBalances[0]);
      fromAssetBalance = defaultBalance.balance.availableBalance;

      final defaultAsset = locator<TradingAssetsService>()
          .cachedAssetList
          .firstWhereOrNull((element) =>
              element.assetInfo?.symbol?.toLowerCase() ==
              defaultBalance.asset.toLowerCase());

      sourceFiatTransferAsset = FiatTransferAsset((b) => b
        ..symbol = defaultAsset?.assetInfo?.symbol
        ..name = defaultAsset?.assetInfo?.name
        ..assetTypeName = defaultAsset?.assetInfo?.name
        ..logoUrl = defaultAsset?.assetInfo?.logoUrl
        ..paymentMethodSource = PaymentMethodSource.PAY);

      EasyLoading.show();
      getTargetCurrency(fromAssetCode!).whenComplete(() {
        EasyLoading.dismiss();
        notifyListeners();
      });
    } else {
      changeFromAsset();
    }
  }

  changeFromAsset() {
    _toChooseFromFiatView();
  }

  _toChooseFromFiatView() {
    _navigationService.navigateToHavenSelectUserAssetsView(
        onContinue: (assetBalance, assetData) {
      final fromText = fromTextEditingController.text
          .removeSymbol(sourceFiatTransferAsset?.symbol ?? "");
      fromText.removeSpaces();

      _analyticsService.logEvent(
          eventName: AnalyticsEvent.selectSourceCryptoCurrency,
          parameters: {
            EventParameterKey.tokenSymbol: assetData?.assetInfo?.symbol,
          }
      );

      sourceFiatTransferAsset = FiatTransferAsset((b) => b
        ..symbol = assetData?.assetInfo?.symbol
        ..name = assetData?.assetInfo?.name
        ..assetTypeName = assetData?.assetInfo?.name
        ..logoUrl = assetData?.assetInfo?.logoUrl
        ..paymentMethodSource = PaymentMethodSource.PAY);

      localAsset = assetData?.assetInfo;
      fromAssetBalance = assetBalance.availableBalance;

      if (fromText.isNotEmpty) {
        fromTextEditingController.text = fromText;
      }

      transferExchangeRate = null;

      getTransferRate().whenComplete(() {
        updateToTextAmount();
        notifyListeners();
      });

      EasyLoading.show();
      getTargetCurrency(fromAssetCode!).whenComplete(() {
        EasyLoading.dismiss();
      });

      notifyListeners();
    });
  }

  void onUseMaxPressed() {
    if (fromAssetBalance != null) {
      String maxBalance = fromAssetBalance!
          .currencyFormat(
              decimalDigits: localAsset?.displayDecimals?.toInt() ?? 8,
              symbol: "")
          .removeTrailingZero();
      fromTextEditingController.text = maxBalance;
      fromTextEditingController.selection =
          TextSelection.collapsed(offset: maxBalance.length);
      onFromInputChanged(maxBalance);
    }
  }

  @override
  String get amountToBeConverted {
    if (transferExchangeRate == null) {
      return "0";
    }
    return "$fromAssetCode $amountConverted";
  }

  @override
  void checkButtonEnabled() {
    _checkInsufficientBalance();

    hasValidInputs = insufficientBalance == false &&
        minimumFunding == false &&
        maximumFunding == false &&
        (fromAmountAsDouble ?? 0) > 0 &&
        transferExchangeRate != null;

    notifyListeners();
  }

  @override
  String get formattedFee {
    if (transferExchangeRate == null) {
      return "0";
    }
    return "$fromAssetCode $processingFee";
  }

  @override
  String? get fromAssetCode => sourceFiatTransferAsset?.symbol;

  @override
  void setErrorMessage() {}

  _checkInsufficientBalance() {
    final amount = fromAmountAsDouble;
    if (amount == null) {
      insufficientBalance = false;
      return;
    }

    if (amount == 0) {
      insufficientBalance = false;
      return;
    }

    insufficientBalance = fromAssetBalance?.toDouble() != null &&
        amount > fromAssetBalance!.toDouble();
  }

  Widget? get fromAssetLogo {
    return ClipOval(
      child: getCurrencyCountryFlag(
            sourceFiatTransferAsset?.symbol,
            size: 27,
          ) ??
          CachedNetworkImage(
            imageUrl: sourceFiatTransferAsset?.logoUrl ?? "",
            height: 27,
            width: 27,
            fit: BoxFit.fitHeight,
            errorWidget: (_, __, ___) => const AssetErrorIconSvg(),
          ),
    );
  }

  @override
  void showFeeExplainer() {
    BuildContext? context = StackedService.navigatorKey?.currentContext;
    if (context == null) return;
    ExternalCloseDialog.showExternalDialog(
      context,
      child: const OnboardFeeExplainer(),
    );
  }

  void onContinue() {
    _navigationService.navigateToSendToBankPreviewView(
      fiatTransferAsset: targetFiatTransferAsset!,
      targetAmount: targetAmount,
      onContinue: (method) async {
        FiatPaymentMethod? selectedOnboardPayMethod;
        PaymentMethodsSvcPaymentMethod? selectedPaymentMethod;

        if (method is PaymentMethodsSvcPaymentMethod) {
          selectedPaymentMethod = method;
        } else if (method is FiatPaymentMethod) {
          selectedOnboardPayMethod = method;
        }

        if (targetFiatTransferAsset?.paymentMethodSource ==
            PaymentMethodSource.PAY) {
          _completeOnboardPay(selectedOnboardPayMethod);
        } else {
          _completeOnboardP2P(selectedPaymentMethod);
        }
      },
    );
  }

  _completeOnboardP2P(PaymentMethodsSvcPaymentMethod? selectedPaymentMethod) {
    if (transferExchangeRate == null &&
        fromAmountAsDouble == null &&
        targetFiatTransferAsset == null &&
        selectedPaymentMethod == null) {
      return;
    }

    final cashTransferModel = CashTransferModel()
      ..assetValue = fromAmountAsDouble.toString()
      ..quote = haven.CashWithdrawalQuote((b) => b
        ..rate = transferExchangeRate!.rate
        ..reference = transferExchangeRate!.reference
        ..currency = transferExchangeRate!.destAsset
        ..asset = transferExchangeRate!.sourceAsset
        ..minAmount = transferExchangeRate!.minAmount
        ..maxAmount = transferExchangeRate!.maxAmount
        ..processingFee = transferExchangeRate!.processingFee
        ..expiresAt = transferExchangeRate!.expiresAt)
      ..flagUrl = targetFiatTransferAsset!.logoUrl
      ..amount = targetAmount.toString()
      ..isOnboardPay = false
      ..paymentMethod = selectedPaymentMethod!;

    _navigationService.navigateToHavenCashWithdrawalConfirmView(
      transferModel: cashTransferModel,
    );
  }

  _completeOnboardPay(FiatPaymentMethod? paymentMethod) {
    if (transferExchangeRate == null &&
        fromAmountAsDouble == null &&
        targetFiatTransferAsset == null &&
        fromAmountAsDouble == null &&
        paymentMethod == null) {
      return;
    }

    _navigationService.navigateToView(ConfirmSendViaOnboardPayView(
      fiatPaymentMethod: paymentMethod!,
      targetFiatTransferAsset: targetFiatTransferAsset!,
      transferExchangeRate: transferExchangeRate!,
      fromAssetAmount: fromAmountAsDouble!,
      toAssetAmount: targetAmount.toString(),
      processingFee: processingFee,
    ));
  }

  void onChangeSource() {
    _navigationService.back();
  }
}
