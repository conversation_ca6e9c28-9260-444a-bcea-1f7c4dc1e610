import 'package:flutter/material.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';
import 'package:onboard_wallet/ui/views/send/data/send_destination_enum.dart';
import 'package:onboard_wallet/ui/views/send/send_trading/send_from_trading_viewmodel.dart';
import 'package:onboard_wallet/ui/views/send/widgets/send_input_field.dart';
import 'package:stacked/stacked.dart';

class SendFromTradingInputField
    extends ViewModelWidget<SendFromTradingViewModel> {
  const SendFromTradingInputField({super.key});

  @override
  Widget build(BuildContext context, SendFromTradingViewModel viewModel) {
    String? toAssetCode = viewModel.toAssetCode;
    if (toAssetCode == null) {
      if (viewModel.sendDestinationEnum == SendDestinationEnum.crypto) {
        toAssetCode = S.current.chooseCrypto;
      } else {
        toAssetCode = S.current.chooseCurrency;
      }
    }
    return SendInputField(
      fromTextEditingController: viewModel.fromTextEditingController,
      toTextEditingController: viewModel.toTextEditingController,
      toInputFormatters: viewModel.toInputFormatters,
      fromInputFormatters: viewModel.fromInputFormatters,
      onFromInputChanged: viewModel.onFromInputChanged,
      onToInputChanged: viewModel.onToInputChanged,
      toAssetCode: toAssetCode,
      toAssetLogo: viewModel.toAssetLogo,
      fromAssetCode: viewModel.fromAssetCode ?? S.current.chooseCrypto,
      hasNoFee: viewModel.hasNoFee,
      amountToBeConverted: viewModel.amountToBeConverted,
      onChangeFromAsset: viewModel.changeFromAsset,
      onChangeToAsset: viewModel.changeToAsset,
      showFeeExplainer: viewModel.showFeeExplainer,
      formattedFee: viewModel.formattedFee,
      fetchingRate: viewModel.fetchingRate,
      formattedRate: viewModel.formattedRate,
      fromAssetLogo: viewModel.fromAssetLogo,
      availableBalance: viewModel.fromAssetBalanceString,
      availableBalanceColor:
          viewModel.insufficientBalance ? ErrorColor.error500 : Grey.grey400,
      onMaxSelected: viewModel.onUseMaxPressed,
      fetchingToAsset: viewModel.fetchingTargetAsset,
      showColoredToDropDown: viewModel.fetchingTargetAsset == false &&
          viewModel.toAssetCode == null && viewModel.fromAssetCode != null,
      disabledToDropDown: viewModel.fromAssetCode == null,
    );
  }
}
