import 'dart:async';
import 'dart:math';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:collection/collection.dart';
import 'package:debounce_throttle/debounce_throttle.dart';
import 'package:flutter/material.dart'
    show Box<PERSON>it, TextEditingController, Widget;
import 'package:flutter/widgets.dart' show BuildContext;
import 'package:nestcoinco_onboard_api_gateway/nestcoinco_onboard_api_gateway.dart';
import 'package:onboard_wallet/api/onboard_exception.dart';
import 'package:onboard_wallet/app/app.locator.dart';
import 'package:onboard_wallet/app/app.router.dart';
import 'package:onboard_wallet/constants/analytic_event.dart';
import 'package:onboard_wallet/extensions/extensions.dart';
import 'package:onboard_wallet/manager/manager.dart';
import 'package:onboard_wallet/models/country.dart';
import 'package:onboard_wallet/models/token/local_token.dart';
import 'package:onboard_wallet/services/send_service.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:onboard_wallet/ui/views/send/choose_external_token_view/choose_external_token_view.dart';
import 'package:onboard_wallet/ui/views/send/data/send_destination_enum.dart';
import 'package:onboard_wallet/ui/widgets/asset_error_icon.dart';
import 'package:onboard_wallet/ui/widgets/onboard_fee_explainer.dart';
import 'package:onboard_wallet/utils/flag_icon.dart';
import 'package:queue/queue.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';
import '../../../models/result.dart';
import '../../dialogs/info_alert/external_close_dialog.dart'
    show ExternalCloseDialog;

abstract class BaseSendViewModel extends BaseViewModel {
  final TransferAccountType transferAccountType;
  final SendDestinationEnum sendDestinationEnum;

  BaseSendViewModel({
    required this.transferAccountType,
    required this.sendDestinationEnum,
  });

  final _sendService = locator<SendService>();
  final _toastManager = locator<ToastManager>();
  final _userService = locator<UserService>();
  final _navigationService = locator<NavigationService>();
  final _analyticsService = locator<AnalyticsService>();

  // Crypto source token
  LocalToken? sourceToken;

  FiatTransferAsset? targetFiatTransferAsset;
  TransferTokenNetwork? targetTokenNetwork;
  CryptoTransferAsset? targetCryptoTransferAsset;

  FiatTransferAssetsList? targetFiatTransferAssetsList;
  CryptoTransferAssetsList? targetCryptoTransferAssetsList;

  TransferExchangeRate? transferExchangeRate;

  TextEditingController fromTextEditingController = TextEditingController();
  TextEditingController toTextEditingController = TextEditingController();

  String? get fromAssetCode;

  String? get toAssetCode {
    if (sendDestinationEnum == SendDestinationEnum.bankAccount) {
      return targetFiatTransferAsset?.symbol.toUpperCase();
    } else {
      return targetCryptoTransferAsset?.symbol;
    }
  }

  Widget? get toAssetLogo {
    if (sendDestinationEnum == SendDestinationEnum.bankAccount) {
      return getCurrencyCountryFlag(
        targetFiatTransferAsset?.symbol,
        size: 27,
      );
    } else if (targetCryptoTransferAsset?.logoUrl != null) {
      return CachedNetworkImage(
        imageUrl: targetCryptoTransferAsset?.logoUrl ?? "",
        height: 27,
        width: 27,
        fit: BoxFit.fitHeight,
        errorWidget: (_, __, ___) => const AssetErrorIconSvg(),
      );
    }
    return null;
  }

  String get formattedRate {
    return transferExchangeRate?.rate.toString() ?? "0";
  }

  num get amountConverted {
    final feePercent = transferExchangeRate?.processingFeePercent ?? 0;
    final fee = transferExchangeRate?.processingFee ?? 0;
    final sourceAmount = fromAmountAsDouble ?? 0;

    final targetAmount = (sourceAmount - fee) * (1 - feePercent / 100);
    return targetAmount;
  }

  ///the amount the user receives in the receiving currency.
  num get targetAmount {
    final rate = transferExchangeRate?.rate ?? 0;
    return amountConverted * rate;
  }

  bool get hasNoFee {
    return transferExchangeRate != null &&
        fromAmountAsDouble != null &&
        fromAmountAsDouble! > 0 &&
        processingFee == 0;
  }

  num get processingFee {
    final finalFee = (fromAmountAsDouble ?? 0) - (amountConverted);
    return max(finalFee.roundUp(4), 0);
  }

  String get amountToBeConverted;

  String get formattedFee;

  final queue = Queue();

  double? get fromAmountAsDouble {
    final rawString = fromTextEditingController.text
        .removeCommas()
        .removeSymbol(fromAssetCode ?? "");
    return double.tryParse(rawString);
  }

  double get toAmountAsDouble {
    final rawString = toTextEditingController.text
        .removeCommas()
        .removeSymbol(toAssetCode ?? "");
    return double.tryParse(rawString) ?? 0;
  }

  final deBouncer = Debouncer<double?>(const Duration(milliseconds: 500),
      initialValue: null, checkEquality: false);

  checkRateCanCoverAmount(double amountAsDouble) {
    queue.add(
        () => fetchRateIfAmountMoreThanMinimum(amountAsDouble).whenComplete(
              () {
                checkButtonEnabled();
                setErrorMessage();
              },
            ));
    queue.add(() => fetchRateIfAmountMoreThanMax(amountAsDouble).whenComplete(
          () {
            checkButtonEnabled();
            setErrorMessage();
          },
        ));
  }

  onViewModelReady() {
    _fetchCountries();
    deBouncer.values.listen((_) {
      if (transferExchangeRate == null) {
        getTransferRate();
      } else {
        if (transferExchangeRate!.swapDetails.performSwap == true) {
          getSwapQuote(transferExchangeRate!);
        }
      }
      if (insufficientBalance == false && fromAmountAsDouble != null) {
        fetchRateIfAmountMoreThanMinimum(fromAmountAsDouble!);
        fetchRateIfAmountMoreThanMax(fromAmountAsDouble!);
      }
    });
  }

  StreamSubscription? countryStreamSubscription;
  Set<LocalCountry> _countries = {};

  _fetchCountries() {
    countryStreamSubscription =
        locator<CardService>().getCardCountries().listen((response) {
      response.when(
        success: (cardCountryList) {
          _countries = cardCountryList.toSet();
        },
        failure: (failure) {},
      );
    });
  }

  onFromInputChanged(String? value) {
    if (value == null || value.isEmpty) {
      reset();
      return;
    }
    double? amountAsDouble = fromAmountAsDouble;
    if (amountAsDouble != null) {
      updateToTextAmount();
      deBouncer.setValue(amountAsDouble);
    }

    setErrorMessage();
    checkButtonEnabled();
    notifyListeners();
  }

  void updateToTextAmount() {
    if (transferExchangeRate != null && transferExchangeRate?.rate != 0) {
      if (sendDestinationEnum == SendDestinationEnum.crypto) {
        toTextEditingController.text =
            "${targetAmount.currencyFormat(symbol: "", decimalDigits: 4).removeTrailingZero()} ${toAssetCode ?? ""}";
      } else {
        toTextEditingController.text =
            "${toAssetCode ?? ""} ${targetAmount.currencyFormat(symbol: "", decimalDigits: 4).removeTrailingZero()}";
      }

      notifyListeners();
    }
  }

  onToInputChanged(String? value) {
    if (value == null || value.isEmpty) {
      reset();
      return;
    }

    updateFromAmount();
  }

  updateFromAmount() {
    final rate = transferExchangeRate?.rate ?? 0;
    if (transferExchangeRate != null && rate != 0) {
      final processingFeePercent =
          transferExchangeRate?.processingFeePercent ?? 0;
      final fee = transferExchangeRate?.processingFee ?? 0;

      final sourceAmount =
          (toAmountAsDouble / (rate) / (1 - processingFeePercent / 100)) + fee;
      deBouncer.setValue(sourceAmount);
      fromTextEditingController.text =
          "${fromAssetCode ?? ""} ${sourceAmount.currencyFormat(symbol: "", decimalDigits: 4)}";

      setErrorMessage();
      checkButtonEnabled();
      notifyListeners();
    }
  }

  bool maximumFunding = false;

  bool minimumFunding = false;

  Future<void> fetchRateIfAmountMoreThanMax(double amountAsDouble) async {
    num? rateMaxAmount = transferExchangeRate?.maxAmount;

    if (rateMaxAmount == null) {
      maximumFunding = false;
      return;
    }
    bool moreThanMaxAmount = amountAsDouble > rateMaxAmount;
    if (moreThanMaxAmount) {
      final response = await getTransferRate();
      response?.when(
        success: (_) {
          final maxAmount = transferExchangeRate?.maxAmount;
          if (maxAmount != null) {
            maximumFunding = amountAsDouble > rateMaxAmount;
          }
        },
        failure: (_) {
          maximumFunding = true;
        },
      );
    } else {
      maximumFunding = moreThanMaxAmount;
    }
    checkButtonEnabled();
    setErrorMessage();
  }

  Future<void> fetchRateIfAmountMoreThanMinimum(double amountAsDouble) async {
    num? rateMinAmount = transferExchangeRate?.minAmount;

    if (rateMinAmount == null) {
      minimumFunding = false;
      return;
    }
    bool lessThanMinAmount = amountAsDouble < rateMinAmount;
    if (lessThanMinAmount) {
      final response = await getTransferRate();
      response?.when(
        success: (_) {
          final minAmount = transferExchangeRate?.minAmount;
          if (minAmount != null) {
            minimumFunding = amountAsDouble > rateMinAmount;
          }
        },
        failure: (_) {
          minimumFunding = true;
        },
      );
    } else {
      minimumFunding = lessThanMinAmount;
    }
    checkButtonEnabled();
  }

  Future getTargetCurrency(String sourceAsset) async {
    fetchingTargetAsset = true;
    final response = await _sendService.getTargetCurrencies(
      sourceAccountType: transferAccountType,
      sourceAsset: sourceAsset,
      sourceNetwork: sourceNetwork,
    );
    fetchingTargetAsset = false;
    response.when(success: (success) async {
      targetFiatTransferAssetsList = success;
      _setDefaultTargetCurrency(sourceAsset);
    }, failure: (failure) {
      _toastManager.showErrorToast(text: failure.message);
    });
  }

  _setDefaultTargetCurrency(String sourceAsset) async {
    if (_countries.isEmpty) return;
    final countryCode = await _userService.getUserTransactionCountryCode();
    final country = _countries.firstWhereOrNull(
        (element) => element.code.toLowerCase() == countryCode!.toLowerCase());
    if (country != null &&
        targetFiatTransferAssetsList != null &&
        targetFiatTransferAsset == null) {
      final currencies = targetFiatTransferAssetsList!.currencies.toList();
      if (currencies.isNotEmpty) {
        final target = currencies.firstWhereOrNull((e) =>
            e.symbol.toLowerCase() == country.currency?.toLowerCase() &&
            e.symbol.toLowerCase() != sourceAsset.toLowerCase());

        if (target != null) {
          transferExchangeRate = null;
          toTextEditingController.clear();
          targetFiatTransferAsset = target;
          notifyListeners();
          getTransferRate();
        }
      }
    }
  }

  String? getCountryCodeFiatTransferAsset(FiatTransferAsset fiat) {
    final country = _countries.firstWhereOrNull(
        (e) => fiat.symbol.toLowerCase() == e.currency?.toLowerCase());
    return country?.code;
  }

  bool _fetchingTargetAsset = false;

  bool get fetchingTargetAsset => _fetchingTargetAsset;

  set fetchingTargetAsset(bool fetchingTargetAsset) {
    _fetchingTargetAsset = fetchingTargetAsset;
    notifyListeners();
  }

  Future getTargetToken(String sourceAsset) async {
    fetchingTargetAsset = true;
    final response = await _sendService.getTransferTargetTokens(
      sourceAccountType: transferAccountType,
      sourceAsset: sourceAsset,
    );
    fetchingTargetAsset = false;
    response.when(success: (success) {
      targetCryptoTransferAssetsList = success;
      notifyListeners();
    }, failure: (failure) {
      _toastManager.showErrorToast(text: failure.message);
    });
  }

  bool _fetchingRate = false;

  bool get fetchingRate => _fetchingRate;

  set fetchingRate(bool fetchingRate) {
    _fetchingRate = fetchingRate;
    notifyListeners();
  }

  Future<Result<TransferExchangeRate>?> getTransferRate() async {
    if (fromAssetCode == null ||
        toAssetCode == null ||
        fromAmountAsDouble == 0 ||
        fromAmountAsDouble == null) {
      return null;
    }
    toTextEditingController.clear();
    fetchingRate = true;
    final response = await _getTransferRate();
    checkButtonEnabled();
    fetchingRate = false;
    if (transferExchangeRate != null) {
      updateToTextAmount();
    }
    notifyListeners();
    return response;
  }

  Future<Result<TransferExchangeRate>> _getTransferRate() async {
    TransferExchangeRateRequest transferExchangeRateRequest =
        TransferExchangeRateRequest((b) {
      b.destAsset = toAssetCode;
      b.destNetwork = destNetwork;
      b.sourceAccountType = transferAccountType;
      b.sourceAsset = fromAssetCode;
      b.sourceAmount = fromAmountAsDouble;
      b.sourceNetwork = sourceNetwork;
      b.sourceTokenAddress = sourceToken?.contractAddress;
    });

    final response = await _sendService.getRateForTransfer(
      transferExchangeRequest: transferExchangeRateRequest,
    );
    response.when(success: (success) {
      if (success?.swapDetails.performSwap == true) {
        getSwapQuote(success!);
      } else {
        transferExchangeRate = success;
        updateToTextAmount();
      }
      return Result.success(data: success);
    }, failure: (failure) {
      _toastManager.showErrorToast(text: failure.message);
      notifyListeners();
      return Result.failure(error: failure);
    });

    return Result.failure(
        error: OnboardExceptions.fromErrorMessage("unknown error"));
  }

  bool hasValidInputs = false;

  bool get buttonEnabled {
    final result = hasValidInputs && !fetchingRate && !isBusy;
    return result;
  }

  void checkButtonEnabled();

  bool insufficientBalance = false;

  void setErrorMessage();

  void reset() {
    maximumFunding = false;
    minimumFunding = false;
    toTextEditingController.clear();
    fromTextEditingController.clear();
    checkButtonEnabled();
  }

  changeToAsset() {
    if (sendDestinationEnum == SendDestinationEnum.bankAccount) {
      _toChooseToFiatView();
    } else {
      _toChooseExternalCryptoView();
    }
  }

  void _toChooseToFiatView() {
    _navigationService.navigateToChooseFiatView(
      fiats: targetFiatTransferAssetsList?.currencies
              .where((e) => e.symbol != fromAssetCode)
              .toList() ??
          [],
      selectedFiat: targetFiatTransferAsset,
      isFrom: false,
      onSelectFiat: (e) {
        if (e != targetFiatTransferAsset) {
          _analyticsService.logEvent(
              eventName: AnalyticsEvent.selectTargetFiatCurrency,
              parameters: {
                EventParameterKey.symbol: e.symbol,
              });
          //clear current rate and current input
          transferExchangeRate = null;
          toTextEditingController.clear();
          targetFiatTransferAsset = e;
          notifyListeners();
          if (fromAmountAsDouble != null && fromAmountAsDouble! > 0) {
            getTransferRate().whenComplete(() {
              updateToTextAmount();
            });
          }
        }
        _navigationService.back();
      },
    );
  }

  String? get destNetwork => targetTokenNetwork?.networkId;

  String? sourceNetwork;

  void _toChooseExternalCryptoView() {
    _navigationService.navigateToView(
      ChooseExternalTokenView(
        onSelectToken: (token, TransferTokenNetwork network) {
          bool isSameTokenAndNetwork = token == targetCryptoTransferAsset &&
              network.coreNetworkId?.toLowerCase() ==
                  targetTokenNetwork?.coreNetworkId?.toLowerCase();
          _analyticsService.logEvent(
              eventName: AnalyticsEvent.selectTargetCryptoCurrency,
              parameters: {
                EventParameterKey.symbol: token.symbol,
                EventParameterKey.network: network.name,
              });
          if (isSameTokenAndNetwork == false) {
            //clear current rate and current input
            transferExchangeRate = null;
            toTextEditingController.clear();
            targetCryptoTransferAsset = token;
            targetTokenNetwork = network;
            notifyListeners();
            getTransferRate().whenComplete(() {
              if (fromAmountAsDouble != null && fromAmountAsDouble! > 0) {
                updateToTextAmount();
              }
            });
          }
        },
        cryptoTransferAssetsList: targetCryptoTransferAssetsList,
        activeToken: targetCryptoTransferAsset,
        transferTokenNetwork: targetTokenNetwork,
      ),
    );
  }

  Future getSwapQuote(TransferExchangeRate transferExchangeRate) async {}

  void onChangeSendSource() {
    _navigationService.navigateToSelectSendSourceView(
      sendDestinationEnum: sendDestinationEnum,
    );
  }

  void showFeeExplainer() {
    BuildContext? context = StackedService.navigatorKey?.currentContext;
    if (context == null) return;
    ExternalCloseDialog.showExternalDialog(
      context,
      child: const OnboardFeeExplainer(),
    );
  }

  ///This method returns true if the rate is expired;
  bool? checkRateIsExpired() {
    final expiration = transferExchangeRate?.expiresAt;
    if (expiration == null) {
      return null;
    }

    return DateTime.now().isAfter(expiration);
  }
}
