import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:onboard_wallet/extensions/extensions.dart';
import 'package:onboard_wallet/gen/assets.gen.dart';
import 'package:onboard_wallet/gen/fonts.gen.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/theme_typography.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';
import 'package:onboard_wallet/ui/common/ui_helpers.dart';
import 'package:onboard_wallet/ui/views/cards/fund/card_fund/widgets/card_funding_input_field.dart';
import 'package:onboard_wallet/ui/views/send/send_with_cash/widgets/asset_drop_down.dart';
import 'package:onboard_wallet/ui/views/trade/widgets/max_button.dart';
import 'package:onboard_wallet/ui/widgets/cards/card_surface.dart';
import 'package:onboard_wallet/ui/widgets/input_field/onboard_input.dart';

class SendInputField extends StatelessWidget {
  final TextEditingController fromTextEditingController;
  final TextEditingController toTextEditingController;
  final List<TextInputFormatter> toInputFormatters;
  final List<TextInputFormatter> fromInputFormatters;
  final ValueChanged<String>? onFromInputChanged;
  final ValueChanged<String>? onToInputChanged;
  final Widget? fromAssetLogo;
  final String? fromAssetCode;
  final GestureTapCallback onChangeFromAsset;
  final Widget? toAssetLogo;
  final String? toAssetCode;
  final GestureTapCallback onChangeToAsset;
  final GestureTapCallback showFeeExplainer;
  final String formattedRate;
  final String amountToBeConverted;
  final bool fetchingRate;
  final bool fetchingFromAsset;
  final bool fetchingToAsset;
  final bool hasNoFee;
  final String formattedFee;
  final String? availableBalance;
  final String? errorMessage;
  final Color? availableBalanceColor;
  final VoidCallback? onMaxSelected;
  final bool showColoredDropDown;
  final bool showColoredToDropDown;
  final bool disabledToDropDown;

  final bool showSourceAssetDropdown;

  const SendInputField({
    super.key,
    required this.fromTextEditingController,
    required this.toTextEditingController,
    required this.toInputFormatters,
    required this.fromInputFormatters,
    required this.onFromInputChanged,
    required this.onToInputChanged,
    required this.toAssetCode,
    required this.fromAssetCode,
    this.fromAssetLogo,
    required this.hasNoFee,
    required this.amountToBeConverted,
    required this.onChangeFromAsset,
    required this.onChangeToAsset,
    required this.showFeeExplainer,
    required this.formattedFee,
    required this.fetchingRate,
    required this.formattedRate,
    this.toAssetLogo,
    this.availableBalance,
    this.availableBalanceColor,
    this.onMaxSelected,
    this.showColoredDropDown = false,
    this.fetchingFromAsset = false,
    required this.fetchingToAsset,
    this.errorMessage,
    this.showSourceAssetDropdown = true,
    this.showColoredToDropDown = false,
    this.disabledToDropDown = false,
  });

  @override
  Widget build(BuildContext context) {
    return CardSurface(
      padding: EdgeInsets.zero,
      borderRadius: BorderRadius.circular(12),
      bgColor: Grey.grey50,
      border: Border.all(
        color: kcBaseWhite,
        width: 3,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CardSurface(
            padding: const EdgeInsets.fromLTRB(0, 14, 10, 14),
            borderRadius: BorderRadius.circular(12),
            child: Column(
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      flex: 2,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(left: 8.0),
                            child: Text(
                              S.current.youSend,
                              style: Theme.of(context)
                                  .textTheme
                                  .body14Regular
                                  .copyWith(
                                    color: Grey.grey500,
                                  ),
                            ),
                          ),
                          OnboardInputField(
                            controller: fromTextEditingController,
                            fillColor: kcBaseWhite,
                            keyboardType: const TextInputType.numberWithOptions(
                              decimal: true,
                            ),
                            hintText: '0',
                            inputFormatters: fromInputFormatters,
                            onChanged: onFromInputChanged,
                            hintStyle: Theme.of(context)
                                .textTheme
                                .headlineSmall
                                ?.copyWith(
                                  fontSize: 22.fontSize,
                                  color: const Color(0xFFE5E4EA),
                                  fontFamily: FontFamily.satoshi,
                                  fontWeight: FontWeight.w900,
                                  letterSpacing: -0.5,
                                ),
                            style: Theme.of(context)
                                .textTheme
                                .headlineSmall
                                ?.copyWith(
                                  fontSize: 22.fontSize,
                                  color: kcBrandBlue,
                                  fontFamily: FontFamily.satoshi,
                                  fontWeight: FontWeight.w900,
                                  letterSpacing: -0.5,
                                ),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0, top: 13),
                      child: ChangeAssetDropDown(
                        onTap: onChangeFromAsset,
                        assetCode: fromAssetCode,
                        logo: fromAssetLogo,
                        loading: fetchingFromAsset,
                        showColoredDropDown: showColoredDropDown,
                        showDropdownIcon: showSourceAssetDropdown,
                      ),
                    ),
                  ],
                ),
                if (availableBalance != null || errorMessage != null) ...[
                  Padding(
                    padding: const EdgeInsets.only(left: 8.0),
                    child: Row(
                      children: [
                        const SizedBox(
                          height: 12,
                        ),
                        if (errorMessage != null) ...[
                          Text(
                            errorMessage!,
                            maxLines: 1,
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      fontSize: 12.fontSize,
                                      overflow: TextOverflow.ellipsis,
                                      color: ErrorColor.error500,
                                    ),
                          ),
                        ] else if (availableBalance != null) ...[
                          Text(
                            '${S.current.available}: ${availableBalance ?? ''}',
                            maxLines: 1,
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      fontSize: 12.fontSize,
                                      overflow: TextOverflow.ellipsis,
                                      color: availableBalanceColor,
                                    ),
                          ),
                        ],
                        const Spacer(),
                        if (onMaxSelected != null)
                          MaxButton(onTap: onMaxSelected)
                      ],
                    ),
                  ),
                ]
              ],
            ),
          ),
          const verticalSpace(15),
          InkWell(
            onTap: showFeeExplainer,
            child: CardFundingPreviewTile(
              icon: Assets.svg.subtractIcon.svg(),
              title: S.current.fee,
              titleIcon: Assets.svg.questionMarkFill.svg(),
              loading: fetchingRate,
              subtitle: hasNoFee
                  ? Assets.svg.freeIcon.svg()
                  : Text(
                      formattedFee,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                      style: Theme.of(context).textTheme.body14Regular.copyWith(
                            color: Grey.grey600,
                          ),
                    ),
            ),
          ),
          const verticalSpace(13),
          CardFundingPreviewTile(
            icon: Assets.svg.equalIcon.svg(),
            title: S.current.totalWellConvert,
            loading: fetchingRate,
            subtitle: Text(
              amountToBeConverted,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              style: Theme.of(context).textTheme.body14Regular.copyWith(
                    color: Grey.grey600,
                  ),
            ),
          ),
          const verticalSpace(13),
          CardFundingPreviewTile(
            icon: Assets.svg.multiplyIcon.svg(),
            title: S.current.rate,
            loading: fetchingRate,
            subtitle: Text(
              formattedRate,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              style: Theme.of(context).textTheme.body14Regular.copyWith(
                    color: Grey.grey600,
                  ),
            ),
          ),
          const verticalSpace(10.67),
          CardSurface(
            borderRadius: BorderRadius.circular(12),
            padding: const EdgeInsets.fromLTRB(0, 13, 9, 17),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  flex: 2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: Text(
                          S.current.youReceive,
                          style: Theme.of(context)
                              .textTheme
                              .body14Regular
                              .copyWith(
                                color: Grey.grey500,
                              ),
                        ),
                      ),
                      OnboardInputField(
                        controller: toTextEditingController,
                        enabled: false,
                        fillColor: kcBaseWhite,
                        keyboardType: const TextInputType.numberWithOptions(
                          decimal: true,
                        ),
                        hintText: '0',
                        inputFormatters: toInputFormatters,
                        onChanged: onToInputChanged,
                        hintStyle:
                            Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  fontSize: 22.fontSize,
                                  color: const Color(0xFFE5E4EA),
                                  fontFamily: FontFamily.satoshi,
                                  fontWeight: FontWeight.w900,
                                  letterSpacing: -0.5,
                                ),
                        style:
                            Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  fontSize: 22.fontSize,
                                  color: kcBrandBlue,
                                  fontFamily: FontFamily.satoshi,
                                  fontWeight: FontWeight.w900,
                                  letterSpacing: -0.5,
                                ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 8.0, top: 13),
                  child: ChangeAssetDropDown(
                    onTap: onChangeToAsset,
                    assetCode: toAssetCode,
                    logo: toAssetLogo,
                    loading: fetchingToAsset,
                    showColoredDropDown: showColoredToDropDown,
                    disabled: disabledToDropDown,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
