import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:nestcoinco_onboard_api_gateway/nestcoinco_onboard_api_gateway.dart';
import 'package:onboard_wallet/app/app.locator.dart';
import 'package:onboard_wallet/haven/ui/widgets/bottomsheet/external_close_sheet.dart';
import 'package:onboard_wallet/models/token_balance/local_address_token_balance.dart';
import 'package:onboard_wallet/services/token_service.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';
import 'package:onboard_wallet/ui/views/send/choose_external_token_view/widget/choose_external_wallet_token_modal.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';

class ChooseExternalWalletTokenViewModel extends BaseViewModel {
  final _navigationService = locator<NavigationService>();

  final CryptoTransferAssetsList? cryptoTransferAssetsList;
  final TransferTokenNetwork? transferTokenNetwork;
  final CryptoTransferAsset? activeToken;



  final void Function(CryptoTransferAsset, TransferTokenNetwork) onSelectToken;

  ChooseExternalWalletTokenViewModel(
      this.cryptoTransferAssetsList, this.onSelectToken,this.transferTokenNetwork,this.activeToken,);

  TextEditingController searchController = TextEditingController();

  List<CryptoTransferAsset> externalTokens = [];

  List<CryptoTransferAsset> tokens = [];

  void onInputChanged(String value) {
    if (value.isEmpty) {
      tokens = externalTokens;
      notifyListeners();
      return;
    }
    tokens = externalTokens
        .where(
            (e) => e.symbol.toLowerCase().contains(value.toLowerCase()) == true || e.name.toLowerCase().contains(value.toLowerCase()))
        .toList();
    notifyListeners();
  }

  void clearField() {
    searchController.clear();
    onInputChanged(searchController.text);
  }

  void onViewModelReady() {
    externalTokens = cryptoTransferAssetsList?.tokens.toList() ?? [];
    tokens = externalTokens;
    notifyListeners();
  }

  void onTapToken(CryptoTransferAsset asset) {
    BuildContext? context = StackedService.navigatorKey?.currentContext;
    if (context == null) return;
    List<TransferTokenNetwork> networks =
        cryptoTransferAssetsList?.networks.where((e) {
              return asset.supportedNetworks.firstWhereOrNull((p) =>
                      p.networkId.toLowerCase() == e.networkId.toLowerCase()) !=
                  null;
            }).toList() ??
            [];
    ExternalCloseSheet.showModal(
      context,
      backgroundColor: Grey.grey25,
      child: ChooseExternalTokenNetworkModalView(
        selectTransferTokenNetwork: transferTokenNetwork,
        onSelectToken: (token, tokenNetwork) {
          _navigationService.back();
          _navigationService.back();
          onSelectToken.call(token, tokenNetwork);
        },
        token: asset,
        currentSelectedToken: activeToken,
        networks: networks,
      ),
    );
  }
}
