import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:nestcoinco_onboard_api_gateway/nestcoinco_onboard_api_gateway.dart';
import 'package:onboard_wallet/gen/assets.gen.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/haven/ui/widgets/app_bar/transparent_app_bar.dart';
import 'package:onboard_wallet/haven/ui/widgets/cards/card_surface.dart';
import 'package:onboard_wallet/theme_typography.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';
import 'package:onboard_wallet/ui/common/ui_helpers.dart';
import 'package:onboard_wallet/ui/views/my_assets/widgets/search_field.dart';
import 'package:onboard_wallet/ui/widgets/asset_error_icon.dart';
import 'package:stacked/stacked.dart';
import 'choose_external_wallet_token_view_model.dart';

class ChooseExternalTokenView
    extends StackedView<ChooseExternalWalletTokenViewModel> {
  final CryptoTransferAssetsList? cryptoTransferAssetsList;
  final TransferTokenNetwork? transferTokenNetwork;
  final CryptoTransferAsset? activeToken;
  final void Function(CryptoTransferAsset, TransferTokenNetwork) onSelectToken;

  const ChooseExternalTokenView({
    super.key,
    required this.onSelectToken,
    this.transferTokenNetwork,
    required this.cryptoTransferAssetsList,
    this.activeToken,
  });

  @override
  Widget builder(BuildContext context,
      ChooseExternalWalletTokenViewModel viewModel, Widget? child) {
    return Scaffold(
      appBar: TransparentAppBar(
        centerTitle: true,
        title: Text(
          S.current.currency,
          style: Theme.of(context).textTheme.body18Medium.copyWith(
                color: kcBaseBlack,
              ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: CustomScrollView(
          slivers: [
            const SliverToBoxAdapter(
              child: verticalSpace(8),
            ),
            SliverToBoxAdapter(
              child: Center(
                child: Text(
                  S.current.chooseYourPreferredCryptoBalance,
                  style: Theme.of(context).textTheme.body14Regular.copyWith(
                        color: Grey.grey500,
                      ),
                ),
              ),
            ),
            const SliverToBoxAdapter(
              child: verticalSpace(32),
            ),
            if (viewModel.externalTokens.isEmpty) ...[
              SliverToBoxAdapter(
                child: Column(
                  children: [
                    const verticalSpace(46),
                    Assets.svg.searchEmptyStateIlos.svg(),
                    const verticalSpace(18),
                    Text(
                      S.current.noBalancesAvailable,
                      style: Theme.of(context).textTheme.body13Light.copyWith(
                            color: Grey.grey600,
                          ),
                    )
                  ],
                ),
              )
            ] else ...[
              SliverSearchField(
                padding: EdgeInsets.zero,
                searchController: viewModel.searchController,
                showClearIcon: viewModel.searchController.text.isNotEmpty,
                onChanged: viewModel.onInputChanged,
                onClearField: () => viewModel.clearField(),
              ),
              const SliverToBoxAdapter(
                child: verticalSpace(13),
              ),
              if (viewModel.tokens.isEmpty)
                ...[
                  SliverToBoxAdapter(
                    child: Column(
                      children: [
                        const verticalSpace(46),
                        Assets.svg.searchEmptyStateIlos.svg(),
                        const verticalSpace(18),
                        Text(
                          S.current.noBalancesAvailable,
                          style: Theme.of(context).textTheme.body13Light.copyWith(
                            color: Grey.grey600,
                          ),
                        )
                      ],
                    ),
                  )
                ]
              else ...[
                SliverToBoxAdapter(
                  child: CardSurface(
                    child: Column(
                      children: [
                        const SizedBox(
                          height: 10,
                        ),
                        ListView.separated(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: viewModel.tokens.length,
                          itemBuilder: (context, index) {
                            final asset = viewModel.tokens[index];
                            return InkWell(
                              onTap: () => viewModel.onTapToken(asset),
                              child: Row(
                                children: [
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(20),
                                    child: CachedNetworkImage(
                                      imageUrl: asset.logoUrl,
                                      height: 34,
                                      width: 34,
                                      fit: BoxFit.fitHeight,
                                      errorWidget: (_, __, ___) =>
                                          const AssetErrorIconSvg(),
                                    ),
                                  ),
                                  const horizontalSpace(8),
                                  Text(
                                    asset.name,
                                    style: Theme.of(context)
                                        .textTheme
                                        .body14Medium
                                        .copyWith(
                                          color: Grey.grey900,
                                        ),
                                  ),
                                  const Spacer(),
                                  const horizontalSpace(8),
                                  Assets.svg.chevronDown.svg(),
                                ],
                              ),
                            );
                          },
                          separatorBuilder: (BuildContext context, int index) {
                            return const SizedBox(
                              height: 25,
                            );
                          },
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                      ],
                    ),
                  ),
                ),
              ]
            ]
          ],
        ),
      ),
    );
  }

  @override
  ChooseExternalWalletTokenViewModel viewModelBuilder(BuildContext context) =>
      ChooseExternalWalletTokenViewModel(cryptoTransferAssetsList,
          onSelectToken, transferTokenNetwork, activeToken);

  @override
  void onViewModelReady(ChooseExternalWalletTokenViewModel viewModel) =>
      viewModel.onViewModelReady();
}
