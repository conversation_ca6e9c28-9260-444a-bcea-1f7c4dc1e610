import 'package:flutter/material.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';
import 'package:onboard_wallet/ui/views/send/data/send_destination_enum.dart';

import 'package:onboard_wallet/ui/views/send/widgets/send_input_field.dart';

import 'package:stacked/stacked.dart';

import '../send_from_virtual_account_viewmodel.dart';

class SendFromVirtualAccountInputField
    extends ViewModelWidget<SendFromVirtualAccountViewmodel> {
  const SendFromVirtualAccountInputField({super.key});

  @override
  Widget build(
      BuildContext context, SendFromVirtualAccountViewmodel viewModel) {
    String? toAssetCode = viewModel.toAssetCode;
    if (toAssetCode == null) {
      if (viewModel.sendDestinationEnum == SendDestinationEnum.crypto) {
        toAssetCode = S.current.chooseCrypto;
      } else {
        toAssetCode = S.current.chooseCurrency;
      }
    }
    return SendInputField(
      fromTextEditingController: viewModel.fromTextEditingController,
      toTextEditingController: viewModel.toTextEditingController,
      toInputFormatters: viewModel.toInputFormatters,
      fromInputFormatters: viewModel.fromInputFormatters,
      onFromInputChanged: viewModel.onFromInputChanged,
      onToInputChanged: viewModel.onToInputChanged,
      toAssetCode: toAssetCode,
      toAssetLogo: viewModel.toAssetLogo,
      fromAssetCode: viewModel.fromAssetCode,
      hasNoFee: viewModel.hasNoFee,
      amountToBeConverted: viewModel.amountToBeConverted,
      onChangeFromAsset: viewModel.changeFromAsset,
      onChangeToAsset: viewModel.changeToAsset,
      showFeeExplainer: viewModel.showFeeExplainer,
      formattedFee: viewModel.formattedFee,
      fetchingRate: viewModel.fetchingRate,
      formattedRate: viewModel.formattedRate,
      fromAssetLogo: viewModel.fromAssetLogo,
      availableBalanceColor: viewModel.showInsufficientBalance
          ? ErrorColor.error500
          : Grey.grey400,
      onMaxSelected: viewModel.onUseMaxPressed,
      availableBalance: viewModel.availableBalance,
      fetchingToAsset: viewModel.fetchingTargetAsset,
      showSourceAssetDropdown: false,
      showColoredToDropDown: viewModel.fetchingTargetAsset == false &&
          viewModel.toAssetCode == null && viewModel.fromAssetCode != null,
    );
  }
}
