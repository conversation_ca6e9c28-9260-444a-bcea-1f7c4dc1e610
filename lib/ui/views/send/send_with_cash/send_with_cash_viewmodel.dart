import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:nestcoinco_onboard_api_gateway/nestcoinco_onboard_api_gateway.dart';
import 'package:nestcoinco_onboard_bridge_integration/nestcoinco_onboard_bridge_integration.dart';
import 'package:onboard_wallet/app/app.locator.dart';
import 'package:onboard_wallet/app/app.logger.dart';
import 'package:onboard_wallet/app/app.router.dart';
import 'package:onboard_wallet/constants/constants.dart';
import 'package:onboard_wallet/enums/enums.dart';
import 'package:onboard_wallet/extensions/extensions.dart';
import 'package:onboard_wallet/extensions/string.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/manager/toast.dart';
import 'package:onboard_wallet/models/token/local_token.dart';
import 'package:onboard_wallet/services/kyc_service.dart';
import 'package:onboard_wallet/services/onboard_provider_service.dart';
import 'package:onboard_wallet/services/ongoing_order_service.dart';
import 'package:onboard_wallet/services/send_service.dart';
import 'package:onboard_wallet/services/user_service.dart';
import 'package:onboard_wallet/services/wallet_navigation_service.dart';
import 'package:onboard_wallet/ui/views/send/base_send_view_model.dart';
import 'package:onboard_wallet/ui/views/send/data/send_destination_enum.dart';
import 'package:onboard_wallet/ui/widgets/asset_error_icon.dart';
import 'package:onboard_wallet/utils/input_formatters.dart';
import 'package:stacked_services/stacked_services.dart';
import 'package:country/country.dart' as country;

import '../../../../services/analytics_service.dart';
import '../../../../utils/flag_icon.dart';

class SendWithCashViewModel extends BaseSendViewModel {
  final _navigationService = locator<NavigationService>();
  final _sendService = locator<SendService>();
  final _toastManager = locator<ToastManager>();
  final _userService = locator<UserService>();
  final _kycService = locator<KycService>();
  final _analyticsService = locator<AnalyticsService>();

  SendWithCashViewModel(
      {required super.sendDestinationEnum, required super.transferAccountType});

  List<TextInputFormatter> get fromInputFormatters => [
        AnyDpDecimalTextInputFormatter(
          leadingSymbol: fromAssetCode == null ? "" : "$fromAssetCode ",
        )
      ];

  List<TextInputFormatter> get toInputFormatters => [
        if (sendDestinationEnum == SendDestinationEnum.bankAccount) ...[
          AnyDpDecimalTextInputFormatter(
            leadingSymbol: "${toAssetCode ?? ""} ",
          )
        ] else ...[
          AnyDpDecimalTextInputFormatter(
              trailingSymbol: " ${toAssetCode ?? ""}")
        ]
      ];

  LocalToken? toToken;

  FiatTransferAsset? sourceFiatTransferAsset;
  CryptoTransferAsset? cryptoTransferAsset;
  FiatTransferAssetsList? fiatTransferAssetsList;
  CryptoTransferAssetsList? cryptoTransferAssetsList;

  List<LocalToken> networkTokens = [];

  changeFromAsset() {
    _toChooseFromFiatView();
  }

  _toChooseFromFiatView() {
    _navigationService.navigateToChooseFiatView(
      fiats: fiatTransferAssetsList?.currencies.toList() ?? [],
      selectedFiat: sourceFiatTransferAsset,
      isFrom: true,
      onSelectFiat: (e) {
        if (e != sourceFiatTransferAsset) {
          _analyticsService.logEvent(
              eventName: AnalyticsEvent.selectSourceFiatCurrency,
              parameters: {EventParameterKey.symbol: e.symbol});
          //clear current rate
          transferExchangeRate = null;
          toTextEditingController.clear();
          final fromText = fromTextEditingController.text
              .trim()
              .removeSymbol(sourceFiatTransferAsset?.symbol ?? "")
              .trim();
          fromText.removeSpaces();
          sourceFiatTransferAsset = e;

          if (fromText.isNotEmpty) {
            fromTextEditingController.text =
                '${sourceFiatTransferAsset?.symbol ?? ""} $fromText';
          } else {
            //fail safe to clear input field
            fromTextEditingController.clear();
          }

          _getTargetAsset();
          notifyListeners();
        }

        _navigationService.back();
      },
    );
  }

  Future<void> onContinue() async {
    if (transferExchangeRate == null) {
      return;
    }

    bool requiresStandardKyc =
        transferExchangeRate!.requiresStandardKyc == true;
    bool requiresExtendedKyc =
        transferExchangeRate!.requiresExtendedKyc == true;

    // First check for extended KYC as it's more restrictive
    if (requiresExtendedKyc) {
      EasyLoading.show();
      final extendedKycStatus =
          await _kycService.getKycStatus(kycPurpose: KycPurpose.usdAccount);
      EasyLoading.dismiss();

      if (extendedKycStatus != LocalKycStatus.verified) {
        // If extended KYC is not verified, initiate it
        final updatedStatus =
            await _kycService.initiateKyc(kycPurpose: KycPurpose.usdAccount);
        if (updatedStatus != LocalKycStatus.verified) {
          return; // Exit if extended KYC is still not verified
        }
      }
    }

    // Then check for standard KYC if needed
    if (requiresStandardKyc) {
      final verified =
          _kycService.currentUser?.kycStatus == KycStatus.VERIFIED.name;
      if (!verified) {
        EasyLoading.show();
        final standardKycStatus = await _kycService.getKycStatus(
            kycPurpose: KycPurpose.sendMoneyAbroad);
        EasyLoading.dismiss();

        if (standardKycStatus != LocalKycStatus.verified) {
          // If standard KYC is not verified, initiate it
          final updatedStatus = await _kycService.initiateKyc(
              kycPurpose: KycPurpose.sendMoneyAbroad);
          if (updatedStatus != LocalKycStatus.verified) {
            return; // Exit if standard KYC is still not verified
          }
        }
      }
    }

    // If we've passed all KYC checks, proceed with sending money
    _sendMoney();
  }

  StreamSubscription? _webEventsStream;

  final _onboardProviderService = locator<OnboardProviderService>();
  OnboardWebEvents? _onboardWebEvents;

  Future<void> _onOnramp(OnrampTransactionResponse? result) async {
    bool injectProvider = result?.injectProvider ?? false;
    if (injectProvider) {
      _navigationService.back();
      _webEventsStream =
          _onboardProviderService.orderWebEventsStream?.listen((orderWebEvent) {
        _onboardWebEvents = orderWebEvent;
        String event = orderWebEvent.name.toLowerCase();
        if (event == OnboardWebEventsNames.orderCompleted.toLowerCase()) {
          _analyticsService.logEvent(eventName: AnalyticsEvent.sendTxSuccess);

          _toTransactionSuccessView();
        }
      });
      await locator<WalletNavigationService>()
          .navigateToExchange(result!.webUrl, onExchangeClose: () {
        if (_onboardWebEvents != null) {
          locator<OngoingOrderService>().fetchOngoingOrders();
          _webEventsStream?.cancel();
          _navigationService
              .popUntil((routes) => routes.settings.name == Routes.homeView);
        }
      });
      _webEventsStream?.cancel();
    }
  }

  Future<void> getExternalAccountCurrency() async {
    final response = await _sendService.getExternalAccountCurrencies();
    await response.when(success: (success) async {
      fiatTransferAssetsList = success;
      final countryCode = await _userService.getUserTransactionCountryCode();
      final currency = country.Countries.values
          .firstWhereOrNull(
              (e) => e.alpha2.toLowerCase() == countryCode?.toLowerCase())
          ?.currencyCode;
      sourceFiatTransferAsset = fiatTransferAssetsList?.currencies
          .firstWhereOrNull(
              (e) => e.symbol.toLowerCase() == currency?.toLowerCase());
      notifyListeners();
      if (sourceFiatTransferAsset != null) {
        _getTargetAsset();
      }
    }, failure: (failure) {
      _toastManager.showErrorToast(text: failure.message);
    });
  }

  @override
  String? get fromAssetCode => sourceFiatTransferAsset?.symbol;

  bool fetchingFromAsset = false;

  @override
  void onViewModelReady() {
    super.onViewModelReady();
    fetchingFromAsset = true;
    notifyListeners();

    getExternalAccountCurrency().whenComplete(() {
      fetchingFromAsset = false;
      notifyListeners();
      if (fromAssetCode != null) {
        ///add symbol to any amount the user has entered while network fetch was happening
        if (fromTextEditingController.text.isNotEmpty) {
          final text = fromTextEditingController.text
              .removeSymbol(fromAssetCode ?? "")
              .trim();
          fromTextEditingController.text = "$fromAssetCode $text";
        }
      }
    });
  }

  @override
  String get amountToBeConverted {
    if (transferExchangeRate == null) {
      return "0";
    }
    return "$fromAssetCode $amountConverted";
  }

  @override
  String get formattedFee {
    if (transferExchangeRate == null) {
      return "0";
    }
    return "$fromAssetCode $processingFee";
  }

  @override
  bool get insufficientBalance => false;

  Widget? get fromAssetLogo {
    if (sourceFiatTransferAsset == null) {
      return null;
    }
    return getCurrencyCountryFlag(
          sourceFiatTransferAsset?.symbol,
          size: 27,
        ) ??
        CachedNetworkImage(
          imageUrl: sourceFiatTransferAsset?.logoUrl ?? "",
          height: 27,
          width: 27,
          fit: BoxFit.fitHeight,
          errorWidget: (_, __, ___) => const AssetErrorIconSvg(),
        );
  }

  @override
  void checkButtonEnabled() {
    hasValidInputs = insufficientBalance == false &&
        minimumFunding == false &&
        maximumFunding == false &&
        (fromAmountAsDouble ?? 0) > 0 &&
        transferExchangeRate != null;
    notifyListeners();
    setErrorMessage();
  }

  String errorDescription = '';

  String? inputFieldErrorMessage;

  @override
  void setErrorMessage() {
    if (minimumFunding) {
      errorDescription = S.current.withdrawalAmountTooLow;
      inputFieldErrorMessage =
          "${S.current.minimum} $fromAssetCode ${transferExchangeRate?.minAmount.currencyFormat(symbol: "") ?? ""}";
    } else if (maximumFunding) {
      errorDescription = S.current.withdrawalAmountTooHigh;
      inputFieldErrorMessage =
          "${S.current.maximum} $fromAssetCode ${transferExchangeRate?.maxAmount.currencyFormat(symbol: "") ?? ""}";
    } else if (insufficientBalance) {
      errorDescription = S.current.insufficientBalance;
      inputFieldErrorMessage = null;
    } else {
      errorDescription = '';
      inputFieldErrorMessage = null;
    }
    notifyListeners();
  }

  void _getTargetAsset() {
    final symbol = sourceFiatTransferAsset?.symbol;
    if (symbol == null) {
      return;
    }

    switch (sendDestinationEnum) {
      case SendDestinationEnum.crypto:
        targetCryptoTransferAsset = null;
        getTargetToken(symbol);
      case SendDestinationEnum.bankAccount:
        targetFiatTransferAsset = null;
        getTargetCurrency(symbol);
    }
  }

  void _toTransactionSuccessView() {
    _navigationService.clearTillFirstAndShow(
      Routes.transactionSuccessView,
      arguments: TransactionSuccessViewArguments(
          title: S.current.transactionSentTitle,
          subtitle: S.current.transactionSentSubtitle,
          onButtonTapped: () {
            _navigationService.back();
          }),
    );
  }

  void _sendMoney() {
    if (sendDestinationEnum == SendDestinationEnum.bankAccount) {
      if (targetFiatTransferAsset == null) {
        return;
      }
      _navigationService.navigateToSendToBankPreviewView(
          fiatTransferAsset: targetFiatTransferAsset!,
          targetAmount: targetAmount,
          onContinue: (paymentMethod) {
            _onCompleteSend(paymentMethod);
          });
    } else {
      _navigationService.navigateToSendWithCashToCryptoView(
        sourceAmount: fromAmountAsDouble!,
        sourceFiatTransferAsset: sourceFiatTransferAsset!,
        transferExchangeRate: transferExchangeRate!,
        transferTokenNetwork: targetTokenNetwork!,
        targetCryptoTransferAsset: targetCryptoTransferAsset!,
      );
    }
  }

  Future<void> _onCompleteSend(paymentMethod) async {
    EasyLoading.show();
    String? id;
    if (paymentMethod is PaymentMethodsSvcPaymentMethod) {
      id = paymentMethod.paymentMethodId;
    } else if (paymentMethod is FiatPaymentMethod) {
      id = paymentMethod.id;
    }

    if (id == null) {
      //shouldn't happen, handle edge case.
      _toastManager.showErrorToast(text: "Error getting payment method id");
      return;
    }

    try {
      final rateIsExpired = checkRateIsExpired();
      if (rateIsExpired == null || rateIsExpired == true) {
        await getTransferRate();
      }
      _analyticsService.logEvent(
          eventName: AnalyticsEvent.attemptSendTransaction);
      final response = await _sendService.initiateOnrampToPaymentMethod(
          paymentMethodOnrampRequest: PaymentMethodOnrampRequest((b) {
        b.paymentMethodId = id;
        b.sourceAmount = fromAmountAsDouble;
        b.destCurrency = targetFiatTransferAsset?.symbol;
        b.paymentMethodSource = targetFiatTransferAsset?.paymentMethodSource;
        b.rateReference = transferExchangeRate?.reference;
        b.sourceCurrency = fromAssetCode;
      }));

      response.when(success: (success) {
        _onOnramp(success);
      }, failure: (failure) {
        _analyticsService.logEvent(eventName: AnalyticsEvent.sendTxFailed);

        _toastManager.showErrorToast(text: failure.message);
      });
    } on Exception catch (e) {
      getLogger(toString()).e(e);
    }
    EasyLoading.dismiss();
  }
}
