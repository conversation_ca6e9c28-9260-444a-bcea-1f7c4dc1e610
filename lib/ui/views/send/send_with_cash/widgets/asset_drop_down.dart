import 'package:flutter/material.dart';
import 'package:onboard_wallet/gen/assets.gen.dart';
import 'package:onboard_wallet/haven/ui/widgets/onbw_shimmer.dart';
import 'package:onboard_wallet/theme_typography.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';
import 'package:onboard_wallet/ui/common/ui_helpers.dart';

class ChangeAssetDropDown extends StatelessWidget {
  final Widget? logo;
  final String? assetCode;
  final GestureTapCallback onTap;
  final bool loading;
  final bool showColoredDropDown;
  final bool showDropdownIcon;
  final bool disabled;

  const ChangeAssetDropDown({
    super.key,
    required this.assetCode,
    this.logo,
    required this.onTap,
    this.loading = false,
    this.showColoredDropDown = false,
    this.showDropdownIcon = true,
    this.disabled = false,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (loading == false && disabled == false) {
          onTap.call();
        }
      },
      child: Container(
        padding: const EdgeInsets.all(5),
        decoration: BoxDecoration(
            color: showColoredDropDown
                ? const Color(0xffEBECFF)
                : BlueGrey.blueGrey40,
            borderRadius: BorderRadius.circular(22),
            border: Border.all(
              color: BlueGrey.blueGrey50,
              width: 1,
            )),
        child: Row(
          children: [
            if (loading) ...[
              OnbwShimmer(
                height: 10,
                width: 30,
                padding: const EdgeInsets.all(1),
                borderRadius: BorderRadius.circular(4),
                shimmerBorderRadius: BorderRadius.circular(4),
              )
            ] else ...[
              if (logo != null)
                SizedBox.square(
                  dimension: 18,
                  child: logo!,
                ),
              const horizontalSpace(4),
              Text(
                assetCode ?? "",
                style: Theme.of(context).textTheme.body11Medium.copyWith(
                      color: showColoredDropDown ? kcBrandPurple : disabled ? Grey.grey400 : Grey.grey900,
                    ),
              ),
            ],
            if (showDropdownIcon) ...[
              const horizontalSpace(3),
              Assets.svg.chevronDown.svg(
                  colorFilter: ColorFilter.mode(
                      loading || disabled
                          ? Grey.grey400
                          : showColoredDropDown
                              ? kcBrandPurple
                              : Grey.grey900,
                      BlendMode.srcIn)),
              const horizontalSpace(1),
            ]
          ],
        ),
      ),
    );
  }
}
