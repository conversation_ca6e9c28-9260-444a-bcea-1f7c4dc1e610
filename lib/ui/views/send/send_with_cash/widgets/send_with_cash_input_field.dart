import 'package:flutter/material.dart';
import 'package:nestcoinco_onboard_api_gateway/nestcoinco_onboard_api_gateway.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/ui/views/send/data/send_destination_enum.dart';

import 'package:onboard_wallet/ui/views/send/send_with_cash/send_with_cash_viewmodel.dart';
import 'package:onboard_wallet/ui/views/send/widgets/send_input_field.dart';

import 'package:stacked/stacked.dart';

class SendWithCashInputField extends ViewModelWidget<SendWithCashViewModel> {
  const SendWithCashInputField({super.key});

  @override
  Widget build(BuildContext context, SendWithCashViewModel viewModel) {
    String? toAssetCode = viewModel.toAssetCode;
    if (toAssetCode == null) {
      if (viewModel.sendDestinationEnum == SendDestinationEnum.crypto) {
        toAssetCode = S.current.chooseCrypto;
      } else {
        toAssetCode = S.current.chooseCurrency;
      }
    }
    return SendInput<PERSON>ield(
      fromTextEditingController: viewModel.fromTextEditingController,
      toTextEditingController: viewModel.toTextEditingController,
      toInputFormatters: viewModel.toInputFormatters,
      fromInputFormatters: viewModel.fromInputFormatters,
      onFromInputChanged: viewModel.onFromInputChanged,
      onToInputChanged: viewModel.onToInputChanged,
      toAssetCode: toAssetCode,
      toAssetLogo: viewModel.toAssetLogo,
      fromAssetCode: viewModel.fromAssetCode ?? S.current.chooseCurrency,
      hasNoFee: viewModel.hasNoFee,
      amountToBeConverted: viewModel.amountToBeConverted,
      onChangeFromAsset: viewModel.changeFromAsset,
      onChangeToAsset: viewModel.changeToAsset,
      showFeeExplainer: viewModel.showFeeExplainer,
      formattedFee: viewModel.formattedFee,
      fetchingRate: viewModel.fetchingRate,
      formattedRate: viewModel.formattedRate,
      fromAssetLogo: viewModel.fromAssetLogo,
      fetchingToAsset: viewModel.fetchingTargetAsset,
      fetchingFromAsset: viewModel.fetchingFromAsset,
      errorMessage: viewModel.inputFieldErrorMessage,
      showColoredDropDown: viewModel.fetchingFromAsset == false &&
          viewModel.fromAssetCode == null,
      disabledToDropDown: viewModel.fromAssetCode == null,
      showColoredToDropDown: viewModel.fetchingTargetAsset == false &&
          viewModel.toAssetCode == null && viewModel.fromAssetCode != null,
    );
  }
}
