import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:onboard_wallet/extensions/extensions.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/theme_typography.dart';
import 'package:onboard_wallet/ui/common/ui_helpers.dart';
import 'package:stacked/stacked.dart';

import '../../../../gen/assets.gen.dart' show Assets;
import '../../../../models/card/local_card_account/local_card_brand.dart'
    show LocalCardBrand;
import '../../../common/app_colors.dart'
    show Black, Grey, Purple, kcBrandPurple;
import '../../cards/widgets/virtual_card_widget.dart' show BaseVirtualCard;
import '../../dashboard/main_dashboard/main_dashboard_viewmodel.dart';
import 'accounts_base_card_widget.dart' show AccountsBaseCardWidget;

class AccountsVirtualCardCard extends ViewModelWidget<MainDashboardViewModel> {
  const AccountsVirtualCardCard({super.key});

  @override
  Widget build(BuildContext context, MainDashboardViewModel viewModel) {
    double cardWidth = 55.77;
    double cardHeight = 28.27;
    return AccountsBaseCardWidget(
      title: S.current.cards,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16),
        child: viewModel.cardAccounts.isEmpty
            ? _CardSetupAccountTile(
                onTap: () => viewModel.toCardDashboard(
                  isAccountList: true,
                  isAccountSetup: true,
                ),
              )
            : SizedBox(
                height: 80,
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  children: [
                    ...viewModel.cardAccounts.mapIndexed((index, e) {
                      return InkWell(
                        onTap: () => viewModel.toCardDashboard(
                          isAccountList: true,
                          isAccountSetup: false,
                          account: e,
                        ),
                        child: Padding(
                          padding: const EdgeInsets.only(right: 30),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              TinyVirtualCard(
                                width: cardWidth,
                                height: cardHeight,
                                cardBrand: e.card?.cardBrand,
                                cardColor: e.getCardColor(),
                              ),
                              const verticalSpace(11),
                              Text(
                                e.card?.label ?? e.label ?? "",
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: Theme.of(context)
                                    .textTheme
                                    .body12Medium
                                    .copyWith(
                                      color: Grey.grey900,
                                    ),
                              ),
                              const verticalSpace(3),
                              if (e.isActive) ...[
                                Text(
                                  e.card?.balance?.currencyFormat(
                                          symbol: e.cardUsageTerms
                                                  ?.cardCurrencySymbol ??
                                              "") ??
                                      "",
                                  style: Theme.of(context)
                                      .textTheme
                                      .body12Regular
                                      .copyWith(
                                        color: Grey.grey600,
                                      ),
                                )
                              ] else ...[
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 6,
                                    vertical: 1,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Black.black25,
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                  child: Text(
                                    e.card?.status?.name.toTitleCase() ?? "",
                                    style: Theme.of(context)
                                        .textTheme
                                        .body11Regular
                                        .copyWith(
                                          fontSize: 10.fontSize,
                                          color: Grey.grey400,
                                        ),
                                  ),
                                )
                              ]
                            ],
                          ),
                        ),
                      );
                    }),
                    if (viewModel.showAddNewCard) ...[
                      InkWell(
                        onTap: viewModel.onNewCardTap,
                        child: Column(
                          children: [
                            Container(
                              height: cardHeight,
                              width: cardWidth,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 17.5,
                                vertical: 5,
                              ),
                              decoration: BoxDecoration(
                                color: Purple.purple50,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Assets.svg.plusThick.svg(),
                            ),
                            const verticalSpace(11),
                            Text(
                              S.current.newCard,
                              style: Theme.of(context)
                                  .textTheme
                                  .body12Medium
                                  .copyWith(
                                    color: kcBrandPurple,
                                  ),
                            ),
                          ],
                        ),
                      )
                    ]
                  ],
                ),
              ),
      ),
    );
  }
}



class TinyVirtualCard extends StatelessWidget {
  final Color cardColor;
  final double width;
  final double height;
  final LocalCardBrand? cardBrand;

  const TinyVirtualCard(
      {super.key,
      required this.cardColor,
      required this.cardBrand,
      required this.width,
      required this.height});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(0.9),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(2.6),
      ),
      child: BaseVirtualCard(
        width: width,
        height: height,
        borderRadius: 2.6,
        cardBoxDecoration: BoxDecoration(
          borderRadius: BorderRadius.circular(2.6),
          color: cardColor,
        ),
        body: Padding(
          padding: const EdgeInsets.all(4),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Align(
                alignment: Alignment.centerRight,
                child: Assets.svg.onboardLogomarkPull.svg(
                  height: 6,
                  width: 6,
                  colorFilter: const ColorFilter.mode(
                    Color(0xFF4A4570),
                    BlendMode.srcIn,
                  ),
                ),
              ),
              const Spacer(),
              if (cardBrand == LocalCardBrand.visa) ...[
                Assets.svg.visaIcon.svg(height: 3, width: 10),
              ] else if (cardBrand == LocalCardBrand.masterCard) ...[
                Assets.svg.masterCardIcon.svg(height: 3, width: 10),
              ]
            ],
          ),
        ),
      ),
    );
  }
}

class _CardSetupAccountTile extends StatelessWidget {
  final GestureTapCallback onTap;
  const _CardSetupAccountTile({
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.translucent,
      child: Row(
        children: [
          const TinyVirtualCard(
            cardColor: Color(0xFF530080),
            cardBrand: LocalCardBrand.visa,
            width: 33,
            height: 19,
          ),
          const horizontalSpace(9),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  S.current.virtualCard,
                  style: Theme.of(context).textTheme.body14Medium.copyWith(
                        color: Grey.grey800,
                      ),
                ),
                const verticalSpace(3),
                Text(
                  S.current.spendGloballyWithUsdCard,
                  style: Theme.of(context).textTheme.body11Regular.copyWith(
                        color: Grey.grey400,
                      ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 17,
              vertical: 6,
            ),
            decoration: BoxDecoration(
              color: Purple.purple50,
              borderRadius: BorderRadius.circular(22),
            ),
            child: Text(
              S.current.setup,
              style: Theme.of(context).textTheme.body12Regular.copyWith(
                    color: kcBrandPurple,
                  ),
            ),
          ),
        ],
      ),
    );
  }
}
