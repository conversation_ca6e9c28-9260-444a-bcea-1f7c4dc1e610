import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:onboard_wallet/app/index.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';
import 'package:onboard_wallet/ui/views/passcode/passcode_viewmodel.dart'
    show PasscodeType, PassCodeEntryPoint;

class LockViewModel extends BaseViewModel {
  final _navigationService = locator<NavigationService>();
  final _authenticationService = locator<AuthenticationService>();
  final _passcodeService = locator<PasscodeService>();
  final _biometricService = locator<BiometricService>();
  final _cloudBackupService = locator<CloudBackupService>();
  final userService = locator<UserService>();
  bool isPasscodeBackup = false;

  onModelReady() async {
    isPasscodeBackup = _passcodeService.hasLocalPinSetup();
    notifyListeners();
    biometricUnlock();
  }

  Future unlockWithPasscodePressed() async {
    _navigationService.navigateToPasscodeUnlockView();
  }

  Future biometricUnlock() async {
    bool? unlock = await _biometricService.unlock();
    if (unlock == true) {
      bool loginState = await _authenticationService.isLoggedIn();
      if (loginState) {
        ///user does not have the new pin system set up
        if (_passcodeService.hasLocalPinSetup() == false) {
          _onNoLocalPinSetUp();
        } else if (_passcodeService.hasOnlinePinSetup() == false) {
          _navigationService.navigateToPasscodeUnlockView();
        } else {
          _navigationService.clearStackAndShow(Routes.homeView);
        }
      } else {
        _navigationService.clearStackAndShow(Routes.authView);
      }
    }
  }

  Future<void> _onNoLocalPinSetUp() async {
    EasyLoading.show();
    final hasOnlinePin = await _passcodeService
        .checkPinExists()
        .whenComplete(() => EasyLoading.dismiss());

    hasOnlinePin.when(success: (success) {
      _onPinSetup(hasNoOnlinePin: success?.success == true);
    }, failure: (failure) {
      _onPinSetup(hasNoOnlinePin: false);
    });
  }

  Future<void> _onPinSetup({required bool hasNoOnlinePin}) async {
    _navigationService.navigateToPasscodeView(
      passcodeType:
          hasNoOnlinePin ? PasscodeType.verifyAccess : PasscodeType.create,
      passCodeEntryPoint: PassCodeEntryPoint.startUpView,
    );
  }

  logUserOut() async {
    EasyLoading.show();
    await _authenticationService.logout();
    await locator<SecureStorageService>().deleteAll();
    EasyLoading.dismiss();
    locator<DatabaseService>().clearDb();
    locator<PreferenceService>().clear();
    _navigationService.clearStackAndShow(Routes.authView);
  }

  logOut() => _checkLogoutNudgeIsRequired();

  Future<void> _checkLogoutNudgeIsRequired() async {
    if (_cloudBackupService.requiresBackUp) {
      _nudgeWalletBackup();
    } else {
      logUserOut();
    }
  }

  _nudgeWalletBackup() async {
    _cloudBackupService.setCallBackToCurrentRoute();
    await _navigationService.navigateToWalletSecurityView();
    if (_cloudBackupService.hasBackUp) {
      logUserOut();
    }
  }
}
