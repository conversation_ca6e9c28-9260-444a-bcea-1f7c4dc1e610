import 'package:flutter/cupertino.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:nestcoinco_onboard_api_gateway/nestcoinco_onboard_api_gateway.dart';
import 'package:onboard_wallet/app/app.locator.dart';
import 'package:onboard_wallet/app/app.logger.dart';
import 'package:onboard_wallet/app/app.router.dart';
import 'package:onboard_wallet/constants/action_target.dart';
import 'package:onboard_wallet/constants/analytic_event.dart';
import 'package:onboard_wallet/constants/string_constants.dart';
import 'package:onboard_wallet/enums/enums.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/manager/manager.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';

enum PasscodeType {
  create,
  confirm,
  createNewPasscode, //used for change passcode
  confirmNewPasscode, //used to confirm change passcode
  verifyAccess, //verifies the passcode on the server
  changePasscode, //used to verify and get the current user's passcode
}

enum PassCodeEntryPoint { securitySetting, secureWallet, startUpView }

class PasscodeViewModel extends BaseViewModel {
  final PasscodeType passcodeType;
  final String? initialPasscode;
  final String? currentPasscode;
  final String? resetPinToken;
  final _navigationService = locator<NavigationService>();
  final _analyticsService = locator<AnalyticsService>();
  final _preferenceService = locator<PreferenceService>();
  final _passcodeService = locator<PasscodeService>();
  final _toastManager = locator<ToastManager>();

  final WalletService _walletService = locator<WalletService>();
  String errorMessage = '';
  final TextEditingController controller = TextEditingController();
  final FocusNode focusNode = FocusNode();
  PassCodeEntryPoint? passCodeEntryPoint;
  final Function()? onConfirmPasscode;

  PasscodeViewModel({
    required this.passcodeType,
    this.initialPasscode,
    this.currentPasscode,
    this.passCodeEntryPoint,
    this.resetPinToken,
    this.onConfirmPasscode,
  });

  String get subCopy {
    switch (passcodeType) {
      case PasscodeType.create:
        return S.current.secureAccountWithPasscode;
      case PasscodeType.confirm:
        return S.current.confirmPasscode;
      case PasscodeType.createNewPasscode:
        return S.current.createANewPasscode;
      case PasscodeType.confirmNewPasscode:
        return S.current.confirmYourNewPasscode;
      case PasscodeType.verifyAccess:
        return S.current.confirmYourCurrentPasscode;
      case PasscodeType.changePasscode:
        return S.current.confirmYourCurrentPasscode;
    }
  }

  void onCodeChanged(String value) {}

  void onCodeComplete(String value) async {
    switch (passcodeType) {
      case PasscodeType.create:
      case PasscodeType.createNewPasscode:
        _logCreatePasscodeEvents();
        _handleCreateCompleted(value);
        break;
      case PasscodeType.confirm:
      case PasscodeType.confirmNewPasscode:
        _confirmPasscode(value);
        _logConfirmPasscodeEvents();
        break;
      case PasscodeType.verifyAccess:
        _verifyAccess(value);
        break;
      case PasscodeType.changePasscode:
        _initiateChangePin(value);
        break;
    }
  }

  Future _verifyAccess(String value) async {
    EasyLoading.show();

    final result = await _passcodeService
        .verifyAccountPin(pin: value)
        .whenComplete(() => EasyLoading.dismiss());

    result.when(success: (status) {
      if (status?.success == true) {
        _passcodeService.savePasscode(value);
        _passcodeService.markOnlinePinSetup();

        _handleConfirmPasscodeSuccessNav();
      } else {
        handleIncorrectPasscode(status?.message);
      }
    }, failure: (error) {
      handleIncorrectPasscode(S.current.incorrectPasscode);
      getLogger(toString()).d(error.message);
    });
  }

  Future _initiateChangePin(String value) async {
    final passcode = await _passcodeService.verifyPasscode(value);

    if (passcode == true) {
      _navigationService.back(result: value);
    } else {
      handleIncorrectPasscode(S.current.incorrectPasscode);
    }
  }

  handleIncorrectPasscode(String? message) {
    controller.clear();
    errorMessage = message ?? S.current.incorrectPasscode;
    setError(true);
    focusNode.requestFocus();
  }

  void _confirmPasscode(String value) async {
    if (initialPasscode != value) {
      controller.clear();
      errorMessage = S.current.passcodeDoesntMatch;
      setError(true);
      focusNode.requestFocus();
    } else {
      try {
        errorMessage = '';
        if (passcodeType == PasscodeType.confirm) {
          _completeSetupPasscode(value);
        } else {
          if (resetPinToken != null && resetPinToken!.isNotEmpty) {
            _completeResetPasscode(value, resetPinToken!);
            return;
          }
          _completeChangePasscode(value);
        }
      } catch (e) {
        locator<ToastManager>().showErrorToast(text: e.toString());
      }
    }
  }

  _completeSetupPasscode(String value) async {
    EasyLoading.show();

    final res = await _passcodeService
        .createAccountPin(pin: value)
        .whenComplete(() => EasyLoading.dismiss());

    res.when(success: (status) async {
      if (status != null && status.success) {
        locator<ToastManager>().showToast(text: S.current.passcodeCreated);
        await _passcodeService.savePasscode(value);
        await _passcodeService.markOnlinePinSetup();

        _handleConfirmPasscodeSuccessNav();
      } else {
        handleIncorrectPasscode(status?.message);
      }
    }, failure: (e) {
      locator<ToastManager>().showErrorToast(text: e.toString());
      getLogger(toString()).e(e.message);
    });
  }

  _completeChangePasscode(String value) async {
    if (currentPasscode?.isNotEmpty == true) {
      EasyLoading.show();

      final res = await _passcodeService
          .changeAccountPin(pin: currentPasscode!, newPin: value)
          .whenComplete(() => EasyLoading.dismiss());

      res.when(success: (status) async {
        if (status != null && status.success) {
          _toastManager.showToast(text: S.current.passcodeChanged);
          await _passcodeService.savePasscode(value);
          if (_walletService.isDefiWalletSetUp) {
            _walletService.backupWallet(
                backupType: BackupType.passcode, passcode: value);
          }
          _handleConfirmPasscodeSuccessNav();
        } else {
          handleIncorrectPasscode(status?.message);
        }
      }, failure: (e) {
        _toastManager.showErrorToast(text: e.toString());
        getLogger(toString()).e(e.message);
      });
    } else {
      _navigationService.popRepeated(3);
      locator<ToastManager>().showToast(text: S.current.errorOccurredTryAgain);
    }
  }

  _completeResetPasscode(String value, String resetToken) async {
    EasyLoading.show();

    final res = await _passcodeService
        .resetAccountPin(pin: value, sessionId: resetToken)
        .whenComplete(() {
      EasyLoading.dismiss();
    });

    res.when(success: (status) async {
      if (status != null && status.success) {
        _passcodeService.savePasscode(value);
        _passcodeService.markOnlinePinSetup();

        if (_walletService.isDefiWalletSetUp) {
          _walletService.backupWallet(
              backupType: BackupType.passcode, passcode: value);
        }

        _handleConfirmPasscodeSuccessNav();

        _toastManager.showToast(
            text: status.message ?? S.current.passcodeResetSuccessful);
      } else {
        handleIncorrectPasscode(
            status?.message ?? S.current.passcodeResetFailed);
      }
    }, failure: (e) {
      _toastManager.showErrorToast(text: e.toString());
      getLogger(toString()).e(e.message);
    });
  }

  void _handleCreateCompleted(String passcode) {
    _navigationService.navigateToPasscodeView(
      preventDuplicates: false,
      passcodeType: passcodeType == PasscodeType.create
          ? PasscodeType.confirm
          : PasscodeType.confirmNewPasscode,
      initialPasscode: passcode,
      passCodeEntryPoint: passCodeEntryPoint,
      currentPasscode: currentPasscode,
      resetPinToken: resetPinToken,
      onConfirmPasscode: onConfirmPasscode,
    );
  }

  Future<void> _handleConfirmPasscodeSuccessNav() async {
    if (onConfirmPasscode != null) {
      onConfirmPasscode?.call();
    } else {
      final isReturningUser = _preferenceService.getBool(key: kReturningUser);
      if (passCodeEntryPoint == PassCodeEntryPoint.startUpView) {
        _handelNavViaStartUpView();
      } else if (passCodeEntryPoint == PassCodeEntryPoint.securitySetting) {
        _navigationService
            .popUntil((route) => route.settings.name == Routes.securityView);
      } else if (isReturningUser == true) {
        _navigationService.clearStackAndShow(Routes.homeView);
      } else {
        if (locator<PasskeyService>().passkeysList.isEmpty) {
          _navigationService.navigateToCreatePasskeyView();
        } else {
          _navigationService.clearStackAndShow(Routes.homeView);
        }
      }
    }

    notifyListeners();
  }

  @override
  void dispose() {
    super.dispose();
    if (!disposed) {
      controller.dispose();
    }
  }

  void _logConfirmPasscodeEvents() {
    if (passCodeEntryPoint == PassCodeEntryPoint.securitySetting) {
      _analyticsService.logEvent(
          eventName: AnalyticsEvent.securitySettingsConfirmPassCode);
    } else if (passCodeEntryPoint == PassCodeEntryPoint.secureWallet) {
      _analyticsService.logEvent(eventName: AnalyticsEvent.confirmsPasscode);
    }
  }

  void _logCreatePasscodeEvents() {
    if (passCodeEntryPoint == PassCodeEntryPoint.secureWallet) {
      _analyticsService.logEvent(eventName: AnalyticsEvent.createsPasscode);
    }
  }

  Future<void> _handelNavViaStartUpView() async {
    bool loginState = await locator<AuthenticationService>().isLoggedIn();

    if (loginState) {
      await locator<WalletService>().initializeWallet(useBiometric: false);

      _navigationService.clearStackAndShow(Routes.homeView);
    } else {
      _navigationService.clearStackAndShow(Routes.authView);
    }
  }

  void resetPasscodePressed() async {
    clearErrors();

    final authorizationService = locator<AuthorizationService>();
    final currentUser = locator<DatabaseService>().getCurrentUser();

    final authorizationSessionRequest = AuthorizationSessionRequest((b) => b
      ..actionTarget = ActionTarget.authorizations
      ..action = AuthorizationSessionAction.RESET_CREDENTIALS
      ..details = AuthorizationSessionData((a) => a
        ..credentialType = AuthenticationMethod.ACCOUNT_PIN
        ..accountId = currentUser?.userId).toBuilder());

    final response = await authorizationService.startAuthFlow(
        authorizationSessionRequest: authorizationSessionRequest);

    response.when(success: (result) {
      if (result.status == AuthSessionResult.completed) {
        _navigationService.navigateToPasscodeView(
            preventDuplicates: false,
            passcodeType: PasscodeType.createNewPasscode,
            resetPinToken: result.sessionId,
            passCodeEntryPoint: PassCodeEntryPoint.startUpView);
      } else {
        _toastManager.showErrorToast(text: S.current.verificationFailed);
      }
    }, failure: (error) {
      getLogger(toString()).e(error.message);
      _toastManager.showErrorToast(text: error.message);
    });
  }
}
