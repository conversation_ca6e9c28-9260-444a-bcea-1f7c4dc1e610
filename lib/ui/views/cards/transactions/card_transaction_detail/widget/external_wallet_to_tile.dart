import 'package:enhance_expansion_panel/enhance_expansion_panel.dart';
import 'package:flutter/material.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/haven/extensions/extensions.dart';
import 'package:onboard_wallet/theme_typography.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';
import 'package:onboard_wallet/ui/common/ui_helpers.dart';
import 'package:onboard_wallet/ui/views/direct_account/direct_account_transfer_preview/widget/da_rate_tile.dart';
import 'package:onboard_wallet/ui/widgets/onbw_expansion_icon.dart';

import '../../../../../../gen/assets.gen.dart';

class ExternalWalletToTile extends StatefulWidget {
  final String networkName;
  final Widget networkIcon;
  final String? txHash;
  final String cryptoAddress;

  const ExternalWalletToTile({
    super.key,
    required this.networkName,
    required this.networkIcon,
    required this.txHash,
    required this.cryptoAddress,
  });

  @override
  State<ExternalWalletToTile> createState() => _ExternalWalletToTileState();
}

class _ExternalWalletToTileState extends State<ExternalWalletToTile> {
  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return EnhanceExpansionPanelList(
      elevation: 0,
      expandedHeaderPadding: EdgeInsets.zero,
      expansionCallback: (_, expanded) {
        setState(() {
          isExpanded = !expanded;
        });
      },
      children: [
        EnhanceExpansionPanel(
          backgroundColor: Colors.white,
          arrowPadding: EdgeInsets.zero,
          canTapOnHeader: true,
          arrowColor: kcBrandBlue,
          arrowPosition: EnhanceExpansionPanelArrowPosition.none,
          headerBuilder: (BuildContext context, bool isExpanded) {
            return Padding(
              padding: const EdgeInsets.only(
                  bottom: 16, top: 8, left: 16, right: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    S.current.to,
                    style: textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: kcBaseBlack,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    widget.cryptoAddress.middleOverflow(),
                    style: textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w400,
                      color: Grey.grey500,
                    ),
                  ),
                  const horizontalSpace(8),
                  OnbwExpansionIcon(isExpanded: isExpanded),
                ],
              ),
            );
          },
          body: Container(
            color: Grey.grey50,
            margin: const EdgeInsets.symmetric(horizontal: 1),
            child: Padding(
                padding: const EdgeInsets.only(right: 20, left: 14),
                child: Column(
                  children: [
                    const verticalSpace(15),
                    DaPreviewSubTile(
                      icon: Assets.svg.networkChainIcon.svg(),
                      padding: EdgeInsets.zero,
                      title: S.current.network,
                      titleTextColor: Grey.grey800,
                      subtitle: Row(
                        children: [
                          SizedBox.square(
                            dimension: 14,
                            child: widget.networkIcon,
                          ),
                          const horizontalSpace(6),
                          Text(
                            widget.networkName,
                            style: Theme.of(context)
                                .textTheme
                                .body14Regular
                                .copyWith(
                                  color: Grey.grey500,
                                ),
                          ),
                        ],
                      ),
                    ),
                    const verticalSpace(16),
                    if (widget.txHash != null) ...[
                      DaPreviewSubTile(
                        icon: Assets.svg.hashSign.svg(),
                        padding: EdgeInsets.zero,
                        title: S.current.hash,
                        titleTextColor: Grey.grey800,
                        subtitle: Row(
                          children: [
                            Text(
                              widget.txHash!.middleOverflow(),
                              style: Theme.of(context)
                                  .textTheme
                                  .body14Regular
                                  .copyWith(
                                    color: Grey.grey500,
                                  ),
                            ),
                            const horizontalSpace(4),
                            Assets.svg.arrowUpRight.svg(
                              colorFilter: const ColorFilter.mode(
                                Grey.grey400,
                                BlendMode.srcIn,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const verticalSpace(16),
                    ]
                  ],
                )),
          ),
          isExpanded: isExpanded,
        )
      ],
    );
  }
}
