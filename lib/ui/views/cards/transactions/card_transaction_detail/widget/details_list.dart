import 'package:enhance_expansion_panel/enhance_expansion_panel.dart';
import 'package:flutter/material.dart';
import 'package:onboard_wallet/extensions/extensions.dart';
import 'package:onboard_wallet/gen/assets.gen.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/theme_typography.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';
import 'package:onboard_wallet/ui/common/ui_helpers.dart';
import 'package:onboard_wallet/ui/views/cards/fund/card_fund/widgets/fund_break_down_items.dart';
import 'package:onboard_wallet/ui/widgets/cards/card_surface.dart';
import 'package:onboard_wallet/ui/widgets/onbw_expansion_icon.dart';
import 'package:stacked/stacked.dart';

import '../card_transaction_detail_viewmodel.dart';

class DetailsList extends ViewModelWidget<CardTransactionDetailViewModel> {
  const DetailsList({super.key});

  @override
  Widget build(BuildContext context, CardTransactionDetailViewModel viewModel) {
    final textTheme = Theme.of(context).textTheme;

    final showExpansionPanel =
        viewModel.showBreakdown && viewModel.totalAndTokenSymbol != null;
    return CardSurface(
      padding: showExpansionPanel
          ? const EdgeInsets.fromLTRB(0, 12, 0, 8)
          : const EdgeInsets.fromLTRB(0, 8, 0, 8),
      child: Column(children: [
        if (showExpansionPanel) ...[
          EnhanceExpansionPanelList(
            elevation: 0,
            expandedHeaderPadding: EdgeInsets.zero,
            expansionCallback: (_, expanded) {
              viewModel.isExpanded = !expanded;
            },
            children: [
              EnhanceExpansionPanel(
                backgroundColor: Colors.white,
                arrowPadding: EdgeInsets.zero,
                canTapOnHeader: true,
                arrowColor: kcBrandBlue,
                arrowPosition: EnhanceExpansionPanelArrowPosition.none,
                headerBuilder: (BuildContext context, bool isExpanded) {
                  return Padding(
                    padding:
                        const EdgeInsets.only(left: 16.0, bottom: 16, top: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          viewModel.title,
                          style: textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w500,
                            color: kcBaseBlack,
                          ),
                        ),
                        const Spacer(),
                        Text(
                          viewModel.totalAndTokenSymbol ?? '',
                          style: textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w400,
                            color: Grey.grey500,
                          ),
                        ),
                        const horizontalSpace(8),
                        OnbwExpansionIcon(isExpanded: isExpanded),
                        const horizontalSpace(16),
                      ],
                    ),
                  );
                },
                body: Container(
                  color: Grey.grey50,
                  margin: const EdgeInsets.symmetric(horizontal: 1),
                  child: Padding(
                    padding: const EdgeInsets.only(right: 20, left: 14),
                    child: FundBreakdownItems(
                      amountUserGets: viewModel.fiatTotal ?? "",
                      formattedRate: viewModel.formattedRate,
                      formattedProcessingFee: viewModel.formattedProcessingFee,
                      feeIsZero: viewModel.hasNoFee,
                      cardCreationFee: viewModel.formattedCardCreationFee,
                      totalBeingConverted: viewModel.totalConverted,
                      subtitle: viewModel.amountTitle,
                      isTransactionView: true,
                    ),
                  ),
                ),
                isExpanded: viewModel.isExpanded,
              )
            ],
          ),
        ],
        Column(
          children: [
            if (viewModel.fromWidget() != null)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: CardTransactionBreakdownTile(
                  leading: S.current.from,
                  trailing: viewModel.fromWidget()!,
                ),
              ),
            if (viewModel.toWidget != null) ...[
              viewModel.toWidget!,
            ],
            if (viewModel.showPaymentOrDeliveryMethod) ...[
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: _PaymentMethod(),
              ),
            ],
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: CardTransactionIdTile(
                txHash: viewModel.transactionId(),
                onTap: viewModel.copyTxId,
              ),
            ),
            if (viewModel.merchantCountryCode != null) ...[
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: CardTransactionBreakdownTile(
                  leading: S.current.country,
                  trailing: Text(
                    '${viewModel.merchantCountryCode}  ${viewModel.merchantCountryFlag}',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w400,
                          color: Grey.grey500,
                        ),
                  ),
                  onTap: viewModel.showCountryDropdown,
                ),
              ),
            ],
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: _CardDateTile(
                date: viewModel.dateAndTime(),
              ),
            ),
          ],
        ),
      ]),
    );
  }
}

class _PaymentMethod extends ViewModelWidget<CardTransactionDetailViewModel> {
  const _PaymentMethod();

  @override
  Widget build(BuildContext context, CardTransactionDetailViewModel viewModel) {
    return InkWell(
      onTap: () {
        if (viewModel.showPaymentMethodArrow) {
          viewModel.onCashTransferPressed.call();
        }
      },
      child: CardTransactionBreakdownTile(
        leading: viewModel.isFundingTransaction
            ? S.current.paymentMethod
            : S.current.deliveryMethod,
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Flexible(
              child: Text(
                viewModel.paymentMethodName,
                style: Theme.of(context).textTheme.body14Regular.copyWith(
                      color: const Color(0xff667085),
                    ),
                textAlign: TextAlign.right,
                maxLines: 1,
              ),
            ),
            const horizontalSpace(4),
            if (viewModel.showPaymentMethodArrow)
              GestureDetector(
                onTap: viewModel.onCashTransferPressed,
                child: SizedBox.square(
                  dimension: 20,
                  child: Assets.svg.arrowUpRight.svg(
                    color: kcHyperLinkBlue,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class CardTransactionIdTile extends StatelessWidget {
  const CardTransactionIdTile({super.key, required this.txHash, this.onTap});

  final String txHash;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return CardTransactionBreakdownTile(
      leading: S.current.transactionId,
      trailing: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: onTap,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Flexible(
              child: Text(
                txHash.middleOverflow(prefixCharacter: 7, suffixCharacter: 3),
                style: Theme.of(context).textTheme.body14Regular.copyWith(
                      color: kcHyperLinkBlue,
                      overflow: TextOverflow.ellipsis,
                    ),
                maxLines: 1,
              ),
            ),
            const horizontalSpace(4),
            SizedBox.square(
              dimension: 20,
              child: Assets.svg.copyFilled.svg(
                color: kcHyperLinkBlue,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _CardDateTile extends StatelessWidget {
  const _CardDateTile({this.date});

  final String? date;

  @override
  Widget build(BuildContext context) {
    return CardTransactionBreakdownTile(
      leading: S.current.date,
      trailing: Text(
        date ?? "",
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w400,
              color: Grey.grey500,
            ),
      ),
    );
  }
}

class CardTransactionBreakdownTile extends StatelessWidget {
  final String leading;
  final Widget trailing;
  final GestureTapCallback? onTap;

  const CardTransactionBreakdownTile({
    super.key,
    required this.leading,
    required this.trailing,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        child: Row(
          children: [
            Text(
              leading,
              style: Theme.of(context).textTheme.body14Medium.copyWith(
                    color: Colors.black,
                  ),
            ),
            const horizontalSpace(12),
            Expanded(
              child: Align(
                alignment: Alignment.centerRight,
                child: trailing,
              ),
            )
          ],
        ),
      ),
    );
  }
}
