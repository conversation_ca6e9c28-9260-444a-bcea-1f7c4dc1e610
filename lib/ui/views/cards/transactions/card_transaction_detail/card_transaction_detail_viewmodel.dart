import 'package:cached_network_image/cached_network_image.dart';
import 'package:collection/collection.dart';
import 'package:country/country.dart' as country_package;
import 'package:currency_code_to_currency_symbol/currency_code_to_currency_symbol.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:onboard_wallet/app/index.dart';
import 'package:onboard_wallet/enums/card_fund_options_enum.dart';
import 'package:onboard_wallet/extensions/date_time.dart';
import 'package:onboard_wallet/extensions/extensions.dart';
import 'package:onboard_wallet/gen/assets.gen.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/manager/manager.dart';
import 'package:onboard_wallet/models/card/local_card_account/index.dart';
import 'package:onboard_wallet/models/token/wallet_type.dart';
import 'package:onboard_wallet/services/card_subscription_service.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';
import 'package:onboard_wallet/ui/common/ui_helpers.dart';
import 'package:onboard_wallet/ui/views/cards/transactions/card_transaction_detail/widget/card_country_banner.dart';
import 'package:onboard_wallet/ui/views/cards/transactions/card_transaction_detail/widget/details_list.dart';
import 'package:onboard_wallet/ui/views/cards/transactions/card_transaction_detail/widget/external_wallet_to_tile.dart';
import 'package:onboard_wallet/ui/views/cards/transactions/card_transaction_detail/widget/fx_fee_explainer.dart';
import 'package:onboard_wallet/ui/widgets/asset_error_icon.dart';
import 'package:onboard_wallet/ui/widgets/bottomsheet/bottomsheet.dart';
import 'package:onboard_wallet/ui/widgets/cards/txn_card.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';

import '../../../../../models/card/local_card_transaction_config/local_card_transaction_config.dart';
import '../../../../../models/models.dart';

class CardTransactionDetailViewModel extends BaseViewModel {
  final _cardService = locator<CardService>();
  final _navigationService = locator<NavigationService>();
  final _cardSubscriptionService = locator<CardSubscriptionService>();
  LocalCardTransaction localTransaction;
  final LocalCardAccount cardAccount;

  CardTransactionDetailViewModel(this.localTransaction, this.cardAccount);

  LocalTransactionStatus? get localTransactionStatus => localTransaction.status;

  bool _isExpanded = false;

  bool get isExpanded => _isExpanded;

  String? get totalAndTokenSymbol {
    if (isFundingTransaction) {
      num? amount = localTransaction.fundingTransaction?.assetAmount;
      if (amount == null) return null;
      if (isCashTransferFunding || isOnboardFast) {
        return '$assetCode ${amount.currencyFormat(symbol: '', decimalDigits: 4).removeTrailingZeroesWithCount(2)}';
      } else {
        return '${amount.currencyFormat(symbol: '', decimalDigits: 4).removeTrailingZeroesWithCount(2)} ${assetCode ?? ''}';
      }
    } else if (isWithdrawalTransaction) {
      var valueToDouble = localTransaction.assetAmount?.amount?.toDouble();
      return valueToDouble?.currencyFormat(symbol: cardCurrencySymbol);
    }
    return null;
  }

  String get tokenSymbol => assetCode ?? "";

  ///This handles case where the card funding has being detected onChain but
  ///third party provider is yet to complete processing the transaction.
  bool get isFundingInProgress {
    final isNotExternalWallet =
        localTransaction.fundingTransaction?.txChannel?.optionsEnum !=
            CardTransactionOptionsEnum.externalWallet;
    return localTransactionStatus == LocalTransactionStatus.pending &&
        localTransaction.cardTransactionType ==
            LocalCardTransactionType.funding &&
        isNotExternalWallet &&
        transactionHash() != null;
  }

  bool get showBreakdown {
    return isFundingTransaction || isWithdrawalTransaction;
  }

  bool get isFundingTransaction {
    final transactionType = localTransaction.cardTransactionType;
    return transactionType == LocalCardTransactionType.creation ||
        transactionType == LocalCardTransactionType.funding;
  }

  bool get isFxFeeCharge {
    final transactionType = localTransaction.cardTransactionType;
    return transactionType == LocalCardTransactionType.charge &&
        localTransaction.description
                ?.toLowerCase()
                .removeSpaces()
                .contains('fxfee') ==
            true;
  }

  bool get _hasCrossBorderFee {
    if (merchantCountryCode == null) return false;
    return merchantCountryCode?.toLowerCase() != cardCountryCode.toLowerCase();
  }

  bool get showCrossBorderFeeBanner {
    return isFxFeeCharge == false &&
        _hasCrossBorderFee &&
        !isBusy &&
        localTransaction.cardTransactionType ==
            LocalCardTransactionType.purchase;
  }

  String? get merchantCountryCode {
    final metaData = localTransaction.metadata;
    if (metaData == null) return null;
    String? merchantCountry = metaData['merchantCountry'];
    return merchantCountry;
  }

  String get merchantCountryFlag {
    return merchantCountry?.flagEmoji ?? '';
  }

  country_package.Country? get merchantCountry {
    return country_package.Countries.values.firstWhereOrNull((element) =>
        element.alpha2.toLowerCase() == merchantCountryCode?.toLowerCase());
  }

  String get cardCountryCode {
    String? currency = cardAccount.cardCurrency ??
        cardAccount.cardUsageTerms?.cardCurrency?.toLowerCase();
    if (currency == null) return "";
    if (currency.toLowerCase() == "gbp") {
      return "uk";
    } else if (currency.toLowerCase() == "eur") {
      return "eu";
    } else if (currency.toLowerCase() == "usd") {
      return "us";
    } else {
      final country = country_package.Countries.values.firstWhereOrNull(
          (element) =>
              element.currencyCode.toLowerCase() == currency.toLowerCase());
      return country?.alpha2 ?? "";
    }
  }

  String? get txCurrencySymbol {
    if (txCurrency != null) {
      final symbol = getCurrencySymbol(txCurrency!);
      return symbol.isEmpty ? null : symbol;
    }
    return null;
  }

  String get merchantCountryName {
    return merchantCountry?.isoShortName ?? "";
  }

  bool get hasNonCardCurrencyFee {
    if (txCurrency == null) return false;
    return txCurrency?.toLowerCase() != cardAccount.cardCurrency?.toLowerCase();
  }

  String? get txCurrency {
    final metaData = localTransaction.metadata;
    if (metaData == null) return null;
    String? txCurrency = metaData['txCurrency'];
    return txCurrency;
  }

  String? get txAmount {
    final metaData = localTransaction.metadata;
    if (metaData == null) return null;
    String? txAmount = metaData['txAmount'];
    return txAmount;
  }

  bool get showNonUSDFeeBanner {
    return isFxFeeCharge == false &&
        (hasNonCardCurrencyFee || _hasCrossBorderFee) &&
        !isBusy &&
        localTransaction.cardTransactionType ==
            LocalCardTransactionType.purchase;
  }

  bool get isWithdrawalTransaction =>
      localTransaction.cardTransactionType ==
      LocalCardTransactionType.withdrawal;

  bool get isCashTransferFunding {
    return localTransaction.fundingTransaction?.txChannel?.optionsEnum ==
        CardTransactionOptionsEnum.cashTransfer;
  }

  String get title {
    if (isFundingTransaction) {
      return S.current.youPaid;
    } else {
      return S.current.youWithdrew;
    }
  }

  String get amountTitle {
    if (isFundingTransaction) {
      return S.current.cardGot;
    }
    return S.current.youGot;
  }

  bool get isOnboardFast {
    return localTransaction.withdrawalTransaction?.txChannel?.optionsEnum ==
        CardTransactionOptionsEnum.onboardFast;
  }

  String? get fiatTotal {
    if (isFundingTransaction) {
      //the amount the card got
      num? fiatAmount = localTransaction.fundingTransaction?.amount;
      if (fiatAmount == null) {
        return null;
      }
      return "${fiatAmount.currencyFormat(symbol: "")} $symbol";
    } else if (isWithdrawalTransaction) {
      //the amount the user got
      num? amount = localTransaction.withdrawalTransaction?.assetAmount;
      if (amount == null) return null;
      if (isCashTransferFunding || isOnboardFast) {
        return '$assetCode ${amount.currencyFormat(symbol: '', decimalDigits: 4).removeTrailingZeroesWithCount(2)}';
      } else {
        return '${amount.currencyFormat(symbol: '', decimalDigits: 4).removeTrailingZeroesWithCount(2)} ${assetCode ?? ''}';
      }
    }
    return null;
  }

  String get cardCurrencySymbol =>
      cardAccount.cardUsageTerms?.cardCurrencySymbol ?? '\$';

  num? get conversionRate {
    if (isFundingTransaction) {
      return localTransaction.fundingTransaction?.rate;
    } else {
      return localTransaction.withdrawalTransaction?.rate;
    }
  }

  String get formattedRate {
    if (conversionRate == null) return "";
    return "${conversionRate!.currencyFormat(symbol: "", decimalDigits: 4).removeTrailingZero()} $tokenSymbol";
  }

  bool get hasNoFee => processingFee == 0;

  String get formattedProcessingFee {
    return processingFee.currencyFormat(symbol: cardCurrencySymbol);
  }

  String? get assetCode {
    if (isCashTransferFunding) {
      return localTransaction.fundingTransaction?.assetCode;
    } else if (isOnboardFast) {
      return localTransaction.withdrawalTransaction?.assetCode;
    } else if (isFundingTransaction || isWithdrawalTransaction) {
      final box = isFundingTransaction
          ? _cardService.cardFundingConfigBox
          : _cardService.cardWithdrawalConfigBox;
      LocalCardTransactionConfig? config =
          box.isEmpty ? null : box.values.first;
      String? assetCode = isFundingTransaction
          ? localTransaction.fundingTransaction?.assetCode
          : localTransaction.withdrawalTransaction?.assetCode;
      final configTokens =
          (config?.localTokens ?? []) + (config?.externalNetworkTokens ?? []);
      final token = configTokens
          .firstWhereOrNull((element) => element.tokenCode == assetCode);
      return token?.symbol;
    }
    return null;
  }

  ///amount for a card fund or withdrawal.
  num? get transactionAmount => localTransaction.assetAmount?.amount;

  num get processingFee =>
      (isFundingTransaction
          ? localTransaction.fundingTransaction?.processingFee
          : localTransaction.withdrawalTransaction?.processingFee) ??
      0;

  num? get cardCreationFee {
    String? fee = localTransaction.metadata?['creationFee'];
    num? feeAsNum = num.tryParse(fee ?? '');
    return feeAsNum;
  }

  String? get tileAmount {
    if (txAmount == null || txCurrency == null) {
      var valueToDouble = localTransaction.assetAmount?.amount?.toDouble();
      return '${valueToDouble?.currencyFormat(symbol: "")} $symbol';
    } else {
      return '$txCurrency $txAmount';
    }
  }

  String get formattedNonUSDFee {
    return "${txCurrencySymbol ?? txCurrency}${txAmount ?? ""}";
  }

  bool get showMarkAsSubscriptionBanner {
    return _cardSubscriptionService.subscriptionRemindersList.firstWhereOrNull(
                (element) => element.transactionId == localTransaction.id) ==
            null &&
        localTransaction.cardTransactionType ==
            LocalCardTransactionType.purchase;
  }

  bool get showPaymentOrDeliveryMethod =>
      isFundingTransaction || isWithdrawalTransaction;

  String? get totalConverted {
    if (processingFee == 0) {
      return null;
    } else {
      final valueToDouble = isWithdrawalTransaction
          ? localTransaction.withdrawalTransaction?.assetAmount
          : localTransaction.fundingTransaction?.amount;
      if (valueToDouble == null) return null;
      if (isCashTransferFunding || isOnboardFast) {
        return '$assetCode ${valueToDouble.currencyFormat(symbol: '', decimalDigits: 4).removeTrailingZeroesWithCount(2)}';
      } else {
        return '${valueToDouble.currencyFormat(symbol: '', decimalDigits: 4).removeTrailingZeroesWithCount(2)} ${assetCode ?? ''}';
      }
    }
  }

  String? get formattedCardCreationFee =>
      cardCreationFee?.currencyFormat(symbol: cardCurrencySymbol);

  set isExpanded(bool isExpanded) {
    _isExpanded = isExpanded;
    notifyListeners();
  }

  String titleText() {
    return localTransaction.description ??
        localTransaction.cardTransactionType?.formatName() ??
        "";
  }

  bool? get isTransferIn => localTransaction.isTransferIn;

  String get iconPath => localTransaction.iconPath;

  onReady() {
    runBusyFuture(_fetchCardDetail());
  }

  _fetchCardDetail() async {
    final accountId = cardAccount.id;
    final cardTransactionId = localTransaction.id;
    if (accountId == null) return;
    final response = await _cardService.getCardTransactionById(
      accountId: accountId,
      cardTransactionId: cardTransactionId,
    );
    if (response != null) {
      localTransaction = response;
      notifyListeners();
    }
  }

  String get paymentMethodName {
    final channel = isWithdrawalTransaction
        ? localTransaction.withdrawalTransaction?.txChannel
        : localTransaction.fundingTransaction?.txChannel;

    if (channel == null) {
      return "";
    } else if (channel.optionsEnum == CardTransactionOptionsEnum.cashTransfer ||
        channel.optionsEnum == CardTransactionOptionsEnum.onboardFast) {
      return S.current.localCurrency.toCapitalized();
    } else if (channel.optionsEnum ==
            CardTransactionOptionsEnum.externalWallet ||
        channel.optionsEnum == CardTransactionOptionsEnum.onboardWallet) {
      return S.current.crypto;
    } else if (channel.optionsEnum ==
        CardTransactionOptionsEnum.virtualAccount) {
      return S.current.virtualAccount;
    }
    return "";
  }

  bool get showPaymentMethodArrow {
    return redirectUrl() != null;
  }

  String? transactionHash() {
    final metadata = localTransaction.metadata;
    String? txHash = metadata?["txHash"];
    txHash ??= localTransaction.withdrawalTransaction?.txHash ??
        localTransaction.fundingTransaction?.txHash;
    return txHash;
  }

  String transactionId() {
    return localTransaction.id;
  }

  String? redirectUrl() {
    final channel = isWithdrawalTransaction
        ? localTransaction.withdrawalTransaction?.txChannel
        : localTransaction.fundingTransaction?.txChannel;
    if (channel == null) {
      return "";
    } else if (channel.optionsEnum == CardTransactionOptionsEnum.cashTransfer ||
        channel.optionsEnum == CardTransactionOptionsEnum.onboardFast) {
      final metadata = localTransaction.metadata;
      if (metadata == null) return null;
      String? redirectUrl = metadata["redirectUrl"];
      return redirectUrl;
    } else if (channel.optionsEnum ==
            CardTransactionOptionsEnum.externalWallet ||
        channel.optionsEnum == CardTransactionOptionsEnum.onboardWallet) {
      final transaction =
          localTransaction.withdrawalTransaction?.recipientAddress ??
              localTransaction.fundingTransaction?.fundingAddress;
      if (transactionHash() != null) {
        String? redirectUrl =
            '${transaction?.network?.explorer}/${transactionHash()}';
        return redirectUrl;
      }
    }
    return null;
  }

  String? amount() {
    var valueToDouble = localTransaction.assetAmount?.amount?.toDouble();
    if (valueToDouble == null) return "";
    String sign = "";
    if (isTransferIn != null) {
      sign = isTransferIn! ? "+" : "-";
    }
    return "$sign${valueToDouble.currencyFormat(symbol: '')} $symbol";
  }

  String? feeValue() {
    var valueToDouble = localTransaction.feeAmount?.toDouble();
    if (valueToDouble == null) return null;
    return "${valueToDouble.currencyFormat(symbol: '').removeTrailingZero()} $symbol";
  }

  String get symbol => localTransaction.assetAmount?.asset ?? "";

  String? date() {
    var formatter = DateFormat("dd/MM/yyyy");
    return formatter.format(localTransaction.createdAtOrNow);
  }

  String? dateAndTime() {
    final dateTime = localTransaction.createdAtOrNow;
    if (dateTime.isThisYear) {
      var formatter = DateFormat("MMM dd, hh.mm aaa");
      return formatter.format(dateTime);
    } else {
      var formatter = DateFormat("MMM dd yyyy, hh.mm aaa");
      return formatter.format(dateTime);
    }
  }

  Color getStatusColor() {
    if (isTransferIn == true) {
      return SuccessColor.success500;
    } else if (isTransferIn == false) {
      return ErrorColor.error500;
    } else {
      return const Color(0xff98A2B3);
    }
  }

  Color getStatusTextColor() {
    if (localTransaction.status == null) {
      return const Color(0xff98A2B3);
    }
    switch (localTransaction.status!) {
      case LocalTransactionStatus.successful:
      case LocalTransactionStatus.completed:
        return SuccessColor.success500;
      case LocalTransactionStatus.failed:
      case LocalTransactionStatus.canceled:
        return ErrorColor.error500;
      case LocalTransactionStatus.pending:
      case LocalTransactionStatus.inProgress:
        return WarningColor.warning500;
    }
  }

  Color getStatusSecondaryColor() {
    if (localTransaction.status == null) {
      return const Color(0xff98A2B3);
    }
    switch (localTransaction.status!) {
      case LocalTransactionStatus.successful:
      case LocalTransactionStatus.completed:
        return SuccessColor.success50;
      case LocalTransactionStatus.failed:
      case LocalTransactionStatus.canceled:
        return ErrorColor.error50;
      case LocalTransactionStatus.pending:
      case LocalTransactionStatus.inProgress:
        return WarningColor.warning50;
    }
  }

  Widget? fromWidget() {
    if (isTransferIn == false) {
      return OnboardWalletTypeTagWidget(
        name: cardAccount.label ?? cardAccount.card?.label ?? "",
        icon: WalletType.virtualCard.iconSvg(),
      );
    }
    return null;
  }

  // TODO: handle case that is not withdrawal or top up
  Widget? get toWidget {
    final cardTransactionType = localTransaction.cardTransactionType;
    if (cardTransactionType == LocalCardTransactionType.withdrawal) {
      LocalWithdrawalTransaction? withdrawalTransaction =
          localTransaction.withdrawalTransaction;

      var txChannel = withdrawalTransaction?.txChannel;
      if (txChannel?.channelId == "spot-wallet") {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: _CardToTile(
            trailing: OnboardWalletTypeTagWidget(
              name: WalletType.spot.name,
              icon: WalletType.spot.iconSvg(),
            ),
          ),
        );
      } else if (txChannel?.channelId == "external-wallet") {
        return ExternalWalletToTile(
          cryptoAddress: withdrawalTransaction?.recipientAddress?.address ?? "",
          networkIcon: CachedNetworkImage(
            imageUrl:
                withdrawalTransaction?.recipientAddress?.network?.logo ?? "",
            errorWidget: (_, __, ___) => const AssetErrorIconSvg(),
          ),
          networkName:
              withdrawalTransaction?.recipientAddress?.network?.name ?? "",
          txHash: transactionHash(),
        );
      } else if (txChannel?.channelId == "onb-fast" ||
          txChannel?.channelId == "onb-exchange") {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: _CardToTile(
            trailing: Text(
                withdrawalTransaction?.paymentMethod?.bank?.bankName ?? ""),
          ),
        );
      }
    } else if (cardTransactionType == LocalCardTransactionType.purchase) {
      // TODO: handle purchase
      return null;
    } else if (isTransferIn == true) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: _CardToTile(
          trailing: OnboardWalletTypeTagWidget(
            name: cardAccount.label ?? cardAccount.card?.label ?? "",
            icon: Assets.svg.virtualCardIcon.svg(),
          ),
        ),
      );
    }
    return null;
  }

  void onCashTransferPressed() {
    final path = redirectUrl();
    if (path == null) return;
    locator<WalletNavigationService>().navigateToExchange(path);
  }

  void copyTxId() {
    Clipboard.setData(ClipboardData(text: transactionId())).then((value) {
      locator<ToastManager>().showToast(text: S.current.copied);
    });
  }

  void showFeeExplainer() {
    BuildContext? context = StackedService.navigatorKey?.currentContext;
    if (context == null) return;
    ExternalCloseSheet.showModal(
      context,
      child: _hasCrossBorderFee
          ? FxFeeExplainer(
              cardCurrency: cardAccount.cardUsageTerms?.cardCurrency ?? "",
              country: cardCountryCode,
            )
          : const FxFeesMayApplyExplainer(),
    );
  }

  void showFxFeesMayApplyExplainer() {
    BuildContext? context = StackedService.navigatorKey?.currentContext;
    if (context == null) return;
    ExternalCloseSheet.showModal(
      context,
      child: _hasCrossBorderFee
          ? FxFeeExplainer(
              cardCurrency: cardAccount.cardUsageTerms?.cardCurrency ?? "",
              country: cardCountryCode,
            )
          : const FxFeesMayApplyExplainer(),
    );
  }

  void showCountryDropdown() {
    BuildContext? context = StackedService.navigatorKey?.currentContext;
    if (context == null) return;
    ExternalCloseSheet.showModal(
      context,
      child: CardCountryBanner(
        countryName: merchantCountryName,
        countryFlag: merchantCountryFlag,
      ),
    );
  }

  void addAsSubscription() {
    _navigationService.navigateToAddCardSubscriptionView(
        cardAccount: cardAccount, cardTransaction: localTransaction);
  }
}

class WalletBuilder extends StatelessWidget {
  const WalletBuilder({super.key, required this.walletType});

  final WalletType walletType;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
      decoration: BoxDecoration(
        color: Grey.grey100,
        borderRadius: BorderRadius.circular(5),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _asset(),
          const horizontalSpace(4),
          Text(
            walletType.name,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w400,
                  color: Grey.grey500,
                  fontSize: 12.fontSize,
                ),
          )
        ],
      ),
    );
  }

  Widget _asset() {
    switch (walletType) {
      case WalletType.spot:
        return Assets.svg.spotWallet.svg(height: 18);
      case WalletType.virtualCard:
        return Assets.svg.virtualCardIcon.svg();
      default:
        return Container();
    }
  }
}

class _CardToTile extends StatelessWidget {
  const _CardToTile({required this.trailing});

  final Widget trailing;

  @override
  Widget build(BuildContext context) {
    return CardTransactionBreakdownTile(
      leading: S.current.to,
      trailing: trailing,
    );
  }
}
