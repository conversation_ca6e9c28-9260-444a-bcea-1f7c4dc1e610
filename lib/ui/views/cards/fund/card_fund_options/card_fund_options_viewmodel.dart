import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:onboard_wallet/app/app.locator.dart';
import 'package:onboard_wallet/constants/analytic_event.dart';
import 'package:onboard_wallet/constants/string_constants.dart';
import 'package:onboard_wallet/enums/card_fund_options_enum.dart';
import 'package:onboard_wallet/enums/card_funding_type.dart';
import 'package:onboard_wallet/manager/toast.dart';
import 'package:onboard_wallet/models/card/initial_funding/tx_channel.dart';
import 'package:onboard_wallet/models/card/local_card_account/local_card_account.dart';
import 'package:onboard_wallet/models/card/local_card_transaction_config/local_card_transaction_config.dart';
import 'package:onboard_wallet/models/country.dart';
import 'package:onboard_wallet/models/local/local.dart';
import 'package:onboard_wallet/models/virtual_account/local_virtual_account/local_virtual_account_status.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:onboard_wallet/ui/views/cards/widgets/multi_cards_pop_up.dart';
import 'package:onboard_wallet/ui/views/create_card/create_card_viewmodel.dart';
import 'package:onboard_wallet/ui/views/transfer/internal/data/virtual_card_wallet.dart';
import 'package:onboard_wallet/ui/widgets/bottomsheet/external_close_sheet.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';

import '../../../../../app/app.router.dart';
import '../../../../../models/card/local_card_account/local_card_usage_terms.dart';
import '../../../../../models/virtual_account/local_virtual_account/local_virtual_account.dart';

class CardFundOptionsViewModel extends ReactiveViewModel {
  final _navigationService = locator<NavigationService>();
  final _cardService = locator<CardService>();
  final _analyticsService = locator<AnalyticsService>();
  final _featureService = locator<FeatureService>();

  final _appSettingsService = locator<AppSettingsService>();

  final CardCreationConfig? cardCreationConfig;
  final CardFundingType cardFundingType;
  final String? packageId;
  double? fundingAmount;
  final LocalCardAccount? cardAccount;
  final LocalCardUsageTerms? cardUsageTerms;

  StreamSubscription? countryStreamSubscription;

  LocalCountry? country;

  Set<LocalCountry> _countries = {};

  CardFundOptionsViewModel(
    this.cardFundingType,
    this.packageId,
    this.cardAccount,
    this.fundingAmount,
    this.cardUsageTerms,
    this.cardCreationConfig,
  );

  LocalNetwork? get activeNetwork => locator<NetworkService>().currentChain;

  StreamSubscription? _streamSubscription;

  LocalCardTransactionConfig? _localCardTransactionConfig;

  LocalCardTransactionConfig? get localCardTransactionConfig =>
      _localCardTransactionConfig;

  bool get showNewOnCardFunding =>
      _appSettingsService.showNewOnCardFunding.value;

  List<LocalTxChannel> get cardFundOptions {
    List<LocalTxChannel> options =
        List.from(_localCardTransactionConfig?.txChannels ?? []);
    if (hideCashFunding) {
      options.removeWhere((element) =>
          element.optionsEnum == CardTransactionOptionsEnum.cashTransfer);
    }

    if (locator<WalletService>().isDefiWalletSetUp == false) {
      options.removeWhere((element) =>
          element.optionsEnum == CardTransactionOptionsEnum.onboardWallet);
    }
    if (_cardService.showCardInternalTransferOption &&
        cardFundingType == CardFundingType.subsequent) {
      options.add(LocalTxChannel.cardToCardFundingChannel);
    }
    return options;
  }

  bool get hideCashFunding => _featureService.isOnRampOff;

  String? get cardCurrency {
    return cardAccount?.cardCurrency ?? cardUsageTerms?.cardCurrency;
  }

  onReady() {
    setBusy(true);
    _getCountries();
  }

  _initFundingConfigs(String? userCountry) {
    setBusy(true);
    _streamSubscription = _cardService
        .getFundingConfig(
      country: userCountry,
      cardCurrency: cardCurrency,
      packageId: packageId ?? cardAccount?.cardPackageId,
    )
        .listen((configResult) {
      setBusy(false);
      configResult.when(
          success: (config) {
            _localCardTransactionConfig = config;
            notifyListeners();
          },
          failure: (failure) {});
    });
  }

  @override
  void dispose() {
    _streamSubscription?.cancel();
    super.dispose();
  }

  onFundOptionsClicked(LocalTxChannel localCardTransactionChannel) {
    saveCountry();
    switch (localCardTransactionChannel.optionsEnum) {
      case CardTransactionOptionsEnum.onboardWallet:
        _analyticsService.logEvent(
            eventName: AnalyticsEvent.fundingViaOnboardWalletSelected,
            parameters: {
              EventParameterKey.cardFundingType: cardFundingType.name,
            });
        onOnboardWalletPressed();
        break;
      case CardTransactionOptionsEnum.externalWallet:
        _analyticsService.logEvent(
            eventName: AnalyticsEvent.fundingViaExternalWalletSelected,
            parameters: {
              EventParameterKey.cardFundingType: cardFundingType.name,
            });
        onExternalWalletPressed();
        break;
      case CardTransactionOptionsEnum.cashTransfer:
        _analyticsService.logEvent(
            eventName: AnalyticsEvent.fundingViaCashTransferSelected,
            parameters: {
              EventParameterKey.cardFundingType: cardFundingType.name,
            });
        onCashTransferPressed();
        break;
      case CardTransactionOptionsEnum.cardToCard:
        _onCardToCardTap();
        _analyticsService.logEvent(eventName: AnalyticsEvent.cardToCardFunding);
        break;
      case CardTransactionOptionsEnum.virtualAccount:
        _toCardFundViaVirtualAccount(localCardTransactionChannel);
        break;
      default:
        //do nothing.
        break;
    }
  }

  onOnboardWalletPressed() {
    if (_localCardTransactionConfig == null) return;
    _navigationService.navigateTo(
      Routes.cardFundSelectAssetView,
      arguments: CardFundSelectAssetViewArguments(
        localCardTransactionConfig: _localCardTransactionConfig!,
        cardFundOptionsEnum: CardTransactionOptionsEnum.onboardWallet,
        cardFundingType: cardFundingType,
        packageId: packageId,
        fundingAmount: fundingAmount,
        localCardAccount: cardAccount,
        cardUsageTerms: cardUsageTerms,
        cardCreationConfig: cardCreationConfig,
      ),
    );
  }

  onCashTransferPressed() {
    final localChannel =
        _localCardTransactionConfig?.txChannels?.firstWhereOrNull(
      (element) =>
          element.optionsEnum == CardTransactionOptionsEnum.cashTransfer,
    );

    if (localChannel == null) return;
    String? assetCode = localChannel.supportedAssetCodes?.isNotEmpty == true
        ? localChannel.supportedAssetCodes?.first
        : null;
    _navigationService.navigateToFundCardViaCashTransferView(
      localCardTransactionChannel: localChannel,
      cardFundingType: cardFundingType,
      initialAmount: fundingAmount,
      packageId: packageId,
      assetCode: assetCode ?? '',
      cardAccount: cardAccount,
      cardUsageTerms: cardUsageTerms,
      cardCreationConfig: cardCreationConfig,
    );
  }

  onExternalWalletPressed() {
    if (_localCardTransactionConfig == null) return;

    _navigationService.navigateTo(
      Routes.cardFundSelectAssetView,
      arguments: CardFundSelectAssetViewArguments(
        localCardTransactionConfig: _localCardTransactionConfig!,
        cardFundOptionsEnum: CardTransactionOptionsEnum.externalWallet,
        cardFundingType: cardFundingType,
        packageId: packageId,
        localCardAccount: cardAccount,
        fundingAmount: fundingAmount,
        cardUsageTerms: cardUsageTerms,
      ),
    );
  }

  void _onCardToCardTap() {
    if (cardAccount == null) {
      return;
    }
    if (showNewOnCardFunding) {
      _appSettingsService.setShowNewOnCardFunding(false);
    }
    if (_cardService.transferableCardAccounts.length > 1) {
      _navigationService.navigateToCardInternalTransferView(
        toCard: VirtualCardWallet(cardAccount!),
      );
    } else {
      _showMultiCardsPopUp();
    }
  }

  void _showMultiCardsPopUp() {
    BuildContext? context = StackedService.navigatorKey?.currentContext;
    if (context == null) return;
    ExternalCloseSheet.showModal(
      context,
      backgroundColor: const Color(0xFFE3F4FD),
      padding: EdgeInsets.zero,
      child: const MultiCardsPopUp(),
    );
  }

  void onChangeCountry() => _showCountriesModal();

  Future<void> _showCountriesModal() async {
    final context = StackedService.navigatorKey?.currentContext;
    if (context == null) return;

    _navigationService.navigateToCountriesListView(
      countries: _countries.toList(),
      selectedCountry: country,
      onSelectCountry: (selectCountry) {
        if (selectCountry != country) {
          _localCardTransactionConfig = null;
          notifyListeners();
          _getRemoteFundingConfigs(country: selectCountry.code);
        }
        country = selectCountry;

        notifyListeners();
        _navigationService.back();
      },
    );
  }

  Future _getRemoteFundingConfigs({
    required String country,
  }) async {
    _localCardTransactionConfig = null;
    setBusy(true);
    final response = await _cardService.getRemoteFundingConfig(
      country: country,
      cardCurrency: cardCurrency,
      packageId: packageId ?? cardAccount?.cardPackageId,
      shouldSaveResponse: false,
    );
    response.when(success: (success) {
      _localCardTransactionConfig = success;
    }, failure: (failure) {
      locator<ToastManager>().showErrorToast(text: failure.message);
    });
    setBusy(false);
  }

  Future<void> _getCountries() async {
    final userService = locator<UserService>();
    final countryCode = await userService.getUserTransactionCountryCode();
    countryStreamSubscription =
        _cardService.getCardCountries().listen((response) {
      //stop loader, when we receive any item.
      response.when(
        success: (cardCountryList) {
          _countries = cardCountryList.toSet();
          _initCountry(cardCountryList, countryCode);
        },
        failure: (failure) {},
      );
    });
  }

  Future<void> _initCountry(
      List<LocalCountry> countries, String? countryCode) async {
    country = countries.firstWhereOrNull(
        (element) => element.code.toLowerCase() == countryCode?.toLowerCase());
    if (country == null && countries.isNotEmpty) {
      country = countries.first;
    }
    _initFundingConfigs(country?.code ?? countryCode);
    notifyListeners();
  }

  @override
  List<ListenableServiceMixin> get listenableServices {
    return [_appSettingsService];
  }

  void _toCardFundViaVirtualAccount(LocalTxChannel channel) {
    final virtualAccountService = locator<VirtualAccountService>();
    LocalVirtualAccount? account = virtualAccountService
        .virtualAccountBox.values
        .firstWhereOrNull((element) =>
            element.accountStatus == LocalVirtualAccountStatus.active);
    if (account == null) {
      _navigationService.navigateToCashDashboardView();
    } else {
      _navigationService.navigateToCardFundViaVirtualAccountView(
        localCardTransactionChannel: channel,
        cardFundingType: cardFundingType,
        virtualAccount: account,
        initialAmount: fundingAmount,
        packageId: packageId,
        cardAccount: cardAccount,
        cardUsageTerms: cardUsageTerms,
        cardCreationConfig: cardCreationConfig,
      );
    }
  }

  void saveCountry() {
    if (country == null) {
      return;
    }
    final prefService = locator<PreferenceService>();
    prefService.setString(
        key: kLastTransactionCountryCode, value: country!.code);
  }
}
