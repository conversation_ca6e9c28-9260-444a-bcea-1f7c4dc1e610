import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:onboard_wallet/enums/card_fund_options_enum.dart';
import 'package:onboard_wallet/extensions/extensions.dart';
import 'package:onboard_wallet/gen/fonts.gen.dart';
import 'package:onboard_wallet/models/card/local_card_account/index.dart';
import 'package:onboard_wallet/theme_typography.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';
import 'package:onboard_wallet/ui/common/ui_helpers.dart';
import 'package:onboard_wallet/ui/views/cards/fund/card_fund_options/widgets/fund_options_skeleton.dart';
import 'package:onboard_wallet/ui/views/fund_options/widgets/country_pill.dart';
import 'package:onboard_wallet/ui/widgets/app_bar/app_bar.dart';
import 'package:onboard_wallet/ui/widgets/asset_error_icon.dart';
import 'package:onboard_wallet/ui/widgets/badge/new_badge.dart';
import 'package:onboard_wallet/ui/widgets/cards/card_surface.dart';
import 'package:onboard_wallet/ui/widgets/container/purple_container.dart';
import 'package:stacked/stacked.dart';

import '../../../../../gen/assets.gen.dart';
import '../../../../../generated/l10n.dart';
import '../../../fund_options/widgets/country_empty_state.dart';
import 'card_withdrawal_options_viewmodel.dart';

class CardWithdrawalOptionsView
    extends StackedView<CardWithdrawalOptionsViewModel> {
  final LocalCardAccount cardAccount;

  const CardWithdrawalOptionsView({
    super.key,
    required this.cardAccount,
  });

  @override
  Widget builder(
    BuildContext context,
    CardWithdrawalOptionsViewModel viewModel,
    Widget? child,
  ) {
    final textTheme = Theme.of(context).textTheme;
    final cardFundOptions = viewModel.cardWithdrawOptions;
    return Scaffold(
      appBar: TransparentAppBar(
        actions: [
          UnconstrainedBox(
            child: Container(
              constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.7),
              child: CountryPill(
                country: viewModel.country,
                onTap: viewModel.onChangeCountry,
              ),
            ),
          ),
          const SizedBox(width: 20),
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.only(
                  right: 70.0,
                  top: 18,
                ),
                child: Text(
                  S.current.whereWouldYouLikeToTransferYourFundsTo,
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        color: kcBrandBlue,
                        fontSize: 25.fontSize,
                        letterSpacing: -0.5,
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ),
              const verticalSpace(28),
              if (viewModel.isBusy == false) ...[
                if (cardFundOptions.isNotEmpty) ...[
                  CardSurface(
                    padding: const EdgeInsets.fromLTRB(18, 10, 24, 12),
                    child: ListView.separated(
                      shrinkWrap: true,
                      itemBuilder: (_, index) {
                        final fundOptions = cardFundOptions[index];
                        return ListTile(
                          contentPadding: EdgeInsets.zero,
                          onTap: () =>
                              viewModel.onOptionsClicked(fundOptions),
                          title: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Flexible(
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  child: Text(
                                    fundOptions.name ?? '',
                                    style: textTheme.body16Medium.copyWith(
                                      color: kcBaseBlack,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(
                                width: 11,
                              ),
                              if (fundOptions.optionsEnum ==
                                      CardTransactionOptionsEnum
                                          .virtualAccount &&
                                  viewModel.showNewOnCardWithdrawal) ...[
                                const NewBadge(),
                              ]
                            ],
                          ),
                          subtitle: Text(
                            fundOptions.description ?? '',
                            style: textTheme.body12Regular.copyWith(
                              color: Grey.grey400,
                              fontFamily: FontFamily.satoshi,
                            ),
                          ),
                          leading: PurpleContainer(
                            boxShape: BoxShape.circle,
                            size: 40,
                            bgColor: kcBaseWhite,
                            child: fundOptions.optionsEnum?.icon ??
                                const AssetErrorIconSvg(),
                          ),
                          trailing:
                              Assets.svg.arrowRight.svg(color: Grey.grey400),
                        );
                      },
                      separatorBuilder: (_, __) {
                        return const verticalSpace(1);
                      },
                      itemCount: cardFundOptions.length,
                    ),
                  ),
                ] else ...[
                  const verticalSpace(20),
                  CountryEmptyState(
                    subtitle: S.current
                        .withdrawalMethodIsCurrentlyNotAvailableForThisCountry,
                    onChangeCountry: viewModel.onChangeCountry,
                    country: viewModel.country,
                  )
                ]
              ] else ...[
                const FundOptionsSkeleton(),
              ]
            ],
          ),
        ),
      ),
    );
  }

  @override
  CardWithdrawalOptionsViewModel viewModelBuilder(
    BuildContext context,
  ) =>
      CardWithdrawalOptionsViewModel(cardAccount);

  @override
  void onViewModelReady(CardWithdrawalOptionsViewModel viewModel) =>
      viewModel.onReady();
}
