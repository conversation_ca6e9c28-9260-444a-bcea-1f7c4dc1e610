import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:onboard_wallet/app/app.locator.dart';
import 'package:onboard_wallet/constants/analytic_event.dart';
import 'package:onboard_wallet/constants/string_constants.dart';
import 'package:onboard_wallet/enums/card_fund_options_enum.dart';
import 'package:onboard_wallet/manager/toast.dart';
import 'package:onboard_wallet/models/card/local_card_account/index.dart';
import 'package:onboard_wallet/models/card/local_card_transaction_config/local_card_transaction_config.dart';
import 'package:onboard_wallet/models/country.dart';
import 'package:onboard_wallet/models/local/local.dart';
import 'package:onboard_wallet/models/virtual_account/local_virtual_account/local_virtual_account.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:onboard_wallet/ui/views/cards/widgets/multi_cards_pop_up.dart';
import 'package:onboard_wallet/ui/views/transfer/internal/data/virtual_card_wallet.dart';
import 'package:onboard_wallet/ui/widgets/bottomsheet/external_close_sheet.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';
import 'package:onboard_wallet/models/virtual_account/local_virtual_account/local_virtual_account_status.dart';

import '../../../../../app/app.router.dart';

class CardWithdrawalOptionsViewModel extends ReactiveViewModel {
  final _navigationService = locator<NavigationService>();
  final _cardService = locator<CardService>();
  final _analyticsService = locator<AnalyticsService>();
  final _featureService = locator<FeatureService>();
  final _appSettingsService = locator<AppSettingsService>();
  final LocalCardAccount cardAccount;

  CardWithdrawalOptionsViewModel(this.cardAccount);

  LocalNetwork? get activeNetwork => locator<NetworkService>().currentChain;

  StreamSubscription? _streamSubscription;

  LocalCardTransactionConfig? _localCardTransactionConfig;

  List<LocalTxChannel> get cardWithdrawOptions {
    final List<LocalTxChannel> options =
        List.from(_localCardTransactionConfig?.txChannels ?? []);
    if (hideCashWithdrawal) {
      options.removeWhere((element) =>
          element.optionsEnum == CardTransactionOptionsEnum.onboardFast);
    }

    if(locator<WalletService>().isDefiWalletSetUp == false) {
      options.removeWhere((element) =>
      element.optionsEnum == CardTransactionOptionsEnum.onboardWallet);
    }
    if (_cardService.showCardInternalTransferOption) {
      options.add(LocalTxChannel.cardToCardWithdrawalChannel);
    }

    return options;
  }

  bool get hideCashWithdrawal => _featureService.isOffRampOff;

  String? get cardCurrency {
    return cardAccount.cardCurrency;
  }

  StreamSubscription? countryStreamSubscription;

  LocalCountry? country;

  Set<LocalCountry> _countries = {};

  bool get showNewOnCardWithdrawal =>
      _appSettingsService.showNewOnCardWithdrawal.value;

  onReady() {
    setBusy(true);
    _getCountries();
  }

  @override
  void dispose() {
    _streamSubscription?.cancel();
    super.dispose();
  }

  onOptionsClicked(LocalTxChannel localCardTransactionChannel) {
    saveCountry();
    switch (localCardTransactionChannel.optionsEnum) {
      case CardTransactionOptionsEnum.onboardWallet:
        _analyticsService.logEvent(
            eventName: AnalyticsEvent.withdrawViaOnboardWallet);
        onOnboardWalletPressed();
        break;
      case CardTransactionOptionsEnum.externalWallet:
        onExternalWalletPressed();
        _analyticsService.logEvent(
            eventName: AnalyticsEvent.withdrawViaExternalWallet);
        break;
      case CardTransactionOptionsEnum.onboardFast:
        onCashTransferPressed();
        _analyticsService.logEvent(
            eventName: AnalyticsEvent.withdrawViaBankAccount);
        break;
      case CardTransactionOptionsEnum.cardToCard:
        _analyticsService.logEvent(
            eventName: AnalyticsEvent.cardToCardWithdrawal);
        _onCardToCardTap();
        break;
      case CardTransactionOptionsEnum.virtualAccount:
        _onVirtualAccountTap(localCardTransactionChannel);
        break;
      default:
        //do nothing.
        break;
    }
  }

  onOnboardWalletPressed() {
    if (_localCardTransactionConfig == null) return;
    _navigationService.navigateToCardWithdrawalSelectAssetView(
      localCardTransactionConfig: _localCardTransactionConfig!,
      cardAccount: cardAccount,
      optionsEnum: CardTransactionOptionsEnum.onboardWallet,
    );
  }

  onCashTransferPressed() {
    LocalTxChannel? localTxChannel = _localCardTransactionConfig?.txChannels
        ?.firstWhereOrNull((element) =>
            element.optionsEnum == CardTransactionOptionsEnum.onboardFast);

    if (localTxChannel == null) return;
    _navigationService.navigateToCardWithdrawalViaCashTransferView(
      cardAccount: cardAccount,
      minWithdrawalAmount: _localCardTransactionConfig?.minAmount ?? 10,
      localTxChannel: localTxChannel,
    );
  }

  onExternalWalletPressed() {
    if (_localCardTransactionConfig == null) return;

    _navigationService.navigateToCardWithdrawalSelectAssetView(
      localCardTransactionConfig: _localCardTransactionConfig!,
      cardAccount: cardAccount,
      optionsEnum: CardTransactionOptionsEnum.externalWallet,
    );
  }

  void onChangeCountry() => _showCountriesModal();

  Future<void> _showCountriesModal() async {
    _navigationService.navigateToCountriesListView(
      countries: _countries.toList(),
      selectedCountry: country,
      onSelectCountry: (selectCountry) {
        if (selectCountry != country) {
          _localCardTransactionConfig = null;
          notifyListeners();
          _getRemoteWithdrawalConfigs(selectCountry.code);
        }
        country = selectCountry;

        notifyListeners();
        _navigationService.back();
      },
    );
  }

  Future _getRemoteWithdrawalConfigs(String country) async {
    _localCardTransactionConfig = null;
    setBusy(true);
    final response = await _cardService.getRemoteWithdrawalConfig(
      country: country,
      cardCurrency: cardCurrency,
      packageId: cardAccount.cardPackageId,
      shouldSaveResponse: false,
    );
    response.when(success: (success) {
      _localCardTransactionConfig = success;
    }, failure: (failure) {
      locator<ToastManager>().showErrorToast(text: failure.message);
    });
    setBusy(false);
  }

  Future<void> _getCountries() async {
    final userService = locator<UserService>();

    String countryCode = await userService.getUserTransactionCountryCode() ?? "NG";

    countryStreamSubscription =
        _cardService.getCardCountries().listen((response) {
      //stop loader, when we receive any item.
      response.when(
        success: (cardCountryList) {
          _countries = cardCountryList.toSet();
          _initCountry(cardCountryList, countryCode);
        },
        failure: (failure) {},
      );
    });
  }

  Future<void> _initCountry(
      List<LocalCountry> countries, String countryCode) async {
    country = countries.firstWhereOrNull(
        (element) => element.code.toLowerCase() == countryCode.toLowerCase());
    if (country == null && countries.isNotEmpty) {
      country = countries.first;
    }
    _initWithdrawalConfigs(country?.code ?? countryCode);
    notifyListeners();
  }

  _initWithdrawalConfigs(String userCountry) {
    setBusy(true);
    _streamSubscription = _cardService
        .getWithdrawalsConfig(
      country: userCountry,
      cardCurrency: cardCurrency,
      packageId: cardAccount.cardPackageId,
    )
        .listen((configResult) {
      setBusy(false);
      configResult.when(
          success: (config) {
            _localCardTransactionConfig = config;
            notifyListeners();
          },
          failure: (failure) {});
    });
  }

  void _onCardToCardTap() {
    if (_cardService.transferableCardAccounts.length > 1) {
      _navigationService.navigateToCardInternalTransferView(
        fromCard: VirtualCardWallet(cardAccount),
      );
    } else {
      _showMultiCardsPopUp();
    }
  }

  @override
  List<ListenableServiceMixin> get listenableServices {
    return [_appSettingsService];
  }

  void _showMultiCardsPopUp() {
    BuildContext? context = StackedService.navigatorKey?.currentContext;
    if (context == null) return;
    ExternalCloseSheet.showModal(
      context,
      backgroundColor: const Color(0xFFE3F4FD),
      padding: EdgeInsets.zero,
      child: const MultiCardsPopUp(),
    );
  }

  void _onVirtualAccountTap(LocalTxChannel channel) {
    if (showNewOnCardWithdrawal) {
      _appSettingsService.setShowNewOnCardWithdrawal(false);
    }
    final virtualAccountService = locator<VirtualAccountService>();
    LocalVirtualAccount? virtualAccount = virtualAccountService
        .virtualAccountBox.values
        .firstWhereOrNull((element) =>
    element.accountStatus == LocalVirtualAccountStatus.active);
    if (virtualAccount == null) {
      _navigationService.navigateToCashDashboardView();
    } else {
      _navigationService.navigateToCardWithdrawalViaVirtualAccountView(
        cardAccount: cardAccount,
        minWithdrawalAmount: _localCardTransactionConfig?.minAmount ?? 5,
        localTxChannel: channel,
      );
    }
  }

  void saveCountry() {
    if (country == null) {
      return;
    }
    final prefService = locator<PreferenceService>();
    prefService.setString(
        key: kLastTransactionCountryCode, value: country!.code);
  }
}
