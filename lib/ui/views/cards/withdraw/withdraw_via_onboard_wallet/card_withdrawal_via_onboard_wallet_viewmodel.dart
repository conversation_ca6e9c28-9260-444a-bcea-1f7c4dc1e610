import 'package:flutter/cupertino.dart';
import 'package:nestcoinco_onboard_api_gateway/nestcoinco_onboard_api_gateway.dart';
import 'package:onboard_wallet/app/app.locator.dart';
import 'package:onboard_wallet/app/app.router.dart';
import 'package:onboard_wallet/constants/string_constants.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/manager/manager.dart';
import 'package:onboard_wallet/models/models.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:onboard_wallet/ui/views/cards/cards.dart';
import 'package:onboard_wallet/ui/views/cards/withdraw/card_withdrawal_view_model.dart';
import 'package:onboard_wallet/ui/views/cards/withdraw/withdraw_via_onboard_wallet/widgets/wallet_selection_sheet.dart';
import 'package:onboard_wallet/ui/views/transfer/internal/data/onboard_wallet_type.dart';
import 'package:onboard_wallet/ui/views/transfer/internal/data/spot_wallet.dart';
import 'package:onboard_wallet/ui/views/transfer/internal/data/trading_wallet.dart';
import 'package:onboard_wallet/ui/widgets/bottomsheet/bottomsheet.dart';
import 'package:stacked_services/stacked_services.dart';
import '../../../../../constants/analytic_event.dart';

class CardWithdrawViaOnboardWalletViewModel extends CardWithdrawalViewModel {
  final _navigationService = locator<NavigationService>();
  final _cardService = locator<CardService>();
  final _toastManager = locator<ToastManager>();
  final _partnerService = locator<PartnersService>();
  final _configService = locator<ConfigService>();
  final _analyticsService = locator<AnalyticsService>();

  LocalToken localToken;

  CardWithdrawViaOnboardWalletViewModel({
    required super.cardAccount,
    required this.localToken,
    required super.minWithdrawalAmount,
    required super.localTxChannel,
  });

  double get balance => cardAccount.card?.balance?.toDouble() ?? 0;

  WalletCardsSvcCryptoRateInfo? cryptoRateInfo;

  OnboardWalletType toWallet = SpotWallet();

  onContinuePressed() {
    _analyticsService.logEvent(
        eventName: AnalyticsEvent.continueWithdrawViaBankAccount);
    String? toAddress =
        toWallet.address(networkId: localToken.network?.networkId);

    String? accountId = cardAccount.id;
    if (toAddress == null ||
        amountUserWantsToWithdraw <= 0 ||
        accountId == null ||
        cryptoRateInfo == null) {
      return;
    }
    _navigationService.navigateToCardWithdrawalPreviewView(
      cardWithdrawalPreviewArgs: CardWithdrawalPreviewArgs(
        toAddress: toAddress,
        withdrawalAmount: amountUserWantsToWithdraw,
        token: localToken,
        toWalletType: toWallet,
        cardCurrencyName: currencyName,
        cardCurrencySymbol: cardCurrencySymbol,
        accountId: accountId,
        withdrawalCryptoRate: cryptoRateInfo!,
      ),
    );
  }

  void onModelReady() {
    getWithdrawalCryptoRate();
  }

  Future<void> getWithdrawalCryptoRate({bool shouldRetry = true}) async {
    setBusy(true);
    String? tokenCode = localToken.tokenCode;
    if (tokenCode == null) {
      setError('No token code');
      return;
    }
    final response = await _cardService.getWithdrawalCryptoRates(
        tokenCode: tokenCode, cardCurrency: currencyName);
    response.when(success: (rateInfo) {
      if (rateInfo != null) {
        setUpCardVariables(rateInfo);
      }
    }, failure: (failure) {
      if (failure.message == kInvalidRateReference && shouldRetry) {
        getWithdrawalCryptoRate(shouldRetry: false);
      }
      _toastManager.showErrorToast(text: failure.message);
    });
  }

  void setUpCardVariables(WalletCardsSvcCryptoRateInfo rateInfo) {
    cryptoRateInfo = rateInfo;
    minWithdrawalAmount = rateInfo.minAmount ?? minWithdrawalAmount;
    onInputChanged(textEditingController.text);
    setBusy(false);
  }

  @override
  void onInputChanged(String? value) {
    final amount = getInputAmountInCardCurrency();

    if (amount == null || amount <= 0) {
      reset();
      return;
    }
    _checkMinimumTransactionAmount(amount);
    _checkMaximumTransactionAmount(amount);
    checkInsufficientBalance(amount);
    setErrorMessage();
    notifyListeners();
  }

  void _checkMaximumTransactionAmount(double amountAsDouble) {
    num? maxAmount = cryptoRateInfo?.maxAmount;
    if (maxAmount == null) return;
    maximumWithdrawal = amountAsDouble > maxAmount;
  }

  void _checkMinimumTransactionAmount(double amount) {
    minimumWithdrawal = amount < minWithdrawalAmount;
  }

  void showToWallets() {
    List<OnboardWalletType> wallets = [SpotWallet()];
    String? netId =
        CoreNetworkId.exchangeNetworkIdMap[localToken.network?.networkId] ??
            localToken.network?.networkId;
    String? tradingWalletAddress =
        _partnerService.tradingAccountForNetworkId(netId)?.address;
    List<LocalToken> exchangeTokens =
        _configService.getActiveNetworkTokensAndMerge(
      networkId: netId,
    );

    if (tradingWalletAddress != null &&
        exchangeTokens.contains(localToken) == false) {
      wallets.add(MerchantWallet());
    }

    BuildContext? context = StackedService.navigatorKey?.currentContext;
    if (context == null) return;
    ExternalCloseSheet.showModal(
      context,
      child: WalletSelectionSheet(
        title: S.current.sendTo,
        selectedWallet: toWallet,
        walletList: wallets,
        onSelectWallet: onSelectToWallet,
      ),
    );
  }

  void onSelectToWallet(OnboardWalletType wallet) {
    _analyticsService.logEvent(
      eventName: AnalyticsEvent.toWalletSwitchOnWithdrawViaOnboardWallet,
    );
    _navigationService.back();
    toWallet = wallet;
    notifyListeners();
  }

  @override
  void dispose() {
    textEditingController.dispose();
    super.dispose();
  }

  @override
  String? get assetCode => localToken.symbol;

  @override
  WalletCardsSvcBaseRateInfo? get rateInfo => cryptoRateInfo;

  @override
  void onFundOptionsTapAnalyticsCall() {
    _analyticsService.logEvent(
        eventName: AnalyticsEvent.withdrawOptionsOnOnboardWalletWithdraw);
  }
}
