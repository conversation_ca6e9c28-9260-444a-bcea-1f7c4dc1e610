// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(
      _current != null,
      'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name =
        (locale.countryCode?.isEmpty ?? false)
            ? locale.languageCode
            : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(
      instance != null,
      'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `Destination Wallet`
  String get destinationWallet {
    return Intl.message(
      'Destination Wallet',
      name: 'destinationWallet',
      desc: '',
      args: [],
    );
  }

  /// `Orders`
  String get orders {
    return Intl.message('Orders', name: 'orders', desc: '', args: []);
  }

  /// `My`
  String get my {
    return Intl.message('My', name: 'my', desc: '', args: []);
  }

  /// `All networks`
  String get allNetworks {
    return Intl.message(
      'All networks',
      name: 'allNetworks',
      desc: '',
      args: [],
    );
  }

  /// `Key Stats`
  String get keyStats {
    return Intl.message('Key Stats', name: 'keyStats', desc: '', args: []);
  }

  /// `Settings`
  String get settings {
    return Intl.message('Settings', name: 'settings', desc: '', args: []);
  }

  /// `Wallet`
  String get wallet {
    return Intl.message('Wallet', name: 'wallet', desc: '', args: []);
  }

  /// `Onboard Wallet`
  String get onboardWallet {
    return Intl.message(
      'Onboard Wallet',
      name: 'onboardWallet',
      desc: '',
      args: [],
    );
  }

  /// `Secure your wallet`
  String get secureYourWallet {
    return Intl.message(
      'Secure your wallet',
      name: 'secureYourWallet',
      desc: '',
      args: [],
    );
  }

  /// `The simplest wallet to\nbuy, sell, store and \nsend crypto`
  String get authViewHeader {
    return Intl.message(
      'The simplest wallet to\nbuy, sell, store and \nsend crypto',
      name: 'authViewHeader',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get cancel {
    return Intl.message('Cancel', name: 'cancel', desc: '', args: []);
  }

  /// `Onboard`
  String get onboard {
    return Intl.message('Onboard', name: 'onboard', desc: '', args: []);
  }

  /// `Available`
  String get available {
    return Intl.message('Available', name: 'available', desc: '', args: []);
  }

  /// `Seed phrase`
  String get seedPhrase {
    return Intl.message('Seed phrase', name: 'seedPhrase', desc: '', args: []);
  }

  /// `Buy {tokenSymbol} and get started!`
  String buyGasAndGetStarted(Object tokenSymbol) {
    return Intl.message(
      'Buy $tokenSymbol and get started!',
      name: 'buyGasAndGetStarted',
      desc: '',
      args: [tokenSymbol],
    );
  }

  /// `Deposit {tokenSymbol} and get started!`
  String depositGasAndGetStarted(Object tokenSymbol) {
    return Intl.message(
      'Deposit $tokenSymbol and get started!',
      name: 'depositGasAndGetStarted',
      desc: '',
      args: [tokenSymbol],
    );
  }

  /// `Avoid failed transactions`
  String get avoidFailedTransactions {
    return Intl.message(
      'Avoid failed transactions',
      name: 'avoidFailedTransactions',
      desc: '',
      args: [],
    );
  }

  /// `{tokenSymbol} token is required to pay network fees for transactions. There's no {tokenSymbol} in your wallet, so we suggest adding some now.`
  String buyGasSubCopy(Object tokenSymbol) {
    return Intl.message(
      '$tokenSymbol token is required to pay network fees for transactions. There\'s no $tokenSymbol in your wallet, so we suggest adding some now.',
      name: 'buyGasSubCopy',
      desc: '',
      args: [tokenSymbol],
    );
  }

  /// `Oops! This code seems incorrect`
  String get oopsThisCodeSeemsIncorrect {
    return Intl.message(
      'Oops! This code seems incorrect',
      name: 'oopsThisCodeSeemsIncorrect',
      desc: '',
      args: [],
    );
  }

  /// `Creating your Onboard Wallet`
  String get creatingYourOnboardWallet {
    return Intl.message(
      'Creating your Onboard Wallet',
      name: 'creatingYourOnboardWallet',
      desc: '',
      args: [],
    );
  }

  /// `Too many attempts. Try again later`
  String get tooManyAttemptsTryAgainLater {
    return Intl.message(
      'Too many attempts. Try again later',
      name: 'tooManyAttemptsTryAgainLater',
      desc: '',
      args: [],
    );
  }

  /// `Signature Request`
  String get signatureRequest {
    return Intl.message(
      'Signature Request',
      name: 'signatureRequest',
      desc: '',
      args: [],
    );
  }

  /// `You are signing the message below`
  String get youAreSigningTheMessageBelow {
    return Intl.message(
      'You are signing the message below',
      name: 'youAreSigningTheMessageBelow',
      desc: '',
      args: [],
    );
  }

  /// `Coin Transfer`
  String get coinTransfer {
    return Intl.message(
      'Coin Transfer',
      name: 'coinTransfer',
      desc: '',
      args: [],
    );
  }

  /// `I understand`
  String get iUnderstand {
    return Intl.message(
      'I understand',
      name: 'iUnderstand',
      desc: '',
      args: [],
    );
  }

  /// `Fees may apply`
  String get feesMayApply {
    return Intl.message(
      'Fees may apply',
      name: 'feesMayApply',
      desc: '',
      args: [],
    );
  }

  /// ` Our payment partners may charge a small fee to process this transaction\nThe receiving amount is only an estimate.`
  String get feesInfoCopy {
    return Intl.message(
      ' Our payment partners may charge a small fee to process this transaction\nThe receiving amount is only an estimate.',
      name: 'feesInfoCopy',
      desc: '',
      args: [],
    );
  }

  /// `Fetching rates...`
  String get fetchingRates {
    return Intl.message(
      'Fetching rates...',
      name: 'fetchingRates',
      desc: '',
      args: [],
    );
  }

  /// `To process this transaction, a small network\n fee will be charged.`
  String get swapFeeCopy {
    return Intl.message(
      'To process this transaction, a small network\n fee will be charged.',
      name: 'swapFeeCopy',
      desc: '',
      args: [],
    );
  }

  /// `Got {tokenSymbol} elsewhere? Deposit`
  String gotGasElsewhere(Object tokenSymbol) {
    return Intl.message(
      'Got $tokenSymbol elsewhere? Deposit',
      name: 'gotGasElsewhere',
      desc: '',
      args: [tokenSymbol],
    );
  }

  /// `'We aren’t able to fetch details for the contract address entered. Please pre-fill manually'`
  String get unableToRecognizeAddressErrorMessage {
    return Intl.message(
      '\'We aren’t able to fetch details for the contract address entered. Please pre-fill manually\'',
      name: 'unableToRecognizeAddressErrorMessage',
      desc: '',
      args: [],
    );
  }

  /// `Token Transfer`
  String get tokenTransfer {
    return Intl.message(
      'Token Transfer',
      name: 'tokenTransfer',
      desc: '',
      args: [],
    );
  }

  /// `NFT Transfer`
  String get nftTransfer {
    return Intl.message(
      'NFT Transfer',
      name: 'nftTransfer',
      desc: '',
      args: [],
    );
  }

  /// `You swap`
  String get youSwap {
    return Intl.message('You swap', name: 'youSwap', desc: '', args: []);
  }

  /// `Internal Transfer`
  String get internalTransfer {
    return Intl.message(
      'Internal Transfer',
      name: 'internalTransfer',
      desc: '',
      args: [],
    );
  }

  /// `Contract Call`
  String get contractCall {
    return Intl.message(
      'Contract Call',
      name: 'contractCall',
      desc: '',
      args: [],
    );
  }

  /// `Deposit Crypto`
  String get depositCrypto {
    return Intl.message(
      'Deposit Crypto',
      name: 'depositCrypto',
      desc: '',
      args: [],
    );
  }

  /// `Destination`
  String get destination {
    return Intl.message('Destination', name: 'destination', desc: '', args: []);
  }

  /// `Receive tokens from external wallet`
  String get receiveTokenFromExternalWallet {
    return Intl.message(
      'Receive tokens from external wallet',
      name: 'receiveTokenFromExternalWallet',
      desc: '',
      args: [],
    );
  }

  /// `Please authenticate to secure your wallet`
  String get backupWalletReason {
    return Intl.message(
      'Please authenticate to secure your wallet',
      name: 'backupWalletReason',
      desc: '',
      args: [],
    );
  }

  /// `Unlock wallet`
  String get unlockWallet {
    return Intl.message(
      'Unlock wallet',
      name: 'unlockWallet',
      desc: '',
      args: [],
    );
  }

  /// `Crypto is visible`
  String get cryptoIsVisible {
    return Intl.message(
      'Crypto is visible',
      name: 'cryptoIsVisible',
      desc: '',
      args: [],
    );
  }

  /// `It will now show up on your portfolio list.`
  String get itWillNowShowUpOnYourPortfolioList {
    return Intl.message(
      'It will now show up on your portfolio list.',
      name: 'itWillNowShowUpOnYourPortfolioList',
      desc: '',
      args: [],
    );
  }

  /// `Unlock wallet to gain access to the app`
  String get unlockToGainAccess {
    return Intl.message(
      'Unlock wallet to gain access to the app',
      name: 'unlockToGainAccess',
      desc: '',
      args: [],
    );
  }

  /// `Create Wallet`
  String get createWallet {
    return Intl.message(
      'Create Wallet',
      name: 'createWallet',
      desc: '',
      args: [],
    );
  }

  /// `Continue with {Google}`
  String continueWith(Object Google) {
    return Intl.message(
      'Continue with $Google',
      name: 'continueWith',
      desc: '',
      args: [Google],
    );
  }

  /// `Switched to {networkName}`
  String switchedToNetwork(Object networkName) {
    return Intl.message(
      'Switched to $networkName',
      name: 'switchedToNetwork',
      desc: '',
      args: [networkName],
    );
  }

  /// `Onboard.xyz`
  String get onboardDotXyz {
    return Intl.message(
      'Onboard.xyz',
      name: 'onboardDotXyz',
      desc: '',
      args: [],
    );
  }

  /// `This is the wallet where your assets will  automatically be sent to once we have received them`
  String get destinationWalletInfo {
    return Intl.message(
      'This is the wallet where your assets will  automatically be sent to once we have received them',
      name: 'destinationWalletInfo',
      desc: '',
      args: [],
    );
  }

  /// `Import wallet`
  String get importWallet {
    return Intl.message(
      'Import wallet',
      name: 'importWallet',
      desc: '',
      args: [],
    );
  }

  /// `Great! You can import this wallet `
  String get importWalletSuccessMessage {
    return Intl.message(
      'Great! You can import this wallet ',
      name: 'importWalletSuccessMessage',
      desc: '',
      args: [],
    );
  }

  /// `Provide your 12 word recovery phrase`
  String get importWalletSubCopy {
    return Intl.message(
      'Provide your 12 word recovery phrase',
      name: 'importWalletSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Paste here or type...`
  String get pasteHereOrType {
    return Intl.message(
      'Paste here or type...',
      name: 'pasteHereOrType',
      desc: '',
      args: [],
    );
  }

  /// `Important`
  String get important {
    return Intl.message('Important', name: 'important', desc: '', args: []);
  }

  /// `Paste`
  String get paste {
    return Intl.message('Paste', name: 'paste', desc: '', args: []);
  }

  /// `Done`
  String get done {
    return Intl.message('Done', name: 'done', desc: '', args: []);
  }

  /// `Email`
  String get email {
    return Intl.message('Email', name: 'email', desc: '', args: []);
  }

  /// `Confirm swap`
  String get confirmSwap {
    return Intl.message(
      'Confirm swap',
      name: 'confirmSwap',
      desc: '',
      args: [],
    );
  }

  /// `My Wallet`
  String get myWallet {
    return Intl.message('My Wallet', name: 'myWallet', desc: '', args: []);
  }

  /// `{symbol} is visible`
  String tokenIsVisible(Object symbol) {
    return Intl.message(
      '$symbol is visible',
      name: 'tokenIsVisible',
      desc: '',
      args: [symbol],
    );
  }

  /// `{symbol} is hidden`
  String tokenIsHidden(Object symbol) {
    return Intl.message(
      '$symbol is hidden',
      name: 'tokenIsHidden',
      desc: '',
      args: [symbol],
    );
  }

  /// `Authenticating your email`
  String get authenticatingYourEmail {
    return Intl.message(
      'Authenticating your email',
      name: 'authenticatingYourEmail',
      desc: '',
      args: [],
    );
  }

  /// `Let's verify your account`
  String get letsVerifyYourAccount {
    return Intl.message(
      'Let\'s verify your account',
      name: 'letsVerifyYourAccount',
      desc: '',
      args: [],
    );
  }

  /// `Rate`
  String get rate {
    return Intl.message('Rate', name: 'rate', desc: '', args: []);
  }

  /// `Spot Wallet`
  String get spotWallet {
    return Intl.message('Spot Wallet', name: 'spotWallet', desc: '', args: []);
  }

  /// `Max Slippage`
  String get maxSlippage {
    return Intl.message(
      'Max Slippage',
      name: 'maxSlippage',
      desc: '',
      args: [],
    );
  }

  /// `Request`
  String get request {
    return Intl.message('Request', name: 'request', desc: '', args: []);
  }

  /// `Contract Address`
  String get contractAddress {
    return Intl.message(
      'Contract Address',
      name: 'contractAddress',
      desc: '',
      args: [],
    );
  }

  /// `You're giving access to transact with this token`
  String get approveTokenSubCubCopy {
    return Intl.message(
      'You\'re giving access to transact with this token',
      name: 'approveTokenSubCubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Confirm that you've entered the correct email and hit continue`
  String get setEmailSubCopy {
    return Intl.message(
      'Confirm that you\'ve entered the correct email and hit continue',
      name: 'setEmailSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `What is a recovery phrase?`
  String get whatIsARecoveryPhrase {
    return Intl.message(
      'What is a recovery phrase?',
      name: 'whatIsARecoveryPhrase',
      desc: '',
      args: [],
    );
  }

  /// `Did you know?`
  String get didYouKnow {
    return Intl.message(
      'Did you know?',
      name: 'didYouKnow',
      desc: '',
      args: [],
    );
  }

  /// `Network Fee`
  String get networkFee {
    return Intl.message('Network Fee', name: 'networkFee', desc: '', args: []);
  }

  /// `Swap successful`
  String get swapSuccessful {
    return Intl.message(
      'Swap successful',
      name: 'swapSuccessful',
      desc: '',
      args: [],
    );
  }

  /// `I want to store and manage my digital assets`
  String get customerSubCopy {
    return Intl.message(
      'I want to store and manage my digital assets',
      name: 'customerSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Tell us a bit about you`
  String get tellUsABitAboutYou {
    return Intl.message(
      'Tell us a bit about you',
      name: 'tellUsABitAboutYou',
      desc: '',
      args: [],
    );
  }

  /// `Which of the options below best describes\nyou`
  String get accountTypeSubCopy {
    return Intl.message(
      'Which of the options below best describes\nyou',
      name: 'accountTypeSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `I want to trade and exchange high volumes of digital assets`
  String get merchantSubCopy {
    return Intl.message(
      'I want to trade and exchange high volumes of digital assets',
      name: 'merchantSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Your recovery phrase is a special kind of password. It is one way to access your wallet and must be kept\nprivate`
  String get recoveryPhraseInfo {
    return Intl.message(
      'Your recovery phrase is a special kind of password. It is one way to access your wallet and must be kept\nprivate',
      name: 'recoveryPhraseInfo',
      desc: '',
      args: [],
    );
  }

  /// `Please confirm that you read below before viewing your {credentialType}`
  String securityWarning(Object credentialType) {
    return Intl.message(
      'Please confirm that you read below before viewing your $credentialType',
      name: 'securityWarning',
      desc: '',
      args: [credentialType],
    );
  }

  /// `Import Existing Wallet`
  String get importExistingWallet {
    return Intl.message(
      'Import Existing Wallet',
      name: 'importExistingWallet',
      desc: '',
      args: [],
    );
  }

  /// `Enter onboard email`
  String get enterOnboardEmail {
    return Intl.message(
      'Enter onboard email',
      name: 'enterOnboardEmail',
      desc: '',
      args: [],
    );
  }

  /// `Failed to Sign Transaction`
  String get failedToSignTransaction {
    return Intl.message(
      'Failed to Sign Transaction',
      name: 'failedToSignTransaction',
      desc: '',
      args: [],
    );
  }

  /// `You receive`
  String get youReceive {
    return Intl.message('You receive', name: 'youReceive', desc: '', args: []);
  }

  /// `Error Switching Chain`
  String get errorSwitchingChain {
    return Intl.message(
      'Error Switching Chain',
      name: 'errorSwitchingChain',
      desc: '',
      args: [],
    );
  }

  /// `Slippage can occur when market prices change between when you place an order and when it is executed.`
  String get maxSlippageInfo {
    return Intl.message(
      'Slippage can occur when market prices change between when you place an order and when it is executed.',
      name: 'maxSlippageInfo',
      desc: '',
      args: [],
    );
  }

  /// `Transaction Cancelled`
  String get transactionCancelled {
    return Intl.message(
      'Transaction Cancelled',
      name: 'transactionCancelled',
      desc: '',
      args: [],
    );
  }

  /// `Authorize Transaction`
  String get authorizeTransaction {
    return Intl.message(
      'Authorize Transaction',
      name: 'authorizeTransaction',
      desc: '',
      args: [],
    );
  }

  /// `\nThis percentage is the maximum slippage`
  String get thisPercentageIsTheMaximumSlippage {
    return Intl.message(
      '\nThis percentage is the maximum slippage',
      name: 'thisPercentageIsTheMaximumSlippage',
      desc: '',
      args: [],
    );
  }

  /// `you're willing to accept before we stop the transaction`
  String get yourWillingToAcceptBeforeWeStopTheTransaction {
    return Intl.message(
      'you\'re willing to accept before we stop the transaction',
      name: 'yourWillingToAcceptBeforeWeStopTheTransaction',
      desc: '',
      args: [],
    );
  }

  /// `Confirm Transaction`
  String get confirmTransaction {
    return Intl.message(
      'Confirm Transaction',
      name: 'confirmTransaction',
      desc: '',
      args: [],
    );
  }

  /// `Transaction Order`
  String get transactionOrder {
    return Intl.message(
      'Transaction Order',
      name: 'transactionOrder',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get continueText {
    return Intl.message('Continue', name: 'continueText', desc: '', args: []);
  }

  /// `Loading`
  String get loading {
    return Intl.message('Loading', name: 'loading', desc: '', args: []);
  }

  /// `Show Private Key`
  String get showPrivateKey {
    return Intl.message(
      'Show Private Key',
      name: 'showPrivateKey',
      desc: '',
      args: [],
    );
  }

  /// `Recovery phrase`
  String get recoveryPhrase {
    return Intl.message(
      'Recovery phrase',
      name: 'recoveryPhrase',
      desc: '',
      args: [],
    );
  }

  /// `Private key`
  String get privateKey {
    return Intl.message('Private key', name: 'privateKey', desc: '', args: []);
  }

  /// `Your wallet recovery details`
  String get yourWalletRecoveryDetails {
    return Intl.message(
      'Your wallet recovery details',
      name: 'yourWalletRecoveryDetails',
      desc: '',
      args: [],
    );
  }

  /// `Your recovery details are backed up`
  String get yourRecoveryDetailsAreBackedUp {
    return Intl.message(
      'Your recovery details are backed up',
      name: 'yourRecoveryDetailsAreBackedUp',
      desc: '',
      args: [],
    );
  }

  /// `Show Recovery Phrase`
  String get showRecoveryPhrase {
    return Intl.message(
      'Show Recovery Phrase',
      name: 'showRecoveryPhrase',
      desc: '',
      args: [],
    );
  }

  /// `Transaction Fee`
  String get transactionFee {
    return Intl.message(
      'Transaction Fee',
      name: 'transactionFee',
      desc: '',
      args: [],
    );
  }

  /// `Total Amount`
  String get totalAmount {
    return Intl.message(
      'Total Amount',
      name: 'totalAmount',
      desc: '',
      args: [],
    );
  }

  /// `Wallet created!`
  String get walletCreated {
    return Intl.message(
      'Wallet created!',
      name: 'walletCreated',
      desc: '',
      args: [],
    );
  }

  /// `Here's your Onboard wallet address. Think of it like an 'account number' for receiving funds.`
  String get walletCreatedSubCopy {
    return Intl.message(
      'Here\'s your Onboard wallet address. Think of it like an \'account number\' for receiving funds.',
      name: 'walletCreatedSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Switch Network`
  String get switchNetwork {
    return Intl.message(
      'Switch Network',
      name: 'switchNetwork',
      desc: '',
      args: [],
    );
  }

  /// `Onboard Wallet wants to change the network`
  String get switchNetworkTitle {
    return Intl.message(
      'Onboard Wallet wants to change the network',
      name: 'switchNetworkTitle',
      desc: '',
      args: [],
    );
  }

  /// `This will switch the network within Onboard wallet to a previously added network`
  String get switchNetworkSubtitle {
    return Intl.message(
      'This will switch the network within Onboard wallet to a previously added network',
      name: 'switchNetworkSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `<EMAIL>`
  String get sampleEmail {
    return Intl.message(
      '<EMAIL>',
      name: 'sampleEmail',
      desc: '',
      args: [],
    );
  }

  /// `Please enter the code sent to`
  String get pleaseEnterTheCodeSentTo {
    return Intl.message(
      'Please enter the code sent to',
      name: 'pleaseEnterTheCodeSentTo',
      desc: '',
      args: [],
    );
  }

  /// `Change email`
  String get changeEmail {
    return Intl.message(
      'Change email',
      name: 'changeEmail',
      desc: '',
      args: [],
    );
  }

  /// `Didn’t receive code?`
  String get didntReceiveCode {
    return Intl.message(
      'Didn’t receive code?',
      name: 'didntReceiveCode',
      desc: '',
      args: [],
    );
  }

  /// `Resend`
  String get resend {
    return Intl.message('Resend', name: 'resend', desc: '', args: []);
  }

  /// `Want to trade?`
  String get wantToTrade {
    return Intl.message(
      'Want to trade?',
      name: 'wantToTrade',
      desc: '',
      args: [],
    );
  }

  /// `To buy assets, please switch to a supported network.`
  String get unsupportedNetworkSubCopy {
    return Intl.message(
      'To buy assets, please switch to a supported network.',
      name: 'unsupportedNetworkSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Resend in {time}`
  String resentCodeIn(Object time) {
    return Intl.message(
      'Resend in $time',
      name: 'resentCodeIn',
      desc: '',
      args: [time],
    );
  }

  /// `Buy, Sell and Swap Crypto with Ease`
  String get tradeIntroHeader {
    return Intl.message(
      'Buy, Sell and Swap Crypto with Ease',
      name: 'tradeIntroHeader',
      desc: '',
      args: [],
    );
  }

  /// `With Onboard Exchange®, we make it easy to go from crypto to your local currency, and back`
  String get tradeIntroSubCopy {
    return Intl.message(
      'With Onboard Exchange®, we make it easy to go from crypto to your local currency, and back',
      name: 'tradeIntroSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Express`
  String get express {
    return Intl.message('Express', name: 'express', desc: '', args: []);
  }

  /// `Welcome Onboard!`
  String get welcomeOnboard {
    return Intl.message(
      'Welcome Onboard!',
      name: 'welcomeOnboard',
      desc: '',
      args: [],
    );
  }

  /// `Received`
  String get received {
    return Intl.message('Received', name: 'received', desc: '', args: []);
  }

  /// `Skip`
  String get skip {
    return Intl.message('Skip', name: 'skip', desc: '', args: []);
  }

  /// `Swap`
  String get swap {
    return Intl.message('Swap', name: 'swap', desc: '', args: []);
  }

  /// `Secure wallet`
  String get secureWallet {
    return Intl.message(
      'Secure wallet',
      name: 'secureWallet',
      desc: '',
      args: [],
    );
  }

  /// `Enable biometric to safeguard your fund!`
  String get encourageBackup {
    return Intl.message(
      'Enable biometric to safeguard your fund!',
      name: 'encourageBackup',
      desc: '',
      args: [],
    );
  }

  /// `Verifying your Onboard Wallet `
  String get verifyingYourOnboardWallet {
    return Intl.message(
      'Verifying your Onboard Wallet ',
      name: 'verifyingYourOnboardWallet',
      desc: '',
      args: [],
    );
  }

  /// `Redirecting to Onboard\nExchange...`
  String get redirectingToOnboardExchange {
    return Intl.message(
      'Redirecting to Onboard\nExchange...',
      name: 'redirectingToOnboardExchange',
      desc: '',
      args: [],
    );
  }

  /// `Please authenticate to secure your wallet`
  String get pleaseAuthenticateToBackup {
    return Intl.message(
      'Please authenticate to secure your wallet',
      name: 'pleaseAuthenticateToBackup',
      desc: '',
      args: [],
    );
  }

  /// `Log out`
  String get logout {
    return Intl.message('Log out', name: 'logout', desc: '', args: []);
  }

  /// `You will be required to sign in and provide security next time `
  String get logoutSubCopy {
    return Intl.message(
      'You will be required to sign in and provide security next time ',
      name: 'logoutSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Unlock`
  String get unlock {
    return Intl.message('Unlock', name: 'unlock', desc: '', args: []);
  }

  /// `Send`
  String get send {
    return Intl.message('Send', name: 'send', desc: '', args: []);
  }

  /// `Receive`
  String get receive {
    return Intl.message('Receive', name: 'receive', desc: '', args: []);
  }

  /// `Don't show this again`
  String get dontShowThisAgain {
    return Intl.message(
      'Don\'t show this again',
      name: 'dontShowThisAgain',
      desc: '',
      args: [],
    );
  }

  /// `Buy`
  String get buy {
    return Intl.message('Buy', name: 'buy', desc: '', args: []);
  }

  /// `Data not provided`
  String get dataNotProvided {
    return Intl.message(
      'Data not provided',
      name: 'dataNotProvided',
      desc: '',
      args: [],
    );
  }

  /// `Chain Id not provided`
  String get chainIdNotProvided {
    return Intl.message(
      'Chain Id not provided',
      name: 'chainIdNotProvided',
      desc: '',
      args: [],
    );
  }

  /// `Failed to sign message`
  String get failedToSignMessage {
    return Intl.message(
      'Failed to sign message',
      name: 'failedToSignMessage',
      desc: '',
      args: [],
    );
  }

  /// `Sign`
  String get sign {
    return Intl.message('Sign', name: 'sign', desc: '', args: []);
  }

  /// `Sign to authenticate your wallet.`
  String get signToAuthenticateYourWallet {
    return Intl.message(
      'Sign to authenticate your wallet.',
      name: 'signToAuthenticateYourWallet',
      desc: '',
      args: [],
    );
  }

  /// `Chain not added`
  String get chainNotAdded {
    return Intl.message(
      'Chain not added',
      name: 'chainNotAdded',
      desc: '',
      args: [],
    );
  }

  /// `Welcome to Onboard!\n\nPlease sign this message to complete authentication. Your signature verifies that you’re the owner of this wallet.\n This request will not trigger a transaction or cost you anything.\n{timeStamp}`
  String authSignMessageCopy(Object timeStamp) {
    return Intl.message(
      'Welcome to Onboard!\n\nPlease sign this message to complete authentication. Your signature verifies that you’re the owner of this wallet.\n This request will not trigger a transaction or cost you anything.\n$timeStamp',
      name: 'authSignMessageCopy',
      desc: '',
      args: [timeStamp],
    );
  }

  /// `Sell`
  String get sell {
    return Intl.message('Sell', name: 'sell', desc: '', args: []);
  }

  /// `Network`
  String get network {
    return Intl.message('Network', name: 'network', desc: '', args: []);
  }

  /// `Selling`
  String get selling {
    return Intl.message('Selling', name: 'selling', desc: '', args: []);
  }

  /// `Address`
  String get address {
    return Intl.message('Address', name: 'address', desc: '', args: []);
  }

  /// `My Balance`
  String get myBalance {
    return Intl.message('My Balance', name: 'myBalance', desc: '', args: []);
  }

  /// `Copy`
  String get copy {
    return Intl.message('Copy', name: 'copy', desc: '', args: []);
  }

  /// `Copied`
  String get copied {
    return Intl.message('Copied', name: 'copied', desc: '', args: []);
  }

  /// `Profile`
  String get profile {
    return Intl.message('Profile', name: 'profile', desc: '', args: []);
  }

  /// `Payment method`
  String get paymentMethod {
    return Intl.message(
      'Payment method',
      name: 'paymentMethod',
      desc: '',
      args: [],
    );
  }

  /// `Notifications`
  String get notifications {
    return Intl.message(
      'Notifications',
      name: 'notifications',
      desc: '',
      args: [],
    );
  }

  /// `FAQs`
  String get faqs {
    return Intl.message('FAQs', name: 'faqs', desc: '', args: []);
  }

  /// `Home`
  String get home {
    return Intl.message('Home', name: 'home', desc: '', args: []);
  }

  /// `Ads`
  String get ads {
    return Intl.message('Ads', name: 'ads', desc: '', args: []);
  }

  /// `Resources`
  String get resources {
    return Intl.message('Resources', name: 'resources', desc: '', args: []);
  }

  /// `Leave Feedback`
  String get leaveFeedback {
    return Intl.message(
      'Leave Feedback',
      name: 'leaveFeedback',
      desc: '',
      args: [],
    );
  }

  /// `Your {credentialType} grants total access to your wallet. Never share it with\nanyone!`
  String securityWarning1(Object credentialType) {
    return Intl.message(
      'Your $credentialType grants total access to your wallet. Never share it with\nanyone!',
      name: 'securityWarning1',
      desc: '',
      args: [credentialType],
    );
  }

  /// `Onboard will never ask you to share your {credentialType}`
  String securityWarning2(Object credentialType) {
    return Intl.message(
      'Onboard will never ask you to share your $credentialType',
      name: 'securityWarning2',
      desc: '',
      args: [credentialType],
    );
  }

  /// `Your {credentialType} grants access to your entire wallet. Use securely!`
  String viewCredentialsWarning(Object credentialType) {
    return Intl.message(
      'Your $credentialType grants access to your entire wallet. Use securely!',
      name: 'viewCredentialsWarning',
      desc: '',
      args: [credentialType],
    );
  }

  /// `Proceed`
  String get proceed {
    return Intl.message('Proceed', name: 'proceed', desc: '', args: []);
  }

  /// `We’ll redirect you to `
  String get wellRedirectYouTo {
    return Intl.message(
      'We’ll redirect you to ',
      name: 'wellRedirectYouTo',
      desc: '',
      args: [],
    );
  }

  /// `Onboard Exchange®`
  String get onboardExchangeTM {
    return Intl.message(
      'Onboard Exchange®',
      name: 'onboardExchangeTM',
      desc: '',
      args: [],
    );
  }

  /// `Wallet Address`
  String get walletAddress {
    return Intl.message(
      'Wallet Address',
      name: 'walletAddress',
      desc: '',
      args: [],
    );
  }

  /// `I understand the risks`
  String get iUnderstandTheRisks {
    return Intl.message(
      'I understand the risks',
      name: 'iUnderstandTheRisks',
      desc: '',
      args: [],
    );
  }

  /// `Wallet security`
  String get walletSecurity {
    return Intl.message(
      'Wallet security',
      name: 'walletSecurity',
      desc: '',
      args: [],
    );
  }

  /// `Your wallet is now protected`
  String get backupCompleted {
    return Intl.message(
      'Your wallet is now protected',
      name: 'backupCompleted',
      desc: '',
      args: [],
    );
  }

  /// `Send to`
  String get sendTo {
    return Intl.message('Send to', name: 'sendTo', desc: '', args: []);
  }

  /// `Wallet info`
  String get walletInfo {
    return Intl.message('Wallet info', name: 'walletInfo', desc: '', args: []);
  }

  /// `General`
  String get general {
    return Intl.message('General', name: 'general', desc: '', args: []);
  }

  /// `From`
  String get from {
    return Intl.message('From', name: 'from', desc: '', args: []);
  }

  /// `To`
  String get to {
    return Intl.message('To', name: 'to', desc: '', args: []);
  }

  /// `Amount`
  String get amount {
    return Intl.message('Amount', name: 'amount', desc: '', args: []);
  }

  /// `Max`
  String get max {
    return Intl.message('Max', name: 'max', desc: '', args: []);
  }

  /// `Confirm`
  String get confirm {
    return Intl.message('Confirm', name: 'confirm', desc: '', args: []);
  }

  /// `Confidential`
  String get confidential {
    return Intl.message(
      'Confidential',
      name: 'confidential',
      desc: '',
      args: [],
    );
  }

  /// `Fee`
  String get fee {
    return Intl.message('Fee', name: 'fee', desc: '', args: []);
  }

  /// `Select Asset`
  String get selectAsset {
    return Intl.message(
      'Select Asset',
      name: 'selectAsset',
      desc: '',
      args: [],
    );
  }

  /// `Customer`
  String get customer {
    return Intl.message('Customer', name: 'customer', desc: '', args: []);
  }

  /// `Merchant`
  String get merchant {
    return Intl.message('Merchant', name: 'merchant', desc: '', args: []);
  }

  /// `Failed`
  String get failed {
    return Intl.message('Failed', name: 'failed', desc: '', args: []);
  }

  /// `Pending`
  String get pending {
    return Intl.message('Pending', name: 'pending', desc: '', args: []);
  }

  /// `Success`
  String get success {
    return Intl.message('Success', name: 'success', desc: '', args: []);
  }

  /// `Approve`
  String get approve {
    return Intl.message('Approve', name: 'approve', desc: '', args: []);
  }

  /// `Swap Tokens`
  String get swapTokens {
    return Intl.message('Swap Tokens', name: 'swapTokens', desc: '', args: []);
  }

  /// `Transactions`
  String get transactions {
    return Intl.message(
      'Transactions',
      name: 'transactions',
      desc: '',
      args: [],
    );
  }

  /// `No assets yet. Add crypto to get started`
  String get noTransactionsCopy {
    return Intl.message(
      'No assets yet. Add crypto to get started',
      name: 'noTransactionsCopy',
      desc: '',
      args: [],
    );
  }

  /// `Approving`
  String get approving {
    return Intl.message('Approving', name: 'approving', desc: '', args: []);
  }

  /// `Get started`
  String get getStarted {
    return Intl.message('Get started', name: 'getStarted', desc: '', args: []);
  }

  /// `Powered by`
  String get poweredBy {
    return Intl.message('Powered by', name: 'poweredBy', desc: '', args: []);
  }

  /// `Onboard Exchange`
  String get onboardExchange {
    return Intl.message(
      'Onboard Exchange',
      name: 'onboardExchange',
      desc: '',
      args: [],
    );
  }

  /// `Despite multiple attempts, we were unable to verify your identity with the details you provided.`
  String get kycMaxAttemptsCopy {
    return Intl.message(
      'Despite multiple attempts, we were unable to verify your identity with the details you provided.',
      name: 'kycMaxAttemptsCopy',
      desc: '',
      args: [],
    );
  }

  /// `Receive Crypto`
  String get receiveCrypto {
    return Intl.message(
      'Receive Crypto',
      name: 'receiveCrypto',
      desc: '',
      args: [],
    );
  }

  /// `Transaction Successful`
  String get transactionSuccessful {
    return Intl.message(
      'Transaction Successful',
      name: 'transactionSuccessful',
      desc: '',
      args: [],
    );
  }

  /// `Transaction failed`
  String get transactionFailed {
    return Intl.message(
      'Transaction failed',
      name: 'transactionFailed',
      desc: '',
      args: [],
    );
  }

  /// `Trade {token}`
  String tradeToken(Object token) {
    return Intl.message(
      'Trade $token',
      name: 'tradeToken',
      desc: '',
      args: [token],
    );
  }

  /// `Swapping`
  String get swapping {
    return Intl.message('Swapping', name: 'swapping', desc: '', args: []);
  }

  /// `Set up wallet protection`
  String get setupWalletProtection {
    return Intl.message(
      'Set up wallet protection',
      name: 'setupWalletProtection',
      desc: '',
      args: [],
    );
  }

  /// `Enable biometrics for login`
  String get enableBiometricForLogin {
    return Intl.message(
      'Enable biometrics for login',
      name: 'enableBiometricForLogin',
      desc: '',
      args: [],
    );
  }

  /// `Exchange`
  String get exchange {
    return Intl.message('Exchange', name: 'exchange', desc: '', args: []);
  }

  /// `Date`
  String get date {
    return Intl.message('Date', name: 'date', desc: '', args: []);
  }

  /// `Transaction ID`
  String get transactionId {
    return Intl.message(
      'Transaction ID',
      name: 'transactionId',
      desc: '',
      args: [],
    );
  }

  /// `My Assets`
  String get myAsset {
    return Intl.message('My Assets', name: 'myAsset', desc: '', args: []);
  }

  /// `You sell`
  String get youSell {
    return Intl.message('You sell', name: 'youSell', desc: '', args: []);
  }

  /// `Insufficient balance`
  String get insufficientBalance {
    return Intl.message(
      'Insufficient balance',
      name: 'insufficientBalance',
      desc: '',
      args: [],
    );
  }

  /// `Choose Network`
  String get chooseNetwork {
    return Intl.message(
      'Choose Network',
      name: 'chooseNetwork',
      desc: '',
      args: [],
    );
  }

  /// `Deposit {name}`
  String depositTokenName(Object name) {
    return Intl.message(
      'Deposit $name',
      name: 'depositTokenName',
      desc: '',
      args: [name],
    );
  }

  /// `Send only`
  String get sendOnly {
    return Intl.message('Send only', name: 'sendOnly', desc: '', args: []);
  }

  /// `to this deposit address to avoid losing your funds`
  String get toThisDepositAddressToAvoidLosingYourFunds {
    return Intl.message(
      'to this deposit address to avoid losing your funds',
      name: 'toThisDepositAddressToAvoidLosingYourFunds',
      desc: '',
      args: [],
    );
  }

  /// `My {network} Address`
  String myNetworkAddress(Object network) {
    return Intl.message(
      'My $network Address',
      name: 'myNetworkAddress',
      desc: '',
      args: [network],
    );
  }

  /// `Share`
  String get share {
    return Intl.message('Share', name: 'share', desc: '', args: []);
  }

  /// `Assets`
  String get assets {
    return Intl.message('Assets', name: 'assets', desc: '', args: []);
  }

  /// `Balance`
  String get balance {
    return Intl.message('Balance', name: 'balance', desc: '', args: []);
  }

  /// `Select Token`
  String get selectToken {
    return Intl.message(
      'Select Token',
      name: 'selectToken',
      desc: '',
      args: [],
    );
  }

  /// `Invalid Address`
  String get invalidAddress {
    return Intl.message(
      'Invalid Address',
      name: 'invalidAddress',
      desc: '',
      args: [],
    );
  }

  /// `Preview`
  String get preview {
    return Intl.message('Preview', name: 'preview', desc: '', args: []);
  }

  /// `Sending`
  String get sending {
    return Intl.message('Sending', name: 'sending', desc: '', args: []);
  }

  /// `Value`
  String get value {
    return Intl.message('Value', name: 'value', desc: '', args: []);
  }

  /// `Find Offers`
  String get findOffers {
    return Intl.message('Find Offers', name: 'findOffers', desc: '', args: []);
  }

  /// `Sent`
  String get sent {
    return Intl.message('Sent', name: 'sent', desc: '', args: []);
  }

  /// `You buy`
  String get youBuy {
    return Intl.message('You buy', name: 'youBuy', desc: '', args: []);
  }

  /// `It costs`
  String get itCosts {
    return Intl.message('It costs', name: 'itCosts', desc: '', args: []);
  }

  /// `We'll notify you when this transaction is completed`
  String get transactionSentSubtitle {
    return Intl.message(
      'We\'ll notify you when this transaction is completed',
      name: 'transactionSentSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Got it`
  String get gotIt {
    return Intl.message('Got it', name: 'gotIt', desc: '', args: []);
  }

  /// `Confirmed`
  String get confirmed {
    return Intl.message('Confirmed', name: 'confirmed', desc: '', args: []);
  }

  /// `Transaction sent`
  String get transactionSentTitle {
    return Intl.message(
      'Transaction sent',
      name: 'transactionSentTitle',
      desc: '',
      args: [],
    );
  }

  /// `Completed`
  String get completed {
    return Intl.message('Completed', name: 'completed', desc: '', args: []);
  }

  /// `Address Copied!`
  String get addressCopied {
    return Intl.message(
      'Address Copied!',
      name: 'addressCopied',
      desc: '',
      args: [],
    );
  }

  /// `App Version Copied!`
  String get appVersionCopied {
    return Intl.message(
      'App Version Copied!',
      name: 'appVersionCopied',
      desc: '',
      args: [],
    );
  }

  /// `Overview`
  String get overview {
    return Intl.message('Overview', name: 'overview', desc: '', args: []);
  }

  /// `Spot`
  String get spot {
    return Intl.message('Spot', name: 'spot', desc: '', args: []);
  }

  /// `Trading`
  String get trading {
    return Intl.message('Trading', name: 'trading', desc: '', args: []);
  }

  /// `Deposit`
  String get deposit {
    return Intl.message('Deposit', name: 'deposit', desc: '', args: []);
  }

  /// `Withdraw`
  String get withdraw {
    return Intl.message('Withdraw', name: 'withdraw', desc: '', args: []);
  }

  /// `Trade`
  String get trade {
    return Intl.message('Trade', name: 'trade', desc: '', args: []);
  }

  /// `Trading Wallet`
  String get tradingWallet {
    return Intl.message(
      'Trading Wallet',
      name: 'tradingWallet',
      desc: '',
      args: [],
    );
  }

  /// `Holds all your assets`
  String get spotWalletIntro {
    return Intl.message(
      'Holds all your assets',
      name: 'spotWalletIntro',
      desc: '',
      args: [],
    );
  }

  /// `Start high volume trades`
  String get tradingWalletIntro {
    return Intl.message(
      'Start high volume trades',
      name: 'tradingWalletIntro',
      desc: '',
      args: [],
    );
  }

  /// `My Wallets`
  String get myWallets {
    return Intl.message('My Wallets', name: 'myWallets', desc: '', args: []);
  }

  /// `Total Balance`
  String get totalBalance {
    return Intl.message(
      'Total Balance',
      name: 'totalBalance',
      desc: '',
      args: [],
    );
  }

  /// `Available Balance`
  String get availableBalance {
    return Intl.message(
      'Available Balance',
      name: 'availableBalance',
      desc: '',
      args: [],
    );
  }

  /// `Locked in orders`
  String get lockedInOrders {
    return Intl.message(
      'Locked in orders',
      name: 'lockedInOrders',
      desc: '',
      args: [],
    );
  }

  /// `Create ads, manage existing orders from customers`
  String get exchangeMerchantSubtitle {
    return Intl.message(
      'Create ads, manage existing orders from customers',
      name: 'exchangeMerchantSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Set Up`
  String get setup {
    return Intl.message('Set Up', name: 'setup', desc: '', args: []);
  }

  /// `Go from crypto to cash and \nback in minutes`
  String get moveToCryptoAndCash {
    return Intl.message(
      'Go from crypto to cash and \nback in minutes',
      name: 'moveToCryptoAndCash',
      desc: '',
      args: [],
    );
  }

  /// `Introducing Your \nMerchat Wallet`
  String get introducingYourWallet {
    return Intl.message(
      'Introducing Your \nMerchat Wallet',
      name: 'introducingYourWallet',
      desc: '',
      args: [],
    );
  }

  /// `Create Ads`
  String get createAds {
    return Intl.message('Create Ads', name: 'createAds', desc: '', args: []);
  }

  /// `Add custom token`
  String get addCustomToken {
    return Intl.message(
      'Add custom token',
      name: 'addCustomToken',
      desc: '',
      args: [],
    );
  }

  /// `Add custom tokens`
  String get addCustomTokens {
    return Intl.message(
      'Add custom tokens',
      name: 'addCustomTokens',
      desc: '',
      args: [],
    );
  }

  /// `Custom Tokens`
  String get customTokens {
    return Intl.message(
      'Custom Tokens',
      name: 'customTokens',
      desc: '',
      args: [],
    );
  }

  /// `Name`
  String get name {
    return Intl.message('Name', name: 'name', desc: '', args: []);
  }

  /// `Symbol`
  String get symbol {
    return Intl.message('Symbol', name: 'symbol', desc: '', args: []);
  }

  /// `Add`
  String get add {
    return Intl.message('Add', name: 'add', desc: '', args: []);
  }

  /// `Oops! This is an invalid {networkName}\ncontract address. Please check again`
  String invalidContractAddressCopy(Object networkName) {
    return Intl.message(
      'Oops! This is an invalid $networkName\ncontract address. Please check again',
      name: 'invalidContractAddressCopy',
      desc: '',
      args: [networkName],
    );
  }

  /// `When a token does not appear on your wallet, it can be manually added`
  String get customTokensSubCopy {
    return Intl.message(
      'When a token does not appear on your wallet, it can be manually added',
      name: 'customTokensSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Search`
  String get search {
    return Intl.message('Search', name: 'search', desc: '', args: []);
  }

  /// `Make profits with your merchant wallet by creating buy and sell ads.`
  String get createAdsIntro {
    return Intl.message(
      'Make profits with your merchant wallet by creating buy and sell ads.',
      name: 'createAdsIntro',
      desc: '',
      args: [],
    );
  }

  /// `Next`
  String get next {
    return Intl.message('Next', name: 'next', desc: '', args: []);
  }

  /// `See all`
  String get seeAll {
    return Intl.message('See all', name: 'seeAll', desc: '', args: []);
  }

  /// `Manage Ads`
  String get manageAds {
    return Intl.message('Manage Ads', name: 'manageAds', desc: '', args: []);
  }

  /// `Control the multiple ads you’ve created and see the funds you can withdraw anytime`
  String get manageAdsIntro {
    return Intl.message(
      'Control the multiple ads you’ve created and see the funds you can withdraw anytime',
      name: 'manageAdsIntro',
      desc: '',
      args: [],
    );
  }

  /// `Trade and Manage Assets`
  String get tradeAndManageAssets {
    return Intl.message(
      'Trade and Manage Assets',
      name: 'tradeAndManageAssets',
      desc: '',
      args: [],
    );
  }

  /// `Merchant wallet created!`
  String get tradingWalletCreated {
    return Intl.message(
      'Merchant wallet created!',
      name: 'tradingWalletCreated',
      desc: '',
      args: [],
    );
  }

  /// `You're all set to create ads. Start by funding your wallet.`
  String get walletCreatedMessage {
    return Intl.message(
      'You\'re all set to create ads. Start by funding your wallet.',
      name: 'walletCreatedMessage',
      desc: '',
      args: [],
    );
  }

  /// `My Merchant Wallet`
  String get myTradingWallet {
    return Intl.message(
      'My Merchant Wallet',
      name: 'myTradingWallet',
      desc: '',
      args: [],
    );
  }

  /// `Set up Wallet`
  String get setupWallet {
    return Intl.message(
      'Set up Wallet',
      name: 'setupWallet',
      desc: '',
      args: [],
    );
  }

  /// `We’re creating a merchant wallet for you. It’ll only take a few moments`
  String get creatingTradingWalletLoadingMessage {
    return Intl.message(
      'We’re creating a merchant wallet for you. It’ll only take a few moments',
      name: 'creatingTradingWalletLoadingMessage',
      desc: '',
      args: [],
    );
  }

  /// `Deposit funds`
  String get depositFunds {
    return Intl.message(
      'Deposit funds',
      name: 'depositFunds',
      desc: '',
      args: [],
    );
  }

  /// `Locked balance`
  String get lockedBalance {
    return Intl.message(
      'Locked balance',
      name: 'lockedBalance',
      desc: '',
      args: [],
    );
  }

  /// `This is the total value of your assets locked in your orders and ads. You’re unable to withdraw directly from this balance`
  String get lockedBalanceDescription {
    return Intl.message(
      'This is the total value of your assets locked in your orders and ads. You’re unable to withdraw directly from this balance',
      name: 'lockedBalanceDescription',
      desc: '',
      args: [],
    );
  }

  /// `The total amount of crypto you can use to create new ads. You can top up or withdraw from this balance anytime.`
  String get availableBalanceDescription {
    return Intl.message(
      'The total amount of crypto you can use to create new ads. You can top up or withdraw from this balance anytime.',
      name: 'availableBalanceDescription',
      desc: '',
      args: [],
    );
  }

  /// `Manage your ads`
  String get manageYourAds {
    return Intl.message(
      'Manage your ads',
      name: 'manageYourAds',
      desc: '',
      args: [],
    );
  }

  /// `Pro merchant tip`
  String get proTradingTips {
    return Intl.message(
      'Pro merchant tip',
      name: 'proTradingTips',
      desc: '',
      args: [],
    );
  }

  /// `We suggest funding your merchant wallet first before creating an ad.`
  String get proTradingTipSubCopy {
    return Intl.message(
      'We suggest funding your merchant wallet first before creating an ad.',
      name: 'proTradingTipSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Continue to Ads`
  String get continueToAds {
    return Intl.message(
      'Continue to Ads',
      name: 'continueToAds',
      desc: '',
      args: [],
    );
  }

  /// `Fund Wallet`
  String get fundWallet {
    return Intl.message('Fund Wallet', name: 'fundWallet', desc: '', args: []);
  }

  /// `Import successful!`
  String get importSuccessful {
    return Intl.message(
      'Import successful!',
      name: 'importSuccessful',
      desc: '',
      args: [],
    );
  }

  /// `Use a different wallet?`
  String get useDifferentWallet {
    return Intl.message(
      'Use a different wallet?',
      name: 'useDifferentWallet',
      desc: '',
      args: [],
    );
  }

  /// `Account {number}`
  String accountWithNumber(Object number) {
    return Intl.message(
      'Account $number',
      name: 'accountWithNumber',
      desc: '',
      args: [number],
    );
  }

  /// `Select wallet to Import`
  String get selectWalletToImport {
    return Intl.message(
      'Select wallet to Import',
      name: 'selectWalletToImport',
      desc: '',
      args: [],
    );
  }

  /// `These are accounts associated with your seedphrase.`
  String get associatedAccounts {
    return Intl.message(
      'These are accounts associated with your seedphrase.',
      name: 'associatedAccounts',
      desc: '',
      args: [],
    );
  }

  /// `Welcome back! 👋`
  String get welcomeBackWithEmoji {
    return Intl.message(
      'Welcome back! 👋',
      name: 'welcomeBackWithEmoji',
      desc: '',
      args: [],
    );
  }

  /// `Let’s get you back in. Verify your email to continue`
  String get letsGetYouBackIn {
    return Intl.message(
      'Let’s get you back in. Verify your email to continue',
      name: 'letsGetYouBackIn',
      desc: '',
      args: [],
    );
  }

  /// `Send to external wallet`
  String get sendToExternalWallet {
    return Intl.message(
      'Send to external wallet',
      name: 'sendToExternalWallet',
      desc: '',
      args: [],
    );
  }

  /// `Transfer between wallets on Onboard`
  String get transferBetweenOnboardWallet {
    return Intl.message(
      'Transfer between wallets on Onboard',
      name: 'transferBetweenOnboardWallet',
      desc: '',
      args: [],
    );
  }

  /// `Transfer {symbol}`
  String transferWithTokenSymbol(Object symbol) {
    return Intl.message(
      'Transfer $symbol',
      name: 'transferWithTokenSymbol',
      desc: '',
      args: [symbol],
    );
  }

  /// `Invalid Amount`
  String get invalidAmount {
    return Intl.message(
      'Invalid Amount',
      name: 'invalidAmount',
      desc: '',
      args: [],
    );
  }

  /// `Paste or scan address`
  String get pasteOrScanAddress {
    return Intl.message(
      'Paste or scan address',
      name: 'pasteOrScanAddress',
      desc: '',
      args: [],
    );
  }

  /// `Ensure the network matches the recipient address or your assets may be lost`
  String get ensureNetworkMatches {
    return Intl.message(
      'Ensure the network matches the recipient address or your assets may be lost',
      name: 'ensureNetworkMatches',
      desc: '',
      args: [],
    );
  }

  /// `Ensure that the address is correct and on the same network. Transactions cannot be reversed`
  String get ensureAddressIsCorrect {
    return Intl.message(
      'Ensure that the address is correct and on the same network. Transactions cannot be reversed',
      name: 'ensureAddressIsCorrect',
      desc: '',
      args: [],
    );
  }

  /// `Sending wallet`
  String get sendingWallet {
    return Intl.message(
      'Sending wallet',
      name: 'sendingWallet',
      desc: '',
      args: [],
    );
  }

  /// `This is the wallet where your assets will be sent out from`
  String get sendingWalletDescription {
    return Intl.message(
      'This is the wallet where your assets will be sent out from',
      name: 'sendingWalletDescription',
      desc: '',
      args: [],
    );
  }

  /// `For extra security, your trading wallet cannot be used for transfers to external wallets.`
  String get tradingWalletNotAllowedForExternalSend {
    return Intl.message(
      'For extra security, your trading wallet cannot be used for transfers to external wallets.',
      name: 'tradingWalletNotAllowedForExternalSend',
      desc: '',
      args: [],
    );
  }

  /// `Send from`
  String get sendFrom {
    return Intl.message('Send from', name: 'sendFrom', desc: '', args: []);
  }

  /// `Insufficient {BNB} balance to cover fees`
  String notEnoughToCoverFee(Object BNB) {
    return Intl.message(
      'Insufficient $BNB balance to cover fees',
      name: 'notEnoughToCoverFee',
      desc: '',
      args: [BNB],
    );
  }

  /// `Scan QR Code`
  String get scanQrcode {
    return Intl.message('Scan QR Code', name: 'scanQrcode', desc: '', args: []);
  }

  /// `Hey,\nWe'd like to confirm that its you! Your signature verifies that you're the owner of this wallet:`
  String get confirmItIsYou {
    return Intl.message(
      'Hey,\nWe\'d like to confirm that its you! Your signature verifies that you\'re the owner of this wallet:',
      name: 'confirmItIsYou',
      desc: '',
      args: [],
    );
  }

  /// `Tap confirm to sign this message.`
  String get confirmToSignMessage {
    return Intl.message(
      'Tap confirm to sign this message.',
      name: 'confirmToSignMessage',
      desc: '',
      args: [],
    );
  }

  /// `Not you?`
  String get notYou {
    return Intl.message('Not you?', name: 'notYou', desc: '', args: []);
  }

  /// `Confirming your Onboard Wallet`
  String get confirmingWallet {
    return Intl.message(
      'Confirming your Onboard Wallet',
      name: 'confirmingWallet',
      desc: '',
      args: [],
    );
  }

  /// `Enter Email`
  String get enterEmail {
    return Intl.message('Enter Email', name: 'enterEmail', desc: '', args: []);
  }

  /// `Confirm the email linked to your Onboard account and tap continue`
  String get confirmEmailUsedInOnboard {
    return Intl.message(
      'Confirm the email linked to your Onboard account and tap continue',
      name: 'confirmEmailUsedInOnboard',
      desc: '',
      args: [],
    );
  }

  /// `Welcome`
  String get welcome {
    return Intl.message('Welcome', name: 'welcome', desc: '', args: []);
  }

  /// `👋`
  String get waveEmoji {
    return Intl.message('👋', name: 'waveEmoji', desc: '', args: []);
  }

  /// `Version {appVersion} (Build {buildNumber})`
  String appVersion(Object appVersion, Object buildNumber) {
    return Intl.message(
      'Version $appVersion (Build $buildNumber)',
      name: 'appVersion',
      desc: '',
      args: [appVersion, buildNumber],
    );
  }

  /// `Wallet confirmed!`
  String get walletConfirmed {
    return Intl.message(
      'Wallet confirmed!',
      name: 'walletConfirmed',
      desc: '',
      args: [],
    );
  }

  /// `Delete account`
  String get deleteAccount {
    return Intl.message(
      'Delete account',
      name: 'deleteAccount',
      desc: '',
      args: [],
    );
  }

  /// `Please read the following requirements before deleting your Onboard account`
  String get deleteAccountWarning {
    return Intl.message(
      'Please read the following requirements before deleting your Onboard account',
      name: 'deleteAccountWarning',
      desc: '',
      args: [],
    );
  }

  /// `I meet these requirements`
  String get meetThisRequirement {
    return Intl.message(
      'I meet these requirements',
      name: 'meetThisRequirement',
      desc: '',
      args: [],
    );
  }

  /// `You will permanently lose all your profile information. Your wallet will remain usable once backed up`
  String get accountWillBeDeletedPermanently {
    return Intl.message(
      'You will permanently lose all your profile information. Your wallet will remain usable once backed up',
      name: 'accountWillBeDeletedPermanently',
      desc: '',
      args: [],
    );
  }

  /// `Yes, delete my account`
  String get yesDeleteMyAccount {
    return Intl.message(
      'Yes, delete my account',
      name: 'yesDeleteMyAccount',
      desc: '',
      args: [],
    );
  }

  /// `Deleting your Onboard Wallet`
  String get deletingYourOnboardWallet {
    return Intl.message(
      'Deleting your Onboard Wallet',
      name: 'deletingYourOnboardWallet',
      desc: '',
      args: [],
    );
  }

  /// `I've saved my private key somewhere. Failing to do this will lead me to loss of my funds forever.`
  String get savedPrivateKeySomewhere {
    return Intl.message(
      'I\'ve saved my private key somewhere. Failing to do this will lead me to loss of my funds forever.',
      name: 'savedPrivateKeySomewhere',
      desc: '',
      args: [],
    );
  }

  /// `I've saved my recovery phrase somewhere. Failing to do this will lead me to loss of my funds forever.`
  String get savedRecoveryKeySomewhere {
    return Intl.message(
      'I\'ve saved my recovery phrase somewhere. Failing to do this will lead me to loss of my funds forever.',
      name: 'savedRecoveryKeySomewhere',
      desc: '',
      args: [],
    );
  }

  /// `I have transferred funds from my trading wallet to my spot wallet to avoid losing them`
  String get haveTransferredFundFromTradingWallet {
    return Intl.message(
      'I have transferred funds from my trading wallet to my spot wallet to avoid losing them',
      name: 'haveTransferredFundFromTradingWallet',
      desc: '',
      args: [],
    );
  }

  /// `No`
  String get no {
    return Intl.message('No', name: 'no', desc: '', args: []);
  }

  /// `Confirm account deletion`
  String get confirmAccountDelete {
    return Intl.message(
      'Confirm account deletion',
      name: 'confirmAccountDelete',
      desc: '',
      args: [],
    );
  }

  /// `Yes, you’re the proud owner of this wallet address.`
  String get walletConfirmedSubCopy {
    return Intl.message(
      'Yes, you’re the proud owner of this wallet address.',
      name: 'walletConfirmedSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Think of it as an ‘account number’ for receiving funds `
  String get walletConfirmedSubCopy2 {
    return Intl.message(
      'Think of it as an ‘account number’ for receiving funds ',
      name: 'walletConfirmedSubCopy2',
      desc: '',
      args: [],
    );
  }

  /// `This email address is already linked to an existing account on Onboard. To proceed, please import the seed phrase of the wallet in use on that account`
  String get existingAccountSubCopy {
    return Intl.message(
      'This email address is already linked to an existing account on Onboard. To proceed, please import the seed phrase of the wallet in use on that account',
      name: 'existingAccountSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Passcode`
  String get passcode {
    return Intl.message('Passcode', name: 'passcode', desc: '', args: []);
  }

  /// `Passcode doesn't match`
  String get passcodeDoesntMatch {
    return Intl.message(
      'Passcode doesn\'t match',
      name: 'passcodeDoesntMatch',
      desc: '',
      args: [],
    );
  }

  /// `Security`
  String get security {
    return Intl.message('Security', name: 'security', desc: '', args: []);
  }

  /// `Change Passcode`
  String get changePasscode {
    return Intl.message(
      'Change Passcode',
      name: 'changePasscode',
      desc: '',
      args: [],
    );
  }

  /// `Unlock with Face ID`
  String get unlockWithFaceId {
    return Intl.message(
      'Unlock with Face ID',
      name: 'unlockWithFaceId',
      desc: '',
      args: [],
    );
  }

  /// `Unlock with Finger print`
  String get unlockWithFingerPrint {
    return Intl.message(
      'Unlock with Finger print',
      name: 'unlockWithFingerPrint',
      desc: '',
      args: [],
    );
  }

  /// `Incorrect passcode. Please try again`
  String get incorrectPasscode {
    return Intl.message(
      'Incorrect passcode. Please try again',
      name: 'incorrectPasscode',
      desc: '',
      args: [],
    );
  }

  /// `New passcode created`
  String get newPasscodeCreated {
    return Intl.message(
      'New passcode created',
      name: 'newPasscodeCreated',
      desc: '',
      args: [],
    );
  }

  /// `Passcode created 🎉`
  String get passcodeCreated {
    return Intl.message(
      'Passcode created 🎉',
      name: 'passcodeCreated',
      desc: '',
      args: [],
    );
  }

  /// `Passcode changed 🎉`
  String get passcodeChanged {
    return Intl.message(
      'Passcode changed 🎉',
      name: 'passcodeChanged',
      desc: '',
      args: [],
    );
  }

  /// `Created`
  String get created {
    return Intl.message('Created', name: 'created', desc: '', args: []);
  }

  /// `Create a new 6 digit passcode `
  String get createANewPasscode {
    return Intl.message(
      'Create a new 6 digit passcode ',
      name: 'createANewPasscode',
      desc: '',
      args: [],
    );
  }

  /// `Confirm your new 6 digit passcode`
  String get confirmYourNewPasscode {
    return Intl.message(
      'Confirm your new 6 digit passcode',
      name: 'confirmYourNewPasscode',
      desc: '',
      args: [],
    );
  }

  /// `Confirm your current 6 digit passcode`
  String get confirmYourCurrentPasscode {
    return Intl.message(
      'Confirm your current 6 digit passcode',
      name: 'confirmYourCurrentPasscode',
      desc: '',
      args: [],
    );
  }

  /// `Set a 6-digit passcode for login`
  String get secureAccountWithPasscode {
    return Intl.message(
      'Set a 6-digit passcode for login',
      name: 'secureAccountWithPasscode',
      desc: '',
      args: [],
    );
  }

  /// `Confirm your 6 digit passcode`
  String get confirmPasscode {
    return Intl.message(
      'Confirm your 6 digit passcode',
      name: 'confirmPasscode',
      desc: '',
      args: [],
    );
  }

  /// `Set passcode`
  String get setPasscode {
    return Intl.message(
      'Set passcode',
      name: 'setPasscode',
      desc: '',
      args: [],
    );
  }

  /// `Unlock with Passcode`
  String get unlockWithPasscode {
    return Intl.message(
      'Unlock with Passcode',
      name: 'unlockWithPasscode',
      desc: '',
      args: [],
    );
  }

  /// `Enter your passcode`
  String get enterYourPasscode {
    return Intl.message(
      'Enter your passcode',
      name: 'enterYourPasscode',
      desc: '',
      args: [],
    );
  }

  /// `Help center`
  String get helpCenter {
    return Intl.message('Help center', name: 'helpCenter', desc: '', args: []);
  }

  /// `Chat with us`
  String get chatWithUs {
    return Intl.message('Chat with us', name: 'chatWithUs', desc: '', args: []);
  }

  /// `Having issues? Reach out to us via our`
  String get havingIssuesReachOut {
    return Intl.message(
      'Having issues? Reach out to us via our',
      name: 'havingIssuesReachOut',
      desc: '',
      args: [],
    );
  }

  /// `Today`
  String get today {
    return Intl.message('Today', name: 'today', desc: '', args: []);
  }

  /// `Yesterday`
  String get yesterday {
    return Intl.message('Yesterday', name: 'yesterday', desc: '', args: []);
  }

  /// `Hide`
  String get hide {
    return Intl.message('Hide', name: 'hide', desc: '', args: []);
  }

  /// `Preferences`
  String get preferences {
    return Intl.message('Preferences', name: 'preferences', desc: '', args: []);
  }

  /// `Manage tokens`
  String get manageTokens {
    return Intl.message(
      'Manage tokens',
      name: 'manageTokens',
      desc: '',
      args: [],
    );
  }

  /// `Hide low reputation crypto`
  String get hideLowReputationTokens {
    return Intl.message(
      'Hide low reputation crypto',
      name: 'hideLowReputationTokens',
      desc: '',
      args: [],
    );
  }

  /// `Prevents random, spam crypto from showing up in your wallet`
  String get preventSpamToken {
    return Intl.message(
      'Prevents random, spam crypto from showing up in your wallet',
      name: 'preventSpamToken',
      desc: '',
      args: [],
    );
  }

  /// `All spam crypto hidden`
  String get allSpamTokenHidden {
    return Intl.message(
      'All spam crypto hidden',
      name: 'allSpamTokenHidden',
      desc: '',
      args: [],
    );
  }

  /// `Undo`
  String get undo {
    return Intl.message('Undo', name: 'undo', desc: '', args: []);
  }

  /// `Spam crypto hidden. Undo in settings`
  String get spamTokenHiddenUndoInSettings {
    return Intl.message(
      'Spam crypto hidden. Undo in settings',
      name: 'spamTokenHiddenUndoInSettings',
      desc: '',
      args: [],
    );
  }

  /// `This token has a low reputation`
  String get tokenHasLowReputation {
    return Intl.message(
      'This token has a low reputation',
      name: 'tokenHasLowReputation',
      desc: '',
      args: [],
    );
  }

  /// `Appears to be a spam crypto. Proceed with caution`
  String get appearToBeSpamToken {
    return Intl.message(
      'Appears to be a spam crypto. Proceed with caution',
      name: 'appearToBeSpamToken',
      desc: '',
      args: [],
    );
  }

  /// `Want to hide all crypto like this?`
  String get wantToHideSpamTokens {
    return Intl.message(
      'Want to hide all crypto like this?',
      name: 'wantToHideSpamTokens',
      desc: '',
      args: [],
    );
  }

  /// `{count, plural, =0{} =1{ 1 hour} other{ {count} hours}} `
  String hours(num count) {
    return Intl.message(
      '${Intl.plural(count, zero: '', one: ' 1 hour', other: ' $count hours')} ',
      name: 'hours',
      desc: '',
      args: [count],
    );
  }

  /// `{count, plural, =0{} =1{ 1 minute} other{ {count} minutes}} `
  String minutes(num count) {
    return Intl.message(
      '${Intl.plural(count, zero: '', one: ' 1 minute', other: ' $count minutes')} ',
      name: 'minutes',
      desc: '',
      args: [count],
    );
  }

  /// `{count, plural, =0{} =1{ 1 second} other{ {count} seconds}} `
  String seconds(num count) {
    return Intl.message(
      '${Intl.plural(count, zero: '', one: ' 1 second', other: ' $count seconds')} ',
      name: 'seconds',
      desc: '',
      args: [count],
    );
  }

  /// `Incorrect passcode. {count, plural, =0{} =1{1 attempt} other{{count} attempts}} left `
  String incorrectPasscodeXAttemptsLeft(num count) {
    return Intl.message(
      'Incorrect passcode. ${Intl.plural(count, zero: '', one: '1 attempt', other: '$count attempts')} left ',
      name: 'incorrectPasscodeXAttemptsLeft',
      desc: 'A plural message',
      args: [count],
    );
  }

  /// `You have {count, plural, =0{no retry} =1{1 retry} other{{count} retries}} left`
  String youHaveXRetriesLeft(num count) {
    return Intl.message(
      'You have ${Intl.plural(count, zero: 'no retry', one: '1 retry', other: '$count retries')} left',
      name: 'youHaveXRetriesLeft',
      desc: '',
      args: [count],
    );
  }

  /// `You have no more attempts. Logging out {count, plural, =0{} =1{in 1 sec..} other{in {count} secs..}}`
  String noAttemptsLeftLoggingOutInXSeconds(num count) {
    return Intl.message(
      'You have no more attempts. Logging out ${Intl.plural(count, zero: '', one: 'in 1 sec..', other: 'in $count secs..')}',
      name: 'noAttemptsLeftLoggingOutInXSeconds',
      desc: 'A plural message',
      args: [count],
    );
  }

  /// `Payment method, notifications`
  String get preferenceList {
    return Intl.message(
      'Payment method, notifications',
      name: 'preferenceList',
      desc: '',
      args: [],
    );
  }

  /// `Fund wallet`
  String get fundwallet {
    return Intl.message('Fund wallet', name: 'fundwallet', desc: '', args: []);
  }

  /// `Description`
  String get description {
    return Intl.message('Description', name: 'description', desc: '', args: []);
  }

  /// `Please confirm this\ntransaction`
  String get pleaseConfirmThisTransaction {
    return Intl.message(
      'Please confirm this\ntransaction',
      name: 'pleaseConfirmThisTransaction',
      desc: '',
      args: [],
    );
  }

  /// `Deny`
  String get deny {
    return Intl.message('Deny', name: 'deny', desc: '', args: []);
  }

  /// `Allow Transaction`
  String get allowTransaction {
    return Intl.message(
      'Allow Transaction',
      name: 'allowTransaction',
      desc: '',
      args: [],
    );
  }

  /// `You'll need to grant approval to spend your`
  String get youllNeedToGrantYourApproval {
    return Intl.message(
      'You\'ll need to grant approval to spend your',
      name: 'youllNeedToGrantYourApproval',
      desc: '',
      args: [],
    );
  }

  /// `Token approved. Complete your swap`
  String get tokenApprovedCompleteYourSwap {
    return Intl.message(
      'Token approved. Complete your swap',
      name: 'tokenApprovedCompleteYourSwap',
      desc: '',
      args: [],
    );
  }

  /// `before confirming swap`
  String get beforeConfirmingSwap {
    return Intl.message(
      'before confirming swap',
      name: 'beforeConfirmingSwap',
      desc: '',
      args: [],
    );
  }

  /// `Approving token`
  String get approvingToken {
    return Intl.message(
      'Approving token',
      name: 'approvingToken',
      desc: '',
      args: [],
    );
  }

  /// `Confirming swap`
  String get confirmingSwap {
    return Intl.message(
      'Confirming swap',
      name: 'confirmingSwap',
      desc: '',
      args: [],
    );
  }

  /// `To start trading and managing ads, please switch to a supported network`
  String get unsupportedTradingNetwork {
    return Intl.message(
      'To start trading and managing ads, please switch to a supported network',
      name: 'unsupportedTradingNetwork',
      desc: '',
      args: [],
    );
  }

  /// `One small thing,`
  String get oneSmallThing {
    return Intl.message(
      'One small thing,',
      name: 'oneSmallThing',
      desc: '',
      args: [],
    );
  }

  /// `Please provide your phone number - we'll only contact if you need us`
  String get phoneNumberViewSubCopy {
    return Intl.message(
      'Please provide your phone number - we\'ll only contact if you need us',
      name: 'phoneNumberViewSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Finish`
  String get finish {
    return Intl.message('Finish', name: 'finish', desc: '', args: []);
  }

  /// `Your data is encrypted`
  String get yourDataIsEncrypted {
    return Intl.message(
      'Your data is encrypted',
      name: 'yourDataIsEncrypted',
      desc: '',
      args: [],
    );
  }

  /// `Before you go`
  String get beforeYouGo {
    return Intl.message(
      'Before you go',
      name: 'beforeYouGo',
      desc: '',
      args: [],
    );
  }

  /// `Phone numbers give us a direct means of helping out if you run into any issues. Sure about this?`
  String get phoneSKipWarning {
    return Intl.message(
      'Phone numbers give us a direct means of helping out if you run into any issues. Sure about this?',
      name: 'phoneSKipWarning',
      desc: '',
      args: [],
    );
  }

  /// ` I'll pass`
  String get illPass {
    return Intl.message(' I\'ll pass', name: 'illPass', desc: '', args: []);
  }

  /// `Add number`
  String get addNumber {
    return Intl.message('Add number', name: 'addNumber', desc: '', args: []);
  }

  /// `Failed to fetch transaction`
  String get failedToGetTransaction {
    return Intl.message(
      'Failed to fetch transaction',
      name: 'failedToGetTransaction',
      desc: '',
      args: [],
    );
  }

  /// `Join our community`
  String get joinCommunity {
    return Intl.message(
      'Join our community',
      name: 'joinCommunity',
      desc: '',
      args: [],
    );
  }

  /// `Visit website`
  String get visitWebsite {
    return Intl.message(
      'Visit website',
      name: 'visitWebsite',
      desc: '',
      args: [],
    );
  }

  /// `Choose country`
  String get chooseCountry {
    return Intl.message(
      'Choose country',
      name: 'chooseCountry',
      desc: '',
      args: [],
    );
  }

  /// `Your ID may be required. `
  String get yourIdMayBeRequired {
    return Intl.message(
      'Your ID may be required. ',
      name: 'yourIdMayBeRequired',
      desc: '',
      args: [],
    );
  }

  /// `Why?`
  String get why {
    return Intl.message('Why?', name: 'why', desc: '', args: []);
  }

  /// `{appName} only requires ID Verification when buying or selling assets with local currencies. This is to deter bad actors from having access and using it for harmful reasons.`
  String kycInfoCopy(Object appName) {
    return Intl.message(
      '$appName only requires ID Verification when buying or selling assets with local currencies. This is to deter bad actors from having access and using it for harmful reasons.',
      name: 'kycInfoCopy',
      desc: '',
      args: [appName],
    );
  }

  /// `Token is now hidden`
  String get tokenIsNowHidden {
    return Intl.message(
      'Token is now hidden',
      name: 'tokenIsNowHidden',
      desc: '',
      args: [],
    );
  }

  /// `Introducing WalletConnect`
  String get introducingWalletConnection {
    return Intl.message(
      'Introducing WalletConnect',
      name: 'introducingWalletConnection',
      desc: '',
      args: [],
    );
  }

  /// `Interact with compatible apps`
  String get interactWithCompatibleApps {
    return Intl.message(
      'Interact with compatible apps',
      name: 'interactWithCompatibleApps',
      desc: '',
      args: [],
    );
  }

  /// `This feature enables your wallet to securely connect to a wide range of platforms and applications.`
  String get interactWithDappMessage {
    return Intl.message(
      'This feature enables your wallet to securely connect to a wide range of platforms and applications.',
      name: 'interactWithDappMessage',
      desc: '',
      args: [],
    );
  }

  /// `How to use wallet connect`
  String get howToUseWalletConnect {
    return Intl.message(
      'How to use wallet connect',
      name: 'howToUseWalletConnect',
      desc: '',
      args: [],
    );
  }

  /// `Just scan or copy it`
  String get justScanOrCopyIt {
    return Intl.message(
      'Just scan or copy it',
      name: 'justScanOrCopyIt',
      desc: '',
      args: [],
    );
  }

  /// `Look for the 'WalletConnect' icon on any compatible service. Scan or copy the QR code and you're connected!`
  String get howToConnectMessage {
    return Intl.message(
      'Look for the \'WalletConnect\' icon on any compatible service. Scan or copy the QR code and you\'re connected!',
      name: 'howToConnectMessage',
      desc: '',
      args: [],
    );
  }

  /// `New`
  String get newText {
    return Intl.message('New', name: 'newText', desc: '', args: []);
  }

  /// `See Connections`
  String get seeConnections {
    return Intl.message(
      'See Connections',
      name: 'seeConnections',
      desc: '',
      args: [],
    );
  }

  /// `Scan or copy QR Code`
  String get scanOrCopyQrCode {
    return Intl.message(
      'Scan or copy QR Code',
      name: 'scanOrCopyQrCode',
      desc: '',
      args: [],
    );
  }

  /// `{x} wants to connect to your wallet`
  String xWantToConnect(Object x) {
    return Intl.message(
      '$x wants to connect to your wallet',
      name: 'xWantToConnect',
      desc: '',
      args: [x],
    );
  }

  /// `Only connect to sites you trust`
  String get connectToOnlySiteYouTrust {
    return Intl.message(
      'Only connect to sites you trust',
      name: 'connectToOnlySiteYouTrust',
      desc: '',
      args: [],
    );
  }

  /// `Reject`
  String get reject {
    return Intl.message('Reject', name: 'reject', desc: '', args: []);
  }

  /// `Connect`
  String get connect {
    return Intl.message('Connect', name: 'connect', desc: '', args: []);
  }

  /// `Wallet connected`
  String get walletConnected {
    return Intl.message(
      'Wallet connected',
      name: 'walletConnected',
      desc: '',
      args: [],
    );
  }

  /// `Return to {Uniswap} to continue`
  String returnToConnectedToDapp(Object Uniswap) {
    return Intl.message(
      'Return to $Uniswap to continue',
      name: 'returnToConnectedToDapp',
      desc: '',
      args: [Uniswap],
    );
  }

  /// `Missing or invalid. URI is not WalletConnect URI`
  String get invalidWalletConnectUri {
    return Intl.message(
      'Missing or invalid. URI is not WalletConnect URI',
      name: 'invalidWalletConnectUri',
      desc: '',
      args: [],
    );
  }

  /// `Session ended`
  String get sessionEnded {
    return Intl.message(
      'Session ended',
      name: 'sessionEnded',
      desc: '',
      args: [],
    );
  }

  /// `Session error`
  String get sessionError {
    return Intl.message(
      'Session error',
      name: 'sessionError',
      desc: '',
      args: [],
    );
  }

  /// `Connected sites`
  String get connectedSites {
    return Intl.message(
      'Connected sites',
      name: 'connectedSites',
      desc: '',
      args: [],
    );
  }

  /// `Connected`
  String get connected {
    return Intl.message('Connected', name: 'connected', desc: '', args: []);
  }

  /// `Site connections`
  String get siteConnections {
    return Intl.message(
      'Site connections',
      name: 'siteConnections',
      desc: '',
      args: [],
    );
  }

  /// `This enables Onboard wallet and other sites to securely connect and interact by scanning or copying their QR code.`
  String get siteConnectionsText {
    return Intl.message(
      'This enables Onboard wallet and other sites to securely connect and interact by scanning or copying their QR code.',
      name: 'siteConnectionsText',
      desc: '',
      args: [],
    );
  }

  /// `Connection details`
  String get connectionDetails {
    return Intl.message(
      'Connection details',
      name: 'connectionDetails',
      desc: '',
      args: [],
    );
  }

  /// `Connected on`
  String get connectedOn {
    return Intl.message(
      'Connected on',
      name: 'connectedOn',
      desc: '',
      args: [],
    );
  }

  /// `Wallet disconnected`
  String get walletDisconnected {
    return Intl.message(
      'Wallet disconnected',
      name: 'walletDisconnected',
      desc: '',
      args: [],
    );
  }

  /// `Your wallet connections with other sites will appear here`
  String get emptyWalletConnectState {
    return Intl.message(
      'Your wallet connections with other sites will appear here',
      name: 'emptyWalletConnectState',
      desc: '',
      args: [],
    );
  }

  /// `Connected Wallet`
  String get connectedWallet {
    return Intl.message(
      'Connected Wallet',
      name: 'connectedWallet',
      desc: '',
      args: [],
    );
  }

  /// `Disconnect`
  String get disconnect {
    return Intl.message('Disconnect', name: 'disconnect', desc: '', args: []);
  }

  /// `Disconnect site`
  String get disconnectSite {
    return Intl.message(
      'Disconnect site',
      name: 'disconnectSite',
      desc: '',
      args: [],
    );
  }

  /// `Disconnecting will end your session with this site. You'll need to setup a new connection if you want to reconnect later`
  String get disconnectSiteWarning {
    return Intl.message(
      'Disconnecting will end your session with this site. You\'ll need to setup a new connection if you want to reconnect later',
      name: 'disconnectSiteWarning',
      desc: '',
      args: [],
    );
  }

  /// `Connecting may take a few seconds`
  String get connectingMayTakeLonger {
    return Intl.message(
      'Connecting may take a few seconds',
      name: 'connectingMayTakeLonger',
      desc: '',
      args: [],
    );
  }

  /// `Connection failed. Refresh the Dapp`
  String get connectionFailedRefreshDapp {
    return Intl.message(
      'Connection failed. Refresh the Dapp',
      name: 'connectionFailedRefreshDapp',
      desc: '',
      args: [],
    );
  }

  /// `Type manually`
  String get typeManually {
    return Intl.message(
      'Type manually',
      name: 'typeManually',
      desc: '',
      args: [],
    );
  }

  /// `Paste your recovery phrase`
  String get pasteYourRecoveryPhrase {
    return Intl.message(
      'Paste your recovery phrase',
      name: 'pasteYourRecoveryPhrase',
      desc: '',
      args: [],
    );
  }

  /// `Clear`
  String get clear {
    return Intl.message('Clear', name: 'clear', desc: '', args: []);
  }

  /// `Import my wallet`
  String get importMyWallet {
    return Intl.message(
      'Import my wallet',
      name: 'importMyWallet',
      desc: '',
      args: [],
    );
  }

  /// `Enter your recovery phrase`
  String get enterYourRecoveryPhrase {
    return Intl.message(
      'Enter your recovery phrase',
      name: 'enterYourRecoveryPhrase',
      desc: '',
      args: [],
    );
  }

  /// `Oops! You’ve got it wrong`
  String get oopsYouveGotItWrong {
    return Intl.message(
      'Oops! You’ve got it wrong',
      name: 'oopsYouveGotItWrong',
      desc: '',
      args: [],
    );
  }

  /// `Edit manually`
  String get editManually {
    return Intl.message(
      'Edit manually',
      name: 'editManually',
      desc: '',
      args: [],
    );
  }

  /// `Heads up!`
  String get headsUp {
    return Intl.message('Heads up!', name: 'headsUp', desc: '', args: []);
  }

  /// `Edits here will only alter what you see on the Onboard app and not your subscription with the merchant`
  String
  get editsHereWillOnlyAlterWhatYouSeeOnTheOnboardAppAndNotYourSubscriptionWithTheMerchant {
    return Intl.message(
      'Edits here will only alter what you see on the Onboard app and not your subscription with the merchant',
      name:
          'editsHereWillOnlyAlterWhatYouSeeOnTheOnboardAppAndNotYourSubscriptionWithTheMerchant',
      desc: '',
      args: [],
    );
  }

  /// `This will exit the Exchange. You'll be taken back to your Wallet, but any ongoing transactions will continue as normal.`
  String get exitingExchangeWarningSubCopy {
    return Intl.message(
      'This will exit the Exchange. You\'ll be taken back to your Wallet, but any ongoing transactions will continue as normal.',
      name: 'exitingExchangeWarningSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `App update available!`
  String get appUpdateAvailable {
    return Intl.message(
      'App update available!',
      name: 'appUpdateAvailable',
      desc: '',
      args: [],
    );
  }

  /// `Is this a recurring payment?`
  String get isThisARecurringPayment {
    return Intl.message(
      'Is this a recurring payment?',
      name: 'isThisARecurringPayment',
      desc: '',
      args: [],
    );
  }

  /// `Mark as a subscription`
  String get markAsASubscription {
    return Intl.message(
      'Mark as a subscription',
      name: 'markAsASubscription',
      desc: '',
      args: [],
    );
  }

  /// `Your app version is out of date.\nUpdate to get the best of\nOnboard `
  String get appUpdateSubcopy {
    return Intl.message(
      'Your app version is out of date.\nUpdate to get the best of\nOnboard ',
      name: 'appUpdateSubcopy',
      desc: '',
      args: [],
    );
  }

  /// `Update App`
  String get updateApp {
    return Intl.message('Update App', name: 'updateApp', desc: '', args: []);
  }

  /// `Yes, exit the exchange`
  String get yesExitTheExchange {
    return Intl.message(
      'Yes, exit the exchange',
      name: 'yesExitTheExchange',
      desc: '',
      args: [],
    );
  }

  /// `Switch between various crypto networks to view your tokens held on each network`
  String get switchNetworkSubCopy {
    return Intl.message(
      'Switch between various crypto networks to view your tokens held on each network',
      name: 'switchNetworkSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Setting up your wallet  🎉`
  String get settingUpYourWallet {
    return Intl.message(
      'Setting up your wallet  🎉',
      name: 'settingUpYourWallet',
      desc: '',
      args: [],
    );
  }

  /// `Creating your private key  🔑`
  String get creatingYourPrivateKey {
    return Intl.message(
      'Creating your private key  🔑',
      name: 'creatingYourPrivateKey',
      desc: '',
      args: [],
    );
  }

  /// `Assigning you full custody 🔐`
  String get assigningYouFullCustody {
    return Intl.message(
      'Assigning you full custody 🔐',
      name: 'assigningYouFullCustody',
      desc: '',
      args: [],
    );
  }

  /// `Here’s your wallet!`
  String get hereIsYourWallet {
    return Intl.message(
      'Here’s your wallet!',
      name: 'hereIsYourWallet',
      desc: '',
      args: [],
    );
  }

  /// `Backup your wallet`
  String get backUpYourWallet {
    return Intl.message(
      'Backup your wallet',
      name: 'backUpYourWallet',
      desc: '',
      args: [],
    );
  }

  /// `No results found`
  String get noResultsFound {
    return Intl.message(
      'No results found',
      name: 'noResultsFound',
      desc: '',
      args: [],
    );
  }

  /// `Fund wallet with your local currency`
  String get fundWalletWithYourLocalCurrency {
    return Intl.message(
      'Fund wallet with your local currency',
      name: 'fundWalletWithYourLocalCurrency',
      desc: '',
      args: [],
    );
  }

  /// `No assets yet. Add crypto to get started`
  String get noAssetsSubCopy {
    return Intl.message(
      'No assets yet. Add crypto to get started',
      name: 'noAssetsSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `This connection will view your wallet balance, activity and request approval for transactions`
  String get walletConnectWarning {
    return Intl.message(
      'This connection will view your wallet balance, activity and request approval for transactions',
      name: 'walletConnectWarning',
      desc: '',
      args: [],
    );
  }

  /// `Application`
  String get application {
    return Intl.message('Application', name: 'application', desc: '', args: []);
  }

  /// `Sign transaction`
  String get signTransaction {
    return Intl.message(
      'Sign transaction',
      name: 'signTransaction',
      desc: '',
      args: [],
    );
  }

  /// `Looks like we ran into a temporary issue connecting this account. Please try again!`
  String get web3AuthFailureCopy {
    return Intl.message(
      'Looks like we ran into a temporary issue connecting this account. Please try again!',
      name: 'web3AuthFailureCopy',
      desc: '',
      args: [],
    );
  }

  /// `Cards`
  String get cards {
    return Intl.message('Cards', name: 'cards', desc: '', args: []);
  }

  /// `Card`
  String get card {
    return Intl.message('Card', name: 'card', desc: '', args: []);
  }

  /// `Features for you`
  String get featuresForYou {
    return Intl.message(
      'Features for you',
      name: 'featuresForYou',
      desc: '',
      args: [],
    );
  }

  /// `Add to Apple or Google pay`
  String get addToAppleOrGooglePay {
    return Intl.message(
      'Add to Apple or Google pay',
      name: 'addToAppleOrGooglePay',
      desc: '',
      args: [],
    );
  }

  /// `Set up card`
  String get setUpCard {
    return Intl.message('Set up card', name: 'setUpCard', desc: '', args: []);
  }

  /// `Fast, affordable funding`
  String get fastAffordableFunding {
    return Intl.message(
      'Fast, affordable funding',
      name: 'fastAffordableFunding',
      desc: '',
      args: [],
    );
  }

  /// `Accepted globally, online`
  String get acceptedGlobalFunding {
    return Intl.message(
      'Accepted globally, online',
      name: 'acceptedGlobalFunding',
      desc: '',
      args: [],
    );
  }

  /// `Spend digital dollars anywhere with virtual cards`
  String get spendDollarAnywhere {
    return Intl.message(
      'Spend digital dollars anywhere with virtual cards',
      name: 'spendDollarAnywhere',
      desc: '',
      args: [],
    );
  }

  /// `Not available in your\ncountry`
  String get notAvailableInYourCountry {
    return Intl.message(
      'Not available in your\ncountry',
      name: 'notAvailableInYourCountry',
      desc: '',
      args: [],
    );
  }

  /// `Notify me`
  String get notifyMe {
    return Intl.message('Notify me', name: 'notifyMe', desc: '', args: []);
  }

  /// `Paste from clipboard`
  String get pasteFromClipboard {
    return Intl.message(
      'Paste from clipboard',
      name: 'pasteFromClipboard',
      desc: '',
      args: [],
    );
  }

  /// `We don't support cards in your location just yet, but we can reach out when we do, if you'd like that.`
  String get notAvailableInYourCountrySubCopy {
    return Intl.message(
      'We don\'t support cards in your location just yet, but we can reach out when we do, if you\'d like that.',
      name: 'notAvailableInYourCountrySubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Country of residence`
  String get countryOfResidence {
    return Intl.message(
      'Country of residence',
      name: 'countryOfResidence',
      desc: '',
      args: [],
    );
  }

  /// `Country`
  String get country {
    return Intl.message('Country', name: 'country', desc: '', args: []);
  }

  /// `Heads up, we’ll redirect you to an ID\nverification service.`
  String get kycRedirectCopy {
    return Intl.message(
      'Heads up, we’ll redirect you to an ID\nverification service.',
      name: 'kycRedirectCopy',
      desc: '',
      args: [],
    );
  }

  /// `Identity is verified`
  String get identityIsVerified {
    return Intl.message(
      'Identity is verified',
      name: 'identityIsVerified',
      desc: '',
      args: [],
    );
  }

  /// `Your details were successfully verified! Now, you're good to go.`
  String get kycVerifiedSubCopy {
    return Intl.message(
      'Your details were successfully verified! Now, you\'re good to go.',
      name: 'kycVerifiedSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Verification is in progress`
  String get verificationIsInProgress {
    return Intl.message(
      'Verification is in progress',
      name: 'verificationIsInProgress',
      desc: '',
      args: [],
    );
  }

  /// `Turn on notifications`
  String get turnOnNotifications {
    return Intl.message(
      'Turn on notifications',
      name: 'turnOnNotifications',
      desc: '',
      args: [],
    );
  }

  /// `You’ll be notified`
  String get youllBeNotified {
    return Intl.message(
      'You’ll be notified',
      name: 'youllBeNotified',
      desc: '',
      args: [],
    );
  }

  /// `Verification failed`
  String get verificationFailed {
    return Intl.message(
      'Verification failed',
      name: 'verificationFailed',
      desc: '',
      args: [],
    );
  }

  /// `Verify me again`
  String get verifyMeAgain {
    return Intl.message(
      'Verify me again',
      name: 'verifyMeAgain',
      desc: '',
      args: [],
    );
  }

  /// `We apologize for the failed KYC verification and any inconvenience it may have caused during your onboarding process.`
  String get startKycFailedCopy {
    return Intl.message(
      'We apologize for the failed KYC verification and any inconvenience it may have caused during your onboarding process.',
      name: 'startKycFailedCopy',
      desc: '',
      args: [],
    );
  }

  /// `Turn on notifications and we’ll update you on the\nstatus of your verification.`
  String get kycInProgressNotificationsCopy {
    return Intl.message(
      'Turn on notifications and we’ll update you on the\nstatus of your verification.',
      name: 'kycInProgressNotificationsCopy',
      desc: '',
      args: [],
    );
  }

  /// `Select funding method`
  String get selectFundingMethod {
    return Intl.message(
      'Select funding method',
      name: 'selectFundingMethod',
      desc: '',
      args: [],
    );
  }

  /// `Cash transfer`
  String get cashTransfer {
    return Intl.message(
      'Cash transfer',
      name: 'cashTransfer',
      desc: '',
      args: [],
    );
  }

  /// `Pay with Wallet Balance`
  String get payWithWalletBalance {
    return Intl.message(
      'Pay with Wallet Balance',
      name: 'payWithWalletBalance',
      desc: '',
      args: [],
    );
  }

  /// `Fund using your local currency`
  String get fundUsingYourLocalCurrency {
    return Intl.message(
      'Fund using your local currency',
      name: 'fundUsingYourLocalCurrency',
      desc: '',
      args: [],
    );
  }

  /// `External wallet`
  String get externalWallet {
    return Intl.message(
      'External wallet',
      name: 'externalWallet',
      desc: '',
      args: [],
    );
  }

  /// `Deposit funds from an external wallet`
  String get depositFundsFromExternalWallet {
    return Intl.message(
      'Deposit funds from an external wallet',
      name: 'depositFundsFromExternalWallet',
      desc: '',
      args: [],
    );
  }

  /// `asset`
  String get asset {
    return Intl.message('asset', name: 'asset', desc: '', args: []);
  }

  /// `Select`
  String get select {
    return Intl.message('Select', name: 'select', desc: '', args: []);
  }

  /// `Fund your card with your wallet balance`
  String get fundCardSelectAssetSubCopy {
    return Intl.message(
      'Fund your card with your wallet balance',
      name: 'fundCardSelectAssetSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Fund your account with any of the supported \nstablecoins`
  String get fundDaSelectAssetSubCopy {
    return Intl.message(
      'Fund your account with any of the supported \nstablecoins',
      name: 'fundDaSelectAssetSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Fund card`
  String get fundCard {
    return Intl.message('Fund card', name: 'fundCard', desc: '', args: []);
  }

  /// `Total`
  String get total {
    return Intl.message('Total', name: 'total', desc: '', args: []);
  }

  /// `Card creation`
  String get cardCreation {
    return Intl.message(
      'Card creation',
      name: 'cardCreation',
      desc: '',
      args: [],
    );
  }

  /// `Easy global payments`
  String get easyGlobalPayments {
    return Intl.message(
      'Easy global payments',
      name: 'easyGlobalPayments',
      desc: '',
      args: [],
    );
  }

  /// `Make fast payments online and in-store, anywhere in the world, with your own {currency} virtual card.`
  String cardIntroVirtualCardsSubCopy(Object currency) {
    return Intl.message(
      'Make fast payments online and in-store, anywhere in the world, with your own $currency virtual card.',
      name: 'cardIntroVirtualCardsSubCopy',
      desc: '',
      args: [currency],
    );
  }

  /// `Processing fee`
  String get processingFee {
    return Intl.message(
      'Processing fee',
      name: 'processingFee',
      desc: '',
      args: [],
    );
  }

  /// `You will pay`
  String get youWillPay {
    return Intl.message('You will pay', name: 'youWillPay', desc: '', args: []);
  }

  /// `Funding your card `
  String get fundingYourCard {
    return Intl.message(
      'Funding your card ',
      name: 'fundingYourCard',
      desc: '',
      args: [],
    );
  }

  /// `You pay`
  String get youPay {
    return Intl.message('You pay', name: 'youPay', desc: '', args: []);
  }

  /// `Virtual card`
  String get virtualCard {
    return Intl.message(
      'Virtual card',
      name: 'virtualCard',
      desc: '',
      args: [],
    );
  }

  /// `Fund your card `
  String get fundYourCard {
    return Intl.message(
      'Fund your card ',
      name: 'fundYourCard',
      desc: '',
      args: [],
    );
  }

  /// `Spend your dollars\nanywhere`
  String get spendYourDollarsAnywhere {
    return Intl.message(
      'Spend your dollars\nanywhere',
      name: 'spendYourDollarsAnywhere',
      desc: '',
      args: [],
    );
  }

  /// `Fund your card anytime using your local currency or crypto, at the best possible rates.`
  String get spendYourDollarsAnywhereSubCopy {
    return Intl.message(
      'Fund your card anytime using your local currency or crypto, at the best possible rates.',
      name: 'spendYourDollarsAnywhereSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Card Request Successful`
  String get cardRequestSuccessful {
    return Intl.message(
      'Card Request Successful',
      name: 'cardRequestSuccessful',
      desc: '',
      args: [],
    );
  }

  /// `Confirming your transaction..`
  String get confirmingYourTransaction {
    return Intl.message(
      'Confirming your transaction..',
      name: 'confirmingYourTransaction',
      desc: '',
      args: [],
    );
  }

  /// `We'll keep checking and let you know once received. You can leave this page anytime.`
  String get confirmingTransactionSubCopy {
    return Intl.message(
      'We\'ll keep checking and let you know once received. You can leave this page anytime.',
      name: 'confirmingTransactionSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Minimum`
  String get minimum {
    return Intl.message('Minimum', name: 'minimum', desc: '', args: []);
  }

  /// `Funding amount too low`
  String get fundingAmountTooLow {
    return Intl.message(
      'Funding amount too low',
      name: 'fundingAmountTooLow',
      desc: '',
      args: [],
    );
  }

  /// `You're all done! We'll notify you soon once your card is activated and ready for use.`
  String get cardRequestSuccessSubCopy {
    return Intl.message(
      'You\'re all done! We\'ll notify you soon once your card is activated and ready for use.',
      name: 'cardRequestSuccessSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Redirecting to 3rd party ID service to complete your verification... `
  String get kycLoaderMessage {
    return Intl.message(
      'Redirecting to 3rd party ID service to complete your verification... ',
      name: 'kycLoaderMessage',
      desc: '',
      args: [],
    );
  }

  /// `We’ll update you on the status of your verification.`
  String get wellUpdateYouOnTheStatusOfYourVerification {
    return Intl.message(
      'We’ll update you on the status of your verification.',
      name: 'wellUpdateYouOnTheStatusOfYourVerification',
      desc: '',
      args: [],
    );
  }

  /// `Activate your {currency} card`
  String activateUSDCard(Object currency) {
    return Intl.message(
      'Activate your $currency card',
      name: 'activateUSDCard',
      desc: '',
      args: [currency],
    );
  }

  /// `You're one step closer to getting your first Onboard debit card!`
  String get stepCloserToGettingCard {
    return Intl.message(
      'You\'re one step closer to getting your first Onboard debit card!',
      name: 'stepCloserToGettingCard',
      desc: '',
      args: [],
    );
  }

  /// `Details`
  String get details {
    return Intl.message('Details', name: 'details', desc: '', args: []);
  }

  /// `Card name`
  String get cardName {
    return Intl.message('Card name', name: 'cardName', desc: '', args: []);
  }

  /// `Card type`
  String get cardType {
    return Intl.message('Card type', name: 'cardType', desc: '', args: []);
  }

  /// `Card creation fee`
  String get cardCreationFee {
    return Intl.message(
      'Card creation fee',
      name: 'cardCreationFee',
      desc: '',
      args: [],
    );
  }

  /// `A one-time fee to create your card`
  String get oneTimeFeeToCreateCard {
    return Intl.message(
      'A one-time fee to create your card',
      name: 'oneTimeFeeToCreateCard',
      desc: '',
      args: [],
    );
  }

  /// `Visa virtual`
  String get visaVirtual {
    return Intl.message(
      'Visa virtual',
      name: 'visaVirtual',
      desc: '',
      args: [],
    );
  }

  /// `View card usage terms`
  String get viewCardUsageTerms {
    return Intl.message(
      'View card usage terms',
      name: 'viewCardUsageTerms',
      desc: '',
      args: [],
    );
  }

  /// `Card usage terms`
  String get cardUsageAndTerms {
    return Intl.message(
      'Card usage terms',
      name: 'cardUsageAndTerms',
      desc: '',
      args: [],
    );
  }

  /// `Please read and accept the following to use this card.`
  String get pleaseReadAndAccept {
    return Intl.message(
      'Please read and accept the following to use this card.',
      name: 'pleaseReadAndAccept',
      desc: '',
      args: [],
    );
  }

  /// `Add card to Apple / Google Pay`
  String get addCardAppleGooglePay {
    return Intl.message(
      'Add card to Apple / Google Pay',
      name: 'addCardAppleGooglePay',
      desc: '',
      args: [],
    );
  }

  /// `When added, your card can be used for contactless payments`
  String get addCardAppleGooglePaySubCopy {
    return Intl.message(
      'When added, your card can be used for contactless payments',
      name: 'addCardAppleGooglePaySubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Minimum card balance`
  String get minimumCardBalance {
    return Intl.message(
      'Minimum card balance',
      name: 'minimumCardBalance',
      desc: '',
      args: [],
    );
  }

  /// `Keep your card funded with at least this amount`
  String get keepYourCardFundedWithAtLeast {
    return Intl.message(
      'Keep your card funded with at least this amount',
      name: 'keepYourCardFundedWithAtLeast',
      desc: '',
      args: [],
    );
  }

  /// `Fees`
  String get fees {
    return Intl.message('Fees', name: 'fees', desc: '', args: []);
  }

  /// `Insufficient fund charge`
  String get insufficientFundCharge {
    return Intl.message(
      'Insufficient fund charge',
      name: 'insufficientFundCharge',
      desc: '',
      args: [],
    );
  }

  /// `A charge may apply each time a card transaction fails due to not being funded`
  String get insufficientFundChargeSubCopy {
    return Intl.message(
      'A charge may apply each time a card transaction fails due to not being funded',
      name: 'insufficientFundChargeSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Monthly maintenance`
  String get monthlyMaintenance {
    return Intl.message(
      'Monthly maintenance',
      name: 'monthlyMaintenance',
      desc: '',
      args: [],
    );
  }

  /// `This helps keep your card functioning at all times`
  String get monthlyMaintenanceSubCopy {
    return Intl.message(
      'This helps keep your card functioning at all times',
      name: 'monthlyMaintenanceSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Transaction limits`
  String get transactionLimit {
    return Intl.message(
      'Transaction limits',
      name: 'transactionLimit',
      desc: '',
      args: [],
    );
  }

  /// `Max. daily spend`
  String get maxDailySpend {
    return Intl.message(
      'Max. daily spend',
      name: 'maxDailySpend',
      desc: '',
      args: [],
    );
  }

  /// `Max. monthly spend`
  String get maxMonthlySpend {
    return Intl.message(
      'Max. monthly spend',
      name: 'maxMonthlySpend',
      desc: '',
      args: [],
    );
  }

  /// `I accept`
  String get iAccept {
    return Intl.message('I accept', name: 'iAccept', desc: '', args: []);
  }

  /// `Pay with External Wallet`
  String get payWithExternalWallet {
    return Intl.message(
      'Pay with External Wallet',
      name: 'payWithExternalWallet',
      desc: '',
      args: [],
    );
  }

  /// `Pay`
  String get pay {
    return Intl.message('Pay', name: 'pay', desc: '', args: []);
  }

  /// `Send at least`
  String get sendAtLeast {
    return Intl.message(
      'Send at least',
      name: 'sendAtLeast',
      desc: '',
      args: [],
    );
  }

  /// `My virtual card address`
  String get myVirtualCardAddress {
    return Intl.message(
      'My virtual card address',
      name: 'myVirtualCardAddress',
      desc: '',
      args: [],
    );
  }

  /// `QR code`
  String get qrCode {
    return Intl.message('QR code', name: 'qrCode', desc: '', args: []);
  }

  /// `I have made exact payment`
  String get iHaveMadeExactPayment {
    return Intl.message(
      'I have made exact payment',
      name: 'iHaveMadeExactPayment',
      desc: '',
      args: [],
    );
  }

  /// `Card address copied`
  String get cardAddressCopied {
    return Intl.message(
      'Card address copied',
      name: 'cardAddressCopied',
      desc: '',
      args: [],
    );
  }

  /// `Yes, cancel`
  String get yesCancel {
    return Intl.message('Yes, cancel', name: 'yesCancel', desc: '', args: []);
  }

  /// `Pay with Cash Transfer`
  String get payWithCashTransfer {
    return Intl.message(
      'Pay with Cash Transfer',
      name: 'payWithCashTransfer',
      desc: '',
      args: [],
    );
  }

  /// `Please only cancel this payment if you have not yet initiated a transfer.`
  String get cancelPaymentSubCopy {
    return Intl.message(
      'Please only cancel this payment if you have not yet initiated a transfer.',
      name: 'cancelPaymentSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Amount copied`
  String get amountCopied {
    return Intl.message(
      'Amount copied',
      name: 'amountCopied',
      desc: '',
      args: [],
    );
  }

  /// `This covers the cost of your transaction - primarily for currency conversion and transaction fees.`
  String get processingFeeInfoCopy {
    return Intl.message(
      'This covers the cost of your transaction - primarily for currency conversion and transaction fees.',
      name: 'processingFeeInfoCopy',
      desc: '',
      args: [],
    );
  }

  /// `Cancel payment`
  String get cancelPayment {
    return Intl.message(
      'Cancel payment',
      name: 'cancelPayment',
      desc: '',
      args: [],
    );
  }

  /// `Card status`
  String get cardStatus {
    return Intl.message('Card status', name: 'cardStatus', desc: '', args: []);
  }

  /// `Fund`
  String get fund {
    return Intl.message('Fund', name: 'fund', desc: '', args: []);
  }

  /// `No transactions yet`
  String get noTransactionsYet {
    return Intl.message(
      'No transactions yet',
      name: 'noTransactionsYet',
      desc: '',
      args: [],
    );
  }

  /// `Card actions`
  String get cardActions {
    return Intl.message(
      'Card actions',
      name: 'cardActions',
      desc: '',
      args: [],
    );
  }

  /// `Add to Google Pay`
  String get addToGooglePay {
    return Intl.message(
      'Add to Google Pay',
      name: 'addToGooglePay',
      desc: '',
      args: [],
    );
  }

  /// `Card details`
  String get cardDetails {
    return Intl.message(
      'Card details',
      name: 'cardDetails',
      desc: '',
      args: [],
    );
  }

  /// `Cardholder name`
  String get cardHolderName {
    return Intl.message(
      'Cardholder name',
      name: 'cardHolderName',
      desc: '',
      args: [],
    );
  }

  /// `Card number`
  String get cardNumber {
    return Intl.message('Card number', name: 'cardNumber', desc: '', args: []);
  }

  /// `Expiry date`
  String get expiryDate {
    return Intl.message('Expiry date', name: 'expiryDate', desc: '', args: []);
  }

  /// `CVC`
  String get cvc {
    return Intl.message('CVC', name: 'cvc', desc: '', args: []);
  }

  /// `Billing address`
  String get billingAddress {
    return Intl.message(
      'Billing address',
      name: 'billingAddress',
      desc: '',
      args: [],
    );
  }

  /// `Show`
  String get show {
    return Intl.message('Show', name: 'show', desc: '', args: []);
  }

  /// `Street`
  String get street {
    return Intl.message('Street', name: 'street', desc: '', args: []);
  }

  /// `City`
  String get city {
    return Intl.message('City', name: 'city', desc: '', args: []);
  }

  /// `State`
  String get state {
    return Intl.message('State', name: 'state', desc: '', args: []);
  }

  /// `ZIP`
  String get zip {
    return Intl.message('ZIP', name: 'zip', desc: '', args: []);
  }

  /// `Country`
  String get coutry {
    return Intl.message('Country', name: 'coutry', desc: '', args: []);
  }

  /// `Tap to copy your wallet address or scan the QR code.`
  String get copyVirtualCardAddressCopy {
    return Intl.message(
      'Tap to copy your wallet address or scan the QR code.',
      name: 'copyVirtualCardAddressCopy',
      desc: '',
      args: [],
    );
  }

  /// `Want a different payment method? `
  String get wantADifferentPaymentMethod {
    return Intl.message(
      'Want a different payment method? ',
      name: 'wantADifferentPaymentMethod',
      desc: '',
      args: [],
    );
  }

  /// `Hub. Find useful apps,\nonchain`
  String get hubFindUsefulAppsOnchain {
    return Intl.message(
      'Hub. Find useful apps,\nonchain',
      name: 'hubFindUsefulAppsOnchain',
      desc: '',
      args: [],
    );
  }

  /// `Discover apps you need`
  String get discoverAppsYouNeed {
    return Intl.message(
      'Discover apps you need',
      name: 'discoverAppsYouNeed',
      desc: '',
      args: [],
    );
  }

  /// `We've curated great apps to inspire and entertain you`
  String get weveCuratedGreatAppsToInspireYou {
    return Intl.message(
      'We\'ve curated great apps to inspire and entertain you',
      name: 'weveCuratedGreatAppsToInspireYou',
      desc: '',
      args: [],
    );
  }

  /// `to the address below.\nSending lower or from a different network may lead to loss of funds.`
  String get fundViaExternalWalletWarning {
    return Intl.message(
      'to the address below.\nSending lower or from a different network may lead to loss of funds.',
      name: 'fundViaExternalWalletWarning',
      desc: '',
      args: [],
    );
  }

  /// `More`
  String get more {
    return Intl.message('More', name: 'more', desc: '', args: []);
  }

  /// `Freeze`
  String get freeze {
    return Intl.message('Freeze', name: 'freeze', desc: '', args: []);
  }

  /// `Make card temporarily inactive`
  String get makeCardInactive {
    return Intl.message(
      'Make card temporarily inactive',
      name: 'makeCardInactive',
      desc: '',
      args: [],
    );
  }

  /// `Delete card`
  String get deleteCard {
    return Intl.message('Delete card', name: 'deleteCard', desc: '', args: []);
  }

  /// `Deleting your card is final. There may be a  wait period before you can create a new one. Any unused balance will be sent to your Onboard Wallet.`
  String get deleteCardWarning {
    return Intl.message(
      'Deleting your card is final. There may be a  wait period before you can create a new one. Any unused balance will be sent to your Onboard Wallet.',
      name: 'deleteCardWarning',
      desc: '',
      args: [],
    );
  }

  /// `Yes, delete`
  String get yesDelete {
    return Intl.message('Yes, delete', name: 'yesDelete', desc: '', args: []);
  }

  /// `Card frozen`
  String get cardFrozen {
    return Intl.message('Card frozen', name: 'cardFrozen', desc: '', args: []);
  }

  /// `Card unfrozen`
  String get cardUnfrozen {
    return Intl.message(
      'Card unfrozen',
      name: 'cardUnfrozen',
      desc: '',
      args: [],
    );
  }

  /// `Card transactions`
  String get cardTransactions {
    return Intl.message(
      'Card transactions',
      name: 'cardTransactions',
      desc: '',
      args: [],
    );
  }

  /// `Verifying otp`
  String get verifyingOtp {
    return Intl.message(
      'Verifying otp',
      name: 'verifyingOtp',
      desc: '',
      args: [],
    );
  }

  /// `This card is frozen!`
  String get thisCardIsFrozen {
    return Intl.message(
      'This card is frozen!',
      name: 'thisCardIsFrozen',
      desc: '',
      args: [],
    );
  }

  /// `Unfreeze`
  String get unfreeze {
    return Intl.message('Unfreeze', name: 'unfreeze', desc: '', args: []);
  }

  /// `Free`
  String get free {
    return Intl.message('Free', name: 'free', desc: '', args: []);
  }

  /// `Maximum`
  String get maximum {
    return Intl.message('Maximum', name: 'maximum', desc: '', args: []);
  }

  /// `Funding amount too high.`
  String get fundingAmountTooHigh {
    return Intl.message(
      'Funding amount too high.',
      name: 'fundingAmountTooHigh',
      desc: '',
      args: [],
    );
  }

  /// `Want to fund?`
  String get wantToFund {
    return Intl.message(
      'Want to fund?',
      name: 'wantToFund',
      desc: '',
      args: [],
    );
  }

  /// `Transfer`
  String get transfer {
    return Intl.message('Transfer', name: 'transfer', desc: '', args: []);
  }

  /// `To fund your card, please switch to a supported network`
  String get wantToFundCardSubCopy {
    return Intl.message(
      'To fund your card, please switch to a supported network',
      name: 'wantToFundCardSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `To fund your account, please switch to a supported network`
  String get wantToFundDaSubCopy {
    return Intl.message(
      'To fund your account, please switch to a supported network',
      name: 'wantToFundDaSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Withdraw from card`
  String get withdrawFromCard {
    return Intl.message(
      'Withdraw from card',
      name: 'withdrawFromCard',
      desc: '',
      args: [],
    );
  }

  /// `Where would you like to send funds to?`
  String get withdrawFromCardOptionsCopy {
    return Intl.message(
      'Where would you like to send funds to?',
      name: 'withdrawFromCardOptionsCopy',
      desc: '',
      args: [],
    );
  }

  /// `Send to Wallet Balance`
  String get sendToWalletBalance {
    return Intl.message(
      'Send to Wallet Balance',
      name: 'sendToWalletBalance',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawal amount `
  String get withdrawalAmount {
    return Intl.message(
      'Withdrawal amount ',
      name: 'withdrawalAmount',
      desc: '',
      args: [],
    );
  }

  /// `You will receive`
  String get youWillReceive {
    return Intl.message(
      'You will receive',
      name: 'youWillReceive',
      desc: '',
      args: [],
    );
  }

  /// `Send to Local currency`
  String get sendToLocalCurrency {
    return Intl.message(
      'Send to Local currency',
      name: 'sendToLocalCurrency',
      desc: '',
      args: [],
    );
  }

  /// `Secured by OnboardFast®`
  String get securedByOnboardFast {
    return Intl.message(
      'Secured by OnboardFast®',
      name: 'securedByOnboardFast',
      desc: '',
      args: [],
    );
  }

  /// `Receiving account`
  String get receivingAccount {
    return Intl.message(
      'Receiving account',
      name: 'receivingAccount',
      desc: '',
      args: [],
    );
  }

  /// `Change`
  String get change {
    return Intl.message('Change', name: 'change', desc: '', args: []);
  }

  /// `Move funds to your {cardCurrency} card`
  String moveFundsToYourSpendingCard(Object cardCurrency) {
    return Intl.message(
      'Move funds to your $cardCurrency card',
      name: 'moveFundsToYourSpendingCard',
      desc: '',
      args: [cardCurrency],
    );
  }

  /// `Overdue fees`
  String get overdueFees {
    return Intl.message(
      'Overdue fees',
      name: 'overdueFees',
      desc: '',
      args: [],
    );
  }

  /// `Fix here to avoid card termination`
  String get fixHereToAvoidCardTermination {
    return Intl.message(
      'Fix here to avoid card termination',
      name: 'fixHereToAvoidCardTermination',
      desc: '',
      args: [],
    );
  }

  /// `You have overdue fees!`
  String get youHaveOverdueFees {
    return Intl.message(
      'You have overdue fees!',
      name: 'youHaveOverdueFees',
      desc: '',
      args: [],
    );
  }

  /// `Card will deactivate in`
  String get cardWillDeactivateIn {
    return Intl.message(
      'Card will deactivate in',
      name: 'cardWillDeactivateIn',
      desc: '',
      args: [],
    );
  }

  /// `Funding required`
  String get fundingRequired {
    return Intl.message(
      'Funding required',
      name: 'fundingRequired',
      desc: '',
      args: [],
    );
  }

  /// `Card maintenance fee`
  String get cardMaintenanceFeeAndMonth {
    return Intl.message(
      'Card maintenance fee',
      name: 'cardMaintenanceFeeAndMonth',
      desc: '',
      args: [],
    );
  }

  /// `Card top-up`
  String get cardTopUp {
    return Intl.message('Card top-up', name: 'cardTopUp', desc: '', args: []);
  }

  /// `Card deactivated`
  String get cardDeactivated {
    return Intl.message(
      'Card deactivated',
      name: 'cardDeactivated',
      desc: '',
      args: [],
    );
  }

  /// `Tap to view more details`
  String get tapToViewMoreDetails {
    return Intl.message(
      'Tap to view more details',
      name: 'tapToViewMoreDetails',
      desc: '',
      args: [],
    );
  }

  /// `Card deactivated!`
  String get cardDeActivated {
    return Intl.message(
      'Card deactivated!',
      name: 'cardDeActivated',
      desc: '',
      args: [],
    );
  }

  /// `To ensure your card always works, we require periodic service payments. Not paying this means we’ll no longer be able to provide the service to you.`
  String get overdueFeesExplainerCopy {
    return Intl.message(
      'To ensure your card always works, we require periodic service payments. Not paying this means we’ll no longer be able to provide the service to you.',
      name: 'overdueFeesExplainerCopy',
      desc: '',
      args: [],
    );
  }

  /// `Your card requires a minimum balance to remain active. Top ups are needed when your balance falls below this amount.`
  String get cardTopUpInfoCopy {
    return Intl.message(
      'Your card requires a minimum balance to remain active. Top ups are needed when your balance falls below this amount.',
      name: 'cardTopUpInfoCopy',
      desc: '',
      args: [],
    );
  }

  /// `You need to fund your card for the items below to keep it active.`
  String get youHaveOverdueFeesSubCopy {
    return Intl.message(
      'You need to fund your card for the items below to keep it active.',
      name: 'youHaveOverdueFeesSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Why did this happen?`
  String get whyDidThisHappen {
    return Intl.message(
      'Why did this happen?',
      name: 'whyDidThisHappen',
      desc: '',
      args: [],
    );
  }

  /// `Set up new card`
  String get setUpNewCard {
    return Intl.message(
      'Set up new card',
      name: 'setUpNewCard',
      desc: '',
      args: [],
    );
  }

  /// `Feature not available`
  String get featureNotAvailable {
    return Intl.message(
      'Feature not available',
      name: 'featureNotAvailable',
      desc: '',
      args: [],
    );
  }

  /// `We can't offer a card to you just yet, but please get in touch with us if you'd like to learn more.`
  String get cardFeatureNotAvailableSubCopy {
    return Intl.message(
      'We can\'t offer a card to you just yet, but please get in touch with us if you\'d like to learn more.',
      name: 'cardFeatureNotAvailableSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Please wait{timer}before setting up a new card`
  String pleaseWait24hrsBeforeSettingUpACard(Object timer) {
    return Intl.message(
      'Please wait${timer}before setting up a new card',
      name: 'pleaseWait24hrsBeforeSettingUpACard',
      desc: '',
      args: [timer],
    );
  }

  /// `Add to Apple Wallet`
  String get addToAppleWallet {
    return Intl.message(
      'Add to Apple Wallet',
      name: 'addToAppleWallet',
      desc: '',
      args: [],
    );
  }

  /// `Sign to authorize this action`
  String get signToAuthorizeAction {
    return Intl.message(
      'Sign to authorize this action',
      name: 'signToAuthorizeAction',
      desc: '',
      args: [],
    );
  }

  /// `I authorize unfreezing my card to reactivate it for transactions`
  String get authorizeUnfreezeCard {
    return Intl.message(
      'I authorize unfreezing my card to reactivate it for transactions',
      name: 'authorizeUnfreezeCard',
      desc: '',
      args: [],
    );
  }

  /// `Card reactivated`
  String get cardReactivated {
    return Intl.message(
      'Card reactivated',
      name: 'cardReactivated',
      desc: '',
      args: [],
    );
  }

  /// `Your card is now frozen, but please note the following terms`
  String get noteTermForFrozenCard {
    return Intl.message(
      'Your card is now frozen, but please note the following terms',
      name: 'noteTermForFrozenCard',
      desc: '',
      args: [],
    );
  }

  /// `While frozen, any attempted transactions on this card will fail`
  String get attemptedTransactionWillFail {
    return Intl.message(
      'While frozen, any attempted transactions on this card will fail',
      name: 'attemptedTransactionWillFail',
      desc: '',
      args: [],
    );
  }

  /// `You can unfreeze card anytime`
  String get canUnfreezeAnytime {
    return Intl.message(
      'You can unfreeze card anytime',
      name: 'canUnfreezeAnytime',
      desc: '',
      args: [],
    );
  }

  /// `Card funding required!`
  String get cardFundingRequired {
    return Intl.message(
      'Card funding required!',
      name: 'cardFundingRequired',
      desc: '',
      args: [],
    );
  }

  /// `Your card is below minimum balance`
  String get yourCardIsBelowMinimumBalance {
    return Intl.message(
      'Your card is below minimum balance',
      name: 'yourCardIsBelowMinimumBalance',
      desc: '',
      args: [],
    );
  }

  /// `Card balance is low!`
  String get cardBalanceIsLow {
    return Intl.message(
      'Card balance is low!',
      name: 'cardBalanceIsLow',
      desc: '',
      args: [],
    );
  }

  /// `Add funds to avoid card charges`
  String get addFundsToAvoidCardCharges {
    return Intl.message(
      'Add funds to avoid card charges',
      name: 'addFundsToAvoidCardCharges',
      desc: '',
      args: [],
    );
  }

  /// `to ensure upcoming payments go through`
  String get toEnsureUpcomingPaymentsGoThrough {
    return Intl.message(
      'to ensure upcoming payments go through',
      name: 'toEnsureUpcomingPaymentsGoThrough',
      desc: '',
      args: [],
    );
  }

  /// `will incur a fee.`
  String get willIncurAFee {
    return Intl.message(
      'will incur a fee.',
      name: 'willIncurAFee',
      desc: '',
      args: [],
    );
  }

  /// `Current balance - {balance}`
  String currentBalance(Object balance) {
    return Intl.message(
      'Current balance - $balance',
      name: 'currentBalance',
      desc: '',
      args: [balance],
    );
  }

  /// `Min. funding required`
  String get minFundingRequired {
    return Intl.message(
      'Min. funding required',
      name: 'minFundingRequired',
      desc: '',
      args: [],
    );
  }

  /// `This keeps your card above the {minimumBalance} minimum balance required to stay active`
  String thisKeepsYourCardAboveTheMinimumBalanceRequired(
    Object minimumBalance,
  ) {
    return Intl.message(
      'This keeps your card above the $minimumBalance minimum balance required to stay active',
      name: 'thisKeepsYourCardAboveTheMinimumBalanceRequired',
      desc: '',
      args: [minimumBalance],
    );
  }

  /// `Your card has been frozen. To fix, fund your card to meet the minimum balance required.`
  String get cardFrozenSubCopy {
    return Intl.message(
      'Your card has been frozen. To fix, fund your card to meet the minimum balance required.',
      name: 'cardFrozenSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Unpaid fees may lead to your card being deactivated`
  String get unPaidFeesMayLeadToYourCardBeingDeactivated {
    return Intl.message(
      'Unpaid fees may lead to your card being deactivated',
      name: 'unPaidFeesMayLeadToYourCardBeingDeactivated',
      desc: '',
      args: [],
    );
  }

  /// `A failed transaction on your card due to insufficient funds `
  String get aFailedTransactionDueToInsufficientFunds {
    return Intl.message(
      'A failed transaction on your card due to insufficient funds ',
      name: 'aFailedTransactionDueToInsufficientFunds',
      desc: '',
      args: [],
    );
  }

  /// `Choose account`
  String get chooseAccount {
    return Intl.message(
      'Choose account',
      name: 'chooseAccount',
      desc: '',
      args: [],
    );
  }

  /// `Where do you want to receive funds in?`
  String get whereYouWantToReceivePay {
    return Intl.message(
      'Where do you want to receive funds in?',
      name: 'whereYouWantToReceivePay',
      desc: '',
      args: [],
    );
  }

  /// `Cancelled`
  String get cancelled {
    return Intl.message('Cancelled', name: 'cancelled', desc: '', args: []);
  }

  /// `Try again`
  String get tryAgain {
    return Intl.message('Try again', name: 'tryAgain', desc: '', args: []);
  }

  /// `Rate is either expired or invalid, please try again.`
  String get rateIsExpiredPleaseTryAgain {
    return Intl.message(
      'Rate is either expired or invalid, please try again.',
      name: 'rateIsExpiredPleaseTryAgain',
      desc: '',
      args: [],
    );
  }

  /// `What’s your name?`
  String get whatIsYourName {
    return Intl.message(
      'What’s your name?',
      name: 'whatIsYourName',
      desc: '',
      args: [],
    );
  }

  /// `Enter your full name just as it is on your`
  String get enterFullName {
    return Intl.message(
      'Enter your full name just as it is on your',
      name: 'enterFullName',
      desc: '',
      args: [],
    );
  }

  /// `official ID`
  String get officialID {
    return Intl.message('official ID', name: 'officialID', desc: '', args: []);
  }

  /// `First name`
  String get firstName {
    return Intl.message('First name', name: 'firstName', desc: '', args: []);
  }

  /// `Last name`
  String get lastName {
    return Intl.message('Last name', name: 'lastName', desc: '', args: []);
  }

  /// `Username`
  String get username {
    return Intl.message('Username', name: 'username', desc: '', args: []);
  }

  /// `Set a username to be used across Onboard`
  String get setUsernameTobeUsedAcrossOnboard {
    return Intl.message(
      'Set a username to be used across Onboard',
      name: 'setUsernameTobeUsedAcrossOnboard',
      desc: '',
      args: [],
    );
  }

  /// `We’ll verify this number to secure your account.`
  String get verifyPhoneNumberToSecureAccount {
    return Intl.message(
      'We’ll verify this number to secure your account.',
      name: 'verifyPhoneNumberToSecureAccount',
      desc: '',
      args: [],
    );
  }

  /// `Change number`
  String get changeNumber {
    return Intl.message(
      'Change number',
      name: 'changeNumber',
      desc: '',
      args: [],
    );
  }

  /// `Verified`
  String get verified {
    return Intl.message('Verified', name: 'verified', desc: '', args: []);
  }

  /// `Add phone number`
  String get addPhoneNumber {
    return Intl.message(
      'Add phone number',
      name: 'addPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `We need a little information to get\nstarted. This will help us secure your\ncard and protect you from fraud.`
  String get startIdentityVerificationSubCopy {
    return Intl.message(
      'We need a little information to get\nstarted. This will help us secure your\ncard and protect you from fraud.',
      name: 'startIdentityVerificationSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `To use cards, let’s verify\nyour identity`
  String get startIdentityVerificationTitle {
    return Intl.message(
      'To use cards, let’s verify\nyour identity',
      name: 'startIdentityVerificationTitle',
      desc: '',
      args: [],
    );
  }

  /// `Now, let’s verify your\nPhoto ID`
  String get startKycTitle {
    return Intl.message(
      'Now, let’s verify your\nPhoto ID',
      name: 'startKycTitle',
      desc: '',
      args: [],
    );
  }

  /// `This will establish your identity, and prevents someone else from claiming your account.`
  String get startKycSubCopy {
    return Intl.message(
      'This will establish your identity, and prevents someone else from claiming your account.',
      name: 'startKycSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `I authorize terminating my card.`
  String get authorizeCardTermination {
    return Intl.message(
      'I authorize terminating my card.',
      name: 'authorizeCardTermination',
      desc: '',
      args: [],
    );
  }

  /// `Send to other wallets`
  String get sendToOtherWallets {
    return Intl.message(
      'Send to other wallets',
      name: 'sendToOtherWallets',
      desc: '',
      args: [],
    );
  }

  /// `Your card has being funded`
  String get yourCardHasBeingFunded {
    return Intl.message(
      'Your card has being funded',
      name: 'yourCardHasBeingFunded',
      desc: '',
      args: [],
    );
  }

  /// `Select network`
  String get selectNetwork {
    return Intl.message(
      'Select network',
      name: 'selectNetwork',
      desc: '',
      args: [],
    );
  }

  /// `Want to withdraw?`
  String get wantToWithdraw {
    return Intl.message(
      'Want to withdraw?',
      name: 'wantToWithdraw',
      desc: '',
      args: [],
    );
  }

  /// `Add your account`
  String get addYourAccount {
    return Intl.message(
      'Add your account',
      name: 'addYourAccount',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawal amount too high`
  String get withdrawalAmountTooHigh {
    return Intl.message(
      'Withdrawal amount too high',
      name: 'withdrawalAmountTooHigh',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawal amount too low`
  String get withdrawalAmountTooLow {
    return Intl.message(
      'Withdrawal amount too low',
      name: 'withdrawalAmountTooLow',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawal will set card below the min. balance of {minBalance}`
  String cardWithdrawalMinimumBalanceWarning(Object minBalance) {
    return Intl.message(
      'Withdrawal will set card below the min. balance of $minBalance',
      name: 'cardWithdrawalMinimumBalanceWarning',
      desc: '',
      args: [minBalance],
    );
  }

  /// `To withdraw from your card, please switch to a supported network`
  String get wantToWithdrawSubCopy {
    return Intl.message(
      'To withdraw from your card, please switch to a supported network',
      name: 'wantToWithdrawSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Which token do you want to receive at your destination wallet?`
  String get cardWithdrawalSelectAssetSubCopy {
    return Intl.message(
      'Which token do you want to receive at your destination wallet?',
      name: 'cardWithdrawalSelectAssetSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `A one-time fee to create your card`
  String get cardCreationFeeSubCopy {
    return Intl.message(
      'A one-time fee to create your card',
      name: 'cardCreationFeeSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Select ID to verify`
  String get selectIdToVerify {
    return Intl.message(
      'Select ID to verify',
      name: 'selectIdToVerify',
      desc: '',
      args: [],
    );
  }

  /// `You’ll be required to take a clear picture of the ID`
  String get selectIdSubCopy {
    return Intl.message(
      'You’ll be required to take a clear picture of the ID',
      name: 'selectIdSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `ID Type`
  String get idType {
    return Intl.message('ID Type', name: 'idType', desc: '', args: []);
  }

  /// `Select ID`
  String get selectIdentityCard {
    return Intl.message(
      'Select ID',
      name: 'selectIdentityCard',
      desc: '',
      args: [],
    );
  }

  /// `Funding amount`
  String get fundingAmount {
    return Intl.message(
      'Funding amount',
      name: 'fundingAmount',
      desc: '',
      args: [],
    );
  }

  /// `Your card ({label}**{maskedNumber}) has been terminated, and can no longer be used for transactions. If applicable, your balance will be sent to your wallet.`
  String cardDeactivatedSubCopy(Object label, Object maskedNumber) {
    return Intl.message(
      'Your card ($label**$maskedNumber) has been terminated, and can no longer be used for transactions. If applicable, your balance will be sent to your wallet.',
      name: 'cardDeactivatedSubCopy',
      desc: '',
      args: [label, maskedNumber],
    );
  }

  /// `What location do you live in most often?`
  String get whatLocationDoYouLive {
    return Intl.message(
      'What location do you live in most often?',
      name: 'whatLocationDoYouLive',
      desc: '',
      args: [],
    );
  }

  /// `Invalid name. Numbers and special characters are not allowed`
  String get invalidFirstName {
    return Intl.message(
      'Invalid name. Numbers and special characters are not allowed',
      name: 'invalidFirstName',
      desc: '',
      args: [],
    );
  }

  /// `Invalid name. Numbers and special characters are not allowed`
  String get invalidLastName {
    return Intl.message(
      'Invalid name. Numbers and special characters are not allowed',
      name: 'invalidLastName',
      desc: '',
      args: [],
    );
  }

  /// `Must be at least 5 characters`
  String get usernameValidationError {
    return Intl.message(
      'Must be at least 5 characters',
      name: 'usernameValidationError',
      desc: '',
      args: [],
    );
  }

  /// `This username has already been taken`
  String get usernameTaken {
    return Intl.message(
      'This username has already been taken',
      name: 'usernameTaken',
      desc: '',
      args: [],
    );
  }

  /// `Funding`
  String get funding {
    return Intl.message('Funding', name: 'funding', desc: '', args: []);
  }

  /// `Purchase`
  String get purchase {
    return Intl.message('Purchase', name: 'purchase', desc: '', args: []);
  }

  /// `Refund`
  String get refund {
    return Intl.message('Refund', name: 'refund', desc: '', args: []);
  }

  /// `Charge`
  String get charge {
    return Intl.message('Charge', name: 'charge', desc: '', args: []);
  }

  /// `In progress`
  String get inProgress {
    return Intl.message('In progress', name: 'inProgress', desc: '', args: []);
  }

  /// `Successful`
  String get successful {
    return Intl.message('Successful', name: 'successful', desc: '', args: []);
  }

  /// `Add your account`
  String get addAccount {
    return Intl.message(
      'Add your account',
      name: 'addAccount',
      desc: '',
      args: [],
    );
  }

  /// `In less than 10mins`
  String get inLessThan10Mins {
    return Intl.message(
      'In less than 10mins',
      name: 'inLessThan10Mins',
      desc: '',
      args: [],
    );
  }

  /// `Home address`
  String get homeAddress {
    return Intl.message(
      'Home address',
      name: 'homeAddress',
      desc: '',
      args: [],
    );
  }

  /// `We only use this to confirm your personal info`
  String get homeAddressSubCopy {
    return Intl.message(
      'We only use this to confirm your personal info',
      name: 'homeAddressSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Address line 1`
  String get addressLineOne {
    return Intl.message(
      'Address line 1',
      name: 'addressLineOne',
      desc: '',
      args: [],
    );
  }

  /// `Address line 2 (optional)`
  String get addressLineTwo {
    return Intl.message(
      'Address line 2 (optional)',
      name: 'addressLineTwo',
      desc: '',
      args: [],
    );
  }

  /// `State/Region`
  String get stateOrRegion {
    return Intl.message(
      'State/Region',
      name: 'stateOrRegion',
      desc: '',
      args: [],
    );
  }

  /// `Postal code`
  String get postalCode {
    return Intl.message('Postal code', name: 'postalCode', desc: '', args: []);
  }

  /// `Address is required`
  String get addressIsRequired {
    return Intl.message(
      'Address is required',
      name: 'addressIsRequired',
      desc: '',
      args: [],
    );
  }

  /// `State is required`
  String get stateIsRequired {
    return Intl.message(
      'State is required',
      name: 'stateIsRequired',
      desc: '',
      args: [],
    );
  }

  /// `Country is required`
  String get countryIsRequired {
    return Intl.message(
      'Country is required',
      name: 'countryIsRequired',
      desc: '',
      args: [],
    );
  }

  /// `Postal code is required`
  String get postalCodeIsRequired {
    return Intl.message(
      'Postal code is required',
      name: 'postalCodeIsRequired',
      desc: '',
      args: [],
    );
  }

  /// `City is required`
  String get cityIsRequired {
    return Intl.message(
      'City is required',
      name: 'cityIsRequired',
      desc: '',
      args: [],
    );
  }

  /// `Complete`
  String get complete {
    return Intl.message('Complete', name: 'complete', desc: '', args: []);
  }

  /// `Rate has being updated to {formattedRate}`
  String rateHasBeingUpdated(Object formattedRate) {
    return Intl.message(
      'Rate has being updated to $formattedRate',
      name: 'rateHasBeingUpdated',
      desc: '',
      args: [formattedRate],
    );
  }

  /// `Card funding`
  String get cardFunding {
    return Intl.message(
      'Card funding',
      name: 'cardFunding',
      desc: '',
      args: [],
    );
  }

  /// `Partially funded`
  String get partiallyFunded {
    return Intl.message(
      'Partially funded',
      name: 'partiallyFunded',
      desc: '',
      args: [],
    );
  }

  /// `Pending via`
  String get pendingVia {
    return Intl.message('Pending via', name: 'pendingVia', desc: '', args: []);
  }

  /// `You've read and accepted the following to use this card.`
  String get youveReadAndAcceptedTheFollowingToUseThisCard {
    return Intl.message(
      'You\'ve read and accepted the following to use this card.',
      name: 'youveReadAndAcceptedTheFollowingToUseThisCard',
      desc: '',
      args: [],
    );
  }

  /// `Activate profile `
  String get activateProfile {
    return Intl.message(
      'Activate profile ',
      name: 'activateProfile',
      desc: '',
      args: [],
    );
  }

  /// `Trade crypto`
  String get tradeCrypto {
    return Intl.message(
      'Trade crypto',
      name: 'tradeCrypto',
      desc: '',
      args: [],
    );
  }

  /// `Protect your wallet`
  String get protectYourWallet {
    return Intl.message(
      'Protect your wallet',
      name: 'protectYourWallet',
      desc: '',
      args: [],
    );
  }

  /// `Enable passcode or biometrics`
  String get enablePasscodeOrBiometrics {
    return Intl.message(
      'Enable passcode or biometrics',
      name: 'enablePasscodeOrBiometrics',
      desc: '',
      args: [],
    );
  }

  /// `Add a phone number`
  String get addAPhoneNumber {
    return Intl.message(
      'Add a phone number',
      name: 'addAPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `We can contact if you need us`
  String get weCanContactIfYouNeedUs {
    return Intl.message(
      'We can contact if you need us',
      name: 'weCanContactIfYouNeedUs',
      desc: '',
      args: [],
    );
  }

  /// `Tell us about you`
  String get tellUsAboutYou {
    return Intl.message(
      'Tell us about you',
      name: 'tellUsAboutYou',
      desc: '',
      args: [],
    );
  }

  /// `Let's get you the best of Onboard`
  String get letsGetYouTheBestOfOnboard {
    return Intl.message(
      'Let\'s get you the best of Onboard',
      name: 'letsGetYouTheBestOfOnboard',
      desc: '',
      args: [],
    );
  }

  /// `Coming soon`
  String get comingSoon {
    return Intl.message('Coming soon', name: 'comingSoon', desc: '', args: []);
  }

  /// `Create your first ad`
  String get createYourFirstAd {
    return Intl.message(
      'Create your first ad',
      name: 'createYourFirstAd',
      desc: '',
      args: [],
    );
  }

  /// `Start making profit on Onboard`
  String get startMakingProfitOnOnboard {
    return Intl.message(
      'Start making profit on Onboard',
      name: 'startMakingProfitOnOnboard',
      desc: '',
      args: [],
    );
  }

  /// `Help us build your ideal app`
  String get helpUsBuildYourIdealApp {
    return Intl.message(
      'Help us build your ideal app',
      name: 'helpUsBuildYourIdealApp',
      desc: '',
      args: [],
    );
  }

  /// `Add crypto to get started`
  String get addCryptoToGetStarted {
    return Intl.message(
      'Add crypto to get started',
      name: 'addCryptoToGetStarted',
      desc: '',
      args: [],
    );
  }

  /// `Date of birth`
  String get dob {
    return Intl.message('Date of birth', name: 'dob', desc: '', args: []);
  }

  /// `Enter your date of birth as it is on your official ID`
  String get dobSubCopy {
    return Intl.message(
      'Enter your date of birth as it is on your official ID',
      name: 'dobSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Enter your {document} number.`
  String enterYourIdNumber(Object document) {
    return Intl.message(
      'Enter your $document number.',
      name: 'enterYourIdNumber',
      desc: '',
      args: [document],
    );
  }

  /// `ID number`
  String get idNumber {
    return Intl.message('ID number', name: 'idNumber', desc: '', args: []);
  }

  /// `Your ID or country not is not supported`
  String get idOrCountryNotSupported {
    return Intl.message(
      'Your ID or country not is not supported',
      name: 'idOrCountryNotSupported',
      desc: '',
      args: [],
    );
  }

  /// `Your country is not supported`
  String get countryNotSupported {
    return Intl.message(
      'Your country is not supported',
      name: 'countryNotSupported',
      desc: '',
      args: [],
    );
  }

  /// `Others`
  String get others {
    return Intl.message('Others', name: 'others', desc: '', args: []);
  }

  /// `Manage`
  String get manage {
    return Intl.message('Manage', name: 'manage', desc: '', args: []);
  }

  /// `Manage assets`
  String get manageAssets {
    return Intl.message(
      'Manage assets',
      name: 'manageAssets',
      desc: '',
      args: [],
    );
  }

  /// `Added to favourites`
  String get addedToFavourites {
    return Intl.message(
      'Added to favourites',
      name: 'addedToFavourites',
      desc: '',
      args: [],
    );
  }

  /// `Removed from favourites`
  String get removedFromFavourites {
    return Intl.message(
      'Removed from favourites',
      name: 'removedFromFavourites',
      desc: '',
      args: [],
    );
  }

  /// `Token hidden!`
  String get tokenHidden {
    return Intl.message(
      'Token hidden!',
      name: 'tokenHidden',
      desc: '',
      args: [],
    );
  }

  /// `It will no longer show up on your asset list. \nWant to hide all tokens like this? \nVisit Settings > Preferences > Manage tokens `
  String get tokenHiddenSubCopy {
    return Intl.message(
      'It will no longer show up on your asset list. \nWant to hide all tokens like this? \nVisit Settings > Preferences > Manage tokens ',
      name: 'tokenHiddenSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Setting up your card 🎉`
  String get settingUpYourCard {
    return Intl.message(
      'Setting up your card 🎉',
      name: 'settingUpYourCard',
      desc: '',
      args: [],
    );
  }

  /// `It’ll be ready for you to use within 24 hours`
  String get cardActivationSubCopy {
    return Intl.message(
      'It’ll be ready for you to use within 24 hours',
      name: 'cardActivationSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Thanks for getting Onboard! 🚀`
  String get scheduledChatNotificationTitle {
    return Intl.message(
      'Thanks for getting Onboard! 🚀',
      name: 'scheduledChatNotificationTitle',
      desc: '',
      args: [],
    );
  }

  /// `{cardCurrencySymbol}1.00 = {conversionRate} {assetCode}`
  String formattedCardRate(
    Object cardCurrencySymbol,
    Object conversionRate,
    Object assetCode,
  ) {
    return Intl.message(
      '${cardCurrencySymbol}1.00 = $conversionRate $assetCode',
      name: 'formattedCardRate',
      desc: '',
      args: [cardCurrencySymbol, conversionRate, assetCode],
    );
  }

  /// `We'd love to help you get started with some tips - tap here to chat with us`
  String get scheduledChatNotificationBody {
    return Intl.message(
      'We\'d love to help you get started with some tips - tap here to chat with us',
      name: 'scheduledChatNotificationBody',
      desc: '',
      args: [],
    );
  }

  /// `Possible spam`
  String get possibleSpam {
    return Intl.message(
      'Possible spam',
      name: 'possibleSpam',
      desc: '',
      args: [],
    );
  }

  /// `Card controls for total security`
  String get cardControlsForTotalSecurity {
    return Intl.message(
      'Card controls for total security',
      name: 'cardControlsForTotalSecurity',
      desc: '',
      args: [],
    );
  }

  /// `Saved addresses`
  String get savedAddresses {
    return Intl.message(
      'Saved addresses',
      name: 'savedAddresses',
      desc: '',
      args: [],
    );
  }

  /// `Save address`
  String get saveAddress {
    return Intl.message(
      'Save address',
      name: 'saveAddress',
      desc: '',
      args: [],
    );
  }

  /// `Add address`
  String get addAddress {
    return Intl.message('Add address', name: 'addAddress', desc: '', args: []);
  }

  /// `Eg. My first wallet`
  String get egMyFirstWallet {
    return Intl.message(
      'Eg. My first wallet',
      name: 'egMyFirstWallet',
      desc: '',
      args: [],
    );
  }

  /// `No specific asset`
  String get noSpecificAsset {
    return Intl.message(
      'No specific asset',
      name: 'noSpecificAsset',
      desc: '',
      args: [],
    );
  }

  /// `For Universal addresses. `
  String get universalAddresses {
    return Intl.message(
      'For Universal addresses. ',
      name: 'universalAddresses',
      desc: '',
      args: [],
    );
  }

  /// `Learn more >`
  String get learnMoreWithArrow {
    return Intl.message(
      'Learn more >',
      name: 'learnMoreWithArrow',
      desc: '',
      args: [],
    );
  }

  /// `Learn more`
  String get learnMore {
    return Intl.message('Learn more', name: 'learnMore', desc: '', args: []);
  }

  /// `Circulating Supply`
  String get circulatingSupply {
    return Intl.message(
      'Circulating Supply',
      name: 'circulatingSupply',
      desc: '',
      args: [],
    );
  }

  /// `24h Volume`
  String get twentyFourHourVolume {
    return Intl.message(
      '24h Volume',
      name: 'twentyFourHourVolume',
      desc: '',
      args: [],
    );
  }

  /// `Rank`
  String get rank {
    return Intl.message('Rank', name: 'rank', desc: '', args: []);
  }

  /// `Market Cap`
  String get marketCap {
    return Intl.message('Market Cap', name: 'marketCap', desc: '', args: []);
  }

  /// `Select suitable network`
  String get selectSuitableNetwork {
    return Intl.message(
      'Select suitable network',
      name: 'selectSuitableNetwork',
      desc: '',
      args: [],
    );
  }

  /// `Address saved`
  String get addressSaved {
    return Intl.message(
      'Address saved',
      name: 'addressSaved',
      desc: '',
      args: [],
    );
  }

  /// `Address deleted`
  String get addressDeleted {
    return Intl.message(
      'Address deleted',
      name: 'addressDeleted',
      desc: '',
      args: [],
    );
  }

  /// `Select an address`
  String get selectAnAddress {
    return Intl.message(
      'Select an address',
      name: 'selectAnAddress',
      desc: '',
      args: [],
    );
  }

  /// `Add wallet address`
  String get addWalletAddress {
    return Intl.message(
      'Add wallet address',
      name: 'addWalletAddress',
      desc: '',
      args: [],
    );
  }

  /// `Delete`
  String get delete {
    return Intl.message('Delete', name: 'delete', desc: '', args: []);
  }

  /// `Edit`
  String get edit {
    return Intl.message('Edit', name: 'edit', desc: '', args: []);
  }

  /// `Card balance`
  String get cardBalance {
    return Intl.message(
      'Card balance',
      name: 'cardBalance',
      desc: '',
      args: [],
    );
  }

  /// `This will be added to your Onboard Wallet balance`
  String get thisWillBeAddedToYourOnboardWalletBalance {
    return Intl.message(
      'This will be added to your Onboard Wallet balance',
      name: 'thisWillBeAddedToYourOnboardWalletBalance',
      desc: '',
      args: [],
    );
  }

  /// `This covers the cost of your transaction - primarily for currency conversion and transaction fees.`
  String get cardTerminationFeeExplainer {
    return Intl.message(
      'This covers the cost of your transaction - primarily for currency conversion and transaction fees.',
      name: 'cardTerminationFeeExplainer',
      desc: '',
      args: [],
    );
  }

  /// `UNIVERSAL ADDRESS`
  String get universalAddress {
    return Intl.message(
      'UNIVERSAL ADDRESS',
      name: 'universalAddress',
      desc: '',
      args: [],
    );
  }

  /// `A universal address is a single address that can receive all tokens that match its network. Think of it like an ‘account number’ for receiving funds. \nEg: Your Onboard address`
  String get universalAddressExplainer {
    return Intl.message(
      'A universal address is a single address that can receive all tokens that match its network. Think of it like an ‘account number’ for receiving funds. \nEg: Your Onboard address',
      name: 'universalAddressExplainer',
      desc: '',
      args: [],
    );
  }

  /// `Delete address?`
  String get deleteAddress {
    return Intl.message(
      'Delete address?',
      name: 'deleteAddress',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this address? This can't be reversed`
  String get deleteAddressSubCopy {
    return Intl.message(
      'Are you sure you want to delete this address? This can\'t be reversed',
      name: 'deleteAddressSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Yes, delete this address`
  String get yesDeleteThisAddress {
    return Intl.message(
      'Yes, delete this address',
      name: 'yesDeleteThisAddress',
      desc: '',
      args: [],
    );
  }

  /// `You paid`
  String get youPaid {
    return Intl.message('You paid', name: 'youPaid', desc: '', args: []);
  }

  /// `You received`
  String get youReceived {
    return Intl.message(
      'You received',
      name: 'youReceived',
      desc: '',
      args: [],
    );
  }

  /// `This action is final. There may be a wait period before you can create a new card.`
  String get deleteCardSubCopy {
    return Intl.message(
      'This action is final. There may be a wait period before you can create a new card.',
      name: 'deleteCardSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Amount funded`
  String get amountFunded {
    return Intl.message(
      'Amount funded',
      name: 'amountFunded',
      desc: '',
      args: [],
    );
  }

  /// `Amount withdrawn`
  String get amountWithdrawn {
    return Intl.message(
      'Amount withdrawn',
      name: 'amountWithdrawn',
      desc: '',
      args: [],
    );
  }

  /// `Connecting may take a few seconds`
  String get connectingMayTakeAFewSeconds {
    return Intl.message(
      'Connecting may take a few seconds',
      name: 'connectingMayTakeAFewSeconds',
      desc: '',
      args: [],
    );
  }

  /// `WalletConnect`
  String get walletConnect {
    return Intl.message(
      'WalletConnect',
      name: 'walletConnect',
      desc: '',
      args: [],
    );
  }

  /// `Switch between supported crypto networks to trade`
  String get switchToExchangeSupportedNetwork {
    return Intl.message(
      'Switch between supported crypto networks to trade',
      name: 'switchToExchangeSupportedNetwork',
      desc: '',
      args: [],
    );
  }

  /// `Transactions in other currencies (or at non-{countryCode} merchants) incur currency conversion fees`
  String fxTransactionsSubtitle(Object countryCode) {
    return Intl.message(
      'Transactions in other currencies (or at non-$countryCode merchants) incur currency conversion fees',
      name: 'fxTransactionsSubtitle',
      desc: '',
      args: [countryCode],
    );
  }

  /// `Non`
  String get non {
    return Intl.message('Non', name: 'non', desc: '', args: []);
  }

  /// `Screenshots aren't a safe way to keep track of your Secret Recovery Phrase. Store it somewhere that isn't backed up online to keep your wallet safe.`
  String get screenshotWarningCopy {
    return Intl.message(
      'Screenshots aren\'t a safe way to keep track of your Secret Recovery Phrase. Store it somewhere that isn\'t backed up online to keep your wallet safe.',
      name: 'screenshotWarningCopy',
      desc: '',
      args: [],
    );
  }

  /// `Hey, what’s your email?`
  String get heyWhatsYourEmail {
    return Intl.message(
      'Hey, what’s your email?',
      name: 'heyWhatsYourEmail',
      desc: '',
      args: [],
    );
  }

  /// `Have other wallets?`
  String get haveOtherWallets {
    return Intl.message(
      'Have other wallets?',
      name: 'haveOtherWallets',
      desc: '',
      args: [],
    );
  }

  /// `Your wallet address`
  String get yourWalletAddress {
    return Intl.message(
      'Your wallet address',
      name: 'yourWalletAddress',
      desc: '',
      args: [],
    );
  }

  /// `Let's secure your wallet`
  String get letsSecureYourWallet {
    return Intl.message(
      'Let\'s secure your wallet',
      name: 'letsSecureYourWallet',
      desc: '',
      args: [],
    );
  }

  /// `Steps needed`
  String get stepsNeeded {
    return Intl.message(
      'Steps needed',
      name: 'stepsNeeded',
      desc: '',
      args: [],
    );
  }

  /// `Set a passcode`
  String get setAPassCode {
    return Intl.message(
      'Set a passcode',
      name: 'setAPassCode',
      desc: '',
      args: [],
    );
  }

  /// `Finish wallet backup`
  String get finishWalletBackUp {
    return Intl.message(
      'Finish wallet backup',
      name: 'finishWalletBackUp',
      desc: '',
      args: [],
    );
  }

  /// `🔥`
  String get flameEmoji {
    return Intl.message('🔥', name: 'flameEmoji', desc: '', args: []);
  }

  /// `Let’s go`
  String get letsGo {
    return Intl.message('Let’s go', name: 'letsGo', desc: '', args: []);
  }

  /// `100% encrypted`
  String get hundredPercentEncrypted {
    return Intl.message(
      '100% encrypted',
      name: 'hundredPercentEncrypted',
      desc: '',
      args: [],
    );
  }

  /// `Your wallet is secure!`
  String get yourWalletIsSecure {
    return Intl.message(
      'Your wallet is secure!',
      name: 'yourWalletIsSecure',
      desc: '',
      args: [],
    );
  }

  /// `All backed up. Remember, no one else can access this wallet, not even us \nat Onboard`
  String get yourWalletIsSecureSubCopy {
    return Intl.message(
      'All backed up. Remember, no one else can access this wallet, not even us \nat Onboard',
      name: 'yourWalletIsSecureSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Backup your wallet to the cloud to regain access if you log out or switch devices`
  String get finishWalletBackUpSubCopy {
    return Intl.message(
      'Backup your wallet to the cloud to regain access if you log out or switch devices',
      name: 'finishWalletBackUpSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `A 6-digit passcode for login on this device`
  String get a6DigitPasscodeForLoginOnThisDevice {
    return Intl.message(
      'A 6-digit passcode for login on this device',
      name: 'a6DigitPasscodeForLoginOnThisDevice',
      desc: '',
      args: [],
    );
  }

  /// `You have an existing account on Onboard.\nChoose a sign in option`
  String get welcomeBackSubCopy {
    return Intl.message(
      'You have an existing account on Onboard.\nChoose a sign in option',
      name: 'welcomeBackSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Invalid Email Address. Please check and try again.`
  String get invalidEmailAddressPleaseCheckAndTryAgain {
    return Intl.message(
      'Invalid Email Address. Please check and try again.',
      name: 'invalidEmailAddressPleaseCheckAndTryAgain',
      desc: '',
      args: [],
    );
  }

  /// `Enable 2FA`
  String get enable2fa {
    return Intl.message('Enable 2FA', name: 'enable2fa', desc: '', args: []);
  }

  /// `To securely regain access to your account when you logout or lose device`
  String get enable2faSubCopy {
    return Intl.message(
      'To securely regain access to your account when you logout or lose device',
      name: 'enable2faSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Phone verification`
  String get phoneVerification {
    return Intl.message(
      'Phone verification',
      name: 'phoneVerification',
      desc: '',
      args: [],
    );
  }

  /// `Google authenticator`
  String get googleAuthenticator {
    return Intl.message(
      'Google authenticator',
      name: 'googleAuthenticator',
      desc: '',
      args: [],
    );
  }

  /// `End to End Encrypted`
  String get endToEndEncrypted {
    return Intl.message(
      'End to End Encrypted',
      name: 'endToEndEncrypted',
      desc: '',
      args: [],
    );
  }

  /// `Something went wrong with this backup.\nBut not to worry, let’s retry this`
  String get backUpErrorMessage {
    return Intl.message(
      'Something went wrong with this backup.\nBut not to worry, let’s retry this',
      name: 'backUpErrorMessage',
      desc: '',
      args: [],
    );
  }

  /// `Never miss a transaction`
  String get neverMissATransaction {
    return Intl.message(
      'Never miss a transaction',
      name: 'neverMissATransaction',
      desc: '',
      args: [],
    );
  }

  /// `Onboard sends notifications to keep you updated on all your \n account activity`
  String get neverMissATransactionSubCopy {
    return Intl.message(
      'Onboard sends notifications to keep you updated on all your \n account activity',
      name: 'neverMissATransactionSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `You received $1,380`
  String get youReceived1380 {
    return Intl.message(
      'You received \$1,380',
      name: 'youReceived1380',
      desc: '',
      args: [],
    );
  }

  /// `Payment successful`
  String get paymentSuccessful {
    return Intl.message(
      'Payment successful',
      name: 'paymentSuccessful',
      desc: '',
      args: [],
    );
  }

  /// `Retry backup`
  String get retryBackup {
    return Intl.message(
      'Retry backup',
      name: 'retryBackup',
      desc: '',
      args: [],
    );
  }

  /// `💳`
  String get creditCardEmoji {
    return Intl.message('💳', name: 'creditCardEmoji', desc: '', args: []);
  }

  /// `You sent 205 USDT`
  String get youSent250USDT {
    return Intl.message(
      'You sent 205 USDT',
      name: 'youSent250USDT',
      desc: '',
      args: [],
    );
  }

  /// `💸`
  String get moneyWingsEmoji {
    return Intl.message('💸', name: 'moneyWingsEmoji', desc: '', args: []);
  }

  /// `now`
  String get nowText {
    return Intl.message('now', name: 'nowText', desc: '', args: []);
  }

  /// `{minute}m ago`
  String minutesAgo(Object minute) {
    return Intl.message(
      '${minute}m ago',
      name: 'minutesAgo',
      desc: '',
      args: [minute],
    );
  }

  /// `Cloud backup`
  String get cloudBackUp {
    return Intl.message(
      'Cloud backup',
      name: 'cloudBackUp',
      desc: '',
      args: [],
    );
  }

  /// `min`
  String get min {
    return Intl.message('min', name: 'min', desc: '', args: []);
  }

  /// `Back up now`
  String get backUpNow {
    return Intl.message('Back up now', name: 'backUpNow', desc: '', args: []);
  }

  /// `Do this later`
  String get doThisLater {
    return Intl.message(
      'Do this later',
      name: 'doThisLater',
      desc: '',
      args: [],
    );
  }

  /// `Backing up wallet`
  String get backingUpWallet {
    return Intl.message(
      'Backing up wallet',
      name: 'backingUpWallet',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure?`
  String get areYouSure {
    return Intl.message(
      'Are you sure?',
      name: 'areYouSure',
      desc: '',
      args: [],
    );
  }

  /// `I’ll take the risk`
  String get illTakeTheRisk {
    return Intl.message(
      'I’ll take the risk',
      name: 'illTakeTheRisk',
      desc: '',
      args: [],
    );
  }

  /// `Finish backup`
  String get finishBackUp {
    return Intl.message(
      'Finish backup',
      name: 'finishBackUp',
      desc: '',
      args: [],
    );
  }

  /// `Wallet backup`
  String get walletBackup {
    return Intl.message(
      'Wallet backup',
      name: 'walletBackup',
      desc: '',
      args: [],
    );
  }

  /// `Backup details`
  String get backUpDetails {
    return Intl.message(
      'Backup details',
      name: 'backUpDetails',
      desc: '',
      args: [],
    );
  }

  /// `Google Drive`
  String get googleDrive {
    return Intl.message(
      'Google Drive',
      name: 'googleDrive',
      desc: '',
      args: [],
    );
  }

  /// `iCloud`
  String get iCloud {
    return Intl.message('iCloud', name: 'iCloud', desc: '', args: []);
  }

  /// `Last checked {date}`
  String lastCheckedDate(Object date) {
    return Intl.message(
      'Last checked $date',
      name: 'lastCheckedDate',
      desc: '',
      args: [date],
    );
  }

  /// `Restore wallet`
  String get restoreWallet {
    return Intl.message(
      'Restore wallet',
      name: 'restoreWallet',
      desc: '',
      args: [],
    );
  }

  /// `Tap button below to get started on wallet recovery`
  String get tapButtonBelowToGetStartedOnRecovery {
    return Intl.message(
      'Tap button below to get started on wallet recovery',
      name: 'tapButtonBelowToGetStartedOnRecovery',
      desc: '',
      args: [],
    );
  }

  /// `Your Onboard account has an existing crypto wallet address attached to it `
  String get recoverAccountSubCopy1 {
    return Intl.message(
      'Your Onboard account has an existing crypto wallet address attached to it ',
      name: 'recoverAccountSubCopy1',
      desc: '',
      args: [],
    );
  }

  /// `Tap preferred button below to get started on wallet recovery`
  String get recoverAccountSubCopy2 {
    return Intl.message(
      'Tap preferred button below to get started on wallet recovery',
      name: 'recoverAccountSubCopy2',
      desc: '',
      args: [],
    );
  }

  /// `We couldn't find a recovery file on\nthis account. Please retry by connecting another {Drive} account.`
  String noCloudBackUpErrorMessage(Object Drive) {
    return Intl.message(
      'We couldn\'t find a recovery file on\nthis account. Please retry by connecting another $Drive account.',
      name: 'noCloudBackUpErrorMessage',
      desc: '',
      args: [Drive],
    );
  }

  /// `Backing up your wallet helps you recover your wallet in the future if you ever need to. It's an essential security step.`
  String get skipAccountBackUpWarningSubCopy {
    return Intl.message(
      'Backing up your wallet helps you recover your wallet in the future if you ever need to. It\'s an essential security step.',
      name: 'skipAccountBackUpWarningSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `We're safely backing up your wallet to your {iCloud}. This should be quick`
  String backingUpAccountSubCopy(Object iCloud) {
    return Intl.message(
      'We\'re safely backing up your wallet to your $iCloud. This should be quick',
      name: 'backingUpAccountSubCopy',
      desc: '',
      args: [iCloud],
    );
  }

  /// `Securing wallet`
  String get securingAccount {
    return Intl.message(
      'Securing wallet',
      name: 'securingAccount',
      desc: '',
      args: [],
    );
  }

  /// `Choose wallet to recover`
  String get chooseWalletToRecover {
    return Intl.message(
      'Choose wallet to recover',
      name: 'chooseWalletToRecover',
      desc: '',
      args: [],
    );
  }

  /// `Confirming wallet backup`
  String get confirmingAccountBackUp {
    return Intl.message(
      'Confirming wallet backup',
      name: 'confirmingAccountBackUp',
      desc: '',
      args: [],
    );
  }

  /// `Multiple wallets found in your cloud storage. \nSelect one to recover`
  String get multipleWalletsFoundInYourCloudStorage {
    return Intl.message(
      'Multiple wallets found in your cloud storage. \nSelect one to recover',
      name: 'multipleWalletsFoundInYourCloudStorage',
      desc: '',
      args: [],
    );
  }

  /// `We’re encrypting your details. Wallet is almost ready`
  String get securingAccountSubCopy {
    return Intl.message(
      'We’re encrypting your details. Wallet is almost ready',
      name: 'securingAccountSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Back up your wallet recovery details to help you regain access when you log out or switch devices`
  String get finishAccountBackUpSubCopy1 {
    return Intl.message(
      'Back up your wallet recovery details to help you regain access when you log out or switch devices',
      name: 'finishAccountBackUpSubCopy1',
      desc: '',
      args: [],
    );
  }

  /// `No one else can access this wallet, \nnot even us at Onboard`
  String get finishAccountBackUpSubCopy2 {
    return Intl.message(
      'No one else can access this wallet, \nnot even us at Onboard',
      name: 'finishAccountBackUpSubCopy2',
      desc: '',
      args: [],
    );
  }

  /// `Your wallet is ready!`
  String get yourWalletIsReady {
    return Intl.message(
      'Your wallet is ready!',
      name: 'yourWalletIsReady',
      desc: '',
      args: [],
    );
  }

  /// `Address above acts like an 'account number' for receiving funds.`
  String get addressActLikeAccount {
    return Intl.message(
      'Address above acts like an \'account number\' for receiving funds.',
      name: 'addressActLikeAccount',
      desc: '',
      args: [],
    );
  }

  /// `It’s self-custody, which means you have total control over your funds.`
  String get youHaveTotalControl {
    return Intl.message(
      'It’s self-custody, which means you have total control over your funds.',
      name: 'youHaveTotalControl',
      desc: '',
      args: [],
    );
  }

  /// `Ongoing orders`
  String get onGoingOrders {
    return Intl.message(
      'Ongoing orders',
      name: 'onGoingOrders',
      desc: '',
      args: [],
    );
  }

  /// `Multiple active orders  •  Tap to review`
  String get multipleActiveOrdersCopy {
    return Intl.message(
      'Multiple active orders  •  Tap to review',
      name: 'multipleActiveOrdersCopy',
      desc: '',
      args: [],
    );
  }

  /// `Tap to review`
  String get tapToReview {
    return Intl.message(
      'Tap to review',
      name: 'tapToReview',
      desc: '',
      args: [],
    );
  }

  /// `You’re selling {token}  •`
  String youreSelling(Object token) {
    return Intl.message(
      'You’re selling $token  •',
      name: 'youreSelling',
      desc: '',
      args: [token],
    );
  }

  /// `How would you like to add crypto?`
  String get howWouldYouLikeToAddCrypto {
    return Intl.message(
      'How would you like to add crypto?',
      name: 'howWouldYouLikeToAddCrypto',
      desc: '',
      args: [],
    );
  }

  /// `You’re buying {token}  •`
  String youreBuying(Object token) {
    return Intl.message(
      'You’re buying $token  •',
      name: 'youreBuying',
      desc: '',
      args: [token],
    );
  }

  /// `Dropbox`
  String get dropbox {
    return Intl.message('Dropbox', name: 'dropbox', desc: '', args: []);
  }

  /// `Extra backup options`
  String get extraBackupOptions {
    return Intl.message(
      'Extra backup options',
      name: 'extraBackupOptions',
      desc: '',
      args: [],
    );
  }

  /// `Back up with multiple cloud services for extra security`
  String get multipleCloudService {
    return Intl.message(
      'Back up with multiple cloud services for extra security',
      name: 'multipleCloudService',
      desc: '',
      args: [],
    );
  }

  /// `Back up`
  String get backup {
    return Intl.message('Back up', name: 'backup', desc: '', args: []);
  }

  /// `Backed up to {drive_name}`
  String backedUpTo(Object drive_name) {
    return Intl.message(
      'Backed up to $drive_name',
      name: 'backedUpTo',
      desc: '',
      args: [drive_name],
    );
  }

  /// `Your account recovery file is backed up to your cloud storage`
  String get backUpDetailDialogSubCopy {
    return Intl.message(
      'Your account recovery file is backed up to your cloud storage',
      name: 'backUpDetailDialogSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Recovering your wallet`
  String get recoveringYourAccount {
    return Intl.message(
      'Recovering your wallet',
      name: 'recoveringYourAccount',
      desc: '',
      args: [],
    );
  }

  /// `To sign back in, we'll require authentication to regain access to your account.`
  String get backupLogoutNudgeSubCopy {
    return Intl.message(
      'To sign back in, we\'ll require authentication to regain access to your account.',
      name: 'backupLogoutNudgeSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `To sign back in, you’ll be required to provide your recovery details to regain access to your account.`
  String get importedWalletLogoutSubCopy {
    return Intl.message(
      'To sign back in, you’ll be required to provide your recovery details to regain access to your account.',
      name: 'importedWalletLogoutSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Select currency`
  String get selectCurrency {
    return Intl.message(
      'Select currency',
      name: 'selectCurrency',
      desc: '',
      args: [],
    );
  }

  /// `Phone number`
  String get phoneNumber {
    return Intl.message(
      'Phone number',
      name: 'phoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Want to edit details? Reach out to us via our`
  String get wantToEditDetailsReachOutToUsViaOur {
    return Intl.message(
      'Want to edit details? Reach out to us via our',
      name: 'wantToEditDetailsReachOutToUsViaOur',
      desc: '',
      args: [],
    );
  }

  /// `Invalid phone number.`
  String get invalidPhoneNumber {
    return Intl.message(
      'Invalid phone number.',
      name: 'invalidPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `You have no more attempts left`
  String get youHaveNoMoreAttemptsLeft {
    return Intl.message(
      'You have no more attempts left',
      name: 'youHaveNoMoreAttemptsLeft',
      desc: '',
      args: [],
    );
  }

  /// `Funding in progress. Completes within 15 mins`
  String get inProgressCompletesWithin15Mins {
    return Intl.message(
      'Funding in progress. Completes within 15 mins',
      name: 'inProgressCompletesWithin15Mins',
      desc: '',
      args: [],
    );
  }

  /// `We’ve received a deposit of {amount} for your virtual card and are now funding it.`
  String cardCryptoFundingInProgressSubCopy(Object amount) {
    return Intl.message(
      'We’ve received a deposit of $amount for your virtual card and are now funding it.',
      name: 'cardCryptoFundingInProgressSubCopy',
      desc: '',
      args: [amount],
    );
  }

  /// `Sit tight, this should be done within \n15 minutes`
  String get sitTightThisShouldBeDoneIn15mins {
    return Intl.message(
      'Sit tight, this should be done within \n15 minutes',
      name: 'sitTightThisShouldBeDoneIn15mins',
      desc: '',
      args: [],
    );
  }

  /// `View wallet address to send crypto `
  String get viewWalletAddressToSendCrypto {
    return Intl.message(
      'View wallet address to send crypto ',
      name: 'viewWalletAddressToSendCrypto',
      desc: '',
      args: [],
    );
  }

  /// `Pay with cash`
  String get payWithCash {
    return Intl.message(
      'Pay with cash',
      name: 'payWithCash',
      desc: '',
      args: [],
    );
  }

  /// `{countryName} {countryCurrency} deposits is coming\nsoon`
  String payWithCashDepositsIsComingSoon(
    Object countryName,
    Object countryCurrency,
  ) {
    return Intl.message(
      '$countryName $countryCurrency deposits is coming\nsoon',
      name: 'payWithCashDepositsIsComingSoon',
      desc: '',
      args: [countryName, countryCurrency],
    );
  }

  /// `Change country`
  String get changeCountry {
    return Intl.message(
      'Change country',
      name: 'changeCountry',
      desc: '',
      args: [],
    );
  }

  /// `Fastest`
  String get fastest {
    return Intl.message('Fastest', name: 'fastest', desc: '', args: []);
  }

  /// `Recommended`
  String get recommended {
    return Intl.message('Recommended', name: 'recommended', desc: '', args: []);
  }

  /// `Send as cash`
  String get sendAsCash {
    return Intl.message('Send as cash', name: 'sendAsCash', desc: '', args: []);
  }

  /// `How would you like to transfer crypto?`
  String get howYouWantToTransfer {
    return Intl.message(
      'How would you like to transfer crypto?',
      name: 'howYouWantToTransfer',
      desc: '',
      args: [],
    );
  }

  /// `How would you like to sell crypto?`
  String get howYouWantToSell {
    return Intl.message(
      'How would you like to sell crypto?',
      name: 'howYouWantToSell',
      desc: '',
      args: [],
    );
  }

  /// `Transfer crypto to other wallets`
  String get transferCryptoToOtherWallet {
    return Intl.message(
      'Transfer crypto to other wallets',
      name: 'transferCryptoToOtherWallet',
      desc: '',
      args: [],
    );
  }

  /// `Transfer crypto to other wallets`
  String get transferTokenToExternalWallet {
    return Intl.message(
      'Transfer crypto to other wallets',
      name: 'transferTokenToExternalWallet',
      desc: '',
      args: [],
    );
  }

  /// `Featured apps`
  String get featuredApps {
    return Intl.message(
      'Featured apps',
      name: 'featuredApps',
      desc: '',
      args: [],
    );
  }

  /// `Discover apps we spotlight. Curated by experts. Handpicked for you.`
  String get discoverAppsWeSpotlight {
    return Intl.message(
      'Discover apps we spotlight. Curated by experts. Handpicked for you.',
      name: 'discoverAppsWeSpotlight',
      desc: '',
      args: [],
    );
  }

  /// `Send {NGN} to a designated account number and receive {BNB} instantly`
  String onboardPaySubtitle(Object NGN, Object BNB) {
    return Intl.message(
      'Send $NGN to a designated account number and receive $BNB instantly',
      name: 'onboardPaySubtitle',
      desc: '',
      args: [NGN, BNB],
    );
  }

  /// `Crypto to Cash, fast`
  String get cryptoToCashFast {
    return Intl.message(
      'Crypto to Cash, fast',
      name: 'cryptoToCashFast',
      desc: '',
      args: [],
    );
  }

  /// `Transfer crypto globally, swiftly converted to EUR or USD`
  String get transferCryptoGlobally {
    return Intl.message(
      'Transfer crypto globally, swiftly converted to EUR or USD',
      name: 'transferCryptoGlobally',
      desc: '',
      args: [],
    );
  }

  /// `Pay USD or EUR to a dedicated account and receive {crypto}`
  String onboardPayOnRampSubtitle(Object crypto) {
    return Intl.message(
      'Pay USD or EUR to a dedicated account and receive $crypto',
      name: 'onboardPayOnRampSubtitle',
      desc: '',
      args: [crypto],
    );
  }

  /// `Send {crypto} to an address and receive USD or EUR in your account.`
  String onboardPayOffRampSubtitle(Object crypto) {
    return Intl.message(
      'Send $crypto to an address and receive USD or EUR in your account.',
      name: 'onboardPayOffRampSubtitle',
      desc: '',
      args: [crypto],
    );
  }

  /// `Send {NGN} to a designated account number and receive {BNB} instantly`
  String instantPaySubtitle(Object NGN, Object BNB) {
    return Intl.message(
      'Send $NGN to a designated account number and receive $BNB instantly',
      name: 'instantPaySubtitle',
      desc: '',
      args: [NGN, BNB],
    );
  }

  /// `Pick from a selection of merchants that offer you great {USD} rates`
  String pickMerchantThatOfferGreatRate(Object USD) {
    return Intl.message(
      'Pick from a selection of merchants that offer you great $USD rates',
      name: 'pickMerchantThatOfferGreatRate',
      desc: '',
      args: [USD],
    );
  }

  /// `Select this provider to complete your {asset} transaction`
  String genericTransactionProviderSubtitle(Object asset) {
    return Intl.message(
      'Select this provider to complete your $asset transaction',
      name: 'genericTransactionProviderSubtitle',
      desc: '',
      args: [asset],
    );
  }

  /// `Receive {NGN} payout in your bank account within minutes`
  String receivePayoutInYourBankAccount(Object NGN) {
    return Intl.message(
      'Receive $NGN payout in your bank account within minutes',
      name: 'receivePayoutInYourBankAccount',
      desc: '',
      args: [NGN],
    );
  }

  /// `Redirecting to {provider}`
  String redirectingToProvider(Object provider) {
    return Intl.message(
      'Redirecting to $provider',
      name: 'redirectingToProvider',
      desc: '',
      args: [provider],
    );
  }

  /// `Convert to your `
  String get convertToYour {
    return Intl.message(
      'Convert to your ',
      name: 'convertToYour',
      desc: '',
      args: [],
    );
  }

  /// `local currency`
  String get localCurrency {
    return Intl.message(
      'local currency',
      name: 'localCurrency',
      desc: '',
      args: [],
    );
  }

  /// `We're unable to complete this swap. Please retry with a different amount, or contact support`
  String get swapGenericErrorMessage {
    return Intl.message(
      'We\'re unable to complete this swap. Please retry with a different amount, or contact support',
      name: 'swapGenericErrorMessage',
      desc: '',
      args: [],
    );
  }

  /// `Account was backed up to {Drive_Type}, which is not supported on this device.`
  String accountWasBackedUpToDriveWhichIsNotSupportedToThisDevice(
    Object Drive_Type,
  ) {
    return Intl.message(
      'Account was backed up to $Drive_Type, which is not supported on this device.',
      name: 'accountWasBackedUpToDriveWhichIsNotSupportedToThisDevice',
      desc: '',
      args: [Drive_Type],
    );
  }

  /// `a drive`
  String get aDrive {
    return Intl.message('a drive', name: 'aDrive', desc: '', args: []);
  }

  /// `Poor internet connection. Please check your connection and try again.`
  String get poorInternetConnectionPleaseTryAgain {
    return Intl.message(
      'Poor internet connection. Please check your connection and try again.',
      name: 'poorInternetConnectionPleaseTryAgain',
      desc: '',
      args: [],
    );
  }

  /// `Invalid home address. Try again`
  String get invalidHomeAddressTryAgain {
    return Intl.message(
      'Invalid home address. Try again',
      name: 'invalidHomeAddressTryAgain',
      desc: '',
      args: [],
    );
  }

  /// `Account recovery details are backed up to your {cloud} storage. It helps you regain account access if you log\nout or switch devices`
  String backupRecoveryItemSubCopy(Object cloud) {
    return Intl.message(
      'Account recovery details are backed up to your $cloud storage. It helps you regain account access if you log\nout or switch devices',
      name: 'backupRecoveryItemSubCopy',
      desc: '',
      args: [cloud],
    );
  }

  /// `{NGN} withdrawals unavailable. Please try again soon.`
  String withdrawalsUnavailablePleaseTryAgainSoon(Object NGN) {
    return Intl.message(
      '$NGN withdrawals unavailable. Please try again soon.',
      name: 'withdrawalsUnavailablePleaseTryAgainSoon',
      desc: '',
      args: [NGN],
    );
  }

  /// `{NGN} deposits unavailable. Please try again soon.`
  String depositsUnavailablePleaseTryAgainSoon(Object NGN) {
    return Intl.message(
      '$NGN deposits unavailable. Please try again soon.',
      name: 'depositsUnavailablePleaseTryAgainSoon',
      desc: '',
      args: [NGN],
    );
  }

  /// `cloud`
  String get cloud {
    return Intl.message('cloud', name: 'cloud', desc: '', args: []);
  }

  /// `Card suspended!`
  String get cardSuspended {
    return Intl.message(
      'Card suspended!',
      name: 'cardSuspended',
      desc: '',
      args: [],
    );
  }

  /// `Reactivate card`
  String get reactivateCard {
    return Intl.message(
      'Reactivate card',
      name: 'reactivateCard',
      desc: '',
      args: [],
    );
  }

  /// `Card is suspended`
  String get cardIsSuspended {
    return Intl.message(
      'Card is suspended',
      name: 'cardIsSuspended',
      desc: '',
      args: [],
    );
  }

  /// `Crypto address`
  String get cryptoAddress {
    return Intl.message(
      'Crypto address',
      name: 'cryptoAddress',
      desc: '',
      args: [],
    );
  }

  /// `Your card has been suspended temporarily. Please note the following`
  String get yourCardHasBeingSuspendedTemporarilyPleaseNoteTheFollowing {
    return Intl.message(
      'Your card has been suspended temporarily. Please note the following',
      name: 'yourCardHasBeingSuspendedTemporarilyPleaseNoteTheFollowing',
      desc: '',
      args: [],
    );
  }

  /// `This was caused by multiple failed transactions on my card due to insufficient funds`
  String
  get thisWasCausedByMultipleFailedTransactionsOnMyCardDueToInsufficientFunds {
    return Intl.message(
      'This was caused by multiple failed transactions on my card due to insufficient funds',
      name:
          'thisWasCausedByMultipleFailedTransactionsOnMyCardDueToInsufficientFunds',
      desc: '',
      args: [],
    );
  }

  /// `I'll ensure my card is well funded for any transactions on it`
  String get illEnsureMyCardIsWellFundedForAnyTransactionsOnIt {
    return Intl.message(
      'I\'ll ensure my card is well funded for any transactions on it',
      name: 'illEnsureMyCardIsWellFundedForAnyTransactionsOnIt',
      desc: '',
      args: [],
    );
  }

  /// `I can reactivate now, but another failure will lead to my card being terminated`
  String get iCanReactivateNowButAnotherFailureWillLeadToMyCardBeingTerminated {
    return Intl.message(
      'I can reactivate now, but another failure will lead to my card being terminated',
      name: 'iCanReactivateNowButAnotherFailureWillLeadToMyCardBeingTerminated',
      desc: '',
      args: [],
    );
  }

  /// `Earn $$ for each friend you invite!`
  String get referAndEarnTitle {
    return Intl.message(
      'Earn \$\$ for each friend you invite!',
      name: 'referAndEarnTitle',
      desc: '',
      args: [],
    );
  }

  /// `You earn a bonus when your friend`
  String get youEarnBonus {
    return Intl.message(
      'You earn a bonus when your friend',
      name: 'youEarnBonus',
      desc: '',
      args: [],
    );
  }

  /// `Signs up with your link`
  String get signUpWithYourLink {
    return Intl.message(
      'Signs up with your link',
      name: 'signUpWithYourLink',
      desc: '',
      args: [],
    );
  }

  /// `Verifies their identity`
  String get verifiesIdentity {
    return Intl.message(
      'Verifies their identity',
      name: 'verifiesIdentity',
      desc: '',
      args: [],
    );
  }

  /// `Performs qualifying action. See terms`
  String get qualifyingMessages {
    return Intl.message(
      'Performs qualifying action. See terms',
      name: 'qualifyingMessages',
      desc: '',
      args: [],
    );
  }

  /// `Invite friends 👋`
  String get inviteFriends {
    return Intl.message(
      'Invite friends 👋',
      name: 'inviteFriends',
      desc: '',
      args: [],
    );
  }

  /// `🎁`
  String get giftEmoji {
    return Intl.message('🎁', name: 'giftEmoji', desc: '', args: []);
  }

  /// `Get a {cardCurrency} card`
  String getAUsdCard(Object cardCurrency) {
    return Intl.message(
      'Get a $cardCurrency card',
      name: 'getAUsdCard',
      desc: '',
      args: [cardCurrency],
    );
  }

  /// `Pay anywhere with Onboard `
  String get payAnywhereWithOnboard {
    return Intl.message(
      'Pay anywhere with Onboard ',
      name: 'payAnywhereWithOnboard',
      desc: '',
      args: [],
    );
  }

  /// `Pay anywhere with an Onboard \ncard`
  String get payAnywhereWithAnOnboardCard {
    return Intl.message(
      'Pay anywhere with an Onboard \ncard',
      name: 'payAnywhereWithAnOnboardCard',
      desc: '',
      args: [],
    );
  }

  /// `Earn $$ for every invitee `
  String get earnForEveryInvitee {
    return Intl.message(
      'Earn \$\$ for every invitee ',
      name: 'earnForEveryInvitee',
      desc: '',
      args: [],
    );
  }

  /// `Sell any crypto`
  String get sellAnyCrypto {
    return Intl.message(
      'Sell any crypto',
      name: 'sellAnyCrypto',
      desc: '',
      args: [],
    );
  }

  /// `Go from any crypto token to \ncash`
  String get goFromAnyCryptoTokenToCash {
    return Intl.message(
      'Go from any crypto token to \ncash',
      name: 'goFromAnyCryptoTokenToCash',
      desc: '',
      args: [],
    );
  }

  /// `Get your friends to sign up and earn`
  String get getYourFriendsToSignUpAndEarn {
    return Intl.message(
      'Get your friends to sign up and earn',
      name: 'getYourFriendsToSignUpAndEarn',
      desc: '',
      args: [],
    );
  }

  /// `Any pending card fees will still be charged.`
  String get anyPendingCardFeesWillStillBeCharged {
    return Intl.message(
      'Any pending card fees will still be charged.',
      name: 'anyPendingCardFeesWillStillBeCharged',
      desc: '',
      args: [],
    );
  }

  /// `Account`
  String get account {
    return Intl.message('Account', name: 'account', desc: '', args: []);
  }

  /// `Personal details`
  String get personalDetails {
    return Intl.message(
      'Personal details',
      name: 'personalDetails',
      desc: '',
      args: [],
    );
  }

  /// `Wallet details`
  String get walletDetails {
    return Intl.message(
      'Wallet details',
      name: 'walletDetails',
      desc: '',
      args: [],
    );
  }

  /// `Rate our app`
  String get rateOurApp {
    return Intl.message('Rate our app', name: 'rateOurApp', desc: '', args: []);
  }

  /// `We’ve moved things around`
  String get weVeMovedThingsAround {
    return Intl.message(
      'We’ve moved things around',
      name: 'weVeMovedThingsAround',
      desc: '',
      args: [],
    );
  }

  /// `Onboard is feeling brand new!`
  String get onboardIsFeelingBrandNew {
    return Intl.message(
      'Onboard is feeling brand new!',
      name: 'onboardIsFeelingBrandNew',
      desc: '',
      args: [],
    );
  }

  /// `See where to view wallet security`
  String get seeWhereToViewWalletSecurity {
    return Intl.message(
      'See where to view wallet security',
      name: 'seeWhereToViewWalletSecurity',
      desc: '',
      args: [],
    );
  }

  /// `Sell {asset} or any crypto token for {fiat_currency}`
  String sellAssetOrAnyCryptoTokenForFiat(Object asset, Object fiat_currency) {
    return Intl.message(
      'Sell $asset or any crypto token for $fiat_currency',
      name: 'sellAssetOrAnyCryptoTokenForFiat',
      desc: '',
      args: [asset, fiat_currency],
    );
  }

  /// `Choose from a variety of payment \nmethods and providers`
  String get chooseFromAVarietyOfPaymentMethodsAndProviders {
    return Intl.message(
      'Choose from a variety of payment \nmethods and providers',
      name: 'chooseFromAVarietyOfPaymentMethodsAndProviders',
      desc: '',
      args: [],
    );
  }

  /// `Connect another account`
  String get connectAnotherAccount {
    return Intl.message(
      'Connect another account',
      name: 'connectAnotherAccount',
      desc: '',
      args: [],
    );
  }

  /// `provider`
  String get provider {
    return Intl.message('provider', name: 'provider', desc: '', args: []);
  }

  /// `How’s your\n experience so far?`
  String get howsYourExperienceSoFar {
    return Intl.message(
      'How’s your\n experience so far?',
      name: 'howsYourExperienceSoFar',
      desc: '',
      args: [],
    );
  }

  /// `We’d love to know!`
  String get wellLoveToKnow {
    return Intl.message(
      'We’d love to know!',
      name: 'wellLoveToKnow',
      desc: '',
      args: [],
    );
  }

  /// `Thanks for sharing`
  String get thanksForSharing {
    return Intl.message(
      'Thanks for sharing',
      name: 'thanksForSharing',
      desc: '',
      args: [],
    );
  }

  /// `A new and improved \ndashboard ✨`
  String get aNewAndImprovedDashboard {
    return Intl.message(
      'A new and improved \ndashboard ✨',
      name: 'aNewAndImprovedDashboard',
      desc: '',
      args: [],
    );
  }

  /// `Supercharge your crypto portfolio 📈`
  String get superChargeYourCryptoPortfolio {
    return Intl.message(
      'Supercharge your crypto portfolio 📈',
      name: 'superChargeYourCryptoPortfolio',
      desc: '',
      args: [],
    );
  }

  /// `You can now buy and sell any\ncrypto asset and keep track of\nprice changes over time`
  String get superChargeYourCryptoPortfolioSubCopy {
    return Intl.message(
      'You can now buy and sell any\ncrypto asset and keep track of\nprice changes over time',
      name: 'superChargeYourCryptoPortfolioSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Tap the new `
  String get tapTheNew {
    return Intl.message('Tap the new ', name: 'tapTheNew', desc: '', args: []);
  }

  /// `A unified view for your crypto`
  String get aUnifiedViewForYourCrypto {
    return Intl.message(
      'A unified view for your crypto',
      name: 'aUnifiedViewForYourCrypto',
      desc: '',
      args: [],
    );
  }

  /// `We now show your balances across\nall networks, and a simplified view\nfor each token you hold`
  String get aUnifiedViewForYourCryptoSubCopy {
    return Intl.message(
      'We now show your balances across\nall networks, and a simplified view\nfor each token you hold',
      name: 'aUnifiedViewForYourCryptoSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `No matching offers`
  String get noMatchingOffers {
    return Intl.message(
      'No matching offers',
      name: 'noMatchingOffers',
      desc: '',
      args: [],
    );
  }

  /// `Delivery method not available`
  String get deliveryMethodNotAvailable {
    return Intl.message(
      'Delivery method not available',
      name: 'deliveryMethodNotAvailable',
      desc: '',
      args: [],
    );
  }

  /// `Payment method not available`
  String get paymentMethodNotAvailable {
    return Intl.message(
      'Payment method not available',
      name: 'paymentMethodNotAvailable',
      desc: '',
      args: [],
    );
  }

  /// `Unfortunately, the selected provider cannot process your desired transaction. Please choose a new one to proceed.`
  String get deliveryMethodNotAvailableSubCopy {
    return Intl.message(
      'Unfortunately, the selected provider cannot process your desired transaction. Please choose a new one to proceed.',
      name: 'deliveryMethodNotAvailableSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Change payment provider`
  String get changePaymentProvider {
    return Intl.message(
      'Change payment provider',
      name: 'changePaymentProvider',
      desc: '',
      args: [],
    );
  }

  /// `Change delivery method`
  String get changeDeliveryMethod {
    return Intl.message(
      'Change delivery method',
      name: 'changeDeliveryMethod',
      desc: '',
      args: [],
    );
  }

  /// `Managing your money just got easier.\nYour card & crypto balances, now in one place`
  String get newAndImprovedDashboardSubCopy {
    return Intl.message(
      'Managing your money just got easier.\nYour card & crypto balances, now in one place',
      name: 'newAndImprovedDashboardSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Take a quick tour of what's changed\nin the app.`
  String get takeAQuickTourOfWhatsChangedInTheApp {
    return Intl.message(
      'Take a quick tour of what\'s changed\nin the app.',
      name: 'takeAQuickTourOfWhatsChangedInTheApp',
      desc: '',
      args: [],
    );
  }

  /// `We're going to keep working hard \nto improve your Onboard experience`
  String get wereGoingToKeepWorkingToImproveYourOnboardExperience {
    return Intl.message(
      'We\'re going to keep working hard \nto improve your Onboard experience',
      name: 'wereGoingToKeepWorkingToImproveYourOnboardExperience',
      desc: '',
      args: [],
    );
  }

  /// `Delivery methods not currently available for this asset`
  String get deliveryMethodsNotCurrentlySupportedForThisAsset {
    return Intl.message(
      'Delivery methods not currently available for this asset',
      name: 'deliveryMethodsNotCurrentlySupportedForThisAsset',
      desc: '',
      args: [],
    );
  }

  /// `Join me and many others using Onboard to make their money borderless. Sign up with my link, earn a reward: {URL}`
  String shareReferralLinkCopy(Object URL) {
    return Intl.message(
      'Join me and many others using Onboard to make their money borderless. Sign up with my link, earn a reward: $URL',
      name: 'shareReferralLinkCopy',
      desc: '',
      args: [URL],
    );
  }

  /// `This is an invalid ID number for this ID type`
  String get idValidationErrorMessage {
    return Intl.message(
      'This is an invalid ID number for this ID type',
      name: 'idValidationErrorMessage',
      desc: '',
      args: [],
    );
  }

  /// `Transaction ID copied!`
  String get transactionIdCopied {
    return Intl.message(
      'Transaction ID copied!',
      name: 'transactionIdCopied',
      desc: '',
      args: [],
    );
  }

  /// `Introducing Direct Account`
  String get introducingDirectAccount {
    return Intl.message(
      'Introducing Direct Account',
      name: 'introducingDirectAccount',
      desc: '',
      args: [],
    );
  }

  /// `Crypto to cash in minutes`
  String get cryptoToCashInMinutes {
    return Intl.message(
      'Crypto to cash in minutes',
      name: 'cryptoToCashInMinutes',
      desc: '',
      args: [],
    );
  }

  /// `How it works`
  String get howItWorks {
    return Intl.message('How it works', name: 'howItWorks', desc: '', args: []);
  }

  /// `Receive as cash`
  String get receiveAsCash {
    return Intl.message(
      'Receive as cash',
      name: 'receiveAsCash',
      desc: '',
      args: [],
    );
  }

  /// `Show me!`
  String get showMe {
    return Intl.message('Show me!', name: 'showMe', desc: '', args: []);
  }

  /// `You don’t have any {currency} direct\naccount`
  String youDontHaveAnyDirectAccounts(Object currency) {
    return Intl.message(
      'You don’t have any $currency direct\naccount',
      name: 'youDontHaveAnyDirectAccounts',
      desc: '',
      args: [currency],
    );
  }

  /// `When you send stablecoins to your dedicated wallet address, it's converted to cash in your bank account in minutes.`
  String get whenYouSendStableCoinsToYourDedicatedWalletAddress {
    return Intl.message(
      'When you send stablecoins to your dedicated wallet address, it\'s converted to cash in your bank account in minutes.',
      name: 'whenYouSendStableCoinsToYourDedicatedWalletAddress',
      desc: '',
      args: [],
    );
  }

  /// `No more delays or manual transactions`
  String get noMoreDelaysOrManualTransactions {
    return Intl.message(
      'No more delays or manual transactions',
      name: 'noMoreDelaysOrManualTransactions',
      desc: '',
      args: [],
    );
  }

  /// `Direct gives you dedicated \nwallet addresses linked to your bank account`
  String get directGivesYouDedicatedWalletAddressLinkedToAccount {
    return Intl.message(
      'Direct gives you dedicated \nwallet addresses linked to your bank account',
      name: 'directGivesYouDedicatedWalletAddressLinkedToAccount',
      desc: '',
      args: [],
    );
  }

  /// `Hub`
  String get hub {
    return Intl.message('Hub', name: 'hub', desc: '', args: []);
  }

  /// `Apps`
  String get apps {
    return Intl.message('Apps', name: 'apps', desc: '', args: []);
  }

  /// `Debit Card`
  String get debitCard {
    return Intl.message('Debit Card', name: 'debitCard', desc: '', args: []);
  }

  /// `Lifestyle`
  String get lifeStyle {
    return Intl.message('Lifestyle', name: 'lifeStyle', desc: '', args: []);
  }

  /// `Sell crypto`
  String get sellCrypto {
    return Intl.message('Sell crypto', name: 'sellCrypto', desc: '', args: []);
  }

  /// `Direct account`
  String get directAccount {
    return Intl.message(
      'Direct account',
      name: 'directAccount',
      desc: '',
      args: [],
    );
  }

  /// `P2P Exchange`
  String get p2pExchange {
    return Intl.message(
      'P2P Exchange',
      name: 'p2pExchange',
      desc: '',
      args: [],
    );
  }

  /// `Sell any crypto token for cash`
  String get sellAnyCryptoTokenForCash {
    return Intl.message(
      'Sell any crypto token for cash',
      name: 'sellAnyCryptoTokenForCash',
      desc: '',
      args: [],
    );
  }

  /// `Trade crypto on a marketplace`
  String get tradeCryptoOnAMarketPlace {
    return Intl.message(
      'Trade crypto on a marketplace',
      name: 'tradeCryptoOnAMarketPlace',
      desc: '',
      args: [],
    );
  }

  /// `Earn rewards`
  String get earnRewards {
    return Intl.message(
      'Earn rewards',
      name: 'earnRewards',
      desc: '',
      args: [],
    );
  }

  /// `Compass`
  String get compass {
    return Intl.message('Compass', name: 'compass', desc: '', args: []);
  }

  /// `Yes, proceed`
  String get yesProceed {
    return Intl.message('Yes, proceed', name: 'yesProceed', desc: '', args: []);
  }

  /// `Heads up. You’re being redirected to an external application`
  String get headsUpYoureBeingRedirectedToAnExternalApplication {
    return Intl.message(
      'Heads up. You’re being redirected to an external application',
      name: 'headsUpYoureBeingRedirectedToAnExternalApplication',
      desc: '',
      args: [],
    );
  }

  /// `Invite friends, earn bonuses`
  String get inviteFriendsEarnBonuses {
    return Intl.message(
      'Invite friends, earn bonuses',
      name: 'inviteFriendsEarnBonuses',
      desc: '',
      args: [],
    );
  }

  /// `Get the best personal finance tips`
  String get getTheBestPersonalFinanceTips {
    return Intl.message(
      'Get the best personal finance tips',
      name: 'getTheBestPersonalFinanceTips',
      desc: '',
      args: [],
    );
  }

  /// `Add new account`
  String get addNewAccount {
    return Intl.message(
      'Add new account',
      name: 'addNewAccount',
      desc: '',
      args: [],
    );
  }

  /// `Your {count, plural, =0{order} =1{order} other{orders}} needs attention`
  String yourOrderNeedsAttention(num count) {
    return Intl.message(
      'Your ${Intl.plural(count, zero: 'order', one: 'order', other: 'orders')} needs attention',
      name: 'yourOrderNeedsAttention',
      desc: '',
      args: [count],
    );
  }

  /// `Verification cancelled`
  String get verificationCancelled {
    return Intl.message(
      'Verification cancelled',
      name: 'verificationCancelled',
      desc: '',
      args: [],
    );
  }

  /// `How would you like to fund your card?`
  String get howWouldYouLikeToFundYourCard {
    return Intl.message(
      'How would you like to fund your card?',
      name: 'howWouldYouLikeToFundYourCard',
      desc: '',
      args: [],
    );
  }

  /// `Funding method is currently not available for the selected country`
  String get fundingMethodIsCurrentlyNotAvailableForTheSelectedCountry {
    return Intl.message(
      'Funding method is currently not available for the selected country',
      name: 'fundingMethodIsCurrentlyNotAvailableForTheSelectedCountry',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawal method is currently not available for the selected country`
  String get withdrawalMethodIsCurrentlyNotAvailableForThisCountry {
    return Intl.message(
      'Withdrawal method is currently not available for the selected country',
      name: 'withdrawalMethodIsCurrentlyNotAvailableForThisCountry',
      desc: '',
      args: [],
    );
  }

  /// `Where would you like to transfer funds to?`
  String get whereWouldYouLikeToTransferYourFundsTo {
    return Intl.message(
      'Where would you like to transfer funds to?',
      name: 'whereWouldYouLikeToTransferYourFundsTo',
      desc: '',
      args: [],
    );
  }

  /// `Delivery method`
  String get deliveryMethod {
    return Intl.message(
      'Delivery method',
      name: 'deliveryMethod',
      desc: '',
      args: [],
    );
  }

  /// `Bank Transfer`
  String get bankTransfer {
    return Intl.message(
      'Bank Transfer',
      name: 'bankTransfer',
      desc: '',
      args: [],
    );
  }

  /// `Please check your network connection and try again`
  String get noInternetConnectionError {
    return Intl.message(
      'Please check your network connection and try again',
      name: 'noInternetConnectionError',
      desc: '',
      args: [],
    );
  }

  /// `This transaction is being processed by the merchant, outside the US`
  String get thisTransactionIsBeingProcessedByChargedByAmerchantOutsideTheUs {
    return Intl.message(
      'This transaction is being processed by the merchant, outside the US',
      name: 'thisTransactionIsBeingProcessedByChargedByAmerchantOutsideTheUs',
      desc: '',
      args: [],
    );
  }

  /// `Therefore, a cross-border fee will be charged separately.`
  String get thereforeACrossBorderFeeWillBeCharged {
    return Intl.message(
      'Therefore, a cross-border fee will be charged separately.',
      name: 'thereforeACrossBorderFeeWillBeCharged',
      desc: '',
      args: [],
    );
  }

  /// `As this is a non-{currency} transaction`
  String asThisIsANonUSDTransaction(Object currency) {
    return Intl.message(
      'As this is a non-$currency transaction',
      name: 'asThisIsANonUSDTransaction',
      desc: '',
      args: [currency],
    );
  }

  /// `an FX conversion fee will be charged separately.`
  String get anFXConversionFeeWillBeChargedSeparately {
    return Intl.message(
      'an FX conversion fee will be charged separately.',
      name: 'anFXConversionFeeWillBeChargedSeparately',
      desc: '',
      args: [],
    );
  }

  /// `Why was this fee charged?`
  String get whyWasThisFeeCharged {
    return Intl.message(
      'Why was this fee charged?',
      name: 'whyWasThisFeeCharged',
      desc: '',
      args: [],
    );
  }

  /// `Currency conversion fees`
  String get currencyConversionFees {
    return Intl.message(
      'Currency conversion fees',
      name: 'currencyConversionFees',
      desc: '',
      args: [],
    );
  }

  /// `Okay`
  String get okay {
    return Intl.message('Okay', name: 'okay', desc: '', args: []);
  }

  /// `Action could not be completed. Please contact support`
  String get actionCouldNotBeCompletedPleaseContactSupport {
    return Intl.message(
      'Action could not be completed. Please contact support',
      name: 'actionCouldNotBeCompletedPleaseContactSupport',
      desc: '',
      args: [],
    );
  }

  /// `Your KYC verification failed due to a data mismatch. Please try again.`
  String get kycFailedDescription {
    return Intl.message(
      'Your KYC verification failed due to a data mismatch. Please try again.',
      name: 'kycFailedDescription',
      desc: '',
      args: [],
    );
  }

  /// `Want to level up?`
  String get wantToLevelUp {
    return Intl.message(
      'Want to level up?',
      name: 'wantToLevelUp',
      desc: '',
      args: [],
    );
  }

  /// `Transfer directly to your bank account`
  String get transferDirectlyToYourBankAccount {
    return Intl.message(
      'Transfer directly to your bank account',
      name: 'transferDirectlyToYourBankAccount',
      desc: '',
      args: [],
    );
  }

  /// `Join 10k+ people gaining financial knowledge`
  String get join10kPeopleGainingFinancialKnowledge {
    return Intl.message(
      'Join 10k+ people gaining financial knowledge',
      name: 'join10kPeopleGainingFinancialKnowledge',
      desc: '',
      args: [],
    );
  }

  /// `What would you like to do now?`
  String get whatWouldYouLikeToDoNow {
    return Intl.message(
      'What would you like to do now?',
      name: 'whatWouldYouLikeToDoNow',
      desc: '',
      args: [],
    );
  }

  /// `Invest in crypto`
  String get investInCrypto {
    return Intl.message(
      'Invest in crypto',
      name: 'investInCrypto',
      desc: '',
      args: [],
    );
  }

  /// `Start saving in dollars`
  String get startSavingInDollars {
    return Intl.message(
      'Start saving in dollars',
      name: 'startSavingInDollars',
      desc: '',
      args: [],
    );
  }

  /// `I want to learn about crypto`
  String get iWantToLearnAboutCrypto {
    return Intl.message(
      'I want to learn about crypto',
      name: 'iWantToLearnAboutCrypto',
      desc: '',
      args: [],
    );
  }

  /// `Don't worry, you can still try out the other options later`
  String get dontWorryYouCanStillTryOutOtherOptionsLater {
    return Intl.message(
      'Don\'t worry, you can still try out the other options later',
      name: 'dontWorryYouCanStillTryOutOtherOptionsLater',
      desc: '',
      args: [],
    );
  }

  /// `I’m here to explore the app`
  String get imHereToExploreTheApp {
    return Intl.message(
      'I’m here to explore the app',
      name: 'imHereToExploreTheApp',
      desc: '',
      args: [],
    );
  }

  /// `The USD card that always works`
  String get introducingOnboardVirtualCards {
    return Intl.message(
      'The USD card that always works',
      name: 'introducingOnboardVirtualCards',
      desc: '',
      args: [],
    );
  }

  /// `Go to dashboard`
  String get gotoDashboard {
    return Intl.message(
      'Go to dashboard',
      name: 'gotoDashboard',
      desc: '',
      args: [],
    );
  }

  /// `Don’t worry, we will notify you once successful`
  String get notifyYouWhenDone {
    return Intl.message(
      'Don’t worry, we will notify you once successful',
      name: 'notifyYouWhenDone',
      desc: '',
      args: [],
    );
  }

  /// `Protect the value of your money`
  String get protectTheValueOfYourMoney {
    return Intl.message(
      'Protect the value of your money',
      name: 'protectTheValueOfYourMoney',
      desc: '',
      args: [],
    );
  }

  /// `Save in {tokenSymbol}`
  String saveInUsdc(Object tokenSymbol) {
    return Intl.message(
      'Save in $tokenSymbol',
      name: 'saveInUsdc',
      desc: '',
      args: [tokenSymbol],
    );
  }

  /// `Choose crypto`
  String get chooseCrypto {
    return Intl.message(
      'Choose crypto',
      name: 'chooseCrypto',
      desc: '',
      args: [],
    );
  }

  /// `Top`
  String get top {
    return Intl.message('Top', name: 'top', desc: '', args: []);
  }

  /// `Gainers`
  String get gainers {
    return Intl.message('Gainers', name: 'gainers', desc: '', args: []);
  }

  /// `Grow your money when you save in a digital dollar like {tokenSymbol}`
  String growYourMoneyWhenYouSaveInADigitalDollarLikeUSDC(Object tokenSymbol) {
    return Intl.message(
      'Grow your money when you save in a digital dollar like $tokenSymbol',
      name: 'growYourMoneyWhenYouSaveInADigitalDollarLikeUSDC',
      desc: '',
      args: [tokenSymbol],
    );
  }

  /// `1 {tokenSymbol} ≈ {rate} USD`
  String usdcEqualsUSd(Object tokenSymbol, Object rate) {
    return Intl.message(
      '1 $tokenSymbol ≈ $rate USD',
      name: 'usdcEqualsUSd',
      desc: '',
      args: [tokenSymbol, rate],
    );
  }

  /// `{tokenSymbol} is US regulated`
  String usdcIsUsRegulated(Object tokenSymbol) {
    return Intl.message(
      '$tokenSymbol is US regulated',
      name: 'usdcIsUsRegulated',
      desc: '',
      args: [tokenSymbol],
    );
  }

  /// `Withdraw your dollars anytime`
  String get withdrawYourDollarsAnytime {
    return Intl.message(
      'Withdraw your dollars anytime',
      name: 'withdrawYourDollarsAnytime',
      desc: '',
      args: [],
    );
  }

  /// `Easy & fast withdrawals, 24/7`
  String get easyAndFastWithdrawal247 {
    return Intl.message(
      'Easy & fast withdrawals, 24/7',
      name: 'easyAndFastWithdrawal247',
      desc: '',
      args: [],
    );
  }

  /// `Money`
  String get money {
    return Intl.message('Money', name: 'money', desc: '', args: []);
  }

  /// `Earn`
  String get earn {
    return Intl.message('Earn', name: 'earn', desc: '', args: []);
  }

  /// `Crypto`
  String get crypto {
    return Intl.message('Crypto', name: 'crypto', desc: '', args: []);
  }

  /// `{tokenSymbol} is a digital dollar that is regulated and fully reserved. Reserves are held at US financial institutions`
  String usdcRegulationSubCopy(Object tokenSymbol) {
    return Intl.message(
      '$tokenSymbol is a digital dollar that is regulated and fully reserved. Reserves are held at US financial institutions',
      name: 'usdcRegulationSubCopy',
      desc: '',
      args: [tokenSymbol],
    );
  }

  /// `Choose payment method`
  String get choosePaymentMethod {
    return Intl.message(
      'Choose payment method',
      name: 'choosePaymentMethod',
      desc: '',
      args: [],
    );
  }

  /// `on`
  String get on {
    return Intl.message('on', name: 'on', desc: '', args: []);
  }

  /// `You can switch networks here 👋`
  String get youCanSwitchNetworksHere {
    return Intl.message(
      'You can switch networks here 👋',
      name: 'youCanSwitchNetworksHere',
      desc: '',
      args: [],
    );
  }

  /// `Need to convert your money to local currency? We've got you`
  String get needTOConvertYourMoneyToALocalCurrency {
    return Intl.message(
      'Need to convert your money to local currency? We\'ve got you',
      name: 'needTOConvertYourMoneyToALocalCurrency',
      desc: '',
      args: [],
    );
  }

  /// `Featured`
  String get featured {
    return Intl.message('Featured', name: 'featured', desc: '', args: []);
  }

  /// `Discover crypto`
  String get discoverCrypto {
    return Intl.message(
      'Discover crypto',
      name: 'discoverCrypto',
      desc: '',
      args: [],
    );
  }

  /// `See more`
  String get seeMore {
    return Intl.message('See more', name: 'seeMore', desc: '', args: []);
  }

  /// `Connect to other applications`
  String get connectOtherApplication {
    return Intl.message(
      'Connect to other applications',
      name: 'connectOtherApplication',
      desc: '',
      args: [],
    );
  }

  /// `Balance hidden!`
  String get balanceHidden {
    return Intl.message(
      'Balance hidden!',
      name: 'balanceHidden',
      desc: '',
      args: [],
    );
  }

  /// `Your balances will no longer be visible. \n Tap the icon beside your balance to unhide`
  String get balanceHiddenSubCopy {
    return Intl.message(
      'Your balances will no longer be visible. \n Tap the icon beside your balance to unhide',
      name: 'balanceHiddenSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Start`
  String get start {
    return Intl.message('Start', name: 'start', desc: '', args: []);
  }

  /// `Finish setup`
  String get finishSetup {
    return Intl.message(
      'Finish setup',
      name: 'finishSetup',
      desc: '',
      args: [],
    );
  }

  /// `Let's finish these tasks to get Onboard!`
  String get finishSetupSubCopy {
    return Intl.message(
      'Let\'s finish these tasks to get Onboard!',
      name: 'finishSetupSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `My Board Pass`
  String get myBoardPass {
    return Intl.message(
      'My Board Pass',
      name: 'myBoardPass',
      desc: '',
      args: [],
    );
  }

  /// `Import wallet with?`
  String get importWalletWith {
    return Intl.message(
      'Import wallet with?',
      name: 'importWalletWith',
      desc: '',
      args: [],
    );
  }

  /// `Choose how you’d like to import an \nexisting wallet `
  String get chooseHowToImport {
    return Intl.message(
      'Choose how you’d like to import an \nexisting wallet ',
      name: 'chooseHowToImport',
      desc: '',
      args: [],
    );
  }

  /// `Recovery or Seed phrase`
  String get recoveryOrSeedPhrase {
    return Intl.message(
      'Recovery or Seed phrase',
      name: 'recoveryOrSeedPhrase',
      desc: '',
      args: [],
    );
  }

  /// `What does this mean?`
  String get whatDoeThisMean {
    return Intl.message(
      'What does this mean?',
      name: 'whatDoeThisMean',
      desc: '',
      args: [],
    );
  }

  /// `Your recovery or seed phrase is a 12 word special kind of password. It is another way to access your account and must be kept private`
  String get recoverySeedPhraseDescription {
    return Intl.message(
      'Your recovery or seed phrase is a 12 word special kind of password. It is another way to access your account and must be kept private',
      name: 'recoverySeedPhraseDescription',
      desc: '',
      args: [],
    );
  }

  /// `Your private key is a 64 character special kind of password. It is one way to access your account. Never share it with anyone!`
  String get privateKeyDescription {
    return Intl.message(
      'Your private key is a 64 character special kind of password. It is one way to access your account. Never share it with anyone!',
      name: 'privateKeyDescription',
      desc: '',
      args: [],
    );
  }

  /// `Enter your private key`
  String get enterYourPrivateKey {
    return Intl.message(
      'Enter your private key',
      name: 'enterYourPrivateKey',
      desc: '',
      args: [],
    );
  }

  /// `Paste or type private key`
  String get pasteOrTypePrivateKeyHere {
    return Intl.message(
      'Paste or type private key',
      name: 'pasteOrTypePrivateKeyHere',
      desc: '',
      args: [],
    );
  }

  /// `Your wallet is not backed up`
  String get yourWalletIsNotBackedUp {
    return Intl.message(
      'Your wallet is not backed up',
      name: 'yourWalletIsNotBackedUp',
      desc: '',
      args: [],
    );
  }

  /// `Verify your backup status`
  String get verifyBackupStatus {
    return Intl.message(
      'Verify your backup status',
      name: 'verifyBackupStatus',
      desc: '',
      args: [],
    );
  }

  /// `Confirm backup`
  String get confirmBackup {
    return Intl.message(
      'Confirm backup',
      name: 'confirmBackup',
      desc: '',
      args: [],
    );
  }

  /// `Sign to process transaction`
  String get signToProcessTransaction {
    return Intl.message(
      'Sign to process transaction',
      name: 'signToProcessTransaction',
      desc: '',
      args: [],
    );
  }

  /// `To complete this transaction, sign the message below`
  String get toCompleteThisTransactionSignTheMessageBelow {
    return Intl.message(
      'To complete this transaction, sign the message below',
      name: 'toCompleteThisTransactionSignTheMessageBelow',
      desc: '',
      args: [],
    );
  }

  /// `A fee applies for non-{currency} transactions or where the payment is processed by the merchant outside the {country}. This fee is from the card network, not Onboard.`
  String currencyConversionFeesExplainer(Object currency, Object country) {
    return Intl.message(
      'A fee applies for non-$currency transactions or where the payment is processed by the merchant outside the $country. This fee is from the card network, not Onboard.',
      name: 'currencyConversionFeesExplainer',
      desc: '',
      args: [currency, country],
    );
  }

  /// `Make payments globally`
  String get makePaymentGlobally {
    return Intl.message(
      'Make payments globally',
      name: 'makePaymentGlobally',
      desc: '',
      args: [],
    );
  }

  /// `Crypto Balance`
  String get cryptoBalance {
    return Intl.message(
      'Crypto Balance',
      name: 'cryptoBalance',
      desc: '',
      args: [],
    );
  }

  /// `My Portfolio`
  String get myPortfolio {
    return Intl.message(
      'My Portfolio',
      name: 'myPortfolio',
      desc: '',
      args: [],
    );
  }

  /// `Discover crypto gems`
  String get discoverCryptoGem {
    return Intl.message(
      'Discover crypto gems',
      name: 'discoverCryptoGem',
      desc: '',
      args: [],
    );
  }

  /// `Payment methods not currently available for this asset. Try another asset or network`
  String get noProviderCopy {
    return Intl.message(
      'Payment methods not currently available for this asset. Try another asset or network',
      name: 'noProviderCopy',
      desc: '',
      args: [],
    );
  }

  /// `About`
  String get about {
    return Intl.message('About', name: 'about', desc: '', args: []);
  }

  /// `Price`
  String get price {
    return Intl.message('Price', name: 'price', desc: '', args: []);
  }

  /// `Get a crypto portfolio`
  String get getCryptoPortfolio {
    return Intl.message(
      'Get a crypto portfolio',
      name: 'getCryptoPortfolio',
      desc: '',
      args: [],
    );
  }

  /// `Start by buying crypto for \nas little as $1`
  String get startByBuying {
    return Intl.message(
      'Start by buying crypto for \nas little as \$1',
      name: 'startByBuying',
      desc: '',
      args: [],
    );
  }

  /// `Set passcode, account backup`
  String get SetPasscodeAccountBackup {
    return Intl.message(
      'Set passcode, account backup',
      name: 'SetPasscodeAccountBackup',
      desc: '',
      args: [],
    );
  }

  /// `Increase your security`
  String get IncreaseYourSecurity {
    return Intl.message(
      'Increase your security',
      name: 'IncreaseYourSecurity',
      desc: '',
      args: [],
    );
  }

  /// `Choose delivery method`
  String get chooseDeliveryMethod {
    return Intl.message(
      'Choose delivery method',
      name: 'chooseDeliveryMethod',
      desc: '',
      args: [],
    );
  }

  /// `Get started on top tradable coins`
  String get getStartedOnTradableCoin {
    return Intl.message(
      'Get started on top tradable coins',
      name: 'getStartedOnTradableCoin',
      desc: '',
      args: [],
    );
  }

  /// `on {networkName}`
  String onNetworkName(Object networkName) {
    return Intl.message(
      'on $networkName',
      name: 'onNetworkName',
      desc: '',
      args: [networkName],
    );
  }

  /// `Missing or invalid. URI is not WalletConnect URI. \nTap anywhere to resume scan.`
  String get invalidWalletConnectUriTapToResume {
    return Intl.message(
      'Missing or invalid. URI is not WalletConnect URI. \nTap anywhere to resume scan.',
      name: 'invalidWalletConnectUriTapToResume',
      desc: '',
      args: [],
    );
  }

  /// `Review`
  String get review {
    return Intl.message('Review', name: 'review', desc: '', args: []);
  }

  /// `Create your new card`
  String get createYourNewCard {
    return Intl.message(
      'Create your new card',
      name: 'createYourNewCard',
      desc: '',
      args: [],
    );
  }

  /// `Subscriptions`
  String get subscriptions {
    return Intl.message(
      'Subscriptions',
      name: 'subscriptions',
      desc: '',
      args: [],
    );
  }

  /// `Shopping`
  String get shopping {
    return Intl.message('Shopping', name: 'shopping', desc: '', args: []);
  }

  /// `Vacation`
  String get vacation {
    return Intl.message('Vacation', name: 'vacation', desc: '', args: []);
  }

  /// `Card currency`
  String get cardCurrency {
    return Intl.message(
      'Card currency',
      name: 'cardCurrency',
      desc: '',
      args: [],
    );
  }

  /// `Card brand`
  String get cardBrand {
    return Intl.message('Card brand', name: 'cardBrand', desc: '', args: []);
  }

  /// `Name your card`
  String get nameYourCard {
    return Intl.message(
      'Name your card',
      name: 'nameYourCard',
      desc: '',
      args: [],
    );
  }

  /// `mastercard`
  String get masterCard {
    return Intl.message('mastercard', name: 'masterCard', desc: '', args: []);
  }

  /// `You're giving access to transact with this token`
  String get giveAccessToTransact {
    return Intl.message(
      'You\'re giving access to transact with this token',
      name: 'giveAccessToTransact',
      desc: '',
      args: [],
    );
  }

  /// `Your wallet backup is not synced to icloud`
  String get iCloudBackupPending {
    return Intl.message(
      'Your wallet backup is not synced to icloud',
      name: 'iCloudBackupPending',
      desc: '',
      args: [],
    );
  }

  /// `To avoid loss of funds, ensure your device is connected to the internet and you have some iCloud storage available. Open Settings > [Your Name] > iCloud to manage your storage.`
  String get iCloudBackupPendingDescription {
    return Intl.message(
      'To avoid loss of funds, ensure your device is connected to the internet and you have some iCloud storage available. Open Settings > [Your Name] > iCloud to manage your storage.',
      name: 'iCloudBackupPendingDescription',
      desc: '',
      args: [],
    );
  }

  /// `Or`
  String get or {
    return Intl.message('Or', name: 'or', desc: '', args: []);
  }

  /// `Try other backup options`
  String get tryExtraBackup {
    return Intl.message(
      'Try other backup options',
      name: 'tryExtraBackup',
      desc: '',
      args: [],
    );
  }

  /// `Generate a transaction statement`
  String get generateATransactionStatement {
    return Intl.message(
      'Generate a transaction statement',
      name: 'generateATransactionStatement',
      desc: '',
      args: [],
    );
  }

  /// `Statement`
  String get statement {
    return Intl.message('Statement', name: 'statement', desc: '', args: []);
  }

  /// `Nickname`
  String get nickName {
    return Intl.message('Nickname', name: 'nickName', desc: '', args: []);
  }

  /// `Pick a color`
  String get pickAColor {
    return Intl.message('Pick a color', name: 'pickAColor', desc: '', args: []);
  }

  /// `Get card statement`
  String get getCardStatement {
    return Intl.message(
      'Get card statement',
      name: 'getCardStatement',
      desc: '',
      args: [],
    );
  }

  /// `Request statement`
  String get requestStatement {
    return Intl.message(
      'Request statement',
      name: 'requestStatement',
      desc: '',
      args: [],
    );
  }

  /// `Choose your time range`
  String get chooseYourTimeRange {
    return Intl.message(
      'Choose your time range',
      name: 'chooseYourTimeRange',
      desc: '',
      args: [],
    );
  }

  /// `Create a new card`
  String get createANewCard {
    return Intl.message(
      'Create a new card',
      name: 'createANewCard',
      desc: '',
      args: [],
    );
  }

  /// `3 months`
  String get threeMonths {
    return Intl.message('3 months', name: 'threeMonths', desc: '', args: []);
  }

  /// `6 months`
  String get sixMonths {
    return Intl.message('6 months', name: 'sixMonths', desc: '', args: []);
  }

  /// `12 months`
  String get twelveMonths {
    return Intl.message('12 months', name: 'twelveMonths', desc: '', args: []);
  }

  /// `Turn on notification`
  String get turnOnNotification {
    return Intl.message(
      'Turn on notification',
      name: 'turnOnNotification',
      desc: '',
      args: [],
    );
  }

  /// `Bridge on any network`
  String get bridgeOnAnyNetwork {
    return Intl.message(
      'Bridge on any network',
      name: 'bridgeOnAnyNetwork',
      desc: '',
      args: [],
    );
  }

  /// `Move your crypto across networks. You can now swap crypto between our supported networks`
  String get bridgeOnAnyNetworkSubCopy {
    return Intl.message(
      'Move your crypto across networks. You can now swap crypto between our supported networks',
      name: 'bridgeOnAnyNetworkSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Card statement request \nsuccessful`
  String get cardStatementRequestSuccessful {
    return Intl.message(
      'Card statement request \nsuccessful',
      name: 'cardStatementRequestSuccessful',
      desc: '',
      args: [],
    );
  }

  /// `All set. A copy of your requested\ncard statement will be sent to your\nemail in a few minutes`
  String get allSetACopyOfYourCardStatementWillBeSentToYourMailInAFewMins {
    return Intl.message(
      'All set. A copy of your requested\ncard statement will be sent to your\nemail in a few minutes',
      name: 'allSetACopyOfYourCardStatementWillBeSentToYourMailInAFewMins',
      desc: '',
      args: [],
    );
  }

  /// `You have insufficient storage available on your Drive folder`
  String get insufficientStorage {
    return Intl.message(
      'You have insufficient storage available on your Drive folder',
      name: 'insufficientStorage',
      desc: '',
      args: [],
    );
  }

  /// `The verified address on this account doesn't match backup address`
  String get verifiedAddressNotCorrect {
    return Intl.message(
      'The verified address on this account doesn\'t match backup address',
      name: 'verifiedAddressNotCorrect',
      desc: '',
      args: [],
    );
  }

  /// `Physical card`
  String get physicalCard {
    return Intl.message(
      'Physical card',
      name: 'physicalCard',
      desc: '',
      args: [],
    );
  }

  /// `Create multiple cards`
  String get createMultipleCards {
    return Intl.message(
      'Create multiple cards',
      name: 'createMultipleCards',
      desc: '',
      args: [],
    );
  }

  /// `You have reached a max`
  String get youHaveReachedAMax {
    return Intl.message(
      'You have reached a max',
      name: 'youHaveReachedAMax',
      desc: '',
      args: [],
    );
  }

  /// `Ok, got it!`
  String get okGotIt {
    return Intl.message('Ok, got it!', name: 'okGotIt', desc: '', args: []);
  }

  /// `You can only have {max} Onboard virtual cards\nactive at a time. Deactivate any of your active\ncards to create a new one`
  String youHaveReachedMaxSubCopy(Object max) {
    return Intl.message(
      'You can only have $max Onboard virtual cards\nactive at a time. Deactivate any of your active\ncards to create a new one',
      name: 'youHaveReachedMaxSubCopy',
      desc: '',
      args: [max],
    );
  }

  /// `You now have more options! Tap on the\nplus icon to add a new card`
  String get createMultipleCardsSubCopy {
    return Intl.message(
      'You now have more options! Tap on the\nplus icon to add a new card',
      name: 'createMultipleCardsSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Should be complete within 10 minutes, but some can take up to 24 hours.`
  String get kycInProgressSubCopy {
    return Intl.message(
      'Should be complete within 10 minutes, but some can take up to 24 hours.',
      name: 'kycInProgressSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Yes, set up a new card`
  String get yesSetUpANewCard {
    return Intl.message(
      'Yes, set up a new card',
      name: 'yesSetUpANewCard',
      desc: '',
      args: [],
    );
  }

  /// `Please confirm`
  String get pleaseConfirm {
    return Intl.message(
      'Please confirm',
      name: 'pleaseConfirm',
      desc: '',
      args: [],
    );
  }

  /// `Creating a new card will replace my deactivated card`
  String get creatingANewCardWillReplaceMyDeactivatedCard {
    return Intl.message(
      'Creating a new card will replace my deactivated card',
      name: 'creatingANewCardWillReplaceMyDeactivatedCard',
      desc: '',
      args: [],
    );
  }

  /// `If the deactivation occurred recently, there may be a wait period before I can create a new card.`
  String get ifTheDeactivationOccurredRecently {
    return Intl.message(
      'If the deactivation occurred recently, there may be a wait period before I can create a new card.',
      name: 'ifTheDeactivationOccurredRecently',
      desc: '',
      args: [],
    );
  }

  /// `You have a previous card ({brand} {maskedPan}) that was deactivated. `
  String youHaveAPreviouslyTerminatedCardSubCopy(
    Object brand,
    Object maskedPan,
  ) {
    return Intl.message(
      'You have a previous card ($brand $maskedPan) that was deactivated. ',
      name: 'youHaveAPreviouslyTerminatedCardSubCopy',
      desc: '',
      args: [brand, maskedPan],
    );
  }

  /// `No route found`
  String get noRouteFound {
    return Intl.message(
      'No route found',
      name: 'noRouteFound',
      desc: '',
      args: [],
    );
  }

  /// `Refresh Balance`
  String get refreshBalance {
    return Intl.message(
      'Refresh Balance',
      name: 'refreshBalance',
      desc: '',
      args: [],
    );
  }

  /// `Buy with cash`
  String get buyWithCash {
    return Intl.message(
      'Buy with cash',
      name: 'buyWithCash',
      desc: '',
      args: [],
    );
  }

  /// `Buy {tokenSymbol} with your local currency`
  String buyTokenWithYourLocalCurrency(Object tokenSymbol) {
    return Intl.message(
      'Buy $tokenSymbol with your local currency',
      name: 'buyTokenWithYourLocalCurrency',
      desc: '',
      args: [tokenSymbol],
    );
  }

  /// `Buy with crypto`
  String get buyWithCrypto {
    return Intl.message(
      'Buy with crypto',
      name: 'buyWithCrypto',
      desc: '',
      args: [],
    );
  }

  /// `Exchange with other crypto pairs`
  String get exchangeWithOtherCryptoPairs {
    return Intl.message(
      'Exchange with other crypto pairs',
      name: 'exchangeWithOtherCryptoPairs',
      desc: '',
      args: [],
    );
  }

  /// `Sell to receive cash`
  String get sellToReceiveCash {
    return Intl.message(
      'Sell to receive cash',
      name: 'sellToReceiveCash',
      desc: '',
      args: [],
    );
  }

  /// `Sell to receive crypto`
  String get sellToReceiveCrypto {
    return Intl.message(
      'Sell to receive crypto',
      name: 'sellToReceiveCrypto',
      desc: '',
      args: [],
    );
  }

  /// `Receive cash in local currency`
  String get receiveCashInYourLocalCurrency {
    return Intl.message(
      'Receive cash in local currency',
      name: 'receiveCashInYourLocalCurrency',
      desc: '',
      args: [],
    );
  }

  /// `Inbox`
  String get inbox {
    return Intl.message('Inbox', name: 'inbox', desc: '', args: []);
  }

  /// `Mark all as read`
  String get markAllAsRead {
    return Intl.message(
      'Mark all as read',
      name: 'markAllAsRead',
      desc: '',
      args: [],
    );
  }

  /// `View your recent notifications here`
  String get notificationsSubtitle {
    return Intl.message(
      'View your recent notifications here',
      name: 'notificationsSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Need some help? Leave us a message`
  String get chatSubtitle {
    return Intl.message(
      'Need some help? Leave us a message',
      name: 'chatSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `View transaction`
  String get viewTransaction {
    return Intl.message(
      'View transaction',
      name: 'viewTransaction',
      desc: '',
      args: [],
    );
  }

  /// `View order`
  String get viewOrder {
    return Intl.message('View order', name: 'viewOrder', desc: '', args: []);
  }

  /// `Your Inbox is here`
  String get yourInboxIsHere {
    return Intl.message(
      'Your Inbox is here',
      name: 'yourInboxIsHere',
      desc: '',
      args: [],
    );
  }

  /// `Your notification history and our help desk now lives here. Never miss any of the things that matter`
  String get inboxTooltipDescription {
    return Intl.message(
      'Your notification history and our help desk now lives here. Never miss any of the things that matter',
      name: 'inboxTooltipDescription',
      desc: '',
      args: [],
    );
  }

  /// `Receive {currency} in your bank account`
  String receiveCurrencyInYourBankAccount(Object currency) {
    return Intl.message(
      'Receive $currency in your bank account',
      name: 'receiveCurrencyInYourBankAccount',
      desc: '',
      args: [currency],
    );
  }

  /// `What does this mean for me?`
  String get whatDoesThisMeanForMe {
    return Intl.message(
      'What does this mean for me?',
      name: 'whatDoesThisMeanForMe',
      desc: '',
      args: [],
    );
  }

  /// `KYC verification failed`
  String get kycVerificationFailed {
    return Intl.message(
      'KYC verification failed',
      name: 'kycVerificationFailed',
      desc: '',
      args: [],
    );
  }

  /// `KYC verification in progress`
  String get kycVerificationInProgress {
    return Intl.message(
      'KYC verification in progress',
      name: 'kycVerificationInProgress',
      desc: '',
      args: [],
    );
  }

  /// `KYC verification successful`
  String get kycVerificationSuccessful {
    return Intl.message(
      'KYC verification successful',
      name: 'kycVerificationSuccessful',
      desc: '',
      args: [],
    );
  }

  /// `No notifications yet`
  String get emptyNotifications {
    return Intl.message(
      'No notifications yet',
      name: 'emptyNotifications',
      desc: '',
      args: [],
    );
  }

  /// `Funding unavailable`
  String get fundingUnavailable {
    return Intl.message(
      'Funding unavailable',
      name: 'fundingUnavailable',
      desc: '',
      args: [],
    );
  }

  /// `Unfortunately, the selected asset cannot process your desired transaction. Please choose another one to proceed.`
  String get fundingUnavailableDescription {
    return Intl.message(
      'Unfortunately, the selected asset cannot process your desired transaction. Please choose another one to proceed.',
      name: 'fundingUnavailableDescription',
      desc: '',
      args: [],
    );
  }

  /// `Change asset`
  String get changeAsset {
    return Intl.message(
      'Change asset',
      name: 'changeAsset',
      desc: '',
      args: [],
    );
  }

  /// `Funding not currently available for this asset. Please try another asset.`
  String get pleaseTryAnotherAsset {
    return Intl.message(
      'Funding not currently available for this asset. Please try another asset.',
      name: 'pleaseTryAnotherAsset',
      desc: '',
      args: [],
    );
  }

  /// `Heads up`
  String get HeadsUp {
    return Intl.message('Heads up', name: 'HeadsUp', desc: '', args: []);
  }

  /// `Cards can be funded with cash or crypto. You've selected an asset which will first be swapped to a USD-based token in order to fund your card`
  String get nonStableCoinCardFundingNote {
    return Intl.message(
      'Cards can be funded with cash or crypto. You\'ve selected an asset which will first be swapped to a USD-based token in order to fund your card',
      name: 'nonStableCoinCardFundingNote',
      desc: '',
      args: [],
    );
  }

  /// `Yes, I understand`
  String get yesIUnderstand {
    return Intl.message(
      'Yes, I understand',
      name: 'yesIUnderstand',
      desc: '',
      args: [],
    );
  }

  /// `We're funding your card with {x} and will notify you when complete`
  String fundingCardWithXAmount(Object x) {
    return Intl.message(
      'We\'re funding your card with $x and will notify you when complete',
      name: 'fundingCardWithXAmount',
      desc: '',
      args: [x],
    );
  }

  /// `Travel`
  String get travel {
    return Intl.message('Travel', name: 'travel', desc: '', args: []);
  }

  /// `Work`
  String get work {
    return Intl.message('Work', name: 'work', desc: '', args: []);
  }

  /// `Let’s find out`
  String get letsFindOut {
    return Intl.message(
      'Let’s find out',
      name: 'letsFindOut',
      desc: '',
      args: [],
    );
  }

  /// `We're so excited to have you Onboard. Let's set your wallet up`
  String get wereSoExcitedToHaveYouSubCopy {
    return Intl.message(
      'We\'re so excited to have you Onboard. Let\'s set your wallet up',
      name: 'wereSoExcitedToHaveYouSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Set up  wallet`
  String get setUpWallet {
    return Intl.message(
      'Set up  wallet',
      name: 'setUpWallet',
      desc: '',
      args: [],
    );
  }

  /// `{name}, you're in!`
  String youReInCopy(Object name) {
    return Intl.message(
      '$name, you\'re in!',
      name: 'youReInCopy',
      desc: '',
      args: [name],
    );
  }

  /// `It's not you, it's us`
  String get itsNotYouItsUs {
    return Intl.message(
      'It\'s not you, it\'s us',
      name: 'itsNotYouItsUs',
      desc: '',
      args: [],
    );
  }

  /// `It doesn't appear our current product is the best fit for your needs right now. We'd love to let you know if this changes in future.`
  String get badFitSurveySubCopy {
    return Intl.message(
      'It doesn\'t appear our current product is the best fit for your needs right now. We\'d love to let you know if this changes in future.',
      name: 'badFitSurveySubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Tap to activate card`
  String get tapToActivateCard {
    return Intl.message(
      'Tap to activate card',
      name: 'tapToActivateCard',
      desc: '',
      args: [],
    );
  }

  /// `To keep Onboard safe and secure, we require verified KYC for transactions involving cash.`
  String get kycAttemptsInfo {
    return Intl.message(
      'To keep Onboard safe and secure, we require verified KYC for transactions involving cash.',
      name: 'kycAttemptsInfo',
      desc: '',
      args: [],
    );
  }

  /// `We're proud you're considering\nOnboard, but first - it's important to us to know if we can serve your needs.`
  String get onboardingSurveySubCopy {
    return Intl.message(
      'We\'re proud you\'re considering\nOnboard, but first - it\'s important to us to know if we can serve your needs.',
      name: 'onboardingSurveySubCopy',
      desc: '',
      args: [],
    );
  }

  /// `No Transactions found`
  String get noTransactionFound {
    return Intl.message(
      'No Transactions found',
      name: 'noTransactionFound',
      desc: '',
      args: [],
    );
  }

  /// `Swapped`
  String get swapped {
    return Intl.message('Swapped', name: 'swapped', desc: '', args: []);
  }

  /// `unknown method`
  String get unknownMethod {
    return Intl.message(
      'unknown method',
      name: 'unknownMethod',
      desc: '',
      args: [],
    );
  }

  /// `Search app or web`
  String get searchWeb {
    return Intl.message(
      'Search app or web',
      name: 'searchWeb',
      desc: '',
      args: [],
    );
  }

  /// `Disconnected`
  String get disconnected {
    return Intl.message(
      'Disconnected',
      name: 'disconnected',
      desc: '',
      args: [],
    );
  }

  /// `Do you want to be redirected to this\nexternal app?`
  String get dappRedirectToBrowser {
    return Intl.message(
      'Do you want to be redirected to this\nexternal app?',
      name: 'dappRedirectToBrowser',
      desc: '',
      args: [],
    );
  }

  /// `Open`
  String get open {
    return Intl.message('Open', name: 'open', desc: '', args: []);
  }

  /// `Unable to get fee`
  String get unableToGetFee {
    return Intl.message(
      'Unable to get fee',
      name: 'unableToGetFee',
      desc: '',
      args: [],
    );
  }

  /// `You get`
  String get youGet {
    return Intl.message('You get', name: 'youGet', desc: '', args: []);
  }

  /// `Transfer amount`
  String get transferAmount {
    return Intl.message(
      'Transfer amount',
      name: 'transferAmount',
      desc: '',
      args: [],
    );
  }

  /// `Show less`
  String get showLess {
    return Intl.message('Show less', name: 'showLess', desc: '', args: []);
  }

  /// `You haven’t searched any sites yet`
  String get noSearchHistory {
    return Intl.message(
      'You haven’t searched any sites yet',
      name: 'noSearchHistory',
      desc: '',
      args: [],
    );
  }

  /// `History`
  String get history {
    return Intl.message('History', name: 'history', desc: '', args: []);
  }

  /// `More cards. More options`
  String get moreCardsMoreOptions {
    return Intl.message(
      'More cards. More options',
      name: 'moreCardsMoreOptions',
      desc: '',
      args: [],
    );
  }

  /// `Get another card`
  String get getAnotherCard {
    return Intl.message(
      'Get another card',
      name: 'getAnotherCard',
      desc: '',
      args: [],
    );
  }

  /// `Don't limit yourself to just one. You can now create multiple Onboard Cards to manage your payments.`
  String get moreCardMoreOptionsSubCopy {
    return Intl.message(
      'Don\'t limit yourself to just one. You can now create multiple Onboard Cards to manage your payments.',
      name: 'moreCardMoreOptionsSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `You don't have enough of the {tokenSymbol} to swap`
  String swapInsufficientBalanceError(Object tokenSymbol) {
    return Intl.message(
      'You don\'t have enough of the $tokenSymbol to swap',
      name: 'swapInsufficientBalanceError',
      desc: '',
      args: [tokenSymbol],
    );
  }

  /// `Delete all tabs?`
  String get deleteAllTab {
    return Intl.message(
      'Delete all tabs?',
      name: 'deleteAllTab',
      desc: '',
      args: [],
    );
  }

  /// `This will clear all your currently active tabs`
  String get deleteAllTabWarning {
    return Intl.message(
      'This will clear all your currently active tabs',
      name: 'deleteAllTabWarning',
      desc: '',
      args: [],
    );
  }

  /// `Close`
  String get close {
    return Intl.message('Close', name: 'close', desc: '', args: []);
  }

  /// `No open tabs yet`
  String get emptyTabMessage {
    return Intl.message(
      'No open tabs yet',
      name: 'emptyTabMessage',
      desc: '',
      args: [],
    );
  }

  /// `Tabs`
  String get tabs {
    return Intl.message('Tabs', name: 'tabs', desc: '', args: []);
  }

  /// `Tab`
  String get tab {
    return Intl.message('Tab', name: 'tab', desc: '', args: []);
  }

  /// `Slippage`
  String get slippage {
    return Intl.message('Slippage', name: 'slippage', desc: '', args: []);
  }

  /// `Manage crypto`
  String get manageCrypto {
    return Intl.message(
      'Manage crypto',
      name: 'manageCrypto',
      desc: '',
      args: [],
    );
  }

  /// `Hidden crypto`
  String get hiddenCrypto {
    return Intl.message(
      'Hidden crypto',
      name: 'hiddenCrypto',
      desc: '',
      args: [],
    );
  }

  /// `Confirm slippage`
  String get confirmSlippage {
    return Intl.message(
      'Confirm slippage',
      name: 'confirmSlippage',
      desc: '',
      args: [],
    );
  }

  /// `Transaction may be front run and result in an unfavorable trade`
  String get yourTransactionMayBeFrontRunAndResultInAnUnfavourableTrade {
    return Intl.message(
      'Transaction may be front run and result in an unfavorable trade',
      name: 'yourTransactionMayBeFrontRunAndResultInAnUnfavourableTrade',
      desc: '',
      args: [],
    );
  }

  /// `Slippage cannot be more than {amount}`
  String slippageCannotBeMoreThan(Object amount) {
    return Intl.message(
      'Slippage cannot be more than $amount',
      name: 'slippageCannotBeMoreThan',
      desc: '',
      args: [amount],
    );
  }

  /// `Market prices can change between when you place an order and when it is executed.\n`
  String get marketPricesCanChangeBetweenWhenYouPlace {
    return Intl.message(
      'Market prices can change between when you place an order and when it is executed.\n',
      name: 'marketPricesCanChangeBetweenWhenYouPlace',
      desc: '',
      args: [],
    );
  }

  /// `This percentage is called the maximum slippage `
  String get thisPercentageIsCalledTheMaximumSlippage {
    return Intl.message(
      'This percentage is called the maximum slippage ',
      name: 'thisPercentageIsCalledTheMaximumSlippage',
      desc: '',
      args: [],
    );
  }

  /// `you're willing to accept before we stop the transaction.`
  String get youreWillingToAcceptBeforeWeStopTheTransaction {
    return Intl.message(
      'you\'re willing to accept before we stop the transaction.',
      name: 'youreWillingToAcceptBeforeWeStopTheTransaction',
      desc: '',
      args: [],
    );
  }

  /// `Your swap will not continue if the rate changes more than this percentage`
  String get yourSwapWillNotContinueIfTheRateChangesMoreThan {
    return Intl.message(
      'Your swap will not continue if the rate changes more than this percentage',
      name: 'yourSwapWillNotContinueIfTheRateChangesMoreThan',
      desc: '',
      args: [],
    );
  }

  /// `Sign one`
  String get signOne {
    return Intl.message('Sign one', name: 'signOne', desc: '', args: []);
  }

  /// `Sign all`
  String get signAll {
    return Intl.message('Sign all', name: 'signAll', desc: '', args: []);
  }

  /// `Message`
  String get message {
    return Intl.message('Message', name: 'message', desc: '', args: []);
  }

  /// `Send international payments`
  String get sendInternationalPayments {
    return Intl.message(
      'Send international payments',
      name: 'sendInternationalPayments',
      desc: '',
      args: [],
    );
  }

  /// `would like to connect`
  String get wouldLikeToConnect {
    return Intl.message(
      'would like to connect',
      name: 'wouldLikeToConnect',
      desc: '',
      args: [],
    );
  }

  /// `Welcome\nto your\ndigital money\napp`
  String get welcomeToYourDigitalMoneyApp {
    return Intl.message(
      'Welcome\nto your\ndigital money\napp',
      name: 'welcomeToYourDigitalMoneyApp',
      desc: '',
      args: [],
    );
  }

  /// `SPEND\nANYWHERE`
  String get spendAnywhere {
    return Intl.message(
      'SPEND\nANYWHERE',
      name: 'spendAnywhere',
      desc: '',
      args: [],
    );
  }

  /// `EXCHANGE\nFAST`
  String get exchangeFast {
    return Intl.message(
      'EXCHANGE\nFAST',
      name: 'exchangeFast',
      desc: '',
      args: [],
    );
  }

  /// `OWN\nYOUR MONEY`
  String get ownYourMoney {
    return Intl.message(
      'OWN\nYOUR MONEY',
      name: 'ownYourMoney',
      desc: '',
      args: [],
    );
  }

  /// `Accept EUR & USD globally, swiftly converted to digital dollars`
  String get acceptEurAndUsdGloballySwiftlyConvertedToDigitalDollars {
    return Intl.message(
      'Accept EUR & USD globally, swiftly converted to digital dollars',
      name: 'acceptEurAndUsdGloballySwiftlyConvertedToDigitalDollars',
      desc: '',
      args: [],
    );
  }

  /// `Get a debit card that just work, globally`
  String get getMultipleCardThatJustWorksGlobally {
    return Intl.message(
      'Get a debit card that just work, globally',
      name: 'getMultipleCardThatJustWorksGlobally',
      desc: '',
      args: [],
    );
  }

  /// `Switch from digital to real-world money, and back`
  String get switchFromDigitalToRealWorldMoneyAndBack {
    return Intl.message(
      'Switch from digital to real-world money, and back',
      name: 'switchFromDigitalToRealWorldMoneyAndBack',
      desc: '',
      args: [],
    );
  }

  /// `A wallet that keeps you (and only you!) in full control`
  String get aWalletThatKeepsYouInFullControl {
    return Intl.message(
      'A wallet that keeps you (and only you!) in full control',
      name: 'aWalletThatKeepsYouInFullControl',
      desc: '',
      args: [],
    );
  }

  /// `Cash to Crypto, fast`
  String get cashToCryptoFast {
    return Intl.message(
      'Cash to Crypto, fast',
      name: 'cashToCryptoFast',
      desc: '',
      args: [],
    );
  }

  /// `Unable to get payment method`
  String get unableToGetPaymentMethod {
    return Intl.message(
      'Unable to get payment method',
      name: 'unableToGetPaymentMethod',
      desc: '',
      args: [],
    );
  }

  /// `Unable to get token`
  String get unableToGetToken {
    return Intl.message(
      'Unable to get token',
      name: 'unableToGetToken',
      desc: '',
      args: [],
    );
  }

  /// `You have not added a {currency} account`
  String youHaveNotAddedAUsdOrEurAccountYet(Object currency) {
    return Intl.message(
      'You have not added a $currency account',
      name: 'youHaveNotAddedAUsdOrEurAccountYet',
      desc: '',
      args: [currency],
    );
  }

  /// `Receive international payments`
  String get receiveInternationalPayments {
    return Intl.message(
      'Receive international payments',
      name: 'receiveInternationalPayments',
      desc: '',
      args: [],
    );
  }

  /// `Move between stablecoins and USD / EUR at low fees`
  String get moveBetweenStablecoinsAndUsdEurAtLowFees {
    return Intl.message(
      'Move between stablecoins and USD / EUR at low fees',
      name: 'moveBetweenStablecoinsAndUsdEurAtLowFees',
      desc: '',
      args: [],
    );
  }

  /// `Global payments, made easy`
  String get globalPaymentsMadeEasy {
    return Intl.message(
      'Global payments, made easy',
      name: 'globalPaymentsMadeEasy',
      desc: '',
      args: [],
    );
  }

  /// `This swap failed. Try increasing your slippage`
  String get thisSwapFailedTryIncreasingYourSlippage {
    return Intl.message(
      'This swap failed. Try increasing your slippage',
      name: 'thisSwapFailedTryIncreasingYourSlippage',
      desc: '',
      args: [],
    );
  }

  /// `You have insufficient {symbol} to pay the fee for this transaction. Top up your {symbol} balance and try again.`
  String insufficientBalanceSwapCopy(Object symbol) {
    return Intl.message(
      'You have insufficient $symbol to pay the fee for this transaction. Top up your $symbol balance and try again.',
      name: 'insufficientBalanceSwapCopy',
      desc: '',
      args: [symbol],
    );
  }

  /// `NFTs, in your wallet`
  String get nftsInYourWallet {
    return Intl.message(
      'NFTs, in your wallet',
      name: 'nftsInYourWallet',
      desc: '',
      args: [],
    );
  }

  /// `Manage and interact with the NFTs you hold, right inside your wallet`
  String get nftTipContent {
    return Intl.message(
      'Manage and interact with the NFTs you hold, right inside your wallet',
      name: 'nftTipContent',
      desc: '',
      args: [],
    );
  }

  /// `You don’t own any NFTs yet`
  String get emptyNftDescription {
    return Intl.message(
      'You don’t own any NFTs yet',
      name: 'emptyNftDescription',
      desc: '',
      args: [],
    );
  }

  /// `Explore NFT shops`
  String get exploreNftShop {
    return Intl.message(
      'Explore NFT shops',
      name: 'exploreNftShop',
      desc: '',
      args: [],
    );
  }

  /// `NFTs`
  String get nfts {
    return Intl.message('NFTs', name: 'nfts', desc: '', args: []);
  }

  /// `Show me`
  String get showMeText {
    return Intl.message('Show me', name: 'showMeText', desc: '', args: []);
  }

  /// `Recents`
  String get recents {
    return Intl.message('Recents', name: 'recents', desc: '', args: []);
  }

  /// `Sort by`
  String get sortBy {
    return Intl.message('Sort by', name: 'sortBy', desc: '', args: []);
  }

  /// `Recently added`
  String get recentlyAdded {
    return Intl.message(
      'Recently added',
      name: 'recentlyAdded',
      desc: '',
      args: [],
    );
  }

  /// `Collection name (A - Z)`
  String get collectionNameOrder {
    return Intl.message(
      'Collection name (A - Z)',
      name: 'collectionNameOrder',
      desc: '',
      args: [],
    );
  }

  /// `A - Z`
  String get aToz {
    return Intl.message('A - Z', name: 'aToz', desc: '', args: []);
  }

  /// `Merchant country`
  String get merchantCountry {
    return Intl.message(
      'Merchant country',
      name: 'merchantCountry',
      desc: '',
      args: [],
    );
  }

  /// `FX fees may apply`
  String get fxFeeMayApply {
    return Intl.message(
      'FX fees may apply',
      name: 'fxFeeMayApply',
      desc: '',
      args: [],
    );
  }

  /// `In app browser`
  String get inAppBrowser {
    return Intl.message(
      'In app browser',
      name: 'inAppBrowser',
      desc: '',
      args: [],
    );
  }

  /// `Discover more apps`
  String get discoverMoreApps {
    return Intl.message(
      'Discover more apps',
      name: 'discoverMoreApps',
      desc: '',
      args: [],
    );
  }

  /// `Explore hub`
  String get exploreHub {
    return Intl.message('Explore hub', name: 'exploreHub', desc: '', args: []);
  }

  /// `Browse the web`
  String get browseTheWeb {
    return Intl.message(
      'Browse the web',
      name: 'browseTheWeb',
      desc: '',
      args: [],
    );
  }

  /// `Search and connect to your fave apps, right from your Wallet`
  String get searchAndConnectToYourFaveAppsRightFromYourWallet {
    return Intl.message(
      'Search and connect to your fave apps, right from your Wallet',
      name: 'searchAndConnectToYourFaveAppsRightFromYourWallet',
      desc: '',
      args: [],
    );
  }

  /// `Tap into any of these categories to discover and learn more about them`
  String get tapIntoAnyOfTheseCategoriesToLearnMoreAboutThem {
    return Intl.message(
      'Tap into any of these categories to discover and learn more about them',
      name: 'tapIntoAnyOfTheseCategoriesToLearnMoreAboutThem',
      desc: '',
      args: [],
    );
  }

  /// `You can now search and connect to your favourite apps`
  String get youCanNowSearchAndConnectToYourFavoriteApps {
    return Intl.message(
      'You can now search and connect to your favourite apps',
      name: 'youCanNowSearchAndConnectToYourFavoriteApps',
      desc: '',
      args: [],
    );
  }

  /// `Specify the token you're swapping from`
  String get specifyTheTokenYoureSwappingFrom {
    return Intl.message(
      'Specify the token you\'re swapping from',
      name: 'specifyTheTokenYoureSwappingFrom',
      desc: '',
      args: [],
    );
  }

  /// `Specify the token you're swapping to`
  String get specifyTheTokenYoureSwappingTo {
    return Intl.message(
      'Specify the token you\'re swapping to',
      name: 'specifyTheTokenYoureSwappingTo',
      desc: '',
      args: [],
    );
  }

  /// `We encountered a temporary issue. Please retry this transaction"`
  String get weEncounteredATemporaryIssuePleaseRetryThisTransaction {
    return Intl.message(
      'We encountered a temporary issue. Please retry this transaction"',
      name: 'weEncounteredATemporaryIssuePleaseRetryThisTransaction',
      desc: '',
      args: [],
    );
  }

  /// `Missing or incorrect data in your request. Please check the details`
  String get missingOrIncorrectDataInYoureRequest {
    return Intl.message(
      'Missing or incorrect data in your request. Please check the details',
      name: 'missingOrIncorrectDataInYoureRequest',
      desc: '',
      args: [],
    );
  }

  /// `Please select a different token from the source token to swap to.`
  String get pleaseSelectADifferentTokenFromTheSourceToken {
    return Intl.message(
      'Please select a different token from the source token to swap to.',
      name: 'pleaseSelectADifferentTokenFromTheSourceToken',
      desc: '',
      args: [],
    );
  }

  /// `Specify the amount of tokens you want to swap`
  String get specifyTheAmountOfTokensYouWantToSwap {
    return Intl.message(
      'Specify the amount of tokens you want to swap',
      name: 'specifyTheAmountOfTokensYouWantToSwap',
      desc: '',
      args: [],
    );
  }

  /// `Not enough tokens available for this swap. Try a lower amount.`
  String get notEnoughTokensAvailableForThisSwapTryALowerAmount {
    return Intl.message(
      'Not enough tokens available for this swap. Try a lower amount.',
      name: 'notEnoughTokensAvailableForThisSwapTryALowerAmount',
      desc: '',
      args: [],
    );
  }

  /// `This transaction was initiated by the merchant in`
  String get thisTransactionWasInitiatedByTheMerchantIn {
    return Intl.message(
      'This transaction was initiated by the merchant in',
      name: 'thisTransactionWasInitiatedByTheMerchantIn',
      desc: '',
      args: [],
    );
  }

  /// `Wire`
  String get wire {
    return Intl.message('Wire', name: 'wire', desc: '', args: []);
  }

  /// `SEPA`
  String get sepa {
    return Intl.message('SEPA', name: 'sepa', desc: '', args: []);
  }

  /// `ACH`
  String get ach {
    return Intl.message('ACH', name: 'ach', desc: '', args: []);
  }

  /// `Collection`
  String get collection {
    return Intl.message('Collection', name: 'collection', desc: '', args: []);
  }

  /// `Properties`
  String get properties {
    return Intl.message('Properties', name: 'properties', desc: '', args: []);
  }

  /// `Save to photos`
  String get saveToPhotos {
    return Intl.message(
      'Save to photos',
      name: 'saveToPhotos',
      desc: '',
      args: [],
    );
  }

  /// `Hide NFT`
  String get hideNFT {
    return Intl.message('Hide NFT', name: 'hideNFT', desc: '', args: []);
  }

  /// `Saved to Gallery`
  String get savedToGallery {
    return Intl.message(
      'Saved to Gallery',
      name: 'savedToGallery',
      desc: '',
      args: [],
    );
  }

  /// `Saved to Photos`
  String get savedToPhotos {
    return Intl.message(
      'Saved to Photos',
      name: 'savedToPhotos',
      desc: '',
      args: [],
    );
  }

  /// `My Items {number}`
  String myItems(Object number) {
    return Intl.message(
      'My Items $number',
      name: 'myItems',
      desc: '',
      args: [number],
    );
  }

  /// `My Item`
  String get myItem {
    return Intl.message('My Item', name: 'myItem', desc: '', args: []);
  }

  /// `Hide NFT collection`
  String get hideNFTCollection {
    return Intl.message(
      'Hide NFT collection',
      name: 'hideNFTCollection',
      desc: '',
      args: [],
    );
  }

  /// `Unhide collection`
  String get unHideCollection {
    return Intl.message(
      'Unhide collection',
      name: 'unHideCollection',
      desc: '',
      args: [],
    );
  }

  /// `Unhide NFT`
  String get unhideNFT {
    return Intl.message('Unhide NFT', name: 'unhideNFT', desc: '', args: []);
  }

  /// `NFT is visible`
  String get nftIsVisible {
    return Intl.message(
      'NFT is visible',
      name: 'nftIsVisible',
      desc: '',
      args: [],
    );
  }

  /// `It will now show up on your portfolio list.`
  String get unhideNftDescription {
    return Intl.message(
      'It will now show up on your portfolio list.',
      name: 'unhideNftDescription',
      desc: '',
      args: [],
    );
  }

  /// `Hidden NFTs {count}`
  String hiddenNFTsCount(Object count) {
    return Intl.message(
      'Hidden NFTs $count',
      name: 'hiddenNFTsCount',
      desc: '',
      args: [count],
    );
  }

  /// `Manage NFTs`
  String get manageNFTs {
    return Intl.message('Manage NFTs', name: 'manageNFTs', desc: '', args: []);
  }

  /// `NFT hidden!`
  String get nftHidden {
    return Intl.message('NFT hidden!', name: 'nftHidden', desc: '', args: []);
  }

  /// `This NFT will no longer show up on your portfolio. To unhide visit “Manage”`
  String get nftHiddenDestription {
    return Intl.message(
      'This NFT will no longer show up on your portfolio. To unhide visit “Manage”',
      name: 'nftHiddenDestription',
      desc: '',
      args: [],
    );
  }

  /// `NFT is hidden`
  String get nftIsHidden {
    return Intl.message(
      'NFT is hidden',
      name: 'nftIsHidden',
      desc: '',
      args: [],
    );
  }

  /// `Hidden NFTs`
  String get hiddenNFTs {
    return Intl.message('Hidden NFTs', name: 'hiddenNFTs', desc: '', args: []);
  }

  /// `No hidden nft`
  String get noHiddenNFT {
    return Intl.message(
      'No hidden nft',
      name: 'noHiddenNFT',
      desc: '',
      args: [],
    );
  }

  /// `Payment reminders`
  String get paymentReminders {
    return Intl.message(
      'Payment reminders',
      name: 'paymentReminders',
      desc: '',
      args: [],
    );
  }

  /// `Edit frequency`
  String get editFrequency {
    return Intl.message(
      'Edit frequency',
      name: 'editFrequency',
      desc: '',
      args: [],
    );
  }

  /// `Amount updated`
  String get amountUpdated {
    return Intl.message(
      'Amount updated',
      name: 'amountUpdated',
      desc: '',
      args: [],
    );
  }

  /// `Frequency updated`
  String get frequencyUpdated {
    return Intl.message(
      'Frequency updated',
      name: 'frequencyUpdated',
      desc: '',
      args: [],
    );
  }

  /// `Next payment date updated`
  String get nextPaymentDateUpdated {
    return Intl.message(
      'Next payment date updated',
      name: 'nextPaymentDateUpdated',
      desc: '',
      args: [],
    );
  }

  /// `We’ll notify you {duration}`
  String wellNotifyYouDurationCopy(Object duration) {
    return Intl.message(
      'We’ll notify you $duration',
      name: 'wellNotifyYouDurationCopy',
      desc: '',
      args: [duration],
    );
  }

  /// `Setup payment reminders`
  String get setUpPaymentReminders {
    return Intl.message(
      'Setup payment reminders',
      name: 'setUpPaymentReminders',
      desc: '',
      args: [],
    );
  }

  /// `Find active subscriptions`
  String get findActiveSubscriptions {
    return Intl.message(
      'Find active subscriptions',
      name: 'findActiveSubscriptions',
      desc: '',
      args: [],
    );
  }

  /// `Tell us which of your card payments occur on a recurring basis and we'll notify you before they come due.`
  String
  get tellUsWhichOfYourPaymentsOccurOnARecurringBasisAndWellNotifyYouBeforeTheyComeDue {
    return Intl.message(
      'Tell us which of your card payments occur on a recurring basis and we\'ll notify you before they come due.',
      name:
          'tellUsWhichOfYourPaymentsOccurOnARecurringBasisAndWellNotifyYouBeforeTheyComeDue',
      desc: '',
      args: [],
    );
  }

  /// `How often will this subscription be billed?`
  String get howOftenWillThisSubscriptionBeBilled {
    return Intl.message(
      'How often will this subscription be billed?',
      name: 'howOftenWillThisSubscriptionBeBilled',
      desc: '',
      args: [],
    );
  }

  /// `Subscription details`
  String get subscriptionDetails {
    return Intl.message(
      'Subscription details',
      name: 'subscriptionDetails',
      desc: '',
      args: [],
    );
  }

  /// `Add as subscription`
  String get addAsSubscription {
    return Intl.message(
      'Add as subscription',
      name: 'addAsSubscription',
      desc: '',
      args: [],
    );
  }

  /// `Frequency`
  String get frequency {
    return Intl.message('Frequency', name: 'frequency', desc: '', args: []);
  }

  /// `Started on`
  String get startedOn {
    return Intl.message('Started on', name: 'startedOn', desc: '', args: []);
  }

  /// `Next payment`
  String get nextPayment {
    return Intl.message(
      'Next payment',
      name: 'nextPayment',
      desc: '',
      args: [],
    );
  }

  /// `Remind me`
  String get remindMe {
    return Intl.message('Remind me', name: 'remindMe', desc: '', args: []);
  }

  /// `Card used`
  String get cardUsed {
    return Intl.message('Card used', name: 'cardUsed', desc: '', args: []);
  }

  /// `1 Week before`
  String get oneWeekBefore {
    return Intl.message(
      '1 Week before',
      name: 'oneWeekBefore',
      desc: '',
      args: [],
    );
  }

  /// `2 Days before`
  String get twoDaysBefore {
    return Intl.message(
      '2 Days before',
      name: 'twoDaysBefore',
      desc: '',
      args: [],
    );
  }

  /// `1 Day before`
  String get oneDayBefore {
    return Intl.message(
      '1 Day before',
      name: 'oneDayBefore',
      desc: '',
      args: [],
    );
  }

  /// `Annually`
  String get annually {
    return Intl.message('Annually', name: 'annually', desc: '', args: []);
  }

  /// `Every 6 months`
  String get everySixMonths {
    return Intl.message(
      'Every 6 months',
      name: 'everySixMonths',
      desc: '',
      args: [],
    );
  }

  /// `Quarterly`
  String get quarterly {
    return Intl.message('Quarterly', name: 'quarterly', desc: '', args: []);
  }

  /// `Monthly`
  String get monthly {
    return Intl.message('Monthly', name: 'monthly', desc: '', args: []);
  }

  /// `Add new`
  String get addNew {
    return Intl.message('Add new', name: 'addNew', desc: '', args: []);
  }

  /// `Every 2 weeks`
  String get everyTwoWeeks {
    return Intl.message(
      'Every 2 weeks',
      name: 'everyTwoWeeks',
      desc: '',
      args: [],
    );
  }

  /// `Weekly`
  String get weekly {
    return Intl.message('Weekly', name: 'weekly', desc: '', args: []);
  }

  /// `Update amount`
  String get updateAmount {
    return Intl.message(
      'Update amount',
      name: 'updateAmount',
      desc: '',
      args: [],
    );
  }

  /// `Activity`
  String get activity {
    return Intl.message('Activity', name: 'activity', desc: '', args: []);
  }

  /// `next on`
  String get nextOn {
    return Intl.message('next on', name: 'nextOn', desc: '', args: []);
  }

  /// `Upcoming payments`
  String get upcomingPayments {
    return Intl.message(
      'Upcoming payments',
      name: 'upcomingPayments',
      desc: '',
      args: [],
    );
  }

  /// `Unmark subscription`
  String get unMarkSubscription {
    return Intl.message(
      'Unmark subscription',
      name: 'unMarkSubscription',
      desc: '',
      args: [],
    );
  }

  /// `Subscription unmarked`
  String get subscriptionUnmarked {
    return Intl.message(
      'Subscription unmarked',
      name: 'subscriptionUnmarked',
      desc: '',
      args: [],
    );
  }

  /// `Mark a subscription`
  String get markASubscription {
    return Intl.message(
      'Mark a subscription',
      name: 'markASubscription',
      desc: '',
      args: [],
    );
  }

  /// `Your subscription was added`
  String get yourSubscriptionWasAdded {
    return Intl.message(
      'Your subscription was added',
      name: 'yourSubscriptionWasAdded',
      desc: '',
      args: [],
    );
  }

  /// `Add another`
  String get addAnother {
    return Intl.message('Add another', name: 'addAnother', desc: '', args: []);
  }

  /// `Go to my subscriptions`
  String get goToMySubscriptions {
    return Intl.message(
      'Go to my subscriptions',
      name: 'goToMySubscriptions',
      desc: '',
      args: [],
    );
  }

  /// `Forgetting payments are now a thing of the past! 🤩`
  String get forgettingPaymentsAreNowAThingOfThePast {
    return Intl.message(
      'Forgetting payments are now a thing of the past! 🤩',
      name: 'forgettingPaymentsAreNowAThingOfThePast',
      desc: '',
      args: [],
    );
  }

  /// `We encountered an error unmarking your subscription`
  String get weEncounteredAnErrorUnmarkingYourSubscription {
    return Intl.message(
      'We encountered an error unmarking your subscription',
      name: 'weEncounteredAnErrorUnmarkingYourSubscription',
      desc: '',
      args: [],
    );
  }

  /// `This will only be removed from your Onboard subscription list. It won't cancel any future payments to the merchant`
  String get unmarkSubscriptionSubCopy {
    return Intl.message(
      'This will only be removed from your Onboard subscription list. It won\'t cancel any future payments to the merchant',
      name: 'unmarkSubscriptionSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Edits here will only alter what you see on the \nOnboard app and not your subscription with the site`
  String get editsHereWillOnlyAlterWhatYouSeeOnTheOnboardApp {
    return Intl.message(
      'Edits here will only alter what you see on the \nOnboard app and not your subscription with the site',
      name: 'editsHereWillOnlyAlterWhatYouSeeOnTheOnboardApp',
      desc: '',
      args: [],
    );
  }

  /// `Stay on top of your subscriptions when you set up payment reminders`
  String get stayOnTopOfYourSubscriptionsWhenYouSetUpPaymentReminders {
    return Intl.message(
      'Stay on top of your subscriptions when you set up payment reminders',
      name: 'stayOnTopOfYourSubscriptionsWhenYouSetUpPaymentReminders',
      desc: '',
      args: [],
    );
  }

  /// `When transactions are in a different currency from what your card is denominated in, an FX conversion fee may be charged separately.`
  String get fxFeesMayApplySubCopy {
    return Intl.message(
      'When transactions are in a different currency from what your card is denominated in, an FX conversion fee may be charged separately.',
      name: 'fxFeesMayApplySubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Your wallet is unable to backup to iCloud`
  String get unableToBackupOnIcloud {
    return Intl.message(
      'Your wallet is unable to backup to iCloud',
      name: 'unableToBackupOnIcloud',
      desc: '',
      args: [],
    );
  }

  /// `Ensure your device is connected to the internet and you have some iCloud storage available. Open Settings > [Your Name] > iCloud to manage your storage.`
  String get icloudBackupFailureTip {
    return Intl.message(
      'Ensure your device is connected to the internet and you have some iCloud storage available. Open Settings > [Your Name] > iCloud to manage your storage.',
      name: 'icloudBackupFailureTip',
      desc: '',
      args: [],
    );
  }

  /// `Retry iCloud`
  String get retryIcloud {
    return Intl.message(
      'Retry iCloud',
      name: 'retryIcloud',
      desc: '',
      args: [],
    );
  }

  /// `Or try other backup options`
  String get tryOtherBackupOption {
    return Intl.message(
      'Or try other backup options',
      name: 'tryOtherBackupOption',
      desc: '',
      args: [],
    );
  }

  /// `Creating a passkey is an essential security step that helps you easily authenticate your account. It takes 5 seconds to setup.`
  String get passkeyCloseDesc {
    return Intl.message(
      'Creating a passkey is an essential security step that helps you easily authenticate your account. It takes 5 seconds to setup.',
      name: 'passkeyCloseDesc',
      desc: '',
      args: [],
    );
  }

  /// `Passkeys created`
  String get passkeyCreated {
    return Intl.message(
      'Passkeys created',
      name: 'passkeyCreated',
      desc: '',
      args: [],
    );
  }

  /// `Set a passkey`
  String get setAPasskey {
    return Intl.message(
      'Set a passkey',
      name: 'setAPasskey',
      desc: '',
      args: [],
    );
  }

  /// `Passkeys`
  String get passkeys {
    return Intl.message('Passkeys', name: 'passkeys', desc: '', args: []);
  }

  /// `Passkey`
  String get passkey {
    return Intl.message('Passkey', name: 'passkey', desc: '', args: []);
  }

  /// `Create a passkey`
  String get createAPassKey {
    return Intl.message(
      'Create a passkey',
      name: 'createAPassKey',
      desc: '',
      args: [],
    );
  }

  /// `Delete your passkey?`
  String get deletePasskey {
    return Intl.message(
      'Delete your passkey?',
      name: 'deletePasskey',
      desc: '',
      args: [],
    );
  }

  /// `You will no longer be able to use this passkey to sign in. You can create a new passkey for this device at any time.`
  String get deletePasskeyWarning {
    return Intl.message(
      'You will no longer be able to use this passkey to sign in. You can create a new passkey for this device at any time.',
      name: 'deletePasskeyWarning',
      desc: '',
      args: [],
    );
  }

  /// `Add Passkey`
  String get addPasskey {
    return Intl.message('Add Passkey', name: 'addPasskey', desc: '', args: []);
  }

  /// `Passkey deleted`
  String get passkeyDeleted {
    return Intl.message(
      'Passkey deleted',
      name: 'passkeyDeleted',
      desc: '',
      args: [],
    );
  }

  /// `Passkey couldn't be deleted due to an error`
  String get passkeyDeletionError {
    return Intl.message(
      'Passkey couldn\'t be deleted due to an error',
      name: 'passkeyDeletionError',
      desc: '',
      args: [],
    );
  }

  /// `Passkey registration failed`
  String get passkeyCreatedFailed {
    return Intl.message(
      'Passkey registration failed',
      name: 'passkeyCreatedFailed',
      desc: '',
      args: [],
    );
  }

  /// `With passkeys, you can speed up accessing your Onboard account on this device using just your biometric data.`
  String get passkeyListInfo {
    return Intl.message(
      'With passkeys, you can speed up accessing your Onboard account on this device using just your biometric data.',
      name: 'passkeyListInfo',
      desc: '',
      args: [],
    );
  }

  /// `Passkey already exists on device`
  String get passkeyExistOnDevice {
    return Intl.message(
      'Passkey already exists on device',
      name: 'passkeyExistOnDevice',
      desc: '',
      args: [],
    );
  }

  /// `Add 2FA`
  String get add2FA {
    return Intl.message('Add 2FA', name: 'add2FA', desc: '', args: []);
  }

  /// `For extra account security, we recommend enabling at least one 2FA method`
  String get add2FASubCopy {
    return Intl.message(
      'For extra account security, we recommend enabling at least one 2FA method',
      name: 'add2FASubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Recover with Passkey`
  String get recoverWithPasskey {
    return Intl.message(
      'Recover with Passkey',
      name: 'recoverWithPasskey',
      desc: '',
      args: [],
    );
  }

  /// `Recover with SMS`
  String get recoverWithSMS {
    return Intl.message(
      'Recover with SMS',
      name: 'recoverWithSMS',
      desc: '',
      args: [],
    );
  }

  /// `More options`
  String get moreOptions {
    return Intl.message(
      'More options',
      name: 'moreOptions',
      desc: '',
      args: [],
    );
  }

  /// `Use your fingerprint and face ID to speed up login. With passkeys, you can more securely complete transaction on Onboard.`
  String get emptyPasskeyDescription {
    return Intl.message(
      'Use your fingerprint and face ID to speed up login. With passkeys, you can more securely complete transaction on Onboard.',
      name: 'emptyPasskeyDescription',
      desc: '',
      args: [],
    );
  }

  /// `Passkey error. Please verify that this is the device associated with the registered passkey.`
  String get passkeyRecoveryError {
    return Intl.message(
      'Passkey error. Please verify that this is the device associated with the registered passkey.',
      name: 'passkeyRecoveryError',
      desc: '',
      args: [],
    );
  }

  /// `Less options`
  String get lessOptions {
    return Intl.message(
      'Less options',
      name: 'lessOptions',
      desc: '',
      args: [],
    );
  }

  /// `KYC verification failed to start`
  String get kycFailedToStart {
    return Intl.message(
      'KYC verification failed to start',
      name: 'kycFailedToStart',
      desc: '',
      args: [],
    );
  }

  /// `Choose virtual card`
  String get chooseVirtualCard {
    return Intl.message(
      'Choose virtual card',
      name: 'chooseVirtualCard',
      desc: '',
      args: [],
    );
  }

  /// `Tap to unlock full app functionality  →`
  String get tapToUnlockFullFunctionality {
    return Intl.message(
      'Tap to unlock full app functionality  →',
      name: 'tapToUnlockFullFunctionality',
      desc: '',
      args: [],
    );
  }

  /// `Unlock full app functionality`
  String get unlockFullAppFunctionality {
    return Intl.message(
      'Unlock full app functionality',
      name: 'unlockFullAppFunctionality',
      desc: '',
      args: [],
    );
  }

  /// `This App Store version of the Onboard app lacks a few features, pending approval from Apple. While that's ongoing, we recommend accessing our full app functionality via TestFlight`
  String get appStoreVersionFunctionality {
    return Intl.message(
      'This App Store version of the Onboard app lacks a few features, pending approval from Apple. While that\'s ongoing, we recommend accessing our full app functionality via TestFlight',
      name: 'appStoreVersionFunctionality',
      desc: '',
      args: [],
    );
  }

  /// `Get the TestFlight version`
  String get getTestFlightVersion {
    return Intl.message(
      'Get the TestFlight version',
      name: 'getTestFlightVersion',
      desc: '',
      args: [],
    );
  }

  /// `We’ll verify this number for account recovery`
  String get weWillVerifyPhoneNumberForRecovery {
    return Intl.message(
      'We’ll verify this number for account recovery',
      name: 'weWillVerifyPhoneNumberForRecovery',
      desc: '',
      args: [],
    );
  }

  /// `Receiving currency`
  String get receivingCurrency {
    return Intl.message(
      'Receiving currency',
      name: 'receivingCurrency',
      desc: '',
      args: [],
    );
  }

  /// `Est. Rate`
  String get estimatedRate {
    return Intl.message('Est. Rate', name: 'estimatedRate', desc: '', args: []);
  }

  /// `Choose currency`
  String get chooseCurrency {
    return Intl.message(
      'Choose currency',
      name: 'chooseCurrency',
      desc: '',
      args: [],
    );
  }

  /// `Tap to toggle currency`
  String get tapToToggleCurrency {
    return Intl.message(
      'Tap to toggle currency',
      name: 'tapToToggleCurrency',
      desc: '',
      args: [],
    );
  }

  /// `What currency do you want to receive funds in?`
  String get whatCurrencyDoYouWantToReceiveFundsIn {
    return Intl.message(
      'What currency do you want to receive funds in?',
      name: 'whatCurrencyDoYouWantToReceiveFundsIn',
      desc: '',
      args: [],
    );
  }

  /// `What currency do you want to send funds in?`
  String get whatCurrencyDoYouWantToSendFundsIn {
    return Intl.message(
      'What currency do you want to send funds in?',
      name: 'whatCurrencyDoYouWantToSendFundsIn',
      desc: '',
      args: [],
    );
  }

  /// `Receiving address`
  String get receivingAddress {
    return Intl.message(
      'Receiving address',
      name: 'receivingAddress',
      desc: '',
      args: [],
    );
  }

  /// `Euro`
  String get euro {
    return Intl.message('Euro', name: 'euro', desc: '', args: []);
  }

  /// `United Stated Dollars`
  String get unitedSatesDollars {
    return Intl.message(
      'United Stated Dollars',
      name: 'unitedSatesDollars',
      desc: '',
      args: [],
    );
  }

  /// `Funding in progress`
  String get fundingInProgress {
    return Intl.message(
      'Funding in progress',
      name: 'fundingInProgress',
      desc: '',
      args: [],
    );
  }

  /// `Your card gets`
  String get yourCardGets {
    return Intl.message(
      'Your card gets',
      name: 'yourCardGets',
      desc: '',
      args: [],
    );
  }

  /// `You pay exactly`
  String get youPayExactly {
    return Intl.message(
      'You pay exactly',
      name: 'youPayExactly',
      desc: '',
      args: [],
    );
  }

  /// `You will get`
  String get youWillGet {
    return Intl.message('You will get', name: 'youWillGet', desc: '', args: []);
  }

  /// `Pay with`
  String get payWith {
    return Intl.message('Pay with', name: 'payWith', desc: '', args: []);
  }

  /// `You send`
  String get youSend {
    return Intl.message('You send', name: 'youSend', desc: '', args: []);
  }

  /// `Total we’ll convert`
  String get totalWellConvert {
    return Intl.message(
      'Total we’ll convert',
      name: 'totalWellConvert',
      desc: '',
      args: [],
    );
  }

  /// `Get a USD bank account`
  String get getAUsBankAccount {
    return Intl.message(
      'Get a USD bank account',
      name: 'getAUsBankAccount',
      desc: '',
      args: [],
    );
  }

  /// `Get a US account`
  String get getAUsAccount {
    return Intl.message(
      'Get a US account',
      name: 'getAUsAccount',
      desc: '',
      args: [],
    );
  }

  /// `Receive dollar payments `
  String get receiveDollarPayments {
    return Intl.message(
      'Receive dollar payments ',
      name: 'receiveDollarPayments',
      desc: '',
      args: [],
    );
  }

  /// `Cash`
  String get cash {
    return Intl.message('Cash', name: 'cash', desc: '', args: []);
  }

  /// `A dedicated USD virtual account`
  String get aDedicatedUsVirtualAccount {
    return Intl.message(
      'A dedicated USD virtual account',
      name: 'aDedicatedUsVirtualAccount',
      desc: '',
      args: [],
    );
  }

  /// `Quick activation and fast funding`
  String get quickActivationAndFunding {
    return Intl.message(
      'Quick activation and fast funding',
      name: 'quickActivationAndFunding',
      desc: '',
      args: [],
    );
  }

  /// `To get a US account, let’s verify your identity`
  String get toGetAUsAccountLetsVerifyYourIdentity {
    return Intl.message(
      'To get a US account, let’s verify your identity',
      name: 'toGetAUsAccountLetsVerifyYourIdentity',
      desc: '',
      args: [],
    );
  }

  /// `We’ll redirect you to 3rd party service.`
  String get wellRedirectYouToA3rdPartyService {
    return Intl.message(
      'We’ll redirect you to 3rd party service.',
      name: 'wellRedirectYouToA3rdPartyService',
      desc: '',
      args: [],
    );
  }

  /// `Your ID is required`
  String get yourIdIsRequired {
    return Intl.message(
      'Your ID is required',
      name: 'yourIdIsRequired',
      desc: '',
      args: [],
    );
  }

  /// `Account status`
  String get accountStatus {
    return Intl.message(
      'Account status',
      name: 'accountStatus',
      desc: '',
      args: [],
    );
  }

  /// `Tap to view details`
  String get tapToViewDetails {
    return Intl.message(
      'Tap to view details',
      name: 'tapToViewDetails',
      desc: '',
      args: [],
    );
  }

  /// `Add money`
  String get addMoney {
    return Intl.message('Add money', name: 'addMoney', desc: '', args: []);
  }

  /// `Only fund account using accepted parties and limits. Avoid reversals & fees.`
  String get onlyFundAccountUsingAcceptedPartiesAndLimit {
    return Intl.message(
      'Only fund account using accepted parties and limits. Avoid reversals & fees.',
      name: 'onlyFundAccountUsingAcceptedPartiesAndLimit',
      desc: '',
      args: [],
    );
  }

  /// `See guidelines`
  String get seeGuidelines {
    return Intl.message(
      'See guidelines',
      name: 'seeGuidelines',
      desc: '',
      args: [],
    );
  }

  /// `Account name`
  String get accountName {
    return Intl.message(
      'Account name',
      name: 'accountName',
      desc: '',
      args: [],
    );
  }

  /// `Account number`
  String get accountNumber {
    return Intl.message(
      'Account number',
      name: 'accountNumber',
      desc: '',
      args: [],
    );
  }

  /// `Routing number`
  String get routingNumber {
    return Intl.message(
      'Routing number',
      name: 'routingNumber',
      desc: '',
      args: [],
    );
  }

  /// `Bank name`
  String get bankName {
    return Intl.message('Bank name', name: 'bankName', desc: '', args: []);
  }

  /// `Bank address`
  String get bankAddress {
    return Intl.message(
      'Bank address',
      name: 'bankAddress',
      desc: '',
      args: [],
    );
  }

  /// `$6 minimum. Only ACH transfers. `
  String get vaFunding {
    return Intl.message(
      '\$6 minimum. Only ACH transfers. ',
      name: 'vaFunding',
      desc: '',
      args: [],
    );
  }

  /// `Takes 1-2 days*`
  String get takes1to2Days {
    return Intl.message(
      'Takes 1-2 days*',
      name: 'takes1to2Days',
      desc: '',
      args: [],
    );
  }

  /// `Takes`
  String get takes {
    return Intl.message('Takes', name: 'takes', desc: '', args: []);
  }

  /// `Guidelines`
  String get guidelines {
    return Intl.message('Guidelines', name: 'guidelines', desc: '', args: []);
  }

  /// `Accepted payments`
  String get acceptedPayments {
    return Intl.message(
      'Accepted payments',
      name: 'acceptedPayments',
      desc: '',
      args: [],
    );
  }

  /// `Limits`
  String get limits {
    return Intl.message('Limits', name: 'limits', desc: '', args: []);
  }

  /// `1st party deposits`
  String get firstPartyDeposits {
    return Intl.message(
      '1st party deposits',
      name: 'firstPartyDeposits',
      desc: '',
      args: [],
    );
  }

  /// `3rd party deposits`
  String get thirdPartyDeposits {
    return Intl.message(
      '3rd party deposits',
      name: 'thirdPartyDeposits',
      desc: '',
      args: [],
    );
  }

  /// `Only {rail} transfers`
  String onlyRailTransfer(Object rail) {
    return Intl.message(
      'Only $rail transfers',
      name: 'onlyRailTransfer',
      desc: '',
      args: [rail],
    );
  }

  /// `Account details`
  String get accountDetails {
    return Intl.message(
      'Account details',
      name: 'accountDetails',
      desc: '',
      args: [],
    );
  }

  /// `Congrats! \nHere's your {currency} account`
  String congratsHereIsYourVaAccount(Object currency) {
    return Intl.message(
      'Congrats! \nHere\'s your $currency account',
      name: 'congratsHereIsYourVaAccount',
      desc: '',
      args: [currency],
    );
  }

  /// `We need a little information to get started. This will help us secure your account and protect you from fraud.`
  String get startBridgeKycSubCopy {
    return Intl.message(
      'We need a little information to get started. This will help us secure your account and protect you from fraud.',
      name: 'startBridgeKycSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Account terms of use`
  String get accountTermsOfUse {
    return Intl.message(
      'Account terms of use',
      name: 'accountTermsOfUse',
      desc: '',
      args: [],
    );
  }

  /// `You’ve read and accepted the following to use this account`
  String get youveReadAndAcceptedTheFollowingToUseThisAccount {
    return Intl.message(
      'You’ve read and accepted the following to use this account',
      name: 'youveReadAndAcceptedTheFollowingToUseThisAccount',
      desc: '',
      args: [],
    );
  }

  /// `Setup and fees`
  String get setUpAndFees {
    return Intl.message(
      'Setup and fees',
      name: 'setUpAndFees',
      desc: '',
      args: [],
    );
  }

  /// `Setup time`
  String get setUpTime {
    return Intl.message('Setup time', name: 'setUpTime', desc: '', args: []);
  }

  /// `Getting your account number takes`
  String get gettingYourAccountNumberTakes {
    return Intl.message(
      'Getting your account number takes',
      name: 'gettingYourAccountNumberTakes',
      desc: '',
      args: [],
    );
  }

  /// `Monthly maintenance fee`
  String get monthlyMaintenanceFee {
    return Intl.message(
      'Monthly maintenance fee',
      name: 'monthlyMaintenanceFee',
      desc: '',
      args: [],
    );
  }

  /// `Settlement time`
  String get settlementTime {
    return Intl.message(
      'Settlement time',
      name: 'settlementTime',
      desc: '',
      args: [],
    );
  }

  /// `Deposits received within`
  String get depositsReceivedWithin {
    return Intl.message(
      'Deposits received within',
      name: 'depositsReceivedWithin',
      desc: '',
      args: [],
    );
  }

  /// `Deposits below this will lead to delays`
  String get depositsBelowThisWillLeadToDelays {
    return Intl.message(
      'Deposits below this will lead to delays',
      name: 'depositsBelowThisWillLeadToDelays',
      desc: '',
      args: [],
    );
  }

  /// `Charged on each deposit to your account`
  String get chargedOnEachDepositToYourAccount {
    return Intl.message(
      'Charged on each deposit to your account',
      name: 'chargedOnEachDepositToYourAccount',
      desc: '',
      args: [],
    );
  }

  /// `Activation fee`
  String get activationFee {
    return Intl.message(
      'Activation fee',
      name: 'activationFee',
      desc: '',
      args: [],
    );
  }

  /// `Minimum deposit`
  String get minimumDeposit {
    return Intl.message(
      'Minimum deposit',
      name: 'minimumDeposit',
      desc: '',
      args: [],
    );
  }

  /// `This service is provided by`
  String get thisServiceIsProvidedBy {
    return Intl.message(
      'This service is provided by',
      name: 'thisServiceIsProvidedBy',
      desc: '',
      args: [],
    );
  }

  /// `Legal`
  String get legal {
    return Intl.message('Legal', name: 'legal', desc: '', args: []);
  }

  /// ` For more information \nregarding your use of this account, view`
  String get forMoreInformationRegardingUseOfThisAccount {
    return Intl.message(
      ' For more information \nregarding your use of this account, view',
      name: 'forMoreInformationRegardingUseOfThisAccount',
      desc: '',
      args: [],
    );
  }

  /// `their Terms of \nService.`
  String get theirTermsOfService {
    return Intl.message(
      'their Terms of \nService.',
      name: 'theirTermsOfService',
      desc: '',
      args: [],
    );
  }

  /// `We'll charge this one-time fee on your first deposit`
  String get wellChargeThisOneTimeFeeOnYourFirstDeposit {
    return Intl.message(
      'We\'ll charge this one-time fee on your first deposit',
      name: 'wellChargeThisOneTimeFeeOnYourFirstDeposit',
      desc: '',
      args: [],
    );
  }

  /// `To create a {currency} account, review & accept these usage terms`
  String toCreateAUSDAccountReviewAndAcceptTheseUsageTerms(Object currency) {
    return Intl.message(
      'To create a $currency account, review & accept these usage terms',
      name: 'toCreateAUSDAccountReviewAndAcceptTheseUsageTerms',
      desc: '',
      args: [currency],
    );
  }

  /// `Account name copied!`
  String get accountNameCopied {
    return Intl.message(
      'Account name copied!',
      name: 'accountNameCopied',
      desc: '',
      args: [],
    );
  }

  /// `Account number copied!`
  String get accountNumberCopied {
    return Intl.message(
      'Account number copied!',
      name: 'accountNumberCopied',
      desc: '',
      args: [],
    );
  }

  /// `Routing number copied!`
  String get routingNumberCopied {
    return Intl.message(
      'Routing number copied!',
      name: 'routingNumberCopied',
      desc: '',
      args: [],
    );
  }

  /// `Bank name copied!`
  String get bankNameCopied {
    return Intl.message(
      'Bank name copied!',
      name: 'bankNameCopied',
      desc: '',
      args: [],
    );
  }

  /// `Bank address copied!`
  String get bankAddressCopied {
    return Intl.message(
      'Bank address copied!',
      name: 'bankAddressCopied',
      desc: '',
      args: [],
    );
  }

  /// `Tap to activate account`
  String get tapToActivateAccount {
    return Intl.message(
      'Tap to activate account',
      name: 'tapToActivateAccount',
      desc: '',
      args: [],
    );
  }

  /// `Make your first \ndeposit`
  String get makeYourFirstDeposit {
    return Intl.message(
      'Make your first \ndeposit',
      name: 'makeYourFirstDeposit',
      desc: '',
      args: [],
    );
  }

  /// `Fund account`
  String get fundAccount {
    return Intl.message(
      'Fund account',
      name: 'fundAccount',
      desc: '',
      args: [],
    );
  }

  /// `days`
  String get days {
    return Intl.message('days', name: 'days', desc: '', args: []);
  }

  /// `maintenance fee`
  String get maintenanceFee {
    return Intl.message(
      'maintenance fee',
      name: 'maintenanceFee',
      desc: '',
      args: [],
    );
  }

  /// `Creating your account`
  String get creatingYourAccount {
    return Intl.message(
      'Creating your account',
      name: 'creatingYourAccount',
      desc: '',
      args: [],
    );
  }

  /// `Taking too long?`
  String get takingTooLong {
    return Intl.message(
      'Taking too long?',
      name: 'takingTooLong',
      desc: '',
      args: [],
    );
  }

  /// `We'll finish this and notify you once complete.`
  String get wellFinishThisAndNotifyYouOnceComplete {
    return Intl.message(
      'We\'ll finish this and notify you once complete.',
      name: 'wellFinishThisAndNotifyYouOnceComplete',
      desc: '',
      args: [],
    );
  }

  /// `Tap here to dismiss`
  String get tapHereToDismiss {
    return Intl.message(
      'Tap here to dismiss',
      name: 'tapHereToDismiss',
      desc: '',
      args: [],
    );
  }

  /// `Activation in progress`
  String get activationInProgress {
    return Intl.message(
      'Activation in progress',
      name: 'activationInProgress',
      desc: '',
      args: [],
    );
  }

  /// `Your account will be ready soon!`
  String get yourAccountWillBeReadySoon {
    return Intl.message(
      'Your account will be ready soon!',
      name: 'yourAccountWillBeReadySoon',
      desc: '',
      args: [],
    );
  }

  /// `You withdraw`
  String get youWithdraw {
    return Intl.message(
      'You withdraw',
      name: 'youWithdraw',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawal will set account below the min. balance of {minBalance}'`
  String accountWithdrawalMinimumBalanceWarning(Object minBalance) {
    return Intl.message(
      'Withdrawal will set account below the min. balance of $minBalance\'',
      name: 'accountWithdrawalMinimumBalanceWarning',
      desc: '',
      args: [minBalance],
    );
  }

  /// `We're setting things up for you. This should be quick!`
  String get wereSettingThingsUpForYouThisShouldBeQuick {
    return Intl.message(
      'We\'re setting things up for you. This should be quick!',
      name: 'wereSettingThingsUpForYouThisShouldBeQuick',
      desc: '',
      args: [],
    );
  }

  /// `One-time {fee} fee charged on 1st deposit`
  String oneTimeFeeChargedOnFirstDeposit(Object fee) {
    return Intl.message(
      'One-time $fee fee charged on 1st deposit',
      name: 'oneTimeFeeChargedOnFirstDeposit',
      desc: '',
      args: [fee],
    );
  }

  /// `You got`
  String get youGot {
    return Intl.message('You got', name: 'youGot', desc: '', args: []);
  }

  /// `You withdrew`
  String get youWithdrew {
    return Intl.message(
      'You withdrew',
      name: 'youWithdrew',
      desc: '',
      args: [],
    );
  }

  /// `Total converted`
  String get totalConverted {
    return Intl.message(
      'Total converted',
      name: 'totalConverted',
      desc: '',
      args: [],
    );
  }

  /// `Card got`
  String get cardGot {
    return Intl.message('Card got', name: 'cardGot', desc: '', args: []);
  }

  /// `Card gets`
  String get cardGets {
    return Intl.message('Card gets', name: 'cardGets', desc: '', args: []);
  }

  /// `Add a home address`
  String get addHomeAddress {
    return Intl.message(
      'Add a home address',
      name: 'addHomeAddress',
      desc: '',
      args: [],
    );
  }

  /// `Create new ad`
  String get createNewAd {
    return Intl.message(
      'Create new ad',
      name: 'createNewAd',
      desc: '',
      args: [],
    );
  }

  /// `View details`
  String get viewDetails {
    return Intl.message(
      'View details',
      name: 'viewDetails',
      desc: '',
      args: [],
    );
  }

  /// `View ad`
  String get viewAd {
    return Intl.message('View ad', name: 'viewAd', desc: '', args: []);
  }

  /// `View message`
  String get viewMessage {
    return Intl.message(
      'View message',
      name: 'viewMessage',
      desc: '',
      args: [],
    );
  }

  /// `View network`
  String get viewNetwork {
    return Intl.message(
      'View network',
      name: 'viewNetwork',
      desc: '',
      args: [],
    );
  }

  /// `View offer`
  String get viewOffer {
    return Intl.message('View offer', name: 'viewOffer', desc: '', args: []);
  }

  /// `View request`
  String get viewRequest {
    return Intl.message(
      'View request',
      name: 'viewRequest',
      desc: '',
      args: [],
    );
  }

  /// `View broadcast`
  String get viewBroadcast {
    return Intl.message(
      'View broadcast',
      name: 'viewBroadcast',
      desc: '',
      args: [],
    );
  }

  /// `Deposit in progress`
  String get depositInProgress {
    return Intl.message(
      'Deposit in progress',
      name: 'depositInProgress',
      desc: '',
      args: [],
    );
  }

  /// `Deposit cancelled`
  String get depositCancelled {
    return Intl.message(
      'Deposit cancelled',
      name: 'depositCancelled',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawal`
  String get withdrawal {
    return Intl.message('Withdrawal', name: 'withdrawal', desc: '', args: []);
  }

  /// `Deposited`
  String get deposited {
    return Intl.message('Deposited', name: 'deposited', desc: '', args: []);
  }

  /// `Withdrew`
  String get withdrew {
    return Intl.message('Withdrew', name: 'withdrew', desc: '', args: []);
  }

  /// `How would you like to fund?`
  String get fundingOptionsTitle {
    return Intl.message(
      'How would you like to fund?',
      name: 'fundingOptionsTitle',
      desc: '',
      args: [],
    );
  }

  /// `{currency} bank transfer`
  String currencyBankTransfer(Object currency) {
    return Intl.message(
      '$currency bank transfer',
      name: 'currencyBankTransfer',
      desc: '',
      args: [currency],
    );
  }

  /// `Add dollars with your details`
  String get fiatTransferOptionSubtitle {
    return Intl.message(
      'Add dollars with your details',
      name: 'fiatTransferOptionSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Fund your {USD} account with your wallet balance`
  String fundVirtualAccountWithOnboardWallet(Object USD) {
    return Intl.message(
      'Fund your $USD account with your wallet balance',
      name: 'fundVirtualAccountWithOnboardWallet',
      desc: '',
      args: [USD],
    );
  }

  /// `Total we’ll convert`
  String get totalWeConvert {
    return Intl.message(
      'Total we’ll convert',
      name: 'totalWeConvert',
      desc: '',
      args: [],
    );
  }

  /// `{currency} account gets`
  String usdAccountGets(Object currency) {
    return Intl.message(
      '$currency account gets',
      name: 'usdAccountGets',
      desc: '',
      args: [currency],
    );
  }

  /// `Pay with Local currency`
  String get payWithLocalCurrency {
    return Intl.message(
      'Pay with Local currency',
      name: 'payWithLocalCurrency',
      desc: '',
      args: [],
    );
  }

  /// `Your account has being funded`
  String get yourAccountHaveBeenFunded {
    return Intl.message(
      'Your account has being funded',
      name: 'yourAccountHaveBeenFunded',
      desc: '',
      args: [],
    );
  }

  /// `Insufficient {tokenSymbol} balance`
  String insufficientTokenBalance(Object tokenSymbol) {
    return Intl.message(
      'Insufficient $tokenSymbol balance',
      name: 'insufficientTokenBalance',
      desc: '',
      args: [tokenSymbol],
    );
  }

  /// `See why`
  String get seeWhy {
    return Intl.message('See why', name: 'seeWhy', desc: '', args: []);
  }

  /// `To cover network fees for this transaction, you'll need {BNB} token.`
  String insufficientGasFallBackCopy(Object BNB) {
    return Intl.message(
      'To cover network fees for this transaction, you\'ll need $BNB token.',
      name: 'insufficientGasFallBackCopy',
      desc: '',
      args: [BNB],
    );
  }

  /// `To cover network fees for this transaction, you'll need {fiatTokenValue} worth of {tokenSymbol} token.`
  String insufficientGasCopy(Object fiatTokenValue, Object tokenSymbol) {
    return Intl.message(
      'To cover network fees for this transaction, you\'ll need $fiatTokenValue worth of $tokenSymbol token.',
      name: 'insufficientGasCopy',
      desc: '',
      args: [fiatTokenValue, tokenSymbol],
    );
  }

  /// `Blockchain networks require fees to be paid in the native currency of the network. These fees aren't paid to Onboard, but to the network to securely validate your transaction.`
  String get insufficientGasSubCopy {
    return Intl.message(
      'Blockchain networks require fees to be paid in the native currency of the network. These fees aren\'t paid to Onboard, but to the network to securely validate your transaction.',
      name: 'insufficientGasSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Account gets`
  String get accountGets {
    return Intl.message(
      'Account gets',
      name: 'accountGets',
      desc: '',
      args: [],
    );
  }

  /// `{currency} Balance`
  String currencyBalance(Object currency) {
    return Intl.message(
      '$currency Balance',
      name: 'currencyBalance',
      desc: '',
      args: [currency],
    );
  }

  /// `We're funding your account with {x} and will notify you when complete`
  String fundingVirtualWithXAmount(Object x) {
    return Intl.message(
      'We\'re funding your account with $x and will notify you when complete',
      name: 'fundingVirtualWithXAmount',
      desc: '',
      args: [x],
    );
  }

  /// `Virtual account address copied`
  String get virtualAccountAddressCopied {
    return Intl.message(
      'Virtual account address copied',
      name: 'virtualAccountAddressCopied',
      desc: '',
      args: [],
    );
  }

  /// `Pay with Onboard Pay`
  String get payWithOnboardPay {
    return Intl.message(
      'Pay with Onboard Pay',
      name: 'payWithOnboardPay',
      desc: '',
      args: [],
    );
  }

  /// `Onboard pay`
  String get onboardPay {
    return Intl.message('Onboard pay', name: 'onboardPay', desc: '', args: []);
  }

  /// `Send to {currency} balance`
  String sendToUSDBalance(Object currency) {
    return Intl.message(
      'Send to $currency balance',
      name: 'sendToUSDBalance',
      desc: '',
      args: [currency],
    );
  }

  /// `Quick question`
  String get quickQuestion {
    return Intl.message(
      'Quick question',
      name: 'quickQuestion',
      desc: '',
      args: [],
    );
  }

  /// `Where did you hear about us?`
  String get whereYouHeardAboutUs {
    return Intl.message(
      'Where did you hear about us?',
      name: 'whereYouHeardAboutUs',
      desc: '',
      args: [],
    );
  }

  /// `Referral code`
  String get referralCode {
    return Intl.message(
      'Referral code',
      name: 'referralCode',
      desc: '',
      args: [],
    );
  }

  /// `Optional`
  String get optional {
    return Intl.message('Optional', name: 'optional', desc: '', args: []);
  }

  /// `Where did you hear about Onboard?`
  String get whereYouHeardAboutOnboard {
    return Intl.message(
      'Where did you hear about Onboard?',
      name: 'whereYouHeardAboutOnboard',
      desc: '',
      args: [],
    );
  }

  /// `Eg In a community call`
  String get exampleOfOtherChannel {
    return Intl.message(
      'Eg In a community call',
      name: 'exampleOfOtherChannel',
      desc: '',
      args: [],
    );
  }

  /// `Send to Onboard Pay`
  String get sendToOnboardPay {
    return Intl.message(
      'Send to Onboard Pay',
      name: 'sendToOnboardPay',
      desc: '',
      args: [],
    );
  }

  /// `This transaction has been submitted to {network} and is awaiting confirmation. Sit tight, it should be done soon.`
  String pendingSourceChainTransactionStatusMessage(Object network) {
    return Intl.message(
      'This transaction has been submitted to $network and is awaiting confirmation. Sit tight, it should be done soon.',
      name: 'pendingSourceChainTransactionStatusMessage',
      desc: '',
      args: [network],
    );
  }

  /// `The transaction is awaiting confirmation on {network}'s network. We'll notify you once completed.`
  String pendingDestinationChainTransactionStatusMessage(Object network) {
    return Intl.message(
      'The transaction is awaiting confirmation on $network\'s network. We\'ll notify you once completed.',
      name: 'pendingDestinationChainTransactionStatusMessage',
      desc: '',
      args: [network],
    );
  }

  /// `Unfortunately, this transaction experienced an issue on the network. Please contact us for help with getting your token refunded.`
  String get refundRequiredTransactionStatusMessage {
    return Intl.message(
      'Unfortunately, this transaction experienced an issue on the network. Please contact us for help with getting your token refunded.',
      name: 'refundRequiredTransactionStatusMessage',
      desc: '',
      args: [],
    );
  }

  /// `Funds have been returned to your wallet. Thanks for your understanding.`
  String get refundedTransactionStatusMessage {
    return Intl.message(
      'Funds have been returned to your wallet. Thanks for your understanding.',
      name: 'refundedTransactionStatusMessage',
      desc: '',
      args: [],
    );
  }

  /// `This transaction is still in progress, but requires a claim step to complete the transfer on {network}. Please contact us for help with this.`
  String claimRequiredTransactionStatusMessage(Object network) {
    return Intl.message(
      'This transaction is still in progress, but requires a claim step to complete the transfer on $network. Please contact us for help with this.',
      name: 'claimRequiredTransactionStatusMessage',
      desc: '',
      args: [network],
    );
  }

  /// `The transaction is in progress. We'll notify you once completed.`
  String get notSentTransactionStatusMessage {
    return Intl.message(
      'The transaction is in progress. We\'ll notify you once completed.',
      name: 'notSentTransactionStatusMessage',
      desc: '',
      args: [],
    );
  }

  /// `Action required`
  String get actionRequired {
    return Intl.message(
      'Action required',
      name: 'actionRequired',
      desc: '',
      args: [],
    );
  }

  /// `Refunded`
  String get refunded {
    return Intl.message('Refunded', name: 'refunded', desc: '', args: []);
  }

  /// `Claim required`
  String get claimRequired {
    return Intl.message(
      'Claim required',
      name: 'claimRequired',
      desc: '',
      args: [],
    );
  }

  /// `You've signed this transaction and it has been submitted to the blockchain. We'll notify you once completed.`
  String get submittedTransactionStatusMessage {
    return Intl.message(
      'You\'ve signed this transaction and it has been submitted to the blockchain. We\'ll notify you once completed.',
      name: 'submittedTransactionStatusMessage',
      desc: '',
      args: [],
    );
  }

  /// `Virtual account`
  String get virtualAccount {
    return Intl.message(
      'Virtual account',
      name: 'virtualAccount',
      desc: '',
      args: [],
    );
  }

  /// `Wallet balance`
  String get walletBalance {
    return Intl.message(
      'Wallet balance',
      name: 'walletBalance',
      desc: '',
      args: [],
    );
  }

  /// `Trading balance`
  String get tradingBalance {
    return Intl.message(
      'Trading balance',
      name: 'tradingBalance',
      desc: '',
      args: [],
    );
  }

  /// `USD Balance`
  String get usdBalance {
    return Intl.message('USD Balance', name: 'usdBalance', desc: '', args: []);
  }

  /// `USD`
  String get usd {
    return Intl.message('USD', name: 'usd', desc: '', args: []);
  }

  /// `DeFi Wallet`
  String get defiWallet {
    return Intl.message('DeFi Wallet', name: 'defiWallet', desc: '', args: []);
  }

  /// `Merchant balance`
  String get merchantBalance {
    return Intl.message(
      'Merchant balance',
      name: 'merchantBalance',
      desc: '',
      args: [],
    );
  }

  /// `All Accounts`
  String get allAccounts {
    return Intl.message(
      'All Accounts',
      name: 'allAccounts',
      desc: '',
      args: [],
    );
  }

  /// `Choose your source`
  String get chooseYourSource {
    return Intl.message(
      'Choose your source',
      name: 'chooseYourSource',
      desc: '',
      args: [],
    );
  }

  /// `Simple crypto investing`
  String get simpleCryptoInvesting {
    return Intl.message(
      'Simple crypto investing',
      name: 'simpleCryptoInvesting',
      desc: '',
      args: [],
    );
  }

  /// `Your keys, your coins`
  String get yourKeysYourCoins {
    return Intl.message(
      'Your keys, your coins',
      name: 'yourKeysYourCoins',
      desc: '',
      args: [],
    );
  }

  /// `New card`
  String get newCard {
    return Intl.message('New card', name: 'newCard', desc: '', args: []);
  }

  /// `Invite 5 friends`
  String get invite5Friends {
    return Intl.message(
      'Invite 5 friends',
      name: 'invite5Friends',
      desc: '',
      args: [],
    );
  }

  /// `Onboard's better with friends`
  String get onboardsBetterWithFriends {
    return Intl.message(
      'Onboard\'s better with friends',
      name: 'onboardsBetterWithFriends',
      desc: '',
      args: [],
    );
  }

  /// `See what's new`
  String get seeWhatsNew {
    return Intl.message(
      'See what\'s new',
      name: 'seeWhatsNew',
      desc: '',
      args: [],
    );
  }

  /// `Check out our latest updates`
  String get checkOutOurLatestUpdates {
    return Intl.message(
      'Check out our latest updates',
      name: 'checkOutOurLatestUpdates',
      desc: '',
      args: [],
    );
  }

  /// `Discover`
  String get discover {
    return Intl.message('Discover', name: 'discover', desc: '', args: []);
  }

  /// `Share feedback`
  String get shareFeedback {
    return Intl.message(
      'Share feedback',
      name: 'shareFeedback',
      desc: '',
      args: [],
    );
  }

  /// `Choose card`
  String get chooseCard {
    return Intl.message('Choose card', name: 'chooseCard', desc: '', args: []);
  }

  /// `Where would you like to send money to?`
  String get whereWouldYouLikeToSendMoneyTo {
    return Intl.message(
      'Where would you like to send money to?',
      name: 'whereWouldYouLikeToSendMoneyTo',
      desc: '',
      args: [],
    );
  }

  /// `To any crypto wallet`
  String get toAnyCryptoWallet {
    return Intl.message(
      'To any crypto wallet',
      name: 'toAnyCryptoWallet',
      desc: '',
      args: [],
    );
  }

  /// `Tell us what you'd like to see!`
  String get tellUsWhatYoudLikeToSee {
    return Intl.message(
      'Tell us what you\'d like to see!',
      name: 'tellUsWhatYoudLikeToSee',
      desc: '',
      args: [],
    );
  }

  /// `The features you have access to depend on your location. You may change this, but be sure to only select a country where you can provide documents that prove you're legally authorized to be there.`
  String get featuresAccessDependsOnLocation {
    return Intl.message(
      'The features you have access to depend on your location. You may change this, but be sure to only select a country where you can provide documents that prove you\'re legally authorized to be there.',
      name: 'featuresAccessDependsOnLocation',
      desc: '',
      args: [],
    );
  }

  /// `Features available`
  String get featuresAvailable {
    return Intl.message(
      'Features available',
      name: 'featuresAvailable',
      desc: '',
      args: [],
    );
  }

  /// `Which of these best describe you?`
  String get whichOfTheseBestDescribeYou {
    return Intl.message(
      'Which of these best describe you?',
      name: 'whichOfTheseBestDescribeYou',
      desc: '',
      args: [],
    );
  }

  /// `Set Passkey`
  String get setPassKey {
    return Intl.message('Set Passkey', name: 'setPassKey', desc: '', args: []);
  }

  /// `For faster logins ⚡`
  String get forFasterLogins {
    return Intl.message(
      'For faster logins ⚡',
      name: 'forFasterLogins',
      desc: '',
      args: [],
    );
  }

  /// `Skip passcode, use your fingerprint or face ID to access your account across your devices`
  String get skipPassCodeNote {
    return Intl.message(
      'Skip passcode, use your fingerprint or face ID to access your account across your devices',
      name: 'skipPassCodeNote',
      desc: '',
      args: [],
    );
  }

  /// `Your complete financial life, all in one place.`
  String get yourCompleteFinancialLife {
    return Intl.message(
      'Your complete financial life, all in one place.',
      name: 'yourCompleteFinancialLife',
      desc: '',
      args: [],
    );
  }

  /// `DO MONEY\nBETTER`
  String get doMoneyBetter {
    return Intl.message(
      'DO MONEY\nBETTER',
      name: 'doMoneyBetter',
      desc: '',
      args: [],
    );
  }

  /// `Where do you live most of the time?`
  String get whereDoYouCurrentlyLive {
    return Intl.message(
      'Where do you live most of the time?',
      name: 'whereDoYouCurrentlyLive',
      desc: '',
      args: [],
    );
  }

  /// `Passkey login cancelled`
  String get passkeyAuthCancelled {
    return Intl.message(
      'Passkey login cancelled',
      name: 'passkeyAuthCancelled',
      desc: '',
      args: [],
    );
  }

  /// `Any cash account`
  String get anyCashAccount {
    return Intl.message(
      'Any cash account',
      name: 'anyCashAccount',
      desc: '',
      args: [],
    );
  }

  /// `Where are you sending money from?`
  String get whereAreYouSendingMoneyFrom {
    return Intl.message(
      'Where are you sending money from?',
      name: 'whereAreYouSendingMoneyFrom',
      desc: '',
      args: [],
    );
  }

  /// `External crypto wallet`
  String get externalCryptoWallet {
    return Intl.message(
      'External crypto wallet',
      name: 'externalCryptoWallet',
      desc: '',
      args: [],
    );
  }

  /// `Active`
  String get active {
    return Intl.message('Active', name: 'active', desc: '', args: []);
  }

  /// `Terminated`
  String get terminated {
    return Intl.message('Terminated', name: 'terminated', desc: '', args: []);
  }

  /// `Currency`
  String get currency {
    return Intl.message('Currency', name: 'currency', desc: '', args: []);
  }

  /// `You transfer`
  String get youTransfer {
    return Intl.message(
      'You transfer',
      name: 'youTransfer',
      desc: '',
      args: [],
    );
  }

  /// `Switch®`
  String get switchText {
    return Intl.message('Switch®', name: 'switchText', desc: '', args: []);
  }

  /// `Active overdue`
  String get activeOverdue {
    return Intl.message(
      'Active overdue',
      name: 'activeOverdue',
      desc: '',
      args: [],
    );
  }

  /// `One last thing...`
  String get oneLastThing {
    return Intl.message(
      'One last thing...',
      name: 'oneLastThing',
      desc: '',
      args: [],
    );
  }

  /// `Which of these best describe you?`
  String get whichBestDescribeYou {
    return Intl.message(
      'Which of these best describe you?',
      name: 'whichBestDescribeYou',
      desc: '',
      args: [],
    );
  }

  /// `Your account is ready!`
  String get yourAccountIsReady {
    return Intl.message(
      'Your account is ready!',
      name: 'yourAccountIsReady',
      desc: '',
      args: [],
    );
  }

  /// `All set. We're so excited to have you here. \nWelcome to Onboard ✨`
  String get allSetExcitedToHaveYouWelcome {
    return Intl.message(
      'All set. We\'re so excited to have you here. \nWelcome to Onboard ✨',
      name: 'allSetExcitedToHaveYouWelcome',
      desc: '',
      args: [],
    );
  }

  /// `By tapping ‘{param}’, you agree to Onboard’s`
  String byTappingYouAgree(Object param) {
    return Intl.message(
      'By tapping ‘$param’, you agree to Onboard’s',
      name: 'byTappingYouAgree',
      desc: '',
      args: [param],
    );
  }

  /// `and`
  String get and {
    return Intl.message('and', name: 'and', desc: '', args: []);
  }

  /// `Privacy Policy`
  String get privacyPolicy {
    return Intl.message(
      'Privacy Policy',
      name: 'privacyPolicy',
      desc: '',
      args: [],
    );
  }

  /// `Terms of Use`
  String get termsOfUse {
    return Intl.message('Terms of Use', name: 'termsOfUse', desc: '', args: []);
  }

  /// `Create passkey`
  String get createPasskey {
    return Intl.message(
      'Create passkey',
      name: 'createPasskey',
      desc: '',
      args: [],
    );
  }

  /// `To any cash account`
  String get toAnyCashAccount {
    return Intl.message(
      'To any cash account',
      name: 'toAnyCashAccount',
      desc: '',
      args: [],
    );
  }

  /// `Send to bank or mobile money wallet`
  String get sendToBankOrMobileMoneyWallet {
    return Intl.message(
      'Send to bank or mobile money wallet',
      name: 'sendToBankOrMobileMoneyWallet',
      desc: '',
      args: [],
    );
  }

  /// `Where to transfer from`
  String get whereToTransferFrom {
    return Intl.message(
      'Where to transfer from',
      name: 'whereToTransferFrom',
      desc: '',
      args: [],
    );
  }

  /// `Send money abroad`
  String get sendMoneyAbroad {
    return Intl.message(
      'Send money abroad',
      name: 'sendMoneyAbroad',
      desc: '',
      args: [],
    );
  }

  /// `USD account`
  String get usdAccount {
    return Intl.message('USD account', name: 'usdAccount', desc: '', args: []);
  }

  /// `USD Virtual card`
  String get usdVirtualAccount {
    return Intl.message(
      'USD Virtual card',
      name: 'usdVirtualAccount',
      desc: '',
      args: [],
    );
  }

  /// `Multi-currency payouts`
  String get multiCurrency {
    return Intl.message(
      'Multi-currency payouts',
      name: 'multiCurrency',
      desc: '',
      args: [],
    );
  }

  /// `P2P transfers`
  String get p2pTransfers {
    return Intl.message(
      'P2P transfers',
      name: 'p2pTransfers',
      desc: '',
      args: [],
    );
  }

  /// `Trading account`
  String get tradingAccount {
    return Intl.message(
      'Trading account',
      name: 'tradingAccount',
      desc: '',
      args: [],
    );
  }

  /// `To get a USD account`
  String get toGetUSDAccount {
    return Intl.message(
      'To get a USD account',
      name: 'toGetUSDAccount',
      desc: '',
      args: [],
    );
  }

  /// `Here’s what you need`
  String get hereIsWhatYouNeed {
    return Intl.message(
      'Here’s what you need',
      name: 'hereIsWhatYouNeed',
      desc: '',
      args: [],
    );
  }

  /// `Provide basic details`
  String get provideBasicDetails {
    return Intl.message(
      'Provide basic details',
      name: 'provideBasicDetails',
      desc: '',
      args: [],
    );
  }

  /// `Identity document`
  String get identityDocument {
    return Intl.message(
      'Identity document',
      name: 'identityDocument',
      desc: '',
      args: [],
    );
  }

  /// `Proof of address`
  String get proofOfAddress {
    return Intl.message(
      'Proof of address',
      name: 'proofOfAddress',
      desc: '',
      args: [],
    );
  }

  /// `Accept terms of service`
  String get acceptTermsOfService {
    return Intl.message(
      'Accept terms of service',
      name: 'acceptTermsOfService',
      desc: '',
      args: [],
    );
  }

  /// `Additional questions`
  String get additionalQuestions {
    return Intl.message(
      'Additional questions',
      name: 'additionalQuestions',
      desc: '',
      args: [],
    );
  }

  /// `To get a USD card`
  String get toGetUSDCard {
    return Intl.message(
      'To get a USD card',
      name: 'toGetUSDCard',
      desc: '',
      args: [],
    );
  }

  /// `To start sending`
  String get toStartSending {
    return Intl.message(
      'To start sending',
      name: 'toStartSending',
      desc: '',
      args: [],
    );
  }

  /// `Docs submitted!\nVerification is in progress`
  String get kycInProgressStatusTitle {
    return Intl.message(
      'Docs submitted!\nVerification is in progress',
      name: 'kycInProgressStatusTitle',
      desc: '',
      args: [],
    );
  }

  /// `Thank you for submitting required docs. Should be complete within 10 minutes, but some can take up to 24 hours.\n\nTurn on notifications and we’ll update you on the status of your verification.`
  String get kycInProgressStatusMessage {
    return Intl.message(
      'Thank you for submitting required docs. Should be complete within 10 minutes, but some can take up to 24 hours.\n\nTurn on notifications and we’ll update you on the status of your verification.',
      name: 'kycInProgressStatusMessage',
      desc: '',
      args: [],
    );
  }

  /// `Identity is verified`
  String get kycVerifiedStatusTitle {
    return Intl.message(
      'Identity is verified',
      name: 'kycVerifiedStatusTitle',
      desc: '',
      args: [],
    );
  }

  /// `Your details were successfully verified!\nNow, you're good to go.`
  String get kycVerifiedStatusMessage {
    return Intl.message(
      'Your details were successfully verified!\nNow, you\'re good to go.',
      name: 'kycVerifiedStatusMessage',
      desc: '',
      args: [],
    );
  }

  /// `Enter your date of birth as it is on your official ID`
  String get dateOfBirthSubtitle {
    return Intl.message(
      'Enter your date of birth as it is on your official ID',
      name: 'dateOfBirthSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Picture of the ID is required`
  String get pictureOfIdIsRequired {
    return Intl.message(
      'Picture of the ID is required',
      name: 'pictureOfIdIsRequired',
      desc: '',
      args: [],
    );
  }

  /// `We’ll redirect you to`
  String get weWillRedirect {
    return Intl.message(
      'We’ll redirect you to',
      name: 'weWillRedirect',
      desc: '',
      args: [],
    );
  }

  /// `3rd party service`
  String get thirdPartyService {
    return Intl.message(
      '3rd party service',
      name: 'thirdPartyService',
      desc: '',
      args: [],
    );
  }

  /// `Select document with proof of your address`
  String get selectDocumentTitle {
    return Intl.message(
      'Select document with proof of your address',
      name: 'selectDocumentTitle',
      desc: '',
      args: [],
    );
  }

  /// `You’ll be required to upload this document`
  String get selectDocumentSubtitle {
    return Intl.message(
      'You’ll be required to upload this document',
      name: 'selectDocumentSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Document type`
  String get documentType {
    return Intl.message(
      'Document type',
      name: 'documentType',
      desc: '',
      args: [],
    );
  }

  /// `Select Document`
  String get selectDocument {
    return Intl.message(
      'Select Document',
      name: 'selectDocument',
      desc: '',
      args: [],
    );
  }

  /// `Upload proof of address`
  String get uploadProofOfDocument {
    return Intl.message(
      'Upload proof of address',
      name: 'uploadProofOfDocument',
      desc: '',
      args: [],
    );
  }

  /// `Doc must match details to be accepted`
  String get proofOfAddressSubtitle {
    return Intl.message(
      'Doc must match details to be accepted',
      name: 'proofOfAddressSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Issue date`
  String get issueDate {
    return Intl.message('Issue date', name: 'issueDate', desc: '', args: []);
  }

  /// `Upload doc`
  String get uploadDoc {
    return Intl.message('Upload doc', name: 'uploadDoc', desc: '', args: []);
  }

  /// `Last 90 days`
  String get last90Days {
    return Intl.message('Last 90 days', name: 'last90Days', desc: '', args: []);
  }

  /// `pdf, jpg, jpeg & png format. 5 MB Max`
  String get addressDocumentFormat {
    return Intl.message(
      'pdf, jpg, jpeg & png format. 5 MB Max',
      name: 'addressDocumentFormat',
      desc: '',
      args: [],
    );
  }

  /// `Photo Library`
  String get photoLibrary {
    return Intl.message(
      'Photo Library',
      name: 'photoLibrary',
      desc: '',
      args: [],
    );
  }

  /// `Take Photo`
  String get takePhoto {
    return Intl.message('Take Photo', name: 'takePhoto', desc: '', args: []);
  }

  /// `Choose File`
  String get chooseFile {
    return Intl.message('Choose File', name: 'chooseFile', desc: '', args: []);
  }

  /// `Use a different doc`
  String get useDifferentDoc {
    return Intl.message(
      'Use a different doc',
      name: 'useDifferentDoc',
      desc: '',
      args: [],
    );
  }

  /// `Uploading`
  String get uploading {
    return Intl.message('Uploading', name: 'uploading', desc: '', args: []);
  }

  /// `Fill the form below`
  String get fillTheForm {
    return Intl.message(
      'Fill the form below',
      name: 'fillTheForm',
      desc: '',
      args: [],
    );
  }

  /// `Let us know how you plan on using your USD account`
  String get fillTheFormSubtitle {
    return Intl.message(
      'Let us know how you plan on using your USD account',
      name: 'fillTheFormSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Employment status`
  String get employmentStatus {
    return Intl.message(
      'Employment status',
      name: 'employmentStatus',
      desc: '',
      args: [],
    );
  }

  /// `Occupation`
  String get occupation {
    return Intl.message('Occupation', name: 'occupation', desc: '', args: []);
  }

  /// `Source of funds`
  String get fundSource {
    return Intl.message(
      'Source of funds',
      name: 'fundSource',
      desc: '',
      args: [],
    );
  }

  /// `Choose status`
  String get chooseStatus {
    return Intl.message(
      'Choose status',
      name: 'chooseStatus',
      desc: '',
      args: [],
    );
  }

  /// `What's your current professional status?`
  String get chooseStatusSubtitle {
    return Intl.message(
      'What\'s your current professional status?',
      name: 'chooseStatusSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `For myself`
  String get forMyself {
    return Intl.message('For myself', name: 'forMyself', desc: '', args: []);
  }

  /// `For someone else`
  String get forSomeoneElse {
    return Intl.message(
      'For someone else',
      name: 'forSomeoneElse',
      desc: '',
      args: [],
    );
  }

  /// `Last one`
  String get lastOne {
    return Intl.message('Last one', name: 'lastOne', desc: '', args: []);
  }

  /// `Purpose of account`
  String get purposeOfAccount {
    return Intl.message(
      'Purpose of account',
      name: 'purposeOfAccount',
      desc: '',
      args: [],
    );
  }

  /// `Amounts to be sent or received`
  String get amountToBeSentOrReceived {
    return Intl.message(
      'Amounts to be sent or received',
      name: 'amountToBeSentOrReceived',
      desc: '',
      args: [],
    );
  }

  /// `Intermediary status`
  String get intermediaryStatus {
    return Intl.message(
      'Intermediary status',
      name: 'intermediaryStatus',
      desc: '',
      args: [],
    );
  }

  /// `Who's this account for?`
  String get whoAccountFor {
    return Intl.message(
      'Who\'s this account for?',
      name: 'whoAccountFor',
      desc: '',
      args: [],
    );
  }

  /// `Are you receiving or sending funds on behalf of someone other than yourself?`
  String get intermediaryStatusSubtitle {
    return Intl.message(
      'Are you receiving or sending funds on behalf of someone other than yourself?',
      name: 'intermediaryStatusSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Choose transaction band`
  String get chooseBand {
    return Intl.message(
      'Choose transaction band',
      name: 'chooseBand',
      desc: '',
      args: [],
    );
  }

  /// `How much do you intend to send and/or receive with this account monthly?`
  String get transactionBandSubtitle {
    return Intl.message(
      'How much do you intend to send and/or receive with this account monthly?',
      name: 'transactionBandSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `What’s the primary purpose for your account?`
  String get purposeOfAccountSubtitle {
    return Intl.message(
      'What’s the primary purpose for your account?',
      name: 'purposeOfAccountSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Browse Assets`
  String get browseAssets {
    return Intl.message(
      'Browse Assets',
      name: 'browseAssets',
      desc: '',
      args: [],
    );
  }

  /// `Your KYC verification failed due to a data mismatch. Please try again.`
  String get kycFailedSubCopy {
    return Intl.message(
      'Your KYC verification failed due to a data mismatch. Please try again.',
      name: 'kycFailedSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Invalid username. Numbers and special characters are not allowed`
  String get invalidUsername {
    return Intl.message(
      'Invalid username. Numbers and special characters are not allowed',
      name: 'invalidUsername',
      desc: '',
      args: [],
    );
  }

  /// `Start KYC`
  String get startKyc {
    return Intl.message('Start KYC', name: 'startKyc', desc: '', args: []);
  }

  /// `Portfolio`
  String get portfolio {
    return Intl.message('Portfolio', name: 'portfolio', desc: '', args: []);
  }

  /// `Convert`
  String get convert {
    return Intl.message('Convert', name: 'convert', desc: '', args: []);
  }

  /// `Asset Prices`
  String get assetPrices {
    return Intl.message(
      'Asset Prices',
      name: 'assetPrices',
      desc: '',
      args: [],
    );
  }

  /// `🔥 Hot`
  String get hot {
    return Intl.message('🔥 Hot', name: 'hot', desc: '', args: []);
  }

  /// `📉 Losers`
  String get losers {
    return Intl.message('📉 Losers', name: 'losers', desc: '', args: []);
  }

  /// `⭐ ️ New`
  String get newTextWithEmoji {
    return Intl.message(
      '⭐ ️ New',
      name: 'newTextWithEmoji',
      desc: '',
      args: [],
    );
  }

  /// `📈 Gainers`
  String get gainersWithEmoji {
    return Intl.message(
      '📈 Gainers',
      name: 'gainersWithEmoji',
      desc: '',
      args: [],
    );
  }

  /// `Links`
  String get links {
    return Intl.message('Links', name: 'links', desc: '', args: []);
  }

  /// `twitter`
  String get twitter {
    return Intl.message('twitter', name: 'twitter', desc: '', args: []);
  }

  /// `reddit`
  String get reddit {
    return Intl.message('reddit', name: 'reddit', desc: '', args: []);
  }

  /// `coingecko`
  String get coinGecko {
    return Intl.message('coingecko', name: 'coinGecko', desc: '', args: []);
  }

  /// `facebook`
  String get facebook {
    return Intl.message('facebook', name: 'facebook', desc: '', args: []);
  }

  /// `github`
  String get github {
    return Intl.message('github', name: 'github', desc: '', args: []);
  }

  /// `explorer`
  String get explorer {
    return Intl.message('explorer', name: 'explorer', desc: '', args: []);
  }

  /// `Market State`
  String get marketState {
    return Intl.message(
      'Market State',
      name: 'marketState',
      desc: '',
      args: [],
    );
  }

  /// `Market Cap Rank`
  String get marketCapRank {
    return Intl.message(
      'Market Cap Rank',
      name: 'marketCapRank',
      desc: '',
      args: [],
    );
  }

  /// `Show more`
  String get showMore {
    return Intl.message('Show more', name: 'showMore', desc: '', args: []);
  }

  /// `How would you like to add assets?`
  String get addAssetsTitle {
    return Intl.message(
      'How would you like to add assets?',
      name: 'addAssetsTitle',
      desc: '',
      args: [],
    );
  }

  /// `What you need to know`
  String get whatYouNeedToKnow {
    return Intl.message(
      'What you need to know',
      name: 'whatYouNeedToKnow',
      desc: '',
      args: [],
    );
  }

  /// `Only deposit {USDC} from the {network} network to not lose funds.`
  String externalDepositInfoOne(Object USDC, Object network) {
    return Intl.message(
      'Only deposit $USDC from the $network network to not lose funds.',
      name: 'externalDepositInfoOne',
      desc: '',
      args: [USDC, network],
    );
  }

  /// `Deposit at least {amount} {asset}. Sending below this amount may lead to a loss of funds or delays.`
  String externalDepositInfoTwo(Object amount, Object asset) {
    return Intl.message(
      'Deposit at least $amount $asset. Sending below this amount may lead to a loss of funds or delays.',
      name: 'externalDepositInfoTwo',
      desc: '',
      args: [amount, asset],
    );
  }

  /// `confirmations`
  String get confirmations {
    return Intl.message(
      'confirmations',
      name: 'confirmations',
      desc: '',
      args: [],
    );
  }

  /// `Minimum block confirmation is {min}`
  String minimumConfirmationsIs(Object min) {
    return Intl.message(
      'Minimum block confirmation is $min',
      name: 'minimumConfirmationsIs',
      desc: '',
      args: [min],
    );
  }

  /// `Minimum confirmations`
  String get minimumConfirmations {
    return Intl.message(
      'Minimum confirmations',
      name: 'minimumConfirmations',
      desc: '',
      args: [],
    );
  }

  /// `Processing time`
  String get processingTime {
    return Intl.message(
      'Processing time',
      name: 'processingTime',
      desc: '',
      args: [],
    );
  }

  /// `block confirmation(s)`
  String get blockConfirmations {
    return Intl.message(
      'block confirmation(s)',
      name: 'blockConfirmations',
      desc: '',
      args: [],
    );
  }

  /// `Enter an amount`
  String get enterAnAmount {
    return Intl.message(
      'Enter an amount',
      name: 'enterAnAmount',
      desc: '',
      args: [],
    );
  }

  /// `Memo`
  String get memo {
    return Intl.message('Memo', name: 'memo', desc: '', args: []);
  }

  /// `Memo copied`
  String get memoCopied {
    return Intl.message('Memo copied', name: 'memoCopied', desc: '', args: []);
  }

  /// `Balances`
  String get balances {
    return Intl.message('Balances', name: 'balances', desc: '', args: []);
  }

  /// `Transaction Failed, try again!`
  String get transactionFailedTryAgain {
    return Intl.message(
      'Transaction Failed, try again!',
      name: 'transactionFailedTryAgain',
      desc: '',
      args: [],
    );
  }

  /// `Transaction sent`
  String get transactionSent {
    return Intl.message(
      'Transaction sent',
      name: 'transactionSent',
      desc: '',
      args: [],
    );
  }

  /// ` Our payment partners may charge a small fee to process this transaction\nThe receiving amount is only an estimate.`
  String get transactionFeeCopy {
    return Intl.message(
      ' Our payment partners may charge a small fee to process this transaction\nThe receiving amount is only an estimate.',
      name: 'transactionFeeCopy',
      desc: '',
      args: [],
    );
  }

  /// `Processing your order`
  String get processingYourOrder {
    return Intl.message(
      'Processing your order',
      name: 'processingYourOrder',
      desc: '',
      args: [],
    );
  }

  /// `Trade not available`
  String get tradeNotAvailable {
    return Intl.message(
      'Trade not available',
      name: 'tradeNotAvailable',
      desc: '',
      args: [],
    );
  }

  /// `Send Funds`
  String get sendFunds {
    return Intl.message('Send Funds', name: 'sendFunds', desc: '', args: []);
  }

  /// `Transfer crypto to other wallets`
  String get externalWalletTransferDesc {
    return Intl.message(
      'Transfer crypto to other wallets',
      name: 'externalWalletTransferDesc',
      desc: '',
      args: [],
    );
  }

  /// `How would you like to transfer assets?`
  String get transferAssetsTitle {
    return Intl.message(
      'How would you like to transfer assets?',
      name: 'transferAssetsTitle',
      desc: '',
      args: [],
    );
  }

  /// `Send {tokenSymbol}`
  String sendWithAssetSymbol(Object tokenSymbol) {
    return Intl.message(
      'Send $tokenSymbol',
      name: 'sendWithAssetSymbol',
      desc: '',
      args: [tokenSymbol],
    );
  }

  /// `Tap to select a network`
  String get tapToSelectNetwork {
    return Intl.message(
      'Tap to select a network',
      name: 'tapToSelectNetwork',
      desc: '',
      args: [],
    );
  }

  /// `Ensure that the address is correct and on the same network. Transactions cannot be reversed`
  String get transferWarning {
    return Intl.message(
      'Ensure that the address is correct and on the same network. Transactions cannot be reversed',
      name: 'transferWarning',
      desc: '',
      args: [],
    );
  }

  /// `Security verification`
  String get securityVerification {
    return Intl.message(
      'Security verification',
      name: 'securityVerification',
      desc: '',
      args: [],
    );
  }

  /// `Sending your order...`
  String get sendingYourOrder {
    return Intl.message(
      'Sending your order...',
      name: 'sendingYourOrder',
      desc: '',
      args: [],
    );
  }

  /// `block(s)`
  String get blocks {
    return Intl.message('block(s)', name: 'blocks', desc: '', args: []);
  }

  /// `Min. network amount is {min}.`
  String minNetworkAmountIs(Object min) {
    return Intl.message(
      'Min. network amount is $min.',
      name: 'minNetworkAmountIs',
      desc: '',
      args: [min],
    );
  }

  /// `Withdrawal not enabled for network`
  String get withdrawalNotEnabledForNetwork {
    return Intl.message(
      'Withdrawal not enabled for network',
      name: 'withdrawalNotEnabledForNetwork',
      desc: '',
      args: [],
    );
  }

  /// `Enter the 6-digit code sent to`
  String get enterTheSixDigitCodeSentTo {
    return Intl.message(
      'Enter the 6-digit code sent to',
      name: 'enterTheSixDigitCodeSentTo',
      desc: '',
      args: [],
    );
  }

  /// `Get the code`
  String get getTheCode {
    return Intl.message('Get the code', name: 'getTheCode', desc: '', args: []);
  }

  /// `Submit`
  String get submit {
    return Intl.message('Submit', name: 'submit', desc: '', args: []);
  }

  /// `Describe your purpose`
  String get describleYourPurpose {
    return Intl.message(
      'Describe your purpose',
      name: 'describleYourPurpose',
      desc: '',
      args: [],
    );
  }

  /// `You don’t have accounts in this currency`
  String get youDontHaveAnAccountsInThisCurrency {
    return Intl.message(
      'You don’t have accounts in this currency',
      name: 'youDontHaveAnAccountsInThisCurrency',
      desc: '',
      args: [],
    );
  }

  /// `Secured by`
  String get securedBy {
    return Intl.message('Secured by', name: 'securedBy', desc: '', args: []);
  }

  /// `OnboardFast®`
  String get onboardFast {
    return Intl.message(
      'OnboardFast®',
      name: 'onboardFast',
      desc: '',
      args: [],
    );
  }

  /// `Arrives`
  String get arrives {
    return Intl.message('Arrives', name: 'arrives', desc: '', args: []);
  }

  /// `Instantly`
  String get instantly {
    return Intl.message('Instantly', name: 'instantly', desc: '', args: []);
  }

  /// `Verification ongoing, completes in 24hrs`
  String get verificationOngoingSubtitle {
    return Intl.message(
      'Verification ongoing, completes in 24hrs',
      name: 'verificationOngoingSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `To get started, setup your DeFi wallet`
  String get toGetStartedSetupYOurDefiWallet {
    return Intl.message(
      'To get started, setup your DeFi wallet',
      name: 'toGetStartedSetupYOurDefiWallet',
      desc: '',
      args: [],
    );
  }

  /// `Fast on & off ramps`
  String get fastOnAndOffRamps {
    return Intl.message(
      'Fast on & off ramps',
      name: 'fastOnAndOffRamps',
      desc: '',
      args: [],
    );
  }

  /// `Easy onchain access`
  String get easyOnchainAccess {
    return Intl.message(
      'Easy onchain access',
      name: 'easyOnchainAccess',
      desc: '',
      args: [],
    );
  }

  /// `Explore DeFi apps & connect to up to 10+ networks with ease`
  String get exploreDefiAppsAndConnectUpTo10PlusNetworksWithEase {
    return Intl.message(
      'Explore DeFi apps & connect to up to 10+ networks with ease',
      name: 'exploreDefiAppsAndConnectUpTo10PlusNetworksWithEase',
      desc: '',
      args: [],
    );
  }

  /// `Go from crypto to cash and back with seamless payment options`
  String get goFromCryptoToCashAndBackWithSeamlessPaymentOptions {
    return Intl.message(
      'Go from crypto to cash and back with seamless payment options',
      name: 'goFromCryptoToCashAndBackWithSeamlessPaymentOptions',
      desc: '',
      args: [],
    );
  }

  /// `You’re in full control of this wallet, with your private keys.`
  String get youreInFullControlOfThisWalletWithYourPrivateKeys {
    return Intl.message(
      'You’re in full control of this wallet, with your private keys.',
      name: 'youreInFullControlOfThisWalletWithYourPrivateKeys',
      desc: '',
      args: [],
    );
  }

  /// `Choose authenticator`
  String get chooseAuthenticator {
    return Intl.message(
      'Choose authenticator',
      name: 'chooseAuthenticator',
      desc: '',
      args: [],
    );
  }

  /// `SMS`
  String get sms {
    return Intl.message('SMS', name: 'sms', desc: '', args: []);
  }

  /// `Transaction Pin`
  String get transactionPin {
    return Intl.message(
      'Transaction Pin',
      name: 'transactionPin',
      desc: '',
      args: [],
    );
  }

  /// `Authenticator App`
  String get authenticatorApp {
    return Intl.message(
      'Authenticator App',
      name: 'authenticatorApp',
      desc: '',
      args: [],
    );
  }

  /// `Switch to another verification method`
  String get switchVerificationMethod {
    return Intl.message(
      'Switch to another verification method',
      name: 'switchVerificationMethod',
      desc: '',
      args: [],
    );
  }

  /// `Code sent successfully`
  String get codeSentSuccessfully {
    return Intl.message(
      'Code sent successfully',
      name: 'codeSentSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `All transactions`
  String get allTransactions {
    return Intl.message(
      'All transactions',
      name: 'allTransactions',
      desc: '',
      args: [],
    );
  }

  /// `Transaction`
  String get transaction {
    return Intl.message('Transaction', name: 'transaction', desc: '', args: []);
  }

  /// `Min amount is {amount}`
  String minAmountRequired(Object amount) {
    return Intl.message(
      'Min amount is $amount',
      name: 'minAmountRequired',
      desc: '',
      args: [amount],
    );
  }

  /// `Max amount is {amount}`
  String maxAmountRequired(Object amount) {
    return Intl.message(
      'Max amount is $amount',
      name: 'maxAmountRequired',
      desc: '',
      args: [amount],
    );
  }

  /// `Select {from} Asset`
  String selectConvertAsset(Object from) {
    return Intl.message(
      'Select $from Asset',
      name: 'selectConvertAsset',
      desc: '',
      args: [from],
    );
  }

  /// `Notes`
  String get notes {
    return Intl.message('Notes', name: 'notes', desc: '', args: []);
  }

  /// `Cash Withdrawal`
  String get cashWithdrawal {
    return Intl.message(
      'Cash Withdrawal',
      name: 'cashWithdrawal',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawing {BNB} or any other crypto asset to cash will require USD conversion.`
  String cashWithdrawalWarning(Object BNB) {
    return Intl.message(
      'Withdrawing $BNB or any other crypto asset to cash will require USD conversion.',
      name: 'cashWithdrawalWarning',
      desc: '',
      args: [BNB],
    );
  }

  /// `Mobile Number`
  String get mobileNumber {
    return Intl.message(
      'Mobile Number',
      name: 'mobileNumber',
      desc: '',
      args: [],
    );
  }

  /// `Chipper Cash Tag`
  String get chipperCashTag {
    return Intl.message(
      'Chipper Cash Tag',
      name: 'chipperCashTag',
      desc: '',
      args: [],
    );
  }

  /// `Where do you want to receive funds in?`
  String get whereDoYouWantToReceiveFundsIn {
    return Intl.message(
      'Where do you want to receive funds in?',
      name: 'whereDoYouWantToReceiveFundsIn',
      desc: '',
      args: [],
    );
  }

  /// `Choose a preferred method`
  String get chooseAPreferredMethod {
    return Intl.message(
      'Choose a preferred method',
      name: 'chooseAPreferredMethod',
      desc: '',
      args: [],
    );
  }

  /// `Send funds to your local banks`
  String get sendFundsToYourLocalBanks {
    return Intl.message(
      'Send funds to your local banks',
      name: 'sendFundsToYourLocalBanks',
      desc: '',
      args: [],
    );
  }

  /// `No fee`
  String get noFee {
    return Intl.message('No fee', name: 'noFee', desc: '', args: []);
  }

  /// `Confirming your verification status..`
  String get confirmingYourVerificationStatus {
    return Intl.message(
      'Confirming your verification status..',
      name: 'confirmingYourVerificationStatus',
      desc: '',
      args: [],
    );
  }

  /// `Unable to get asset details`
  String get unableToGetAssetDetails {
    return Intl.message(
      'Unable to get asset details',
      name: 'unableToGetAssetDetails',
      desc: '',
      args: [],
    );
  }

  /// `Transaction completed`
  String get transactionCompleted {
    return Intl.message(
      'Transaction completed',
      name: 'transactionCompleted',
      desc: '',
      args: [],
    );
  }

  /// `I've transferred`
  String get iHaveTransferred {
    return Intl.message(
      'I\'ve transferred',
      name: 'iHaveTransferred',
      desc: '',
      args: [],
    );
  }

  /// `Your deposit is being processed`
  String get yourDepositIsBeingProcessed {
    return Intl.message(
      'Your deposit is being processed',
      name: 'yourDepositIsBeingProcessed',
      desc: '',
      args: [],
    );
  }

  /// `We'll keep checking and let you know once received. You can leave this page anytime.`
  String get confirmingYourDepositDescription {
    return Intl.message(
      'We\'ll keep checking and let you know once received. You can leave this page anytime.',
      name: 'confirmingYourDepositDescription',
      desc: '',
      args: [],
    );
  }

  /// `SignIn/Signup below to transact`
  String get getStartedToTransact {
    return Intl.message(
      'SignIn/Signup below to transact',
      name: 'getStartedToTransact',
      desc: '',
      args: [],
    );
  }

  /// `SignIn/Signup to view market Data`
  String get getStartedToViewMarketData {
    return Intl.message(
      'SignIn/Signup to view market Data',
      name: 'getStartedToViewMarketData',
      desc: '',
      args: [],
    );
  }

  /// `required`
  String get required {
    return Intl.message('required', name: 'required', desc: '', args: []);
  }

  /// `Checking deposit status...`
  String get checkingDepositStatus {
    return Intl.message(
      'Checking deposit status...',
      name: 'checkingDepositStatus',
      desc: '',
      args: [],
    );
  }

  /// `Submitting your photo ID...`
  String get submittingKycJob {
    return Intl.message(
      'Submitting your photo ID...',
      name: 'submittingKycJob',
      desc: '',
      args: [],
    );
  }

  /// `Why Haven?`
  String get whyHaven {
    return Intl.message('Why Haven?', name: 'whyHaven', desc: '', args: []);
  }

  /// `Okay, got it`
  String get okayGotIt {
    return Intl.message('Okay, got it', name: 'okayGotIt', desc: '', args: []);
  }

  /// `Personalized financial & investment guidance from a dedicated assistant.`
  String get whyHavenOne {
    return Intl.message(
      'Personalized financial & investment guidance from a dedicated assistant.',
      name: 'whyHavenOne',
      desc: '',
      args: [],
    );
  }

  /// `Diverse investments in USD and 50+ digital assets like Bitcoin & Solana.`
  String get whyHavenTwo {
    return Intl.message(
      'Diverse investments in USD and 50+ digital assets like Bitcoin & Solana.',
      name: 'whyHavenTwo',
      desc: '',
      args: [],
    );
  }

  /// `Coming soon: USD/EUR accounts, card cashback, payments in 30+ countries.`
  String get whyHavenThree {
    return Intl.message(
      'Coming soon: USD/EUR accounts, card cashback, payments in 30+ countries.',
      name: 'whyHavenThree',
      desc: '',
      args: [],
    );
  }

  /// `Premium: curated investments, concierge services, exclusive events.`
  String get whyHavenFour {
    return Intl.message(
      'Premium: curated investments, concierge services, exclusive events.',
      name: 'whyHavenFour',
      desc: '',
      args: [],
    );
  }

  /// `Discover membership benefits`
  String get discoverMembershipBenefits {
    return Intl.message(
      'Discover membership benefits',
      name: 'discoverMembershipBenefits',
      desc: '',
      args: [],
    );
  }

  /// `Confirming deposit`
  String get confirmingDeposit {
    return Intl.message(
      'Confirming deposit',
      name: 'confirmingDeposit',
      desc: '',
      args: [],
    );
  }

  /// `Cancel confirmation`
  String get cancelConfirmation {
    return Intl.message(
      'Cancel confirmation',
      name: 'cancelConfirmation',
      desc: '',
      args: [],
    );
  }

  /// `deposit confirmed`
  String get depositConfirmed {
    return Intl.message(
      'deposit confirmed',
      name: 'depositConfirmed',
      desc: '',
      args: [],
    );
  }

  /// `Get ready to explore a world of financial opportunities tailored just for you.`
  String get depositConfirmedDesc {
    return Intl.message(
      'Get ready to explore a world of financial opportunities tailored just for you.',
      name: 'depositConfirmedDesc',
      desc: '',
      args: [],
    );
  }

  /// `Create a passkey`
  String get createAPasskey {
    return Intl.message(
      'Create a passkey',
      name: 'createAPasskey',
      desc: '',
      args: [],
    );
  }

  /// `With passkeys, you don’t need to remember complex passwords.`
  String get passkeyDescOne {
    return Intl.message(
      'With passkeys, you don’t need to remember complex passwords.',
      name: 'passkeyDescOne',
      desc: '',
      args: [],
    );
  }

  /// `Instead, you can use your fingerprint, face, PIN or pattern to access your account across your devices.`
  String get passkeyDescTwo {
    return Intl.message(
      'Instead, you can use your fingerprint, face, PIN or pattern to access your account across your devices.',
      name: 'passkeyDescTwo',
      desc: '',
      args: [],
    );
  }

  /// `Passkey created successfully`
  String get passkeyCreatedSuccessfully {
    return Intl.message(
      'Passkey created successfully',
      name: 'passkeyCreatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Passkey setup`
  String get passkeySetup {
    return Intl.message(
      'Passkey setup',
      name: 'passkeySetup',
      desc: '',
      args: [],
    );
  }

  /// `Your passkey is created!`
  String get yourPasskeyIsCreated {
    return Intl.message(
      'Your passkey is created!',
      name: 'yourPasskeyIsCreated',
      desc: '',
      args: [],
    );
  }

  /// `You can now use your fingerprint, face scan, PIN or pattern to access to your wallet across your devices.`
  String get passkeyCreatedDesc {
    return Intl.message(
      'You can now use your fingerprint, face scan, PIN or pattern to access to your wallet across your devices.',
      name: 'passkeyCreatedDesc',
      desc: '',
      args: [],
    );
  }

  /// `encrypted`
  String get encrypted {
    return Intl.message('encrypted', name: 'encrypted', desc: '', args: []);
  }

  /// `Passkey authorization failed.`
  String get passkeyAuthorizationFailed {
    return Intl.message(
      'Passkey authorization failed.',
      name: 'passkeyAuthorizationFailed',
      desc: '',
      args: [],
    );
  }

  /// `Authorizing...`
  String get authorizing {
    return Intl.message(
      'Authorizing...',
      name: 'authorizing',
      desc: '',
      args: [],
    );
  }

  /// `Email verification...`
  String get emailVerification {
    return Intl.message(
      'Email verification...',
      name: 'emailVerification',
      desc: '',
      args: [],
    );
  }

  /// `Verify with passkey`
  String get verifyWithPasskey {
    return Intl.message(
      'Verify with passkey',
      name: 'verifyWithPasskey',
      desc: '',
      args: [],
    );
  }

  /// `Verify using your fingerprint, face scan, PIN or pattern on your device.`
  String get verifyWithPasskeyDesc {
    return Intl.message(
      'Verify using your fingerprint, face scan, PIN or pattern on your device.',
      name: 'verifyWithPasskeyDesc',
      desc: '',
      args: [],
    );
  }

  /// `Verify`
  String get verify {
    return Intl.message('Verify', name: 'verify', desc: '', args: []);
  }

  /// `Authenticate via {auth}`
  String authenticateVia(Object auth) {
    return Intl.message(
      'Authenticate via $auth',
      name: 'authenticateVia',
      desc: '',
      args: [auth],
    );
  }

  /// `Verifying`
  String get verifying {
    return Intl.message('Verifying', name: 'verifying', desc: '', args: []);
  }

  /// `{auth} verification code`
  String authVerificationCode(Object auth) {
    return Intl.message(
      '$auth verification code',
      name: 'authVerificationCode',
      desc: '',
      args: [auth],
    );
  }

  /// `Verify with {auth}`
  String verifyWith(Object auth) {
    return Intl.message(
      'Verify with $auth',
      name: 'verifyWith',
      desc: '',
      args: [auth],
    );
  }

  /// `Requesting code`
  String get requestingCode {
    return Intl.message(
      'Requesting code',
      name: 'requestingCode',
      desc: '',
      args: [],
    );
  }

  /// `Code sent`
  String get codeSent {
    return Intl.message('Code sent', name: 'codeSent', desc: '', args: []);
  }

  /// `Authorization failed, try again`
  String get authorizationFailedTryAgain {
    return Intl.message(
      'Authorization failed, try again',
      name: 'authorizationFailedTryAgain',
      desc: '',
      args: [],
    );
  }

  /// `No network with withdrawal available`
  String get noNetworkWithWithdrawalAvailable {
    return Intl.message(
      'No network with withdrawal available',
      name: 'noNetworkWithWithdrawalAvailable',
      desc: '',
      args: [],
    );
  }

  /// `No network with deposit available`
  String get noNetworkWithDepositAvailable {
    return Intl.message(
      'No network with deposit available',
      name: 'noNetworkWithDepositAvailable',
      desc: '',
      args: [],
    );
  }

  /// `Added`
  String get added {
    return Intl.message('Added', name: 'added', desc: '', args: []);
  }

  /// `You don't have any passkeys`
  String get noPasskeysTitle {
    return Intl.message(
      'You don\'t have any passkeys',
      name: 'noPasskeysTitle',
      desc: '',
      args: [],
    );
  }

  /// `Passkeys help you authenticate your wallet faster, using your device’s biometrics or security PIN.`
  String get noPasskeysDesc {
    return Intl.message(
      'Passkeys help you authenticate your wallet faster, using your device’s biometrics or security PIN.',
      name: 'noPasskeysDesc',
      desc: '',
      args: [],
    );
  }

  /// `By deleting your passkey, you'll lose access to secure and easy authentication of your wallet`
  String get deletePasskeyConfirmation {
    return Intl.message(
      'By deleting your passkey, you\'ll lose access to secure and easy authentication of your wallet',
      name: 'deletePasskeyConfirmation',
      desc: '',
      args: [],
    );
  }

  /// `Go back`
  String get goBack {
    return Intl.message('Go back', name: 'goBack', desc: '', args: []);
  }

  /// `Buy using NGN via P2P`
  String get buyUsingNgnViaPtoP {
    return Intl.message(
      'Buy using NGN via P2P',
      name: 'buyUsingNgnViaPtoP',
      desc: '',
      args: [],
    );
  }

  /// `Select asset to fund`
  String get selectAssetToFund {
    return Intl.message(
      'Select asset to fund',
      name: 'selectAssetToFund',
      desc: '',
      args: [],
    );
  }

  /// `Start trading today`
  String get startTradingToday {
    return Intl.message(
      'Start trading today',
      name: 'startTradingToday',
      desc: '',
      args: [],
    );
  }

  /// `Fund with USDT using P2P or from an external wallet`
  String get startTradingTodaySubtitle {
    return Intl.message(
      'Fund with USDT using P2P or from an external wallet',
      name: 'startTradingTodaySubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Deposit USDT`
  String get depositUSDT {
    return Intl.message(
      'Deposit USDT',
      name: 'depositUSDT',
      desc: '',
      args: [],
    );
  }

  /// `How would you like to use Haven?`
  String get onboardingOptionsScreenTitle {
    return Intl.message(
      'How would you like to use Haven?',
      name: 'onboardingOptionsScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `Don't worry, you can still try out the other options later`
  String get onboardingOptionsScreenSubtitle {
    return Intl.message(
      'Don\'t worry, you can still try out the other options later',
      name: 'onboardingOptionsScreenSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Trade digital assets`
  String get tradeDigitalAssets {
    return Intl.message(
      'Trade digital assets',
      name: 'tradeDigitalAssets',
      desc: '',
      args: [],
    );
  }

  /// `Deposit and swap BTC, SOL and more`
  String get tradeDigitalAssetsDesc {
    return Intl.message(
      'Deposit and swap BTC, SOL and more',
      name: 'tradeDigitalAssetsDesc',
      desc: '',
      args: [],
    );
  }

  /// `Convert crypto to cash`
  String get convertCryptoToCash {
    return Intl.message(
      'Convert crypto to cash',
      name: 'convertCryptoToCash',
      desc: '',
      args: [],
    );
  }

  /// `Near-instant withdrawals to bank`
  String get convertCryptoToCashDesc {
    return Intl.message(
      'Near-instant withdrawals to bank',
      name: 'convertCryptoToCashDesc',
      desc: '',
      args: [],
    );
  }

  /// `USD Cards & Rewards`
  String get USDCardsAndRewards {
    return Intl.message(
      'USD Cards & Rewards',
      name: 'USDCardsAndRewards',
      desc: '',
      args: [],
    );
  }

  /// `Get cashback on every purchase`
  String get USDCardsAndRewardsDesc {
    return Intl.message(
      'Get cashback on every purchase',
      name: 'USDCardsAndRewardsDesc',
      desc: '',
      args: [],
    );
  }

  /// `International Payments`
  String get internationalPayments {
    return Intl.message(
      'International Payments',
      name: 'internationalPayments',
      desc: '',
      args: [],
    );
  }

  /// `Receive USD, and send money globally`
  String get internationalPaymentsDesc {
    return Intl.message(
      'Receive USD, and send money globally',
      name: 'internationalPaymentsDesc',
      desc: '',
      args: [],
    );
  }

  /// `VIP Access`
  String get vipAccess {
    return Intl.message('VIP Access', name: 'vipAccess', desc: '', args: []);
  }

  /// `All of the above + premium benefits`
  String get vipAccessDesc {
    return Intl.message(
      'All of the above + premium benefits',
      name: 'vipAccessDesc',
      desc: '',
      args: [],
    );
  }

  /// `Select asset to send`
  String get selectAssetToSend {
    return Intl.message(
      'Select asset to send',
      name: 'selectAssetToSend',
      desc: '',
      args: [],
    );
  }

  /// `Beneficiary Network`
  String get beneficiaryNetwork {
    return Intl.message(
      'Beneficiary Network',
      name: 'beneficiaryNetwork',
      desc: '',
      args: [],
    );
  }

  /// `This is the network where your assets will be sent out on.`
  String get sendingNetworkDesc {
    return Intl.message(
      'This is the network where your assets will be sent out on.',
      name: 'sendingNetworkDesc',
      desc: '',
      args: [],
    );
  }

  /// `Filter`
  String get filter {
    return Intl.message('Filter', name: 'filter', desc: '', args: []);
  }

  /// `Filters`
  String get filters {
    return Intl.message('Filters', name: 'filters', desc: '', args: []);
  }

  /// `All Assets`
  String get allAssets {
    return Intl.message('All Assets', name: 'allAssets', desc: '', args: []);
  }

  /// `Transaction type`
  String get transactionType {
    return Intl.message(
      'Transaction type',
      name: 'transactionType',
      desc: '',
      args: [],
    );
  }

  /// `Reset filters`
  String get resetFilters {
    return Intl.message(
      'Reset filters',
      name: 'resetFilters',
      desc: '',
      args: [],
    );
  }

  /// `Apply filters`
  String get applyFilters {
    return Intl.message(
      'Apply filters',
      name: 'applyFilters',
      desc: '',
      args: [],
    );
  }

  /// `View all`
  String get viewAll {
    return Intl.message('View all', name: 'viewAll', desc: '', args: []);
  }

  /// `Set up 6-digit PIN`
  String get setupSixDigitPin {
    return Intl.message(
      'Set up 6-digit PIN',
      name: 'setupSixDigitPin',
      desc: '',
      args: [],
    );
  }

  /// `This allows you transact more securely on your device`
  String get setupPinSubtitle {
    return Intl.message(
      'This allows you transact more securely on your device',
      name: 'setupPinSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Create your PIN`
  String get createYourPin {
    return Intl.message(
      'Create your PIN',
      name: 'createYourPin',
      desc: '',
      args: [],
    );
  }

  /// `Set up PIN`
  String get setupPin {
    return Intl.message('Set up PIN', name: 'setupPin', desc: '', args: []);
  }

  /// `Setup your 6-digit PIN`
  String get setupYourSixDigitPin {
    return Intl.message(
      'Setup your 6-digit PIN',
      name: 'setupYourSixDigitPin',
      desc: '',
      args: [],
    );
  }

  /// `Confirm your 6-digit PIN`
  String get confirmYourSixDigitPin {
    return Intl.message(
      'Confirm your 6-digit PIN',
      name: 'confirmYourSixDigitPin',
      desc: '',
      args: [],
    );
  }

  /// `Change PIN`
  String get changePin {
    return Intl.message('Change PIN', name: 'changePin', desc: '', args: []);
  }

  /// `Create a new 6-digit pin`
  String get createNewSixDigitPin {
    return Intl.message(
      'Create a new 6-digit pin',
      name: 'createNewSixDigitPin',
      desc: '',
      args: [],
    );
  }

  /// `Confirm your new 6-digit pin`
  String get confirmYourNewSixDigitPin {
    return Intl.message(
      'Confirm your new 6-digit pin',
      name: 'confirmYourNewSixDigitPin',
      desc: '',
      args: [],
    );
  }

  /// `Confirm your current 6-digit pin`
  String get confirmYourCurrentSixDigitPin {
    return Intl.message(
      'Confirm your current 6-digit pin',
      name: 'confirmYourCurrentSixDigitPin',
      desc: '',
      args: [],
    );
  }

  /// `Pin created 🎉`
  String get pinCreated {
    return Intl.message(
      'Pin created 🎉',
      name: 'pinCreated',
      desc: '',
      args: [],
    );
  }

  /// `PIN doesn't match`
  String get pinDoesntMatch {
    return Intl.message(
      'PIN doesn\'t match',
      name: 'pinDoesntMatch',
      desc: '',
      args: [],
    );
  }

  /// `Incorrect pin. Please try again`
  String get incorrectPin {
    return Intl.message(
      'Incorrect pin. Please try again',
      name: 'incorrectPin',
      desc: '',
      args: [],
    );
  }

  /// `New pin created`
  String get newPinCreated {
    return Intl.message(
      'New pin created',
      name: 'newPinCreated',
      desc: '',
      args: [],
    );
  }

  /// `Pin creation failed`
  String get pinCreationFailed {
    return Intl.message(
      'Pin creation failed',
      name: 'pinCreationFailed',
      desc: '',
      args: [],
    );
  }

  /// `Pin change failed`
  String get pinChangeFailed {
    return Intl.message(
      'Pin change failed',
      name: 'pinChangeFailed',
      desc: '',
      args: [],
    );
  }

  /// `Verify Transaction`
  String get verifyTransaction {
    return Intl.message(
      'Verify Transaction',
      name: 'verifyTransaction',
      desc: '',
      args: [],
    );
  }

  /// `Enter your 6-digit PIN`
  String get enterYourSixDigitPin {
    return Intl.message(
      'Enter your 6-digit PIN',
      name: 'enterYourSixDigitPin',
      desc: '',
      args: [],
    );
  }

  /// `Authorization channel not available`
  String get authMethodNotAvailable {
    return Intl.message(
      'Authorization channel not available',
      name: 'authMethodNotAvailable',
      desc: '',
      args: [],
    );
  }

  /// `Oops! Incorrect pin`
  String get validationFailedIncorrectPin {
    return Intl.message(
      'Oops! Incorrect pin',
      name: 'validationFailedIncorrectPin',
      desc: '',
      args: [],
    );
  }

  /// `failed, Incorrect otp`
  String get validationFailedIncorrectOtp {
    return Intl.message(
      'failed, Incorrect otp',
      name: 'validationFailedIncorrectOtp',
      desc: '',
      args: [],
    );
  }

  /// `authorization failed, try again`
  String get authorizationFailed {
    return Intl.message(
      'authorization failed, try again',
      name: 'authorizationFailed',
      desc: '',
      args: [],
    );
  }

  /// `Current code seems incorrect`
  String get oopsCurrentCodeSeemsIncorrect {
    return Intl.message(
      'Current code seems incorrect',
      name: 'oopsCurrentCodeSeemsIncorrect',
      desc: '',
      args: [],
    );
  }

  /// `No assets found`
  String get noAssetsFound {
    return Intl.message(
      'No assets found',
      name: 'noAssetsFound',
      desc: '',
      args: [],
    );
  }

  /// `Hide $0 balances`
  String get hideBalance {
    return Intl.message(
      'Hide \$0 balances',
      name: 'hideBalance',
      desc: '',
      args: [],
    );
  }

  /// `No transactions found`
  String get noTransactionsFound {
    return Intl.message(
      'No transactions found',
      name: 'noTransactionsFound',
      desc: '',
      args: [],
    );
  }

  /// `Terms of Service`
  String get termsOfService {
    return Intl.message(
      'Terms of Service',
      name: 'termsOfService',
      desc: '',
      args: [],
    );
  }

  /// `&`
  String get andSign {
    return Intl.message('&', name: 'andSign', desc: '', args: []);
  }

  /// `Enter or Paste Memo`
  String get enterOrPasteMemo {
    return Intl.message(
      'Enter or Paste Memo',
      name: 'enterOrPasteMemo',
      desc: '',
      args: [],
    );
  }

  /// `What would you like to do?`
  String get whatWouldYouLikeToDo {
    return Intl.message(
      'What would you like to do?',
      name: 'whatWouldYouLikeToDo',
      desc: '',
      args: [],
    );
  }

  /// `Grow your wealth with digital assets`
  String get growYourWealthWithDigitalAssets {
    return Intl.message(
      'Grow your wealth with digital assets',
      name: 'growYourWealthWithDigitalAssets',
      desc: '',
      args: [],
    );
  }

  /// `Protect your money from devaluation`
  String get protectYourMoney {
    return Intl.message(
      'Protect your money from devaluation',
      name: 'protectYourMoney',
      desc: '',
      args: [],
    );
  }

  /// `Great choice`
  String get greatChoiceNoComma {
    return Intl.message(
      'Great choice',
      name: 'greatChoiceNoComma',
      desc: '',
      args: [],
    );
  }

  /// `Great choice,`
  String get greatChoiceWithComma {
    return Intl.message(
      'Great choice,',
      name: 'greatChoiceWithComma',
      desc: '',
      args: [],
    );
  }

  /// `To kickstart your crypto journey, please answer a few short questions. It’ll take less than 1 minute.`
  String get toKickstartYourCrypto {
    return Intl.message(
      'To kickstart your crypto journey, please answer a few short questions. It’ll take less than 1 minute.',
      name: 'toKickstartYourCrypto',
      desc: '',
      args: [],
    );
  }

  /// `Let’s get started`
  String get letGetStarted {
    return Intl.message(
      'Let’s get started',
      name: 'letGetStarted',
      desc: '',
      args: [],
    );
  }

  /// `How much experience do you have in crypto?`
  String get howMuchExperienceCrypto {
    return Intl.message(
      'How much experience do you have in crypto?',
      name: 'howMuchExperienceCrypto',
      desc: '',
      args: [],
    );
  }

  /// `What’s your preferred investment style?`
  String get whatYourPreferredInvestment {
    return Intl.message(
      'What’s your preferred investment style?',
      name: 'whatYourPreferredInvestment',
      desc: '',
      args: [],
    );
  }

  /// `Just a moment...`
  String get justAMoment {
    return Intl.message(
      'Just a moment...',
      name: 'justAMoment',
      desc: '',
      args: [],
    );
  }

  /// `We’ll recommend a portfolio based on your answers`
  String get weWillRecommendAPortfolioBasedOnYourAnswers {
    return Intl.message(
      'We’ll recommend a portfolio based on your answers',
      name: 'weWillRecommendAPortfolioBasedOnYourAnswers',
      desc: '',
      args: [],
    );
  }

  /// `Analyzing your interests`
  String get analyzingYourInterests {
    return Intl.message(
      'Analyzing your interests',
      name: 'analyzingYourInterests',
      desc: '',
      args: [],
    );
  }

  /// `Analyzing your experience`
  String get analyzingYourExperience {
    return Intl.message(
      'Analyzing your experience',
      name: 'analyzingYourExperience',
      desc: '',
      args: [],
    );
  }

  /// `Analyzing your goals`
  String get analyzingYourGoals {
    return Intl.message(
      'Analyzing your goals',
      name: 'analyzingYourGoals',
      desc: '',
      args: [],
    );
  }

  /// `You’re a crypto maxi`
  String get youACryptoMaxi {
    return Intl.message(
      'You’re a crypto maxi',
      name: 'youACryptoMaxi',
      desc: '',
      args: [],
    );
  }

  /// `Failed to load data. Swipe down to refresh!`
  String get failToLoadData {
    return Intl.message(
      'Failed to load data. Swipe down to refresh!',
      name: 'failToLoadData',
      desc: '',
      args: [],
    );
  }

  /// `Last 1 year trend`
  String get last1YearTrend {
    return Intl.message(
      'Last 1 year trend',
      name: 'last1YearTrend',
      desc: '',
      args: [],
    );
  }

  /// `Onboard is not a financial advisor. Digital assets are volatile, so do your own research`
  String get onboardIsNotAFinancialAdvisor {
    return Intl.message(
      'Onboard is not a financial advisor. Digital assets are volatile, so do your own research',
      name: 'onboardIsNotAFinancialAdvisor',
      desc: '',
      args: [],
    );
  }

  /// `By proceeding, you agree to the`
  String get byProceedingYouAgree {
    return Intl.message(
      'By proceeding, you agree to the',
      name: 'byProceedingYouAgree',
      desc: '',
      args: [],
    );
  }

  /// `Disclaimer:`
  String get disclaimerColon {
    return Intl.message(
      'Disclaimer:',
      name: 'disclaimerColon',
      desc: '',
      args: [],
    );
  }

  /// `terms.`
  String get termDot {
    return Intl.message('terms.', name: 'termDot', desc: '', args: []);
  }

  /// `Deposit to start trading`
  String get depositToStartTrading {
    return Intl.message(
      'Deposit to start trading',
      name: 'depositToStartTrading',
      desc: '',
      args: [],
    );
  }

  /// `Here’s your recommended portfolio`
  String get hereRecommendedPortfolio {
    return Intl.message(
      'Here’s your recommended portfolio',
      name: 'hereRecommendedPortfolio',
      desc: '',
      args: [],
    );
  }

  /// `Results are in`
  String get resultsAreIn {
    return Intl.message(
      'Results are in',
      name: 'resultsAreIn',
      desc: '',
      args: [],
    );
  }

  /// `Investor`
  String get investor {
    return Intl.message('Investor', name: 'investor', desc: '', args: []);
  }

  /// `You're a`
  String get youAreA {
    return Intl.message('You\'re a', name: 'youAreA', desc: '', args: []);
  }

  /// `You’re an`
  String get youAreAn {
    return Intl.message('You’re an', name: 'youAreAn', desc: '', args: []);
  }

  /// `Review information`
  String get reviewInformation {
    return Intl.message(
      'Review information',
      name: 'reviewInformation',
      desc: '',
      args: [],
    );
  }

  /// `Action required. Tap here`
  String get actionRequiredTapHere {
    return Intl.message(
      'Action required. Tap here',
      name: 'actionRequiredTapHere',
      desc: '',
      args: [],
    );
  }

  /// `In order to process this transaction, we’ll need additional information from you.`
  String get actionRequiredDesc {
    return Intl.message(
      'In order to process this transaction, we’ll need additional information from you.',
      name: 'actionRequiredDesc',
      desc: '',
      args: [],
    );
  }

  /// `Tap below to view and submit the required information`
  String get tapBelowToSubmitRequiredInfo {
    return Intl.message(
      'Tap below to view and submit the required information',
      name: 'tapBelowToSubmitRequiredInfo',
      desc: '',
      args: [],
    );
  }

  /// `I'm sending to myself`
  String get iAmSendingToMyself {
    return Intl.message(
      'I\'m sending to myself',
      name: 'iAmSendingToMyself',
      desc: '',
      args: [],
    );
  }

  /// `Beneficiary full name`
  String get beneficiaryFullName {
    return Intl.message(
      'Beneficiary full name',
      name: 'beneficiaryFullName',
      desc: '',
      args: [],
    );
  }

  /// `Who are you sending to?`
  String get whoAreYouSendingTo {
    return Intl.message(
      'Who are you sending to?',
      name: 'whoAreYouSendingTo',
      desc: '',
      args: [],
    );
  }

  /// `We need this to comply with regulations. Incorrect details could delay or cancel your transaction`
  String get beneficiaryInfoDesc {
    return Intl.message(
      'We need this to comply with regulations. Incorrect details could delay or cancel your transaction',
      name: 'beneficiaryInfoDesc',
      desc: '',
      args: [],
    );
  }

  /// `Invalid full name, Numbers and special characters are not allowed`
  String get invalidFullName {
    return Intl.message(
      'Invalid full name, Numbers and special characters are not allowed',
      name: 'invalidFullName',
      desc: '',
      args: [],
    );
  }

  /// `Beneficiary Information`
  String get beneficiaryInformation {
    return Intl.message(
      'Beneficiary Information',
      name: 'beneficiaryInformation',
      desc: '',
      args: [],
    );
  }

  /// `What is the destination wallet?`
  String get whatIsTheDestinationWallet {
    return Intl.message(
      'What is the destination wallet?',
      name: 'whatIsTheDestinationWallet',
      desc: '',
      args: [],
    );
  }

  /// `Self-custody wallet`
  String get selfCustodyWallet {
    return Intl.message(
      'Self-custody wallet',
      name: 'selfCustodyWallet',
      desc: '',
      args: [],
    );
  }

  /// `e.g. MetaMask, Trust, Onboard`
  String get selfCustodyWalletExamples {
    return Intl.message(
      'e.g. MetaMask, Trust, Onboard',
      name: 'selfCustodyWalletExamples',
      desc: '',
      args: [],
    );
  }

  /// `e.g. Binance, Coinbase, CEX.io`
  String get exchangeExamples {
    return Intl.message(
      'e.g. Binance, Coinbase, CEX.io',
      name: 'exchangeExamples',
      desc: '',
      args: [],
    );
  }

  /// `Welcome back`
  String get welcomeBack {
    return Intl.message(
      'Welcome back',
      name: 'welcomeBack',
      desc: '',
      args: [],
    );
  }

  /// `Your account is waiting securely! \nLogin below to continue`
  String get sessionLockDesc {
    return Intl.message(
      'Your account is waiting securely! \nLogin below to continue',
      name: 'sessionLockDesc',
      desc: '',
      args: [],
    );
  }

  /// `You were away for a while`
  String get youWereHereForAWhile {
    return Intl.message(
      'You were away for a while',
      name: 'youWereHereForAWhile',
      desc: '',
      args: [],
    );
  }

  /// `Your account is secure! Tap the icon below to unlock using your device`
  String get yourAccountIsSecure {
    return Intl.message(
      'Your account is secure! Tap the icon below to unlock using your device',
      name: 'yourAccountIsSecure',
      desc: '',
      args: [],
    );
  }

  /// `Ongoing order`
  String get onGoingOrder {
    return Intl.message(
      'Ongoing order',
      name: 'onGoingOrder',
      desc: '',
      args: [],
    );
  }

  /// `You’re buying`
  String get youAreBuying {
    return Intl.message(
      'You’re buying',
      name: 'youAreBuying',
      desc: '',
      args: [],
    );
  }

  /// `Multiple active orders`
  String get multipleActiveOrders {
    return Intl.message(
      'Multiple active orders',
      name: 'multipleActiveOrders',
      desc: '',
      args: [],
    );
  }

  /// `Slide to review`
  String get slideToReview {
    return Intl.message(
      'Slide to review',
      name: 'slideToReview',
      desc: '',
      args: [],
    );
  }

  /// `You’ll need to fund your wallet to access savings and trading on Onboard.`
  String get exitOnboardingNote {
    return Intl.message(
      'You’ll need to fund your wallet to access savings and trading on Onboard.',
      name: 'exitOnboardingNote',
      desc: '',
      args: [],
    );
  }

  /// `Yes, skip`
  String get yesSkip {
    return Intl.message('Yes, skip', name: 'yesSkip', desc: '', args: []);
  }

  /// `Very Aggressive`
  String get veryAggressive {
    return Intl.message(
      'Very Aggressive',
      name: 'veryAggressive',
      desc: '',
      args: [],
    );
  }

  /// `Completing this step helps us tailor the best experience for you. Want to continue?`
  String get completingWantToContinue {
    return Intl.message(
      'Completing this step helps us tailor the best experience for you. Want to continue?',
      name: 'completingWantToContinue',
      desc: '',
      args: [],
    );
  }

  /// `Leaving so soon? 🤔`
  String get leavingSoonWithEmoji {
    return Intl.message(
      'Leaving so soon? 🤔',
      name: 'leavingSoonWithEmoji',
      desc: '',
      args: [],
    );
  }

  /// `Don’t miss out! 💰`
  String get doNotMissOut {
    return Intl.message(
      'Don’t miss out! 💰',
      name: 'doNotMissOut',
      desc: '',
      args: [],
    );
  }

  /// `Your personalized portfolio is ready - fund your wallet to start investing!`
  String get yourPersonalisedPortfolio {
    return Intl.message(
      'Your personalized portfolio is ready - fund your wallet to start investing!',
      name: 'yourPersonalisedPortfolio',
      desc: '',
      args: [],
    );
  }

  /// `Your withdrawal limits`
  String get yourWithdrawalLimits {
    return Intl.message(
      'Your withdrawal limits',
      name: 'yourWithdrawalLimits',
      desc: '',
      args: [],
    );
  }

  /// `Per withdrawal`
  String get perWithdrawal {
    return Intl.message(
      'Per withdrawal',
      name: 'perWithdrawal',
      desc: '',
      args: [],
    );
  }

  /// `Daily limit`
  String get dailyLimit {
    return Intl.message('Daily limit', name: 'dailyLimit', desc: '', args: []);
  }

  /// `Weekly limit`
  String get weeklyLimit {
    return Intl.message(
      'Weekly limit',
      name: 'weeklyLimit',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawal limit exceeded.`
  String get withdrawalLimitsExceeded {
    return Intl.message(
      'Withdrawal limit exceeded.',
      name: 'withdrawalLimitsExceeded',
      desc: '',
      args: [],
    );
  }

  /// `View limits`
  String get viewLimits {
    return Intl.message('View limits', name: 'viewLimits', desc: '', args: []);
  }

  /// `Min. amount is {amount}.`
  String minimumAmountIs(Object amount) {
    return Intl.message(
      'Min. amount is $amount.',
      name: 'minimumAmountIs',
      desc: '',
      args: [amount],
    );
  }

  /// `Reset pin`
  String get resetPin {
    return Intl.message('Reset pin', name: 'resetPin', desc: '', args: []);
  }

  /// `Passcode reset successful`
  String get passcodeResetSuccessful {
    return Intl.message(
      'Passcode reset successful',
      name: 'passcodeResetSuccessful',
      desc: '',
      args: [],
    );
  }

  /// `Passcode reset failed`
  String get passcodeResetFailed {
    return Intl.message(
      'Passcode reset failed',
      name: 'passcodeResetFailed',
      desc: '',
      args: [],
    );
  }

  /// `Forgot Passcode?`
  String get forgotPin {
    return Intl.message(
      'Forgot Passcode?',
      name: 'forgotPin',
      desc: '',
      args: [],
    );
  }

  /// `Reset`
  String get reset {
    return Intl.message('Reset', name: 'reset', desc: '', args: []);
  }

  /// `International money transfer`
  String get internationalMoneyTransfer {
    return Intl.message(
      'International money transfer',
      name: 'internationalMoneyTransfer',
      desc: '',
      args: [],
    );
  }

  /// `Move money to multiple destinations across the globe`
  String get internationalMoneyMove {
    return Intl.message(
      'Move money to multiple destinations across the globe',
      name: 'internationalMoneyMove',
      desc: '',
      args: [],
    );
  }

  /// `Superior exchange rates`
  String get superiorExchangeRate {
    return Intl.message(
      'Superior exchange rates',
      name: 'superiorExchangeRate',
      desc: '',
      args: [],
    );
  }

  /// `We offer the best rates possible for almost any currency. No hidden fees`
  String get weOfferTheBestRatesPossible {
    return Intl.message(
      'We offer the best rates possible for almost any currency. No hidden fees',
      name: 'weOfferTheBestRatesPossible',
      desc: '',
      args: [],
    );
  }

  /// `Multiple delivery methods`
  String get multipleDeliveryMethod {
    return Intl.message(
      'Multiple delivery methods',
      name: 'multipleDeliveryMethod',
      desc: '',
      args: [],
    );
  }

  /// `You can send money to a bank, mobile money or even a crypto wallet`
  String get youCanSendMoneyToABankMobileMoney {
    return Intl.message(
      'You can send money to a bank, mobile money or even a crypto wallet',
      name: 'youCanSendMoneyToABankMobileMoney',
      desc: '',
      args: [],
    );
  }

  /// `Send to a crypto wallet`
  String get sendToACryptoWallet {
    return Intl.message(
      'Send to a crypto wallet',
      name: 'sendToACryptoWallet',
      desc: '',
      args: [],
    );
  }

  /// `Let's personalize your crypto journey`
  String get letsPersonalizeYourCryptoJourney {
    return Intl.message(
      'Let\'s personalize your crypto journey',
      name: 'letsPersonalizeYourCryptoJourney',
      desc: '',
      args: [],
    );
  }

  /// `Verify ID to start trading`
  String get verifyIdToStartTrading {
    return Intl.message(
      'Verify ID to start trading',
      name: 'verifyIdToStartTrading',
      desc: '',
      args: [],
    );
  }

  /// `No options available for this question`
  String get noOptionsAvailableForThisQuestion {
    return Intl.message(
      'No options available for this question',
      name: 'noOptionsAvailableForThisQuestion',
      desc: '',
      args: [],
    );
  }

  /// `Why do I need KYC to trade`
  String get whyDoINeedKycToTrade {
    return Intl.message(
      'Why do I need KYC to trade',
      name: 'whyDoINeedKycToTrade',
      desc: '',
      args: [],
    );
  }

  /// `Why do I need KYC to trade?`
  String get whyDoINeedKycToTradeWithQuestionMark {
    return Intl.message(
      'Why do I need KYC to trade?',
      name: 'whyDoINeedKycToTradeWithQuestionMark',
      desc: '',
      args: [],
    );
  }

  /// `To keep Onboard secure and compliant, we require verified KYC for transactions involving cash.`
  String get toKeepOnboardSecureAndCompliantWeRequireKycForCashTx {
    return Intl.message(
      'To keep Onboard secure and compliant, we require verified KYC for transactions involving cash.',
      name: 'toKeepOnboardSecureAndCompliantWeRequireKycForCashTx',
      desc: '',
      args: [],
    );
  }

  /// `Don't want KYC?`
  String get dontWantKyc {
    return Intl.message(
      'Don\'t want KYC?',
      name: 'dontWantKyc',
      desc: '',
      args: [],
    );
  }

  /// `Setup a self custody wallet`
  String get setUpASelfCustodyWallet {
    return Intl.message(
      'Setup a self custody wallet',
      name: 'setUpASelfCustodyWallet',
      desc: '',
      args: [],
    );
  }

  /// `We only recommend this wallet if you already have some knowledge on DeFi and blockchain networks.`
  String get weOnlyRecommendWalletDesc {
    return Intl.message(
      'We only recommend this wallet if you already have some knowledge on DeFi and blockchain networks.',
      name: 'weOnlyRecommendWalletDesc',
      desc: '',
      args: [],
    );
  }

  /// `Start trading`
  String get startTrading {
    return Intl.message(
      'Start trading',
      name: 'startTrading',
      desc: '',
      args: [],
    );
  }

  /// `Sorry, your age does not meet our minimum requirement of 18 years.`
  String get below18YearsError {
    return Intl.message(
      'Sorry, your age does not meet our minimum requirement of 18 years.',
      name: 'below18YearsError',
      desc: '',
      args: [],
    );
  }

  /// `To start trading`
  String get toStartTrading {
    return Intl.message(
      'To start trading',
      name: 'toStartTrading',
      desc: '',
      args: [],
    );
  }

  /// `Wire deposit fee`
  String get wireDepositFee {
    return Intl.message(
      'Wire deposit fee',
      name: 'wireDepositFee',
      desc: '',
      args: [],
    );
  }

  /// `Charged on each wire deposit to your account`
  String get chargedOnEachWireDepositToYourAccount {
    return Intl.message(
      'Charged on each wire deposit to your account',
      name: 'chargedOnEachWireDepositToYourAccount',
      desc: '',
      args: [],
    );
  }

  /// `This code is your referrer’s Board Pass (found in their 'Settings' menu). Enter the code here, so perks or rewards can be assigned`
  String get referralCodeExplainer {
    return Intl.message(
      'This code is your referrer’s Board Pass (found in their \'Settings\' menu). Enter the code here, so perks or rewards can be assigned',
      name: 'referralCodeExplainer',
      desc: '',
      args: [],
    );
  }

  /// `KYC verification incomplete`
  String get kycIncomplete {
    return Intl.message(
      'KYC verification incomplete',
      name: 'kycIncomplete',
      desc: '',
      args: [],
    );
  }

  /// `Get a dedicated USD bank account`
  String get getADedicatedUsBankAccount {
    return Intl.message(
      'Get a dedicated USD bank account',
      name: 'getADedicatedUsBankAccount',
      desc: '',
      args: [],
    );
  }

  /// `Quick activation`
  String get quickActivation {
    return Intl.message(
      'Quick activation',
      name: 'quickActivation',
      desc: '',
      args: [],
    );
  }

  /// `Setup & start receiving payments within \n1 business day`
  String get setUpAndStartReceivingPaymentsWithin1BusinessDay {
    return Intl.message(
      'Setup & start receiving payments within \n1 business day',
      name: 'setUpAndStartReceivingPaymentsWithin1BusinessDay',
      desc: '',
      args: [],
    );
  }

  /// `Get paid by anyone`
  String get getPaidByAnyone {
    return Intl.message(
      'Get paid by anyone',
      name: 'getPaidByAnyone',
      desc: '',
      args: [],
    );
  }

  /// `Accept ACH & Wire payments from Deel, Payoneer, Upwork, Paypal & more`
  String get acceptAchAndWirePaymentsFromDeelAndMore {
    return Intl.message(
      'Accept ACH & Wire payments from Deel, Payoneer, Upwork, Paypal & more',
      name: 'acceptAchAndWirePaymentsFromDeelAndMore',
      desc: '',
      args: [],
    );
  }

  /// `Flexible funding & withdrawal`
  String get flexibleFundingAndWithdrawal {
    return Intl.message(
      'Flexible funding & withdrawal',
      name: 'flexibleFundingAndWithdrawal',
      desc: '',
      args: [],
    );
  }

  /// `Fund your USD account with, or withdraw to, other currencies`
  String get fundYourUsdAccountWithOrWithdrawToOtherCurrencies {
    return Intl.message(
      'Fund your USD account with, or withdraw to, other currencies',
      name: 'fundYourUsdAccountWithOrWithdrawToOtherCurrencies',
      desc: '',
      args: [],
    );
  }

  /// `There are no assets to display`
  String get thereAreNoAssetsToDisplay {
    return Intl.message(
      'There are no assets to display',
      name: 'thereAreNoAssetsToDisplay',
      desc: '',
      args: [],
    );
  }

  /// `A few more details to close this out`
  String get lastOnboardingKycQuestionSubtitle {
    return Intl.message(
      'A few more details to close this out',
      name: 'lastOnboardingKycQuestionSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Referral`
  String get referral {
    return Intl.message('Referral', name: 'referral', desc: '', args: []);
  }

  /// `Spend globally with USD card`
  String get spendGloballyWithUsdCard {
    return Intl.message(
      'Spend globally with USD card',
      name: 'spendGloballyWithUsdCard',
      desc: '',
      args: [],
    );
  }

  /// `US Account`
  String get usAccount {
    return Intl.message('US Account', name: 'usAccount', desc: '', args: []);
  }

  /// `Get a trading portfolio`
  String get getATradingPortfolio {
    return Intl.message(
      'Get a trading portfolio',
      name: 'getATradingPortfolio',
      desc: '',
      args: [],
    );
  }

  /// `Onboard accounts`
  String get onboardAccounts {
    return Intl.message(
      'Onboard accounts',
      name: 'onboardAccounts',
      desc: '',
      args: [],
    );
  }

  /// `External accounts`
  String get externalAccounts {
    return Intl.message(
      'External accounts',
      name: 'externalAccounts',
      desc: '',
      args: [],
    );
  }

  /// `From any cash account`
  String get fromAnyCashAccount {
    return Intl.message(
      'From any cash account',
      name: 'fromAnyCashAccount',
      desc: '',
      args: [],
    );
  }

  /// `Send from any bank or mobile money wallet`
  String get sendFromAnyBankOrMobileMoneyWallet {
    return Intl.message(
      'Send from any bank or mobile money wallet',
      name: 'sendFromAnyBankOrMobileMoneyWallet',
      desc: '',
      args: [],
    );
  }

  /// `From any external crypto wallet`
  String get fromAnyExternalCryptoWallet {
    return Intl.message(
      'From any external crypto wallet',
      name: 'fromAnyExternalCryptoWallet',
      desc: '',
      args: [],
    );
  }

  /// `Send from crypto wallet`
  String get sendFromCryptoWallet {
    return Intl.message(
      'Send from crypto wallet',
      name: 'sendFromCryptoWallet',
      desc: '',
      args: [],
    );
  }

  /// `Merchant wallet`
  String get merchantWallet {
    return Intl.message(
      'Merchant wallet',
      name: 'merchantWallet',
      desc: '',
      args: [],
    );
  }

  /// `Begin your simple crypto investing`
  String get beginYourCryptoInvesting {
    return Intl.message(
      'Begin your simple crypto investing',
      name: 'beginYourCryptoInvesting',
      desc: '',
      args: [],
    );
  }

  /// `Tap here for quick access to your accounts or to open a new one`
  String get tapHereForQuickAccess {
    return Intl.message(
      'Tap here for quick access to your accounts or to open a new one',
      name: 'tapHereForQuickAccess',
      desc: '',
      args: [],
    );
  }

  /// `View your accounts`
  String get viewYourAccounts {
    return Intl.message(
      'View your accounts',
      name: 'viewYourAccounts',
      desc: '',
      args: [],
    );
  }

  /// `Switch between accounts`
  String get switchBetweenAccount {
    return Intl.message(
      'Switch between accounts',
      name: 'switchBetweenAccount',
      desc: '',
      args: [],
    );
  }

  /// `A quick swipe takes you to each account that you have `
  String get aQuickSwipeAccount {
    return Intl.message(
      'A quick swipe takes you to each account that you have ',
      name: 'aQuickSwipeAccount',
      desc: '',
      args: [],
    );
  }

  /// `Send money globally`
  String get sendMoneyGlobally {
    return Intl.message(
      'Send money globally',
      name: 'sendMoneyGlobally',
      desc: '',
      args: [],
    );
  }

  /// `Failed to load data. Please refresh`
  String get failToLoadDataPleaseRefresh {
    return Intl.message(
      'Failed to load data. Please refresh',
      name: 'failToLoadDataPleaseRefresh',
      desc: '',
      args: [],
    );
  }

  /// `Access fast, affordable transfers to send money anywhere`
  String get accessFastAffordableTransferMoney {
    return Intl.message(
      'Access fast, affordable transfers to send money anywhere',
      name: 'accessFastAffordableTransferMoney',
      desc: '',
      args: [],
    );
  }

  /// `KYC verification in review`
  String get kycInReview {
    return Intl.message(
      'KYC verification in review',
      name: 'kycInReview',
      desc: '',
      args: [],
    );
  }

  /// `Your verification is in review`
  String get yourKycIsInReview {
    return Intl.message(
      'Your verification is in review',
      name: 'yourKycIsInReview',
      desc: '',
      args: [],
    );
  }

  /// `We need a few more details`
  String get WeNeedMoreDetails {
    return Intl.message(
      'We need a few more details',
      name: 'WeNeedMoreDetails',
      desc: '',
      args: [],
    );
  }

  /// `Basic ID verification`
  String get basicIdVerification {
    return Intl.message(
      'Basic ID verification',
      name: 'basicIdVerification',
      desc: '',
      args: [],
    );
  }

  /// `Additional ID verification`
  String get additionalIdVerification {
    return Intl.message(
      'Additional ID verification',
      name: 'additionalIdVerification',
      desc: '',
      args: [],
    );
  }

  /// `Choose your preferred crypto balance`
  String get chooseYourPreferredCryptoBalance {
    return Intl.message(
      'Choose your preferred crypto balance',
      name: 'chooseYourPreferredCryptoBalance',
      desc: '',
      args: [],
    );
  }

  /// `Complete verification`
  String get completeVerification {
    return Intl.message(
      'Complete verification',
      name: 'completeVerification',
      desc: '',
      args: [],
    );
  }

  /// `Your KYC verification is incomplete`
  String get yourKycIsIncomplete {
    return Intl.message(
      'Your KYC verification is incomplete',
      name: 'yourKycIsIncomplete',
      desc: '',
      args: [],
    );
  }

  /// `Thank you for starting the verification process. You have a few steps left to completion. It will take only 3 mins`
  String get kcyInCompleteSubtitle {
    return Intl.message(
      'Thank you for starting the verification process. You have a few steps left to completion. It will take only 3 mins',
      name: 'kcyInCompleteSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Proof of address — Legible & matches ID info`
  String get proofOfAddressTip {
    return Intl.message(
      'Proof of address — Legible & matches ID info',
      name: 'proofOfAddressTip',
      desc: '',
      args: [],
    );
  }

  /// `Your ID — Upload a clear, untampered version`
  String get uKycIdTip {
    return Intl.message(
      'Your ID — Upload a clear, untampered version',
      name: 'uKycIdTip',
      desc: '',
      args: [],
    );
  }

  /// `Provide consistent & valid information`
  String get provideConsistentInfo {
    return Intl.message(
      'Provide consistent & valid information',
      name: 'provideConsistentInfo',
      desc: '',
      args: [],
    );
  }

  /// `Helpful Tips:`
  String get helpfulTipsWithColon {
    return Intl.message(
      'Helpful Tips:',
      name: 'helpfulTipsWithColon',
      desc: '',
      args: [],
    );
  }

  /// `Selfie — well-lit, face showing clearly.`
  String get selfieKycTips {
    return Intl.message(
      'Selfie — well-lit, face showing clearly.',
      name: 'selfieKycTips',
      desc: '',
      args: [],
    );
  }

  /// `ID document — clearly captured, all parts visible.`
  String get idDocumentKycTips {
    return Intl.message(
      'ID document — clearly captured, all parts visible.',
      name: 'idDocumentKycTips',
      desc: '',
      args: [],
    );
  }

  /// `ID number — please make sure to enter it correctly.`
  String get idNumberKycTips {
    return Intl.message(
      'ID number — please make sure to enter it correctly.',
      name: 'idNumberKycTips',
      desc: '',
      args: [],
    );
  }

  /// `Something went wrong.\nBut not to worry, please retry`
  String get tradingWalletCreationError {
    return Intl.message(
      'Something went wrong.\\nBut not to worry, please retry',
      name: 'tradingWalletCreationError',
      desc: '',
      args: [],
    );
  }

  /// `error occurred, try again`
  String get errorOccurredTryAgain {
    return Intl.message(
      'error occurred, try again',
      name: 'errorOccurredTryAgain',
      desc: '',
      args: [],
    );
  }

  /// `Start by funding your portfolio with as little as $1`
  String get startByFundingYourPortfolioWithAsLittleAsOneDollar {
    return Intl.message(
      'Start by funding your portfolio with as little as \$1',
      name: 'startByFundingYourPortfolioWithAsLittleAsOneDollar',
      desc: '',
      args: [],
    );
  }

  /// `Recipient gets`
  String get recipientGets {
    return Intl.message(
      'Recipient gets',
      name: 'recipientGets',
      desc: '',
      args: [],
    );
  }

  /// `Crypto Maxi ✨`
  String get cryptoMaxi {
    return Intl.message(
      'Crypto Maxi ✨',
      name: 'cryptoMaxi',
      desc: '',
      args: [],
    );
  }

  /// `Card terminated successfully`
  String get cardTerminatedSuccessfully {
    return Intl.message(
      'Card terminated successfully',
      name: 'cardTerminatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `You have {kycRetriesLeft} retries left`
  String verificationAttemptLeft(Object kycRetriesLeft) {
    return Intl.message(
      'You have $kycRetriesLeft retries left',
      name: 'verificationAttemptLeft',
      desc: '',
      args: [kycRetriesLeft],
    );
  }

  /// `We’ll send you a code to confirm phone number`
  String get wellSendYouACodeToConfirmPhoneNumber {
    return Intl.message(
      'We’ll send you a code to confirm phone number',
      name: 'wellSendYouACodeToConfirmPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Send via WhatsApp`
  String get sendViaWhatsapp {
    return Intl.message(
      'Send via WhatsApp',
      name: 'sendViaWhatsapp',
      desc: '',
      args: [],
    );
  }

  /// `Send via SMS`
  String get sendViaSms {
    return Intl.message('Send via SMS', name: 'sendViaSms', desc: '', args: []);
  }

  /// `Via WhatsApp`
  String get viaWhatsapp {
    return Intl.message(
      'Via WhatsApp',
      name: 'viaWhatsapp',
      desc: '',
      args: [],
    );
  }

  /// `Via SMS`
  String get viaSms {
    return Intl.message('Via SMS', name: 'viaSms', desc: '', args: []);
  }

  /// `File is too large. Please upload a doc {size} or less`
  String proofOfAddressFileTooLarge(Object size) {
    return Intl.message(
      'File is too large. Please upload a doc $size or less',
      name: 'proofOfAddressFileTooLarge',
      desc: '',
      args: [size],
    );
  }

  /// `Invalid session Id`
  String get invalidSessionId {
    return Intl.message(
      'Invalid session Id',
      name: 'invalidSessionId',
      desc: '',
      args: [],
    );
  }

  /// `An error occurred, please try again`
  String get anErrorOccurredPleaseTryAgain {
    return Intl.message(
      'An error occurred, please try again',
      name: 'anErrorOccurredPleaseTryAgain',
      desc: '',
      args: [],
    );
  }

  /// `No Authorization methods available`
  String get noAuthMethodsAvailable {
    return Intl.message(
      'No Authorization methods available',
      name: 'noAuthMethodsAvailable',
      desc: '',
      args: [],
    );
  }

  /// `No active auth session`
  String get noActiveAuthSession {
    return Intl.message(
      'No active auth session',
      name: 'noActiveAuthSession',
      desc: '',
      args: [],
    );
  }

  /// `Verify with passcode`
  String get verifyWithPasscode {
    return Intl.message(
      'Verify with passcode',
      name: 'verifyWithPasscode',
      desc: '',
      args: [],
    );
  }

  /// `Verify with authenticatorApp`
  String get verifyWithAuthenticatorApp {
    return Intl.message(
      'Verify with authenticatorApp',
      name: 'verifyWithAuthenticatorApp',
      desc: '',
      args: [],
    );
  }

  /// `Enter the 6-digit code generated`
  String get enterTheSixDigitCodeGenerated {
    return Intl.message(
      'Enter the 6-digit code generated',
      name: 'enterTheSixDigitCodeGenerated',
      desc: '',
      args: [],
    );
  }

  /// `Verify with Defi Wallet`
  String get verifyWithDefiWallet {
    return Intl.message(
      'Verify with Defi Wallet',
      name: 'verifyWithDefiWallet',
      desc: '',
      args: [],
    );
  }

  /// `Unsupported authentication method`
  String get UnsupportedAuthenticationMethod {
    return Intl.message(
      'Unsupported authentication method',
      name: 'UnsupportedAuthenticationMethod',
      desc: '',
      args: [],
    );
  }

  /// `Provide ID for additional verification`
  String get additionalIdVerificationPageTitle {
    return Intl.message(
      'Provide ID for additional verification',
      name: 'additionalIdVerificationPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `Choose ID type and provide ID number`
  String get additionalIdVerificationPageSubtitle {
    return Intl.message(
      'Choose ID type and provide ID number',
      name: 'additionalIdVerificationPageSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Choose ID type`
  String get chooseIdType {
    return Intl.message(
      'Choose ID type',
      name: 'chooseIdType',
      desc: '',
      args: [],
    );
  }

  /// `You will be required to provide ID number`
  String get chooseIdTypeSubtitle {
    return Intl.message(
      'You will be required to provide ID number',
      name: 'chooseIdTypeSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `ID type number`
  String get idTypeNumber {
    return Intl.message(
      'ID type number',
      name: 'idTypeNumber',
      desc: '',
      args: [],
    );
  }

  /// `Please provide a description of the\ndocument type you want to provided`
  String get otherAdditionalIdVerificationDescription {
    return Intl.message(
      'Please provide a description of the\ndocument type you want to provided',
      name: 'otherAdditionalIdVerificationDescription',
      desc: '',
      args: [],
    );
  }

  /// `Describe document`
  String get describeDocument {
    return Intl.message(
      'Describe document',
      name: 'describeDocument',
      desc: '',
      args: [],
    );
  }

  /// `Try another method`
  String get tryAnotherMethod {
    return Intl.message(
      'Try another method',
      name: 'tryAnotherMethod',
      desc: '',
      args: [],
    );
  }

  /// `Confirm with passkey`
  String get confirmWithPasskey {
    return Intl.message(
      'Confirm with passkey',
      name: 'confirmWithPasskey',
      desc: '',
      args: [],
    );
  }

  /// `Confirm with email`
  String get confirmWithEmail {
    return Intl.message(
      'Confirm with email',
      name: 'confirmWithEmail',
      desc: '',
      args: [],
    );
  }

  /// `Confirm with phone number`
  String get confirmWithPhoneNumber {
    return Intl.message(
      'Confirm with phone number',
      name: 'confirmWithPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `We need to verify that it’s really you behind the screen`
  String get weNeedToVerifyItsReallyYouBehindTheScreen {
    return Intl.message(
      'We need to verify that it’s really you behind the screen',
      name: 'weNeedToVerifyItsReallyYouBehindTheScreen',
      desc: '',
      args: [],
    );
  }

  /// `Confirm via passkey`
  String get confirmViaPasskey {
    return Intl.message(
      'Confirm via passkey',
      name: 'confirmViaPasskey',
      desc: '',
      args: [],
    );
  }

  /// `Confirm via email`
  String get confirmViaEmail {
    return Intl.message(
      'Confirm via email',
      name: 'confirmViaEmail',
      desc: '',
      args: [],
    );
  }

  /// `Confirm via account pin`
  String get confirmViaAccountPin {
    return Intl.message(
      'Confirm via account pin',
      name: 'confirmViaAccountPin',
      desc: '',
      args: [],
    );
  }

  /// `Confirm via SMS`
  String get confirmViaSMS {
    return Intl.message(
      'Confirm via SMS',
      name: 'confirmViaSMS',
      desc: '',
      args: [],
    );
  }

  /// `Confirm via authenticator app`
  String get confirmViaAuthenticatorApp {
    return Intl.message(
      'Confirm via authenticator app',
      name: 'confirmViaAuthenticatorApp',
      desc: '',
      args: [],
    );
  }

  /// `Confirm via wallet`
  String get confirmViaWallet {
    return Intl.message(
      'Confirm via wallet',
      name: 'confirmViaWallet',
      desc: '',
      args: [],
    );
  }

  /// `Choose method`
  String get chooseMethod {
    return Intl.message(
      'Choose method',
      name: 'chooseMethod',
      desc: '',
      args: [],
    );
  }

  /// `Confirm with passcode`
  String get confirmWithPasscode {
    return Intl.message(
      'Confirm with passcode',
      name: 'confirmWithPasscode',
      desc: '',
      args: [],
    );
  }

  /// `Confirm with Authenticator`
  String get confirmWithAuthenticator {
    return Intl.message(
      'Confirm with Authenticator',
      name: 'confirmWithAuthenticator',
      desc: '',
      args: [],
    );
  }

  /// `Enter the code sent to`
  String get enterTheCodeSentTo {
    return Intl.message(
      'Enter the code sent to',
      name: 'enterTheCodeSentTo',
      desc: '',
      args: [],
    );
  }

  /// `Send code`
  String get sendCode {
    return Intl.message('Send code', name: 'sendCode', desc: '', args: []);
  }

  /// `Resend in {timer}`
  String resendIn(Object timer) {
    return Intl.message(
      'Resend in $timer',
      name: 'resendIn',
      desc: '',
      args: [timer],
    );
  }

  /// `Via WhatsApp`
  String get viaWhatsApp {
    return Intl.message(
      'Via WhatsApp',
      name: 'viaWhatsApp',
      desc: '',
      args: [],
    );
  }

  /// `Via SMS`
  String get viaSMS {
    return Intl.message('Via SMS', name: 'viaSMS', desc: '', args: []);
  }

  /// `Email me a code`
  String get emailMeACode {
    return Intl.message(
      'Email me a code',
      name: 'emailMeACode',
      desc: '',
      args: [],
    );
  }

  /// `Confirm with wallet`
  String get confirmWithWallet {
    return Intl.message(
      'Confirm with wallet',
      name: 'confirmWithWallet',
      desc: '',
      args: [],
    );
  }

  /// `No defi wallet found on device`
  String get noDefiWalletFoundOnDevice {
    return Intl.message(
      'No defi wallet found on device',
      name: 'noDefiWalletFoundOnDevice',
      desc: '',
      args: [],
    );
  }

  /// `Choose your preferred asset `
  String get chooseYourPreferredAsset {
    return Intl.message(
      'Choose your preferred asset ',
      name: 'chooseYourPreferredAsset',
      desc: '',
      args: [],
    );
  }

  /// `Please sign to confirm authorization.\nYour signature proves that you are the rightful owner of this DeFi wallet.`
  String get walletAuthorizationMessage {
    return Intl.message(
      'Please sign to confirm authorization.\nYour signature proves that you are the rightful owner of this DeFi wallet.',
      name: 'walletAuthorizationMessage',
      desc: '',
      args: [],
    );
  }

  /// `Bank`
  String get bank {
    return Intl.message('Bank', name: 'bank', desc: '', args: []);
  }

  /// `To access global payments`
  String get toAccessGlobalPayment {
    return Intl.message(
      'To access global payments',
      name: 'toAccessGlobalPayment',
      desc: '',
      args: [],
    );
  }

  /// `Send exactly`
  String get sendExactly {
    return Intl.message(
      'Send exactly',
      name: 'sendExactly',
      desc: '',
      args: [],
    );
  }

  /// `I have sent`
  String get iHaveSent {
    return Intl.message('I have sent', name: 'iHaveSent', desc: '', args: []);
  }

  /// `This wallet is in watch mode. Recover wallet to perform transactions`
  String get thisWalletIsInWatchModeSubCopy {
    return Intl.message(
      'This wallet is in watch mode. Recover wallet to perform transactions',
      name: 'thisWalletIsInWatchModeSubCopy',
      desc: '',
      args: [],
    );
  }

  /// `Restore your wallet`
  String get restoreYourWallet {
    return Intl.message(
      'Restore your wallet',
      name: 'restoreYourWallet',
      desc: '',
      args: [],
    );
  }

  /// `Restore`
  String get restore {
    return Intl.message('Restore', name: 'restore', desc: '', args: []);
  }

  /// `Make sure you send only to`
  String get makeSureYouSendOnlyTo {
    return Intl.message(
      'Make sure you send only to',
      name: 'makeSureYouSendOnlyTo',
      desc: '',
      args: [],
    );
  }

  /// `Sending from a different network`
  String get sendingFromADifferentNetwork {
    return Intl.message(
      'Sending from a different network',
      name: 'sendingFromADifferentNetwork',
      desc: '',
      args: [],
    );
  }

  /// `may lead to loss of funds.`
  String get mayLeadToLossOfFunds {
    return Intl.message(
      'may lead to loss of funds.',
      name: 'mayLeadToLossOfFunds',
      desc: '',
      args: [],
    );
  }

  /// `You're sending {BNB} which will first be converted to USD, and may cause slight variations in the final received {NGN} amount`
  String sendDefiSwapWarning(Object BNB, Object NGN) {
    return Intl.message(
      'You\'re sending $BNB which will first be converted to USD, and may cause slight variations in the final received $NGN amount',
      name: 'sendDefiSwapWarning',
      desc: '',
      args: [BNB, NGN],
    );
  }

  /// `in 10mins`
  String get in10Mins {
    return Intl.message('in 10mins', name: 'in10Mins', desc: '', args: []);
  }

  /// `Recipient got`
  String get recipientGot {
    return Intl.message(
      'Recipient got',
      name: 'recipientGot',
      desc: '',
      args: [],
    );
  }

  /// `Account restricted`
  String get accountRestricted {
    return Intl.message(
      'Account restricted',
      name: 'accountRestricted',
      desc: '',
      args: [],
    );
  }

  /// `Account deactivated`
  String get accountDeactivated {
    return Intl.message(
      'Account deactivated',
      name: 'accountDeactivated',
      desc: '',
      args: [],
    );
  }

  /// `Reach out to us for more info`
  String get reachOutToUsForMoreInfo {
    return Intl.message(
      'Reach out to us for more info',
      name: 'reachOutToUsForMoreInfo',
      desc: '',
      args: [],
    );
  }

  /// `Your account ({bankName}, {maskedAccountNumber}) has been deactivated and cannot be used for transactions. Incoming deposits will be reversed.`
  String accountDeactivatedSubCopy(
    Object bankName,
    Object maskedAccountNumber,
  ) {
    return Intl.message(
      'Your account ($bankName, $maskedAccountNumber) has been deactivated and cannot be used for transactions. Incoming deposits will be reversed.',
      name: 'accountDeactivatedSubCopy',
      desc: '',
      args: [bankName, maskedAccountNumber],
    );
  }

  /// `Your account ({bankName}, {maskedAccountNumber}) has been restricted and cannot be used for transactions. Incoming deposits will be reversed until resolved.`
  String accountRestrictedSubCopy(Object bankName, Object maskedAccountNumber) {
    return Intl.message(
      'Your account ($bankName, $maskedAccountNumber) has been restricted and cannot be used for transactions. Incoming deposits will be reversed until resolved.',
      name: 'accountRestrictedSubCopy',
      desc: '',
      args: [bankName, maskedAccountNumber],
    );
  }

  /// `Accounts`
  String get accounts {
    return Intl.message('Accounts', name: 'accounts', desc: '', args: []);
  }

  /// `Passkey registration failed. Login again to try again.`
  String get invalidPasskeySession {
    return Intl.message(
      'Passkey registration failed. Login again to try again.',
      name: 'invalidPasskeySession',
      desc: '',
      args: [],
    );
  }

  /// `My {currency} account address`
  String myUsdAccountAddress(Object currency) {
    return Intl.message(
      'My $currency account address',
      name: 'myUsdAccountAddress',
      desc: '',
      args: [currency],
    );
  }

  /// `Reset Passcode`
  String get resetPasscode {
    return Intl.message(
      'Reset Passcode',
      name: 'resetPasscode',
      desc: '',
      args: [],
    );
  }

  /// `Forgot Passcode`
  String get forgotPasscode {
    return Intl.message(
      'Forgot Passcode',
      name: 'forgotPasscode',
      desc: '',
      args: [],
    );
  }

  /// `Update your passcode anytime`
  String get updateYourPasscodeAnytime {
    return Intl.message(
      'Update your passcode anytime',
      name: 'updateYourPasscodeAnytime',
      desc: '',
      args: [],
    );
  }

  /// `Setup passcode to secure account`
  String get setUpPasscodeToSecureAccount {
    return Intl.message(
      'Setup passcode to secure account',
      name: 'setUpPasscodeToSecureAccount',
      desc: '',
      args: [],
    );
  }

  /// `Can't remember your current passcode?`
  String get cantRememberYourCurrentPasscode {
    return Intl.message(
      'Can\'t remember your current passcode?',
      name: 'cantRememberYourCurrentPasscode',
      desc: '',
      args: [],
    );
  }

  /// `Blockchain Withdrawal`
  String get blockchainWithdrawal {
    return Intl.message(
      'Blockchain Withdrawal',
      name: 'blockchainWithdrawal',
      desc: '',
      args: [],
    );
  }

  /// `Asset not supported by merchant`
  String get assetNotSupportedByMerchant {
    return Intl.message(
      'Asset not supported by merchant',
      name: 'assetNotSupportedByMerchant',
      desc: '',
      args: [],
    );
  }

  /// `No balances available`
  String get noBalancesAvailable {
    return Intl.message(
      'No balances available',
      name: 'noBalancesAvailable',
      desc: '',
      args: [],
    );
  }

  /// `Hash`
  String get hash {
    return Intl.message('Hash', name: 'hash', desc: '', args: []);
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[Locale.fromSubtags(languageCode: 'en')];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
