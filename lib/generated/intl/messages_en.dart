// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m0(bankName, maskedAccountNumber) =>
      "Your account (${bankName}, ${maskedAccountNumber}) has been deactivated and cannot be used for transactions. Incoming deposits will be reversed.";

  static String m1(bankName, maskedAccountNumber) =>
      "Your account (${bankName}, ${maskedAccountNumber}) has been restricted and cannot be used for transactions. Incoming deposits will be reversed until resolved.";

  static String m2(Drive_Type) =>
      "Account was backed up to ${Drive_Type}, which is not supported on this device.";

  static String m3(number) => "Account ${number}";

  static String m4(minBalance) =>
      "Withdrawal will set account below the min. balance of ${minBalance}\'";

  static String m5(currency) => "Activate your ${currency} card";

  static String m6(appVersion, buildNumber) =>
      "Version ${appVersion} (Build ${buildNumber})";

  static String m7(currency) => "As this is a non-${currency} transaction";

  static String m8(timeStamp) =>
      "Welcome to Onboard!\n\nPlease sign this message to complete authentication. Your signature verifies that you’re the owner of this wallet.\n This request will not trigger a transaction or cost you anything.\n${timeStamp}";

  static String m9(auth) => "${auth} verification code";

  static String m10(auth) => "Authenticate via ${auth}";

  static String m11(drive_name) => "Backed up to ${drive_name}";

  static String m12(iCloud) =>
      "We\'re safely backing up your wallet to your ${iCloud}. This should be quick";

  static String m13(cloud) =>
      "Account recovery details are backed up to your ${cloud} storage. It helps you regain account access if you log\nout or switch devices";

  static String m14(tokenSymbol) => "Buy ${tokenSymbol} and get started!";

  static String m15(tokenSymbol) =>
      "${tokenSymbol} token is required to pay network fees for transactions. There\'s no ${tokenSymbol} in your wallet, so we suggest adding some now.";

  static String m16(tokenSymbol) =>
      "Buy ${tokenSymbol} with your local currency";

  static String m17(param) => "By tapping ‘${param}’, you agree to Onboard’s";

  static String m18(amount) =>
      "We’ve received a deposit of ${amount} for your virtual card and are now funding it.";

  static String m19(label, maskedNumber) =>
      "Your card (${label}**${maskedNumber}) has been terminated, and can no longer be used for transactions. If applicable, your balance will be sent to your wallet.";

  static String m20(currency) =>
      "Make fast payments online and in-store, anywhere in the world, with your own ${currency} virtual card.";

  static String m21(minBalance) =>
      "Withdrawal will set card below the min. balance of ${minBalance}";

  static String m22(BNB) =>
      "Withdrawing ${BNB} or any other crypto asset to cash will require USD conversion.";

  static String m23(network) =>
      "This transaction is still in progress, but requires a claim step to complete the transfer on ${network}. Please contact us for help with this.";

  static String m24(currency) => "Congrats! \nHere\'s your ${currency} account";

  static String m25(Google) => "Continue with ${Google}";

  static String m26(currency) => "${currency} Balance";

  static String m27(currency) => "${currency} bank transfer";

  static String m28(currency, country) =>
      "A fee applies for non-${currency} transactions or where the payment is processed by the merchant outside the ${country}. This fee is from the card network, not Onboard.";

  static String m29(balance) => "Current balance - ${balance}";

  static String m30(tokenSymbol) => "Deposit ${tokenSymbol} and get started!";

  static String m31(name) => "Deposit ${name}";

  static String m32(NGN) =>
      "${NGN} deposits unavailable. Please try again soon.";

  static String m33(document) => "Enter your ${document} number.";

  static String m34(USDC, network) =>
      "Only deposit ${USDC} from the ${network} network to not lose funds.";

  static String m35(amount, asset) =>
      "Deposit at least ${amount} ${asset}. Sending below this amount may lead to a loss of funds or delays.";

  static String m36(cardCurrencySymbol, conversionRate, assetCode) =>
      "${cardCurrencySymbol}1.00 = ${conversionRate} ${assetCode}";

  static String m37(USD) => "Fund your ${USD} account with your wallet balance";

  static String m38(x) =>
      "We\'re funding your card with ${x} and will notify you when complete";

  static String m39(x) =>
      "We\'re funding your account with ${x} and will notify you when complete";

  static String m40(countryCode) =>
      "Transactions in other currencies (or at non-${countryCode} merchants) incur currency conversion fees";

  static String m41(asset) =>
      "Select this provider to complete your ${asset} transaction";

  static String m42(cardCurrency) => "Get a ${cardCurrency} card";

  static String m43(tokenSymbol) => "Got ${tokenSymbol} elsewhere? Deposit";

  static String m44(tokenSymbol) =>
      "Grow your money when you save in a digital dollar like ${tokenSymbol}";

  static String m45(count) => "Hidden NFTs ${count}";

  static String m46(count) =>
      "${Intl.plural(count, zero: '', one: ' 1 hour', other: ' ${count} hours')} ";

  static String m47(count) =>
      "Incorrect passcode. ${Intl.plural(count, zero: '', one: '1 attempt', other: '${count} attempts')} left ";

  static String m48(NGN, BNB) =>
      "Send ${NGN} to a designated account number and receive ${BNB} instantly";

  static String m49(symbol) =>
      "You have insufficient ${symbol} to pay the fee for this transaction. Top up your ${symbol} balance and try again.";

  static String m50(fiatTokenValue, tokenSymbol) =>
      "To cover network fees for this transaction, you\'ll need ${fiatTokenValue} worth of ${tokenSymbol} token.";

  static String m51(BNB) =>
      "To cover network fees for this transaction, you\'ll need ${BNB} token.";

  static String m52(tokenSymbol) => "Insufficient ${tokenSymbol} balance";

  static String m53(networkName) =>
      "Oops! This is an invalid ${networkName}\ncontract address. Please check again";

  static String m54(appName) =>
      "${appName} only requires ID Verification when buying or selling assets with local currencies. This is to deter bad actors from having access and using it for harmful reasons.";

  static String m55(date) => "Last checked ${date}";

  static String m56(amount) => "Max amount is ${amount}";

  static String m57(amount) => "Min amount is ${amount}";

  static String m58(min) => "Min. network amount is ${min}.";

  static String m59(amount) => "Min. amount is ${amount}.";

  static String m60(min) => "Minimum block confirmation is ${min}";

  static String m61(count) =>
      "${Intl.plural(count, zero: '', one: ' 1 minute', other: ' ${count} minutes')} ";

  static String m62(minute) => "${minute}m ago";

  static String m63(cardCurrency) => "Move funds to your ${cardCurrency} card";

  static String m64(number) => "My Items ${number}";

  static String m65(network) => "My ${network} Address";

  static String m66(currency) => "My ${currency} account address";

  static String m67(count) =>
      "You have no more attempts. Logging out ${Intl.plural(count, zero: '', one: 'in 1 sec..', other: 'in ${count} secs..')}";

  static String m68(Drive) =>
      "We couldn\'t find a recovery file on\nthis account. Please retry by connecting another ${Drive} account.";

  static String m69(BNB) => "Insufficient ${BNB} balance to cover fees";

  static String m70(networkName) => "on ${networkName}";

  static String m71(crypto) =>
      "Send ${crypto} to an address and receive USD or EUR in your account.";

  static String m72(crypto) =>
      "Pay USD or EUR to a dedicated account and receive ${crypto}";

  static String m73(NGN, BNB) =>
      "Send ${NGN} to a designated account number and receive ${BNB} instantly";

  static String m74(fee) => "One-time ${fee} fee charged on 1st deposit";

  static String m75(rail) => "Only ${rail} transfers";

  static String m76(countryName, countryCurrency) =>
      "${countryName} ${countryCurrency} deposits is coming\nsoon";

  static String m77(network) =>
      "The transaction is awaiting confirmation on ${network}\'s network. We\'ll notify you once completed.";

  static String m78(network) =>
      "This transaction has been submitted to ${network} and is awaiting confirmation. Sit tight, it should be done soon.";

  static String m79(USD) =>
      "Pick from a selection of merchants that offer you great ${USD} rates";

  static String m80(timer) => "Please wait${timer}before setting up a new card";

  static String m81(size) =>
      "File is too large. Please upload a doc ${size} or less";

  static String m82(formattedRate) =>
      "Rate has being updated to ${formattedRate}";

  static String m83(currency) => "Receive ${currency} in your bank account";

  static String m84(NGN) =>
      "Receive ${NGN} payout in your bank account within minutes";

  static String m85(provider) => "Redirecting to ${provider}";

  static String m86(timer) => "Resend in ${timer}";

  static String m87(time) => "Resend in ${time}";

  static String m88(Uniswap) => "Return to ${Uniswap} to continue";

  static String m89(tokenSymbol) => "Save in ${tokenSymbol}";

  static String m90(count) =>
      "${Intl.plural(count, zero: '', one: ' 1 second', other: ' ${count} seconds')} ";

  static String m91(credentialType) =>
      "Please confirm that you read below before viewing your ${credentialType}";

  static String m92(credentialType) =>
      "Your ${credentialType} grants total access to your wallet. Never share it with\nanyone!";

  static String m93(credentialType) =>
      "Onboard will never ask you to share your ${credentialType}";

  static String m94(from) => "Select ${from} Asset";

  static String m95(asset, fiat_currency) =>
      "Sell ${asset} or any crypto token for ${fiat_currency}";

  static String m96(BNB, NGN) =>
      "You\'re sending ${BNB} which will first be converted to USD, and may cause slight variations in the final received ${NGN} amount";

  static String m97(currency) => "Send to ${currency} balance";

  static String m98(tokenSymbol) => "Send ${tokenSymbol}";

  static String m99(URL) =>
      "Join me and many others using Onboard to make their money borderless. Sign up with my link, earn a reward: ${URL}";

  static String m100(amount) => "Slippage cannot be more than ${amount}";

  static String m101(tokenSymbol) =>
      "You don\'t have enough of the ${tokenSymbol} to swap";

  static String m102(networkName) => "Switched to ${networkName}";

  static String m103(minimumBalance) =>
      "This keeps your card above the ${minimumBalance} minimum balance required to stay active";

  static String m104(currency) =>
      "To create a ${currency} account, review & accept these usage terms";

  static String m105(symbol) => "${symbol} is hidden";

  static String m106(symbol) => "${symbol} is visible";

  static String m107(token) => "Trade ${token}";

  static String m108(symbol) => "Transfer ${symbol}";

  static String m109(currency) => "${currency} account gets";

  static String m110(tokenSymbol, rate) => "1 ${tokenSymbol} ≈ ${rate} USD";

  static String m111(tokenSymbol) => "${tokenSymbol} is US regulated";

  static String m112(tokenSymbol) =>
      "${tokenSymbol} is a digital dollar that is regulated and fully reserved. Reserves are held at US financial institutions";

  static String m113(kycRetriesLeft) =>
      "You have ${kycRetriesLeft} retries left";

  static String m114(auth) => "Verify with ${auth}";

  static String m115(credentialType) =>
      "Your ${credentialType} grants access to your entire wallet. Use securely!";

  static String m116(duration) => "We’ll notify you ${duration}";

  static String m117(NGN) =>
      "${NGN} withdrawals unavailable. Please try again soon.";

  static String m118(x) => "${x} wants to connect to your wallet";

  static String m119(currency) =>
      "You don’t have any ${currency} direct\naccount";

  static String m120(brand, maskedPan) =>
      "You have a previous card (${brand} ${maskedPan}) that was deactivated. ";

  static String m121(currency) => "You have not added a ${currency} account";

  static String m122(max) =>
      "You can only have ${max} Onboard virtual cards\nactive at a time. Deactivate any of your active\ncards to create a new one";

  static String m123(count) =>
      "You have ${Intl.plural(count, zero: 'no retry', one: '1 retry', other: '${count} retries')} left";

  static String m124(name) => "${name}, you\'re in!";

  static String m125(count) =>
      "Your ${Intl.plural(count, zero: 'order', one: 'order', other: 'orders')} needs attention";

  static String m126(token) => "You’re buying ${token}  •";

  static String m127(token) => "You’re selling ${token}  •";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "HeadsUp": MessageLookupByLibrary.simpleMessage("Heads up"),
    "IncreaseYourSecurity": MessageLookupByLibrary.simpleMessage(
      "Increase your security",
    ),
    "SetPasscodeAccountBackup": MessageLookupByLibrary.simpleMessage(
      "Set passcode, account backup",
    ),
    "USDCardsAndRewards": MessageLookupByLibrary.simpleMessage(
      "USD Cards & Rewards",
    ),
    "USDCardsAndRewardsDesc": MessageLookupByLibrary.simpleMessage(
      "Get cashback on every purchase",
    ),
    "UnsupportedAuthenticationMethod": MessageLookupByLibrary.simpleMessage(
      "Unsupported authentication method",
    ),
    "WeNeedMoreDetails": MessageLookupByLibrary.simpleMessage(
      "We need a few more details",
    ),
    "a6DigitPasscodeForLoginOnThisDevice": MessageLookupByLibrary.simpleMessage(
      "A 6-digit passcode for login on this device",
    ),
    "aDedicatedUsVirtualAccount": MessageLookupByLibrary.simpleMessage(
      "A dedicated USD virtual account",
    ),
    "aDrive": MessageLookupByLibrary.simpleMessage("a drive"),
    "aFailedTransactionDueToInsufficientFunds":
        MessageLookupByLibrary.simpleMessage(
          "A failed transaction on your card due to insufficient funds ",
        ),
    "aNewAndImprovedDashboard": MessageLookupByLibrary.simpleMessage(
      "A new and improved \ndashboard ✨",
    ),
    "aQuickSwipeAccount": MessageLookupByLibrary.simpleMessage(
      "A quick swipe takes you to each account that you have ",
    ),
    "aToz": MessageLookupByLibrary.simpleMessage("A - Z"),
    "aUnifiedViewForYourCrypto": MessageLookupByLibrary.simpleMessage(
      "A unified view for your crypto",
    ),
    "aUnifiedViewForYourCryptoSubCopy": MessageLookupByLibrary.simpleMessage(
      "We now show your balances across\nall networks, and a simplified view\nfor each token you hold",
    ),
    "aWalletThatKeepsYouInFullControl": MessageLookupByLibrary.simpleMessage(
      "A wallet that keeps you (and only you!) in full control",
    ),
    "about": MessageLookupByLibrary.simpleMessage("About"),
    "acceptAchAndWirePaymentsFromDeelAndMore":
        MessageLookupByLibrary.simpleMessage(
          "Accept ACH & Wire payments from Deel, Payoneer, Upwork, Paypal & more",
        ),
    "acceptEurAndUsdGloballySwiftlyConvertedToDigitalDollars":
        MessageLookupByLibrary.simpleMessage(
          "Accept EUR & USD globally, swiftly converted to digital dollars",
        ),
    "acceptTermsOfService": MessageLookupByLibrary.simpleMessage(
      "Accept terms of service",
    ),
    "acceptedGlobalFunding": MessageLookupByLibrary.simpleMessage(
      "Accepted globally, online",
    ),
    "acceptedPayments": MessageLookupByLibrary.simpleMessage(
      "Accepted payments",
    ),
    "accessFastAffordableTransferMoney": MessageLookupByLibrary.simpleMessage(
      "Access fast, affordable transfers to send money anywhere",
    ),
    "account": MessageLookupByLibrary.simpleMessage("Account"),
    "accountDeactivated": MessageLookupByLibrary.simpleMessage(
      "Account deactivated",
    ),
    "accountDeactivatedSubCopy": m0,
    "accountDetails": MessageLookupByLibrary.simpleMessage("Account details"),
    "accountGets": MessageLookupByLibrary.simpleMessage("Account gets"),
    "accountName": MessageLookupByLibrary.simpleMessage("Account name"),
    "accountNameCopied": MessageLookupByLibrary.simpleMessage(
      "Account name copied!",
    ),
    "accountNumber": MessageLookupByLibrary.simpleMessage("Account number"),
    "accountNumberCopied": MessageLookupByLibrary.simpleMessage(
      "Account number copied!",
    ),
    "accountRestricted": MessageLookupByLibrary.simpleMessage(
      "Account restricted",
    ),
    "accountRestrictedSubCopy": m1,
    "accountStatus": MessageLookupByLibrary.simpleMessage("Account status"),
    "accountTermsOfUse": MessageLookupByLibrary.simpleMessage(
      "Account terms of use",
    ),
    "accountTypeSubCopy": MessageLookupByLibrary.simpleMessage(
      "Which of the options below best describes\nyou",
    ),
    "accountWasBackedUpToDriveWhichIsNotSupportedToThisDevice": m2,
    "accountWillBeDeletedPermanently": MessageLookupByLibrary.simpleMessage(
      "You will permanently lose all your profile information. Your wallet will remain usable once backed up",
    ),
    "accountWithNumber": m3,
    "accountWithdrawalMinimumBalanceWarning": m4,
    "accounts": MessageLookupByLibrary.simpleMessage("Accounts"),
    "ach": MessageLookupByLibrary.simpleMessage("ACH"),
    "actionCouldNotBeCompletedPleaseContactSupport":
        MessageLookupByLibrary.simpleMessage(
          "Action could not be completed. Please contact support",
        ),
    "actionRequired": MessageLookupByLibrary.simpleMessage("Action required"),
    "actionRequiredDesc": MessageLookupByLibrary.simpleMessage(
      "In order to process this transaction, we’ll need additional information from you.",
    ),
    "actionRequiredTapHere": MessageLookupByLibrary.simpleMessage(
      "Action required. Tap here",
    ),
    "activateProfile": MessageLookupByLibrary.simpleMessage(
      "Activate profile ",
    ),
    "activateUSDCard": m5,
    "activationFee": MessageLookupByLibrary.simpleMessage("Activation fee"),
    "activationInProgress": MessageLookupByLibrary.simpleMessage(
      "Activation in progress",
    ),
    "active": MessageLookupByLibrary.simpleMessage("Active"),
    "activeOverdue": MessageLookupByLibrary.simpleMessage("Active overdue"),
    "activity": MessageLookupByLibrary.simpleMessage("Activity"),
    "add": MessageLookupByLibrary.simpleMessage("Add"),
    "add2FA": MessageLookupByLibrary.simpleMessage("Add 2FA"),
    "add2FASubCopy": MessageLookupByLibrary.simpleMessage(
      "For extra account security, we recommend enabling at least one 2FA method",
    ),
    "addAPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Add a phone number",
    ),
    "addAccount": MessageLookupByLibrary.simpleMessage("Add your account"),
    "addAddress": MessageLookupByLibrary.simpleMessage("Add address"),
    "addAnother": MessageLookupByLibrary.simpleMessage("Add another"),
    "addAsSubscription": MessageLookupByLibrary.simpleMessage(
      "Add as subscription",
    ),
    "addAssetsTitle": MessageLookupByLibrary.simpleMessage(
      "How would you like to add assets?",
    ),
    "addCardAppleGooglePay": MessageLookupByLibrary.simpleMessage(
      "Add card to Apple / Google Pay",
    ),
    "addCardAppleGooglePaySubCopy": MessageLookupByLibrary.simpleMessage(
      "When added, your card can be used for contactless payments",
    ),
    "addCryptoToGetStarted": MessageLookupByLibrary.simpleMessage(
      "Add crypto to get started",
    ),
    "addCustomToken": MessageLookupByLibrary.simpleMessage("Add custom token"),
    "addCustomTokens": MessageLookupByLibrary.simpleMessage(
      "Add custom tokens",
    ),
    "addFundsToAvoidCardCharges": MessageLookupByLibrary.simpleMessage(
      "Add funds to avoid card charges",
    ),
    "addHomeAddress": MessageLookupByLibrary.simpleMessage(
      "Add a home address",
    ),
    "addMoney": MessageLookupByLibrary.simpleMessage("Add money"),
    "addNew": MessageLookupByLibrary.simpleMessage("Add new"),
    "addNewAccount": MessageLookupByLibrary.simpleMessage("Add new account"),
    "addNumber": MessageLookupByLibrary.simpleMessage("Add number"),
    "addPasskey": MessageLookupByLibrary.simpleMessage("Add Passkey"),
    "addPhoneNumber": MessageLookupByLibrary.simpleMessage("Add phone number"),
    "addToAppleOrGooglePay": MessageLookupByLibrary.simpleMessage(
      "Add to Apple or Google pay",
    ),
    "addToAppleWallet": MessageLookupByLibrary.simpleMessage(
      "Add to Apple Wallet",
    ),
    "addToGooglePay": MessageLookupByLibrary.simpleMessage("Add to Google Pay"),
    "addWalletAddress": MessageLookupByLibrary.simpleMessage(
      "Add wallet address",
    ),
    "addYourAccount": MessageLookupByLibrary.simpleMessage("Add your account"),
    "added": MessageLookupByLibrary.simpleMessage("Added"),
    "addedToFavourites": MessageLookupByLibrary.simpleMessage(
      "Added to favourites",
    ),
    "additionalIdVerification": MessageLookupByLibrary.simpleMessage(
      "Additional ID verification",
    ),
    "additionalIdVerificationPageSubtitle":
        MessageLookupByLibrary.simpleMessage(
          "Choose ID type and provide ID number",
        ),
    "additionalIdVerificationPageTitle": MessageLookupByLibrary.simpleMessage(
      "Provide ID for additional verification",
    ),
    "additionalQuestions": MessageLookupByLibrary.simpleMessage(
      "Additional questions",
    ),
    "address": MessageLookupByLibrary.simpleMessage("Address"),
    "addressActLikeAccount": MessageLookupByLibrary.simpleMessage(
      "Address above acts like an \'account number\' for receiving funds.",
    ),
    "addressCopied": MessageLookupByLibrary.simpleMessage("Address Copied!"),
    "addressDeleted": MessageLookupByLibrary.simpleMessage("Address deleted"),
    "addressDocumentFormat": MessageLookupByLibrary.simpleMessage(
      "pdf, jpg, jpeg & png format. 5 MB Max",
    ),
    "addressIsRequired": MessageLookupByLibrary.simpleMessage(
      "Address is required",
    ),
    "addressLineOne": MessageLookupByLibrary.simpleMessage("Address line 1"),
    "addressLineTwo": MessageLookupByLibrary.simpleMessage(
      "Address line 2 (optional)",
    ),
    "addressSaved": MessageLookupByLibrary.simpleMessage("Address saved"),
    "ads": MessageLookupByLibrary.simpleMessage("Ads"),
    "allAccounts": MessageLookupByLibrary.simpleMessage("All Accounts"),
    "allAssets": MessageLookupByLibrary.simpleMessage("All Assets"),
    "allNetworks": MessageLookupByLibrary.simpleMessage("All networks"),
    "allSetACopyOfYourCardStatementWillBeSentToYourMailInAFewMins":
        MessageLookupByLibrary.simpleMessage(
          "All set. A copy of your requested\ncard statement will be sent to your\nemail in a few minutes",
        ),
    "allSetExcitedToHaveYouWelcome": MessageLookupByLibrary.simpleMessage(
      "All set. We\'re so excited to have you here. \nWelcome to Onboard ✨",
    ),
    "allSpamTokenHidden": MessageLookupByLibrary.simpleMessage(
      "All spam crypto hidden",
    ),
    "allTransactions": MessageLookupByLibrary.simpleMessage("All transactions"),
    "allowTransaction": MessageLookupByLibrary.simpleMessage(
      "Allow Transaction",
    ),
    "amount": MessageLookupByLibrary.simpleMessage("Amount"),
    "amountCopied": MessageLookupByLibrary.simpleMessage("Amount copied"),
    "amountFunded": MessageLookupByLibrary.simpleMessage("Amount funded"),
    "amountToBeSentOrReceived": MessageLookupByLibrary.simpleMessage(
      "Amounts to be sent or received",
    ),
    "amountUpdated": MessageLookupByLibrary.simpleMessage("Amount updated"),
    "amountWithdrawn": MessageLookupByLibrary.simpleMessage("Amount withdrawn"),
    "anErrorOccurredPleaseTryAgain": MessageLookupByLibrary.simpleMessage(
      "An error occurred, please try again",
    ),
    "anFXConversionFeeWillBeChargedSeparately":
        MessageLookupByLibrary.simpleMessage(
          "an FX conversion fee will be charged separately.",
        ),
    "analyzingYourExperience": MessageLookupByLibrary.simpleMessage(
      "Analyzing your experience",
    ),
    "analyzingYourGoals": MessageLookupByLibrary.simpleMessage(
      "Analyzing your goals",
    ),
    "analyzingYourInterests": MessageLookupByLibrary.simpleMessage(
      "Analyzing your interests",
    ),
    "and": MessageLookupByLibrary.simpleMessage("and"),
    "andSign": MessageLookupByLibrary.simpleMessage("&"),
    "annually": MessageLookupByLibrary.simpleMessage("Annually"),
    "anyCashAccount": MessageLookupByLibrary.simpleMessage("Any cash account"),
    "anyPendingCardFeesWillStillBeCharged":
        MessageLookupByLibrary.simpleMessage(
          "Any pending card fees will still be charged.",
        ),
    "appStoreVersionFunctionality": MessageLookupByLibrary.simpleMessage(
      "This App Store version of the Onboard app lacks a few features, pending approval from Apple. While that\'s ongoing, we recommend accessing our full app functionality via TestFlight",
    ),
    "appUpdateAvailable": MessageLookupByLibrary.simpleMessage(
      "App update available!",
    ),
    "appUpdateSubcopy": MessageLookupByLibrary.simpleMessage(
      "Your app version is out of date.\nUpdate to get the best of\nOnboard ",
    ),
    "appVersion": m6,
    "appVersionCopied": MessageLookupByLibrary.simpleMessage(
      "App Version Copied!",
    ),
    "appearToBeSpamToken": MessageLookupByLibrary.simpleMessage(
      "Appears to be a spam crypto. Proceed with caution",
    ),
    "application": MessageLookupByLibrary.simpleMessage("Application"),
    "applyFilters": MessageLookupByLibrary.simpleMessage("Apply filters"),
    "approve": MessageLookupByLibrary.simpleMessage("Approve"),
    "approveTokenSubCubCopy": MessageLookupByLibrary.simpleMessage(
      "You\'re giving access to transact with this token",
    ),
    "approving": MessageLookupByLibrary.simpleMessage("Approving"),
    "approvingToken": MessageLookupByLibrary.simpleMessage("Approving token"),
    "apps": MessageLookupByLibrary.simpleMessage("Apps"),
    "areYouSure": MessageLookupByLibrary.simpleMessage("Are you sure?"),
    "arrives": MessageLookupByLibrary.simpleMessage("Arrives"),
    "asThisIsANonUSDTransaction": m7,
    "asset": MessageLookupByLibrary.simpleMessage("asset"),
    "assetNotSupportedByMerchant": MessageLookupByLibrary.simpleMessage(
      "Asset not supported by merchant",
    ),
    "assetPrices": MessageLookupByLibrary.simpleMessage("Asset Prices"),
    "assets": MessageLookupByLibrary.simpleMessage("Assets"),
    "assigningYouFullCustody": MessageLookupByLibrary.simpleMessage(
      "Assigning you full custody 🔐",
    ),
    "associatedAccounts": MessageLookupByLibrary.simpleMessage(
      "These are accounts associated with your seedphrase.",
    ),
    "attemptedTransactionWillFail": MessageLookupByLibrary.simpleMessage(
      "While frozen, any attempted transactions on this card will fail",
    ),
    "authMethodNotAvailable": MessageLookupByLibrary.simpleMessage(
      "Authorization channel not available",
    ),
    "authSignMessageCopy": m8,
    "authVerificationCode": m9,
    "authViewHeader": MessageLookupByLibrary.simpleMessage(
      "The simplest wallet to\nbuy, sell, store and \nsend crypto",
    ),
    "authenticateVia": m10,
    "authenticatingYourEmail": MessageLookupByLibrary.simpleMessage(
      "Authenticating your email",
    ),
    "authenticatorApp": MessageLookupByLibrary.simpleMessage(
      "Authenticator App",
    ),
    "authorizationFailed": MessageLookupByLibrary.simpleMessage(
      "authorization failed, try again",
    ),
    "authorizationFailedTryAgain": MessageLookupByLibrary.simpleMessage(
      "Authorization failed, try again",
    ),
    "authorizeCardTermination": MessageLookupByLibrary.simpleMessage(
      "I authorize terminating my card.",
    ),
    "authorizeTransaction": MessageLookupByLibrary.simpleMessage(
      "Authorize Transaction",
    ),
    "authorizeUnfreezeCard": MessageLookupByLibrary.simpleMessage(
      "I authorize unfreezing my card to reactivate it for transactions",
    ),
    "authorizing": MessageLookupByLibrary.simpleMessage("Authorizing..."),
    "available": MessageLookupByLibrary.simpleMessage("Available"),
    "availableBalance": MessageLookupByLibrary.simpleMessage(
      "Available Balance",
    ),
    "availableBalanceDescription": MessageLookupByLibrary.simpleMessage(
      "The total amount of crypto you can use to create new ads. You can top up or withdraw from this balance anytime.",
    ),
    "avoidFailedTransactions": MessageLookupByLibrary.simpleMessage(
      "Avoid failed transactions",
    ),
    "backUpDetailDialogSubCopy": MessageLookupByLibrary.simpleMessage(
      "Your account recovery file is backed up to your cloud storage",
    ),
    "backUpDetails": MessageLookupByLibrary.simpleMessage("Backup details"),
    "backUpErrorMessage": MessageLookupByLibrary.simpleMessage(
      "Something went wrong with this backup.\nBut not to worry, let’s retry this",
    ),
    "backUpNow": MessageLookupByLibrary.simpleMessage("Back up now"),
    "backUpYourWallet": MessageLookupByLibrary.simpleMessage(
      "Backup your wallet",
    ),
    "backedUpTo": m11,
    "backingUpAccountSubCopy": m12,
    "backingUpWallet": MessageLookupByLibrary.simpleMessage(
      "Backing up wallet",
    ),
    "backup": MessageLookupByLibrary.simpleMessage("Back up"),
    "backupCompleted": MessageLookupByLibrary.simpleMessage(
      "Your wallet is now protected",
    ),
    "backupLogoutNudgeSubCopy": MessageLookupByLibrary.simpleMessage(
      "To sign back in, we\'ll require authentication to regain access to your account.",
    ),
    "backupRecoveryItemSubCopy": m13,
    "backupWalletReason": MessageLookupByLibrary.simpleMessage(
      "Please authenticate to secure your wallet",
    ),
    "badFitSurveySubCopy": MessageLookupByLibrary.simpleMessage(
      "It doesn\'t appear our current product is the best fit for your needs right now. We\'d love to let you know if this changes in future.",
    ),
    "balance": MessageLookupByLibrary.simpleMessage("Balance"),
    "balanceHidden": MessageLookupByLibrary.simpleMessage("Balance hidden!"),
    "balanceHiddenSubCopy": MessageLookupByLibrary.simpleMessage(
      "Your balances will no longer be visible. \n Tap the icon beside your balance to unhide",
    ),
    "balances": MessageLookupByLibrary.simpleMessage("Balances"),
    "bank": MessageLookupByLibrary.simpleMessage("Bank"),
    "bankAddress": MessageLookupByLibrary.simpleMessage("Bank address"),
    "bankAddressCopied": MessageLookupByLibrary.simpleMessage(
      "Bank address copied!",
    ),
    "bankName": MessageLookupByLibrary.simpleMessage("Bank name"),
    "bankNameCopied": MessageLookupByLibrary.simpleMessage("Bank name copied!"),
    "bankTransfer": MessageLookupByLibrary.simpleMessage("Bank Transfer"),
    "basicIdVerification": MessageLookupByLibrary.simpleMessage(
      "Basic ID verification",
    ),
    "beforeConfirmingSwap": MessageLookupByLibrary.simpleMessage(
      "before confirming swap",
    ),
    "beforeYouGo": MessageLookupByLibrary.simpleMessage("Before you go"),
    "beginYourCryptoInvesting": MessageLookupByLibrary.simpleMessage(
      "Begin your simple crypto investing",
    ),
    "below18YearsError": MessageLookupByLibrary.simpleMessage(
      "Sorry, your age does not meet our minimum requirement of 18 years.",
    ),
    "beneficiaryFullName": MessageLookupByLibrary.simpleMessage(
      "Beneficiary full name",
    ),
    "beneficiaryInfoDesc": MessageLookupByLibrary.simpleMessage(
      "We need this to comply with regulations. Incorrect details could delay or cancel your transaction",
    ),
    "beneficiaryInformation": MessageLookupByLibrary.simpleMessage(
      "Beneficiary Information",
    ),
    "beneficiaryNetwork": MessageLookupByLibrary.simpleMessage(
      "Beneficiary Network",
    ),
    "billingAddress": MessageLookupByLibrary.simpleMessage("Billing address"),
    "blockConfirmations": MessageLookupByLibrary.simpleMessage(
      "block confirmation(s)",
    ),
    "blockchainWithdrawal": MessageLookupByLibrary.simpleMessage(
      "Blockchain Withdrawal",
    ),
    "blocks": MessageLookupByLibrary.simpleMessage("block(s)"),
    "bridgeOnAnyNetwork": MessageLookupByLibrary.simpleMessage(
      "Bridge on any network",
    ),
    "bridgeOnAnyNetworkSubCopy": MessageLookupByLibrary.simpleMessage(
      "Move your crypto across networks. You can now swap crypto between our supported networks",
    ),
    "browseAssets": MessageLookupByLibrary.simpleMessage("Browse Assets"),
    "browseTheWeb": MessageLookupByLibrary.simpleMessage("Browse the web"),
    "buy": MessageLookupByLibrary.simpleMessage("Buy"),
    "buyGasAndGetStarted": m14,
    "buyGasSubCopy": m15,
    "buyTokenWithYourLocalCurrency": m16,
    "buyUsingNgnViaPtoP": MessageLookupByLibrary.simpleMessage(
      "Buy using NGN via P2P",
    ),
    "buyWithCash": MessageLookupByLibrary.simpleMessage("Buy with cash"),
    "buyWithCrypto": MessageLookupByLibrary.simpleMessage("Buy with crypto"),
    "byProceedingYouAgree": MessageLookupByLibrary.simpleMessage(
      "By proceeding, you agree to the",
    ),
    "byTappingYouAgree": m17,
    "canUnfreezeAnytime": MessageLookupByLibrary.simpleMessage(
      "You can unfreeze card anytime",
    ),
    "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
    "cancelConfirmation": MessageLookupByLibrary.simpleMessage(
      "Cancel confirmation",
    ),
    "cancelPayment": MessageLookupByLibrary.simpleMessage("Cancel payment"),
    "cancelPaymentSubCopy": MessageLookupByLibrary.simpleMessage(
      "Please only cancel this payment if you have not yet initiated a transfer.",
    ),
    "cancelled": MessageLookupByLibrary.simpleMessage("Cancelled"),
    "cantRememberYourCurrentPasscode": MessageLookupByLibrary.simpleMessage(
      "Can\'t remember your current passcode?",
    ),
    "card": MessageLookupByLibrary.simpleMessage("Card"),
    "cardActions": MessageLookupByLibrary.simpleMessage("Card actions"),
    "cardActivationSubCopy": MessageLookupByLibrary.simpleMessage(
      "It’ll be ready for you to use within 24 hours",
    ),
    "cardAddressCopied": MessageLookupByLibrary.simpleMessage(
      "Card address copied",
    ),
    "cardBalance": MessageLookupByLibrary.simpleMessage("Card balance"),
    "cardBalanceIsLow": MessageLookupByLibrary.simpleMessage(
      "Card balance is low!",
    ),
    "cardBrand": MessageLookupByLibrary.simpleMessage("Card brand"),
    "cardControlsForTotalSecurity": MessageLookupByLibrary.simpleMessage(
      "Card controls for total security",
    ),
    "cardCreation": MessageLookupByLibrary.simpleMessage("Card creation"),
    "cardCreationFee": MessageLookupByLibrary.simpleMessage(
      "Card creation fee",
    ),
    "cardCreationFeeSubCopy": MessageLookupByLibrary.simpleMessage(
      "A one-time fee to create your card",
    ),
    "cardCryptoFundingInProgressSubCopy": m18,
    "cardCurrency": MessageLookupByLibrary.simpleMessage("Card currency"),
    "cardDeActivated": MessageLookupByLibrary.simpleMessage(
      "Card deactivated!",
    ),
    "cardDeactivated": MessageLookupByLibrary.simpleMessage("Card deactivated"),
    "cardDeactivatedSubCopy": m19,
    "cardDetails": MessageLookupByLibrary.simpleMessage("Card details"),
    "cardFeatureNotAvailableSubCopy": MessageLookupByLibrary.simpleMessage(
      "We can\'t offer a card to you just yet, but please get in touch with us if you\'d like to learn more.",
    ),
    "cardFrozen": MessageLookupByLibrary.simpleMessage("Card frozen"),
    "cardFrozenSubCopy": MessageLookupByLibrary.simpleMessage(
      "Your card has been frozen. To fix, fund your card to meet the minimum balance required.",
    ),
    "cardFunding": MessageLookupByLibrary.simpleMessage("Card funding"),
    "cardFundingRequired": MessageLookupByLibrary.simpleMessage(
      "Card funding required!",
    ),
    "cardGets": MessageLookupByLibrary.simpleMessage("Card gets"),
    "cardGot": MessageLookupByLibrary.simpleMessage("Card got"),
    "cardHolderName": MessageLookupByLibrary.simpleMessage("Cardholder name"),
    "cardIntroVirtualCardsSubCopy": m20,
    "cardIsSuspended": MessageLookupByLibrary.simpleMessage(
      "Card is suspended",
    ),
    "cardMaintenanceFeeAndMonth": MessageLookupByLibrary.simpleMessage(
      "Card maintenance fee",
    ),
    "cardName": MessageLookupByLibrary.simpleMessage("Card name"),
    "cardNumber": MessageLookupByLibrary.simpleMessage("Card number"),
    "cardReactivated": MessageLookupByLibrary.simpleMessage("Card reactivated"),
    "cardRequestSuccessSubCopy": MessageLookupByLibrary.simpleMessage(
      "You\'re all done! We\'ll notify you soon once your card is activated and ready for use.",
    ),
    "cardRequestSuccessful": MessageLookupByLibrary.simpleMessage(
      "Card Request Successful",
    ),
    "cardStatementRequestSuccessful": MessageLookupByLibrary.simpleMessage(
      "Card statement request \nsuccessful",
    ),
    "cardStatus": MessageLookupByLibrary.simpleMessage("Card status"),
    "cardSuspended": MessageLookupByLibrary.simpleMessage("Card suspended!"),
    "cardTerminatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Card terminated successfully",
    ),
    "cardTerminationFeeExplainer": MessageLookupByLibrary.simpleMessage(
      "This covers the cost of your transaction - primarily for currency conversion and transaction fees.",
    ),
    "cardTopUp": MessageLookupByLibrary.simpleMessage("Card top-up"),
    "cardTopUpInfoCopy": MessageLookupByLibrary.simpleMessage(
      "Your card requires a minimum balance to remain active. Top ups are needed when your balance falls below this amount.",
    ),
    "cardTransactions": MessageLookupByLibrary.simpleMessage(
      "Card transactions",
    ),
    "cardType": MessageLookupByLibrary.simpleMessage("Card type"),
    "cardUnfrozen": MessageLookupByLibrary.simpleMessage("Card unfrozen"),
    "cardUsageAndTerms": MessageLookupByLibrary.simpleMessage(
      "Card usage terms",
    ),
    "cardUsed": MessageLookupByLibrary.simpleMessage("Card used"),
    "cardWillDeactivateIn": MessageLookupByLibrary.simpleMessage(
      "Card will deactivate in",
    ),
    "cardWithdrawalMinimumBalanceWarning": m21,
    "cardWithdrawalSelectAssetSubCopy": MessageLookupByLibrary.simpleMessage(
      "Which token do you want to receive at your destination wallet?",
    ),
    "cards": MessageLookupByLibrary.simpleMessage("Cards"),
    "cash": MessageLookupByLibrary.simpleMessage("Cash"),
    "cashToCryptoFast": MessageLookupByLibrary.simpleMessage(
      "Cash to Crypto, fast",
    ),
    "cashTransfer": MessageLookupByLibrary.simpleMessage("Cash transfer"),
    "cashWithdrawal": MessageLookupByLibrary.simpleMessage("Cash Withdrawal"),
    "cashWithdrawalWarning": m22,
    "chainIdNotProvided": MessageLookupByLibrary.simpleMessage(
      "Chain Id not provided",
    ),
    "chainNotAdded": MessageLookupByLibrary.simpleMessage("Chain not added"),
    "change": MessageLookupByLibrary.simpleMessage("Change"),
    "changeAsset": MessageLookupByLibrary.simpleMessage("Change asset"),
    "changeCountry": MessageLookupByLibrary.simpleMessage("Change country"),
    "changeDeliveryMethod": MessageLookupByLibrary.simpleMessage(
      "Change delivery method",
    ),
    "changeEmail": MessageLookupByLibrary.simpleMessage("Change email"),
    "changeNumber": MessageLookupByLibrary.simpleMessage("Change number"),
    "changePasscode": MessageLookupByLibrary.simpleMessage("Change Passcode"),
    "changePaymentProvider": MessageLookupByLibrary.simpleMessage(
      "Change payment provider",
    ),
    "changePin": MessageLookupByLibrary.simpleMessage("Change PIN"),
    "charge": MessageLookupByLibrary.simpleMessage("Charge"),
    "chargedOnEachDepositToYourAccount": MessageLookupByLibrary.simpleMessage(
      "Charged on each deposit to your account",
    ),
    "chargedOnEachWireDepositToYourAccount":
        MessageLookupByLibrary.simpleMessage(
          "Charged on each wire deposit to your account",
        ),
    "chatSubtitle": MessageLookupByLibrary.simpleMessage(
      "Need some help? Leave us a message",
    ),
    "chatWithUs": MessageLookupByLibrary.simpleMessage("Chat with us"),
    "checkOutOurLatestUpdates": MessageLookupByLibrary.simpleMessage(
      "Check out our latest updates",
    ),
    "checkingDepositStatus": MessageLookupByLibrary.simpleMessage(
      "Checking deposit status...",
    ),
    "chipperCashTag": MessageLookupByLibrary.simpleMessage("Chipper Cash Tag"),
    "chooseAPreferredMethod": MessageLookupByLibrary.simpleMessage(
      "Choose a preferred method",
    ),
    "chooseAccount": MessageLookupByLibrary.simpleMessage("Choose account"),
    "chooseAuthenticator": MessageLookupByLibrary.simpleMessage(
      "Choose authenticator",
    ),
    "chooseBand": MessageLookupByLibrary.simpleMessage(
      "Choose transaction band",
    ),
    "chooseCard": MessageLookupByLibrary.simpleMessage("Choose card"),
    "chooseCountry": MessageLookupByLibrary.simpleMessage("Choose country"),
    "chooseCrypto": MessageLookupByLibrary.simpleMessage("Choose crypto"),
    "chooseCurrency": MessageLookupByLibrary.simpleMessage("Choose currency"),
    "chooseDeliveryMethod": MessageLookupByLibrary.simpleMessage(
      "Choose delivery method",
    ),
    "chooseFile": MessageLookupByLibrary.simpleMessage("Choose File"),
    "chooseFromAVarietyOfPaymentMethodsAndProviders":
        MessageLookupByLibrary.simpleMessage(
          "Choose from a variety of payment \nmethods and providers",
        ),
    "chooseHowToImport": MessageLookupByLibrary.simpleMessage(
      "Choose how you’d like to import an \nexisting wallet ",
    ),
    "chooseIdType": MessageLookupByLibrary.simpleMessage("Choose ID type"),
    "chooseIdTypeSubtitle": MessageLookupByLibrary.simpleMessage(
      "You will be required to provide ID number",
    ),
    "chooseMethod": MessageLookupByLibrary.simpleMessage("Choose method"),
    "chooseNetwork": MessageLookupByLibrary.simpleMessage("Choose Network"),
    "choosePaymentMethod": MessageLookupByLibrary.simpleMessage(
      "Choose payment method",
    ),
    "chooseStatus": MessageLookupByLibrary.simpleMessage("Choose status"),
    "chooseStatusSubtitle": MessageLookupByLibrary.simpleMessage(
      "What\'s your current professional status?",
    ),
    "chooseVirtualCard": MessageLookupByLibrary.simpleMessage(
      "Choose virtual card",
    ),
    "chooseWalletToRecover": MessageLookupByLibrary.simpleMessage(
      "Choose wallet to recover",
    ),
    "chooseYourPreferredAsset": MessageLookupByLibrary.simpleMessage(
      "Choose your preferred asset ",
    ),
    "chooseYourPreferredCryptoBalance": MessageLookupByLibrary.simpleMessage(
      "Choose your preferred crypto balance",
    ),
    "chooseYourSource": MessageLookupByLibrary.simpleMessage(
      "Choose your source",
    ),
    "chooseYourTimeRange": MessageLookupByLibrary.simpleMessage(
      "Choose your time range",
    ),
    "circulatingSupply": MessageLookupByLibrary.simpleMessage(
      "Circulating Supply",
    ),
    "city": MessageLookupByLibrary.simpleMessage("City"),
    "cityIsRequired": MessageLookupByLibrary.simpleMessage("City is required"),
    "claimRequired": MessageLookupByLibrary.simpleMessage("Claim required"),
    "claimRequiredTransactionStatusMessage": m23,
    "clear": MessageLookupByLibrary.simpleMessage("Clear"),
    "close": MessageLookupByLibrary.simpleMessage("Close"),
    "cloud": MessageLookupByLibrary.simpleMessage("cloud"),
    "cloudBackUp": MessageLookupByLibrary.simpleMessage("Cloud backup"),
    "codeSent": MessageLookupByLibrary.simpleMessage("Code sent"),
    "codeSentSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Code sent successfully",
    ),
    "coinGecko": MessageLookupByLibrary.simpleMessage("coingecko"),
    "coinTransfer": MessageLookupByLibrary.simpleMessage("Coin Transfer"),
    "collection": MessageLookupByLibrary.simpleMessage("Collection"),
    "collectionNameOrder": MessageLookupByLibrary.simpleMessage(
      "Collection name (A - Z)",
    ),
    "comingSoon": MessageLookupByLibrary.simpleMessage("Coming soon"),
    "compass": MessageLookupByLibrary.simpleMessage("Compass"),
    "complete": MessageLookupByLibrary.simpleMessage("Complete"),
    "completeVerification": MessageLookupByLibrary.simpleMessage(
      "Complete verification",
    ),
    "completed": MessageLookupByLibrary.simpleMessage("Completed"),
    "completingWantToContinue": MessageLookupByLibrary.simpleMessage(
      "Completing this step helps us tailor the best experience for you. Want to continue?",
    ),
    "confidential": MessageLookupByLibrary.simpleMessage("Confidential"),
    "confirm": MessageLookupByLibrary.simpleMessage("Confirm"),
    "confirmAccountDelete": MessageLookupByLibrary.simpleMessage(
      "Confirm account deletion",
    ),
    "confirmBackup": MessageLookupByLibrary.simpleMessage("Confirm backup"),
    "confirmEmailUsedInOnboard": MessageLookupByLibrary.simpleMessage(
      "Confirm the email linked to your Onboard account and tap continue",
    ),
    "confirmItIsYou": MessageLookupByLibrary.simpleMessage(
      "Hey,\nWe\'d like to confirm that its you! Your signature verifies that you\'re the owner of this wallet:",
    ),
    "confirmPasscode": MessageLookupByLibrary.simpleMessage(
      "Confirm your 6 digit passcode",
    ),
    "confirmSlippage": MessageLookupByLibrary.simpleMessage("Confirm slippage"),
    "confirmSwap": MessageLookupByLibrary.simpleMessage("Confirm swap"),
    "confirmToSignMessage": MessageLookupByLibrary.simpleMessage(
      "Tap confirm to sign this message.",
    ),
    "confirmTransaction": MessageLookupByLibrary.simpleMessage(
      "Confirm Transaction",
    ),
    "confirmViaAccountPin": MessageLookupByLibrary.simpleMessage(
      "Confirm via account pin",
    ),
    "confirmViaAuthenticatorApp": MessageLookupByLibrary.simpleMessage(
      "Confirm via authenticator app",
    ),
    "confirmViaEmail": MessageLookupByLibrary.simpleMessage(
      "Confirm via email",
    ),
    "confirmViaPasskey": MessageLookupByLibrary.simpleMessage(
      "Confirm via passkey",
    ),
    "confirmViaSMS": MessageLookupByLibrary.simpleMessage("Confirm via SMS"),
    "confirmViaWallet": MessageLookupByLibrary.simpleMessage(
      "Confirm via wallet",
    ),
    "confirmWithAuthenticator": MessageLookupByLibrary.simpleMessage(
      "Confirm with Authenticator",
    ),
    "confirmWithEmail": MessageLookupByLibrary.simpleMessage(
      "Confirm with email",
    ),
    "confirmWithPasscode": MessageLookupByLibrary.simpleMessage(
      "Confirm with passcode",
    ),
    "confirmWithPasskey": MessageLookupByLibrary.simpleMessage(
      "Confirm with passkey",
    ),
    "confirmWithPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Confirm with phone number",
    ),
    "confirmWithWallet": MessageLookupByLibrary.simpleMessage(
      "Confirm with wallet",
    ),
    "confirmYourCurrentPasscode": MessageLookupByLibrary.simpleMessage(
      "Confirm your current 6 digit passcode",
    ),
    "confirmYourCurrentSixDigitPin": MessageLookupByLibrary.simpleMessage(
      "Confirm your current 6-digit pin",
    ),
    "confirmYourNewPasscode": MessageLookupByLibrary.simpleMessage(
      "Confirm your new 6 digit passcode",
    ),
    "confirmYourNewSixDigitPin": MessageLookupByLibrary.simpleMessage(
      "Confirm your new 6-digit pin",
    ),
    "confirmYourSixDigitPin": MessageLookupByLibrary.simpleMessage(
      "Confirm your 6-digit PIN",
    ),
    "confirmations": MessageLookupByLibrary.simpleMessage("confirmations"),
    "confirmed": MessageLookupByLibrary.simpleMessage("Confirmed"),
    "confirmingAccountBackUp": MessageLookupByLibrary.simpleMessage(
      "Confirming wallet backup",
    ),
    "confirmingDeposit": MessageLookupByLibrary.simpleMessage(
      "Confirming deposit",
    ),
    "confirmingSwap": MessageLookupByLibrary.simpleMessage("Confirming swap"),
    "confirmingTransactionSubCopy": MessageLookupByLibrary.simpleMessage(
      "We\'ll keep checking and let you know once received. You can leave this page anytime.",
    ),
    "confirmingWallet": MessageLookupByLibrary.simpleMessage(
      "Confirming your Onboard Wallet",
    ),
    "confirmingYourDepositDescription": MessageLookupByLibrary.simpleMessage(
      "We\'ll keep checking and let you know once received. You can leave this page anytime.",
    ),
    "confirmingYourTransaction": MessageLookupByLibrary.simpleMessage(
      "Confirming your transaction..",
    ),
    "confirmingYourVerificationStatus": MessageLookupByLibrary.simpleMessage(
      "Confirming your verification status..",
    ),
    "congratsHereIsYourVaAccount": m24,
    "connect": MessageLookupByLibrary.simpleMessage("Connect"),
    "connectAnotherAccount": MessageLookupByLibrary.simpleMessage(
      "Connect another account",
    ),
    "connectOtherApplication": MessageLookupByLibrary.simpleMessage(
      "Connect to other applications",
    ),
    "connectToOnlySiteYouTrust": MessageLookupByLibrary.simpleMessage(
      "Only connect to sites you trust",
    ),
    "connected": MessageLookupByLibrary.simpleMessage("Connected"),
    "connectedOn": MessageLookupByLibrary.simpleMessage("Connected on"),
    "connectedSites": MessageLookupByLibrary.simpleMessage("Connected sites"),
    "connectedWallet": MessageLookupByLibrary.simpleMessage("Connected Wallet"),
    "connectingMayTakeAFewSeconds": MessageLookupByLibrary.simpleMessage(
      "Connecting may take a few seconds",
    ),
    "connectingMayTakeLonger": MessageLookupByLibrary.simpleMessage(
      "Connecting may take a few seconds",
    ),
    "connectionDetails": MessageLookupByLibrary.simpleMessage(
      "Connection details",
    ),
    "connectionFailedRefreshDapp": MessageLookupByLibrary.simpleMessage(
      "Connection failed. Refresh the Dapp",
    ),
    "continueText": MessageLookupByLibrary.simpleMessage("Continue"),
    "continueToAds": MessageLookupByLibrary.simpleMessage("Continue to Ads"),
    "continueWith": m25,
    "contractAddress": MessageLookupByLibrary.simpleMessage("Contract Address"),
    "contractCall": MessageLookupByLibrary.simpleMessage("Contract Call"),
    "convert": MessageLookupByLibrary.simpleMessage("Convert"),
    "convertCryptoToCash": MessageLookupByLibrary.simpleMessage(
      "Convert crypto to cash",
    ),
    "convertCryptoToCashDesc": MessageLookupByLibrary.simpleMessage(
      "Near-instant withdrawals to bank",
    ),
    "convertToYour": MessageLookupByLibrary.simpleMessage("Convert to your "),
    "copied": MessageLookupByLibrary.simpleMessage("Copied"),
    "copy": MessageLookupByLibrary.simpleMessage("Copy"),
    "copyVirtualCardAddressCopy": MessageLookupByLibrary.simpleMessage(
      "Tap to copy your wallet address or scan the QR code.",
    ),
    "country": MessageLookupByLibrary.simpleMessage("Country"),
    "countryIsRequired": MessageLookupByLibrary.simpleMessage(
      "Country is required",
    ),
    "countryNotSupported": MessageLookupByLibrary.simpleMessage(
      "Your country is not supported",
    ),
    "countryOfResidence": MessageLookupByLibrary.simpleMessage(
      "Country of residence",
    ),
    "coutry": MessageLookupByLibrary.simpleMessage("Country"),
    "createANewCard": MessageLookupByLibrary.simpleMessage("Create a new card"),
    "createANewPasscode": MessageLookupByLibrary.simpleMessage(
      "Create a new 6 digit passcode ",
    ),
    "createAPassKey": MessageLookupByLibrary.simpleMessage("Create a passkey"),
    "createAPasskey": MessageLookupByLibrary.simpleMessage("Create a passkey"),
    "createAds": MessageLookupByLibrary.simpleMessage("Create Ads"),
    "createAdsIntro": MessageLookupByLibrary.simpleMessage(
      "Make profits with your merchant wallet by creating buy and sell ads.",
    ),
    "createMultipleCards": MessageLookupByLibrary.simpleMessage(
      "Create multiple cards",
    ),
    "createMultipleCardsSubCopy": MessageLookupByLibrary.simpleMessage(
      "You now have more options! Tap on the\nplus icon to add a new card",
    ),
    "createNewAd": MessageLookupByLibrary.simpleMessage("Create new ad"),
    "createNewSixDigitPin": MessageLookupByLibrary.simpleMessage(
      "Create a new 6-digit pin",
    ),
    "createPasskey": MessageLookupByLibrary.simpleMessage("Create passkey"),
    "createWallet": MessageLookupByLibrary.simpleMessage("Create Wallet"),
    "createYourFirstAd": MessageLookupByLibrary.simpleMessage(
      "Create your first ad",
    ),
    "createYourNewCard": MessageLookupByLibrary.simpleMessage(
      "Create your new card",
    ),
    "createYourPin": MessageLookupByLibrary.simpleMessage("Create your PIN"),
    "created": MessageLookupByLibrary.simpleMessage("Created"),
    "creatingANewCardWillReplaceMyDeactivatedCard":
        MessageLookupByLibrary.simpleMessage(
          "Creating a new card will replace my deactivated card",
        ),
    "creatingTradingWalletLoadingMessage": MessageLookupByLibrary.simpleMessage(
      "We’re creating a merchant wallet for you. It’ll only take a few moments",
    ),
    "creatingYourAccount": MessageLookupByLibrary.simpleMessage(
      "Creating your account",
    ),
    "creatingYourOnboardWallet": MessageLookupByLibrary.simpleMessage(
      "Creating your Onboard Wallet",
    ),
    "creatingYourPrivateKey": MessageLookupByLibrary.simpleMessage(
      "Creating your private key  🔑",
    ),
    "creditCardEmoji": MessageLookupByLibrary.simpleMessage("💳"),
    "crypto": MessageLookupByLibrary.simpleMessage("Crypto"),
    "cryptoAddress": MessageLookupByLibrary.simpleMessage("Crypto address"),
    "cryptoBalance": MessageLookupByLibrary.simpleMessage("Crypto Balance"),
    "cryptoIsVisible": MessageLookupByLibrary.simpleMessage(
      "Crypto is visible",
    ),
    "cryptoMaxi": MessageLookupByLibrary.simpleMessage("Crypto Maxi ✨"),
    "cryptoToCashFast": MessageLookupByLibrary.simpleMessage(
      "Crypto to Cash, fast",
    ),
    "cryptoToCashInMinutes": MessageLookupByLibrary.simpleMessage(
      "Crypto to cash in minutes",
    ),
    "currency": MessageLookupByLibrary.simpleMessage("Currency"),
    "currencyBalance": m26,
    "currencyBankTransfer": m27,
    "currencyConversionFees": MessageLookupByLibrary.simpleMessage(
      "Currency conversion fees",
    ),
    "currencyConversionFeesExplainer": m28,
    "currentBalance": m29,
    "customTokens": MessageLookupByLibrary.simpleMessage("Custom Tokens"),
    "customTokensSubCopy": MessageLookupByLibrary.simpleMessage(
      "When a token does not appear on your wallet, it can be manually added",
    ),
    "customer": MessageLookupByLibrary.simpleMessage("Customer"),
    "customerSubCopy": MessageLookupByLibrary.simpleMessage(
      "I want to store and manage my digital assets",
    ),
    "cvc": MessageLookupByLibrary.simpleMessage("CVC"),
    "dailyLimit": MessageLookupByLibrary.simpleMessage("Daily limit"),
    "dappRedirectToBrowser": MessageLookupByLibrary.simpleMessage(
      "Do you want to be redirected to this\nexternal app?",
    ),
    "dataNotProvided": MessageLookupByLibrary.simpleMessage(
      "Data not provided",
    ),
    "date": MessageLookupByLibrary.simpleMessage("Date"),
    "dateOfBirthSubtitle": MessageLookupByLibrary.simpleMessage(
      "Enter your date of birth as it is on your official ID",
    ),
    "days": MessageLookupByLibrary.simpleMessage("days"),
    "debitCard": MessageLookupByLibrary.simpleMessage("Debit Card"),
    "defiWallet": MessageLookupByLibrary.simpleMessage("DeFi Wallet"),
    "delete": MessageLookupByLibrary.simpleMessage("Delete"),
    "deleteAccount": MessageLookupByLibrary.simpleMessage("Delete account"),
    "deleteAccountWarning": MessageLookupByLibrary.simpleMessage(
      "Please read the following requirements before deleting your Onboard account",
    ),
    "deleteAddress": MessageLookupByLibrary.simpleMessage("Delete address?"),
    "deleteAddressSubCopy": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this address? This can\'t be reversed",
    ),
    "deleteAllTab": MessageLookupByLibrary.simpleMessage("Delete all tabs?"),
    "deleteAllTabWarning": MessageLookupByLibrary.simpleMessage(
      "This will clear all your currently active tabs",
    ),
    "deleteCard": MessageLookupByLibrary.simpleMessage("Delete card"),
    "deleteCardSubCopy": MessageLookupByLibrary.simpleMessage(
      "This action is final. There may be a wait period before you can create a new card.",
    ),
    "deleteCardWarning": MessageLookupByLibrary.simpleMessage(
      "Deleting your card is final. There may be a  wait period before you can create a new one. Any unused balance will be sent to your Onboard Wallet.",
    ),
    "deletePasskey": MessageLookupByLibrary.simpleMessage(
      "Delete your passkey?",
    ),
    "deletePasskeyConfirmation": MessageLookupByLibrary.simpleMessage(
      "By deleting your passkey, you\'ll lose access to secure and easy authentication of your wallet",
    ),
    "deletePasskeyWarning": MessageLookupByLibrary.simpleMessage(
      "You will no longer be able to use this passkey to sign in. You can create a new passkey for this device at any time.",
    ),
    "deletingYourOnboardWallet": MessageLookupByLibrary.simpleMessage(
      "Deleting your Onboard Wallet",
    ),
    "deliveryMethod": MessageLookupByLibrary.simpleMessage("Delivery method"),
    "deliveryMethodNotAvailable": MessageLookupByLibrary.simpleMessage(
      "Delivery method not available",
    ),
    "deliveryMethodNotAvailableSubCopy": MessageLookupByLibrary.simpleMessage(
      "Unfortunately, the selected provider cannot process your desired transaction. Please choose a new one to proceed.",
    ),
    "deliveryMethodsNotCurrentlySupportedForThisAsset":
        MessageLookupByLibrary.simpleMessage(
          "Delivery methods not currently available for this asset",
        ),
    "deny": MessageLookupByLibrary.simpleMessage("Deny"),
    "deposit": MessageLookupByLibrary.simpleMessage("Deposit"),
    "depositCancelled": MessageLookupByLibrary.simpleMessage(
      "Deposit cancelled",
    ),
    "depositConfirmed": MessageLookupByLibrary.simpleMessage(
      "deposit confirmed",
    ),
    "depositConfirmedDesc": MessageLookupByLibrary.simpleMessage(
      "Get ready to explore a world of financial opportunities tailored just for you.",
    ),
    "depositCrypto": MessageLookupByLibrary.simpleMessage("Deposit Crypto"),
    "depositFunds": MessageLookupByLibrary.simpleMessage("Deposit funds"),
    "depositFundsFromExternalWallet": MessageLookupByLibrary.simpleMessage(
      "Deposit funds from an external wallet",
    ),
    "depositGasAndGetStarted": m30,
    "depositInProgress": MessageLookupByLibrary.simpleMessage(
      "Deposit in progress",
    ),
    "depositToStartTrading": MessageLookupByLibrary.simpleMessage(
      "Deposit to start trading",
    ),
    "depositTokenName": m31,
    "depositUSDT": MessageLookupByLibrary.simpleMessage("Deposit USDT"),
    "deposited": MessageLookupByLibrary.simpleMessage("Deposited"),
    "depositsBelowThisWillLeadToDelays": MessageLookupByLibrary.simpleMessage(
      "Deposits below this will lead to delays",
    ),
    "depositsReceivedWithin": MessageLookupByLibrary.simpleMessage(
      "Deposits received within",
    ),
    "depositsUnavailablePleaseTryAgainSoon": m32,
    "describeDocument": MessageLookupByLibrary.simpleMessage(
      "Describe document",
    ),
    "describleYourPurpose": MessageLookupByLibrary.simpleMessage(
      "Describe your purpose",
    ),
    "description": MessageLookupByLibrary.simpleMessage("Description"),
    "destination": MessageLookupByLibrary.simpleMessage("Destination"),
    "destinationWallet": MessageLookupByLibrary.simpleMessage(
      "Destination Wallet",
    ),
    "destinationWalletInfo": MessageLookupByLibrary.simpleMessage(
      "This is the wallet where your assets will  automatically be sent to once we have received them",
    ),
    "details": MessageLookupByLibrary.simpleMessage("Details"),
    "didYouKnow": MessageLookupByLibrary.simpleMessage("Did you know?"),
    "didntReceiveCode": MessageLookupByLibrary.simpleMessage(
      "Didn’t receive code?",
    ),
    "directAccount": MessageLookupByLibrary.simpleMessage("Direct account"),
    "directGivesYouDedicatedWalletAddressLinkedToAccount":
        MessageLookupByLibrary.simpleMessage(
          "Direct gives you dedicated \nwallet addresses linked to your bank account",
        ),
    "disclaimerColon": MessageLookupByLibrary.simpleMessage("Disclaimer:"),
    "disconnect": MessageLookupByLibrary.simpleMessage("Disconnect"),
    "disconnectSite": MessageLookupByLibrary.simpleMessage("Disconnect site"),
    "disconnectSiteWarning": MessageLookupByLibrary.simpleMessage(
      "Disconnecting will end your session with this site. You\'ll need to setup a new connection if you want to reconnect later",
    ),
    "disconnected": MessageLookupByLibrary.simpleMessage("Disconnected"),
    "discover": MessageLookupByLibrary.simpleMessage("Discover"),
    "discoverAppsWeSpotlight": MessageLookupByLibrary.simpleMessage(
      "Discover apps we spotlight. Curated by experts. Handpicked for you.",
    ),
    "discoverAppsYouNeed": MessageLookupByLibrary.simpleMessage(
      "Discover apps you need",
    ),
    "discoverCrypto": MessageLookupByLibrary.simpleMessage("Discover crypto"),
    "discoverCryptoGem": MessageLookupByLibrary.simpleMessage(
      "Discover crypto gems",
    ),
    "discoverMembershipBenefits": MessageLookupByLibrary.simpleMessage(
      "Discover membership benefits",
    ),
    "discoverMoreApps": MessageLookupByLibrary.simpleMessage(
      "Discover more apps",
    ),
    "doMoneyBetter": MessageLookupByLibrary.simpleMessage("DO MONEY\nBETTER"),
    "doNotMissOut": MessageLookupByLibrary.simpleMessage("Don’t miss out! 💰"),
    "doThisLater": MessageLookupByLibrary.simpleMessage("Do this later"),
    "dob": MessageLookupByLibrary.simpleMessage("Date of birth"),
    "dobSubCopy": MessageLookupByLibrary.simpleMessage(
      "Enter your date of birth as it is on your official ID",
    ),
    "documentType": MessageLookupByLibrary.simpleMessage("Document type"),
    "done": MessageLookupByLibrary.simpleMessage("Done"),
    "dontShowThisAgain": MessageLookupByLibrary.simpleMessage(
      "Don\'t show this again",
    ),
    "dontWantKyc": MessageLookupByLibrary.simpleMessage("Don\'t want KYC?"),
    "dontWorryYouCanStillTryOutOtherOptionsLater":
        MessageLookupByLibrary.simpleMessage(
          "Don\'t worry, you can still try out the other options later",
        ),
    "dropbox": MessageLookupByLibrary.simpleMessage("Dropbox"),
    "earn": MessageLookupByLibrary.simpleMessage("Earn"),
    "earnForEveryInvitee": MessageLookupByLibrary.simpleMessage(
      "Earn \$\$ for every invitee ",
    ),
    "earnRewards": MessageLookupByLibrary.simpleMessage("Earn rewards"),
    "easyAndFastWithdrawal247": MessageLookupByLibrary.simpleMessage(
      "Easy & fast withdrawals, 24/7",
    ),
    "easyGlobalPayments": MessageLookupByLibrary.simpleMessage(
      "Easy global payments",
    ),
    "easyOnchainAccess": MessageLookupByLibrary.simpleMessage(
      "Easy onchain access",
    ),
    "edit": MessageLookupByLibrary.simpleMessage("Edit"),
    "editFrequency": MessageLookupByLibrary.simpleMessage("Edit frequency"),
    "editManually": MessageLookupByLibrary.simpleMessage("Edit manually"),
    "editsHereWillOnlyAlterWhatYouSeeOnTheOnboardApp":
        MessageLookupByLibrary.simpleMessage(
          "Edits here will only alter what you see on the \nOnboard app and not your subscription with the site",
        ),
    "editsHereWillOnlyAlterWhatYouSeeOnTheOnboardAppAndNotYourSubscriptionWithTheMerchant":
        MessageLookupByLibrary.simpleMessage(
          "Edits here will only alter what you see on the Onboard app and not your subscription with the merchant",
        ),
    "egMyFirstWallet": MessageLookupByLibrary.simpleMessage(
      "Eg. My first wallet",
    ),
    "email": MessageLookupByLibrary.simpleMessage("Email"),
    "emailMeACode": MessageLookupByLibrary.simpleMessage("Email me a code"),
    "emailVerification": MessageLookupByLibrary.simpleMessage(
      "Email verification...",
    ),
    "employmentStatus": MessageLookupByLibrary.simpleMessage(
      "Employment status",
    ),
    "emptyNftDescription": MessageLookupByLibrary.simpleMessage(
      "You don’t own any NFTs yet",
    ),
    "emptyNotifications": MessageLookupByLibrary.simpleMessage(
      "No notifications yet",
    ),
    "emptyPasskeyDescription": MessageLookupByLibrary.simpleMessage(
      "Use your fingerprint and face ID to speed up login. With passkeys, you can more securely complete transaction on Onboard.",
    ),
    "emptyTabMessage": MessageLookupByLibrary.simpleMessage("No open tabs yet"),
    "emptyWalletConnectState": MessageLookupByLibrary.simpleMessage(
      "Your wallet connections with other sites will appear here",
    ),
    "enable2fa": MessageLookupByLibrary.simpleMessage("Enable 2FA"),
    "enable2faSubCopy": MessageLookupByLibrary.simpleMessage(
      "To securely regain access to your account when you logout or lose device",
    ),
    "enableBiometricForLogin": MessageLookupByLibrary.simpleMessage(
      "Enable biometrics for login",
    ),
    "enablePasscodeOrBiometrics": MessageLookupByLibrary.simpleMessage(
      "Enable passcode or biometrics",
    ),
    "encourageBackup": MessageLookupByLibrary.simpleMessage(
      "Enable biometric to safeguard your fund!",
    ),
    "encrypted": MessageLookupByLibrary.simpleMessage("encrypted"),
    "endToEndEncrypted": MessageLookupByLibrary.simpleMessage(
      "End to End Encrypted",
    ),
    "ensureAddressIsCorrect": MessageLookupByLibrary.simpleMessage(
      "Ensure that the address is correct and on the same network. Transactions cannot be reversed",
    ),
    "ensureNetworkMatches": MessageLookupByLibrary.simpleMessage(
      "Ensure the network matches the recipient address or your assets may be lost",
    ),
    "enterAnAmount": MessageLookupByLibrary.simpleMessage("Enter an amount"),
    "enterEmail": MessageLookupByLibrary.simpleMessage("Enter Email"),
    "enterFullName": MessageLookupByLibrary.simpleMessage(
      "Enter your full name just as it is on your",
    ),
    "enterOnboardEmail": MessageLookupByLibrary.simpleMessage(
      "Enter onboard email",
    ),
    "enterOrPasteMemo": MessageLookupByLibrary.simpleMessage(
      "Enter or Paste Memo",
    ),
    "enterTheCodeSentTo": MessageLookupByLibrary.simpleMessage(
      "Enter the code sent to",
    ),
    "enterTheSixDigitCodeGenerated": MessageLookupByLibrary.simpleMessage(
      "Enter the 6-digit code generated",
    ),
    "enterTheSixDigitCodeSentTo": MessageLookupByLibrary.simpleMessage(
      "Enter the 6-digit code sent to",
    ),
    "enterYourIdNumber": m33,
    "enterYourPasscode": MessageLookupByLibrary.simpleMessage(
      "Enter your passcode",
    ),
    "enterYourPrivateKey": MessageLookupByLibrary.simpleMessage(
      "Enter your private key",
    ),
    "enterYourRecoveryPhrase": MessageLookupByLibrary.simpleMessage(
      "Enter your recovery phrase",
    ),
    "enterYourSixDigitPin": MessageLookupByLibrary.simpleMessage(
      "Enter your 6-digit PIN",
    ),
    "errorOccurredTryAgain": MessageLookupByLibrary.simpleMessage(
      "error occurred, try again",
    ),
    "errorSwitchingChain": MessageLookupByLibrary.simpleMessage(
      "Error Switching Chain",
    ),
    "estimatedRate": MessageLookupByLibrary.simpleMessage("Est. Rate"),
    "euro": MessageLookupByLibrary.simpleMessage("Euro"),
    "everySixMonths": MessageLookupByLibrary.simpleMessage("Every 6 months"),
    "everyTwoWeeks": MessageLookupByLibrary.simpleMessage("Every 2 weeks"),
    "exampleOfOtherChannel": MessageLookupByLibrary.simpleMessage(
      "Eg In a community call",
    ),
    "exchange": MessageLookupByLibrary.simpleMessage("Exchange"),
    "exchangeExamples": MessageLookupByLibrary.simpleMessage(
      "e.g. Binance, Coinbase, CEX.io",
    ),
    "exchangeFast": MessageLookupByLibrary.simpleMessage("EXCHANGE\nFAST"),
    "exchangeMerchantSubtitle": MessageLookupByLibrary.simpleMessage(
      "Create ads, manage existing orders from customers",
    ),
    "exchangeWithOtherCryptoPairs": MessageLookupByLibrary.simpleMessage(
      "Exchange with other crypto pairs",
    ),
    "existingAccountSubCopy": MessageLookupByLibrary.simpleMessage(
      "This email address is already linked to an existing account on Onboard. To proceed, please import the seed phrase of the wallet in use on that account",
    ),
    "exitOnboardingNote": MessageLookupByLibrary.simpleMessage(
      "You’ll need to fund your wallet to access savings and trading on Onboard.",
    ),
    "exitingExchangeWarningSubCopy": MessageLookupByLibrary.simpleMessage(
      "This will exit the Exchange. You\'ll be taken back to your Wallet, but any ongoing transactions will continue as normal.",
    ),
    "expiryDate": MessageLookupByLibrary.simpleMessage("Expiry date"),
    "exploreDefiAppsAndConnectUpTo10PlusNetworksWithEase":
        MessageLookupByLibrary.simpleMessage(
          "Explore DeFi apps & connect to up to 10+ networks with ease",
        ),
    "exploreHub": MessageLookupByLibrary.simpleMessage("Explore hub"),
    "exploreNftShop": MessageLookupByLibrary.simpleMessage("Explore NFT shops"),
    "explorer": MessageLookupByLibrary.simpleMessage("explorer"),
    "express": MessageLookupByLibrary.simpleMessage("Express"),
    "externalAccounts": MessageLookupByLibrary.simpleMessage(
      "External accounts",
    ),
    "externalCryptoWallet": MessageLookupByLibrary.simpleMessage(
      "External crypto wallet",
    ),
    "externalDepositInfoOne": m34,
    "externalDepositInfoTwo": m35,
    "externalWallet": MessageLookupByLibrary.simpleMessage("External wallet"),
    "externalWalletTransferDesc": MessageLookupByLibrary.simpleMessage(
      "Transfer crypto to other wallets",
    ),
    "extraBackupOptions": MessageLookupByLibrary.simpleMessage(
      "Extra backup options",
    ),
    "facebook": MessageLookupByLibrary.simpleMessage("facebook"),
    "failToLoadData": MessageLookupByLibrary.simpleMessage(
      "Failed to load data. Swipe down to refresh!",
    ),
    "failToLoadDataPleaseRefresh": MessageLookupByLibrary.simpleMessage(
      "Failed to load data. Please refresh",
    ),
    "failed": MessageLookupByLibrary.simpleMessage("Failed"),
    "failedToGetTransaction": MessageLookupByLibrary.simpleMessage(
      "Failed to fetch transaction",
    ),
    "failedToSignMessage": MessageLookupByLibrary.simpleMessage(
      "Failed to sign message",
    ),
    "failedToSignTransaction": MessageLookupByLibrary.simpleMessage(
      "Failed to Sign Transaction",
    ),
    "faqs": MessageLookupByLibrary.simpleMessage("FAQs"),
    "fastAffordableFunding": MessageLookupByLibrary.simpleMessage(
      "Fast, affordable funding",
    ),
    "fastOnAndOffRamps": MessageLookupByLibrary.simpleMessage(
      "Fast on & off ramps",
    ),
    "fastest": MessageLookupByLibrary.simpleMessage("Fastest"),
    "featureNotAvailable": MessageLookupByLibrary.simpleMessage(
      "Feature not available",
    ),
    "featured": MessageLookupByLibrary.simpleMessage("Featured"),
    "featuredApps": MessageLookupByLibrary.simpleMessage("Featured apps"),
    "featuresAccessDependsOnLocation": MessageLookupByLibrary.simpleMessage(
      "The features you have access to depend on your location. You may change this, but be sure to only select a country where you can provide documents that prove you\'re legally authorized to be there.",
    ),
    "featuresAvailable": MessageLookupByLibrary.simpleMessage(
      "Features available",
    ),
    "featuresForYou": MessageLookupByLibrary.simpleMessage("Features for you"),
    "fee": MessageLookupByLibrary.simpleMessage("Fee"),
    "fees": MessageLookupByLibrary.simpleMessage("Fees"),
    "feesInfoCopy": MessageLookupByLibrary.simpleMessage(
      " Our payment partners may charge a small fee to process this transaction\nThe receiving amount is only an estimate.",
    ),
    "feesMayApply": MessageLookupByLibrary.simpleMessage("Fees may apply"),
    "fetchingRates": MessageLookupByLibrary.simpleMessage("Fetching rates..."),
    "fiatTransferOptionSubtitle": MessageLookupByLibrary.simpleMessage(
      "Add dollars with your details",
    ),
    "fillTheForm": MessageLookupByLibrary.simpleMessage("Fill the form below"),
    "fillTheFormSubtitle": MessageLookupByLibrary.simpleMessage(
      "Let us know how you plan on using your USD account",
    ),
    "filter": MessageLookupByLibrary.simpleMessage("Filter"),
    "filters": MessageLookupByLibrary.simpleMessage("Filters"),
    "findActiveSubscriptions": MessageLookupByLibrary.simpleMessage(
      "Find active subscriptions",
    ),
    "findOffers": MessageLookupByLibrary.simpleMessage("Find Offers"),
    "finish": MessageLookupByLibrary.simpleMessage("Finish"),
    "finishAccountBackUpSubCopy1": MessageLookupByLibrary.simpleMessage(
      "Back up your wallet recovery details to help you regain access when you log out or switch devices",
    ),
    "finishAccountBackUpSubCopy2": MessageLookupByLibrary.simpleMessage(
      "No one else can access this wallet, \nnot even us at Onboard",
    ),
    "finishBackUp": MessageLookupByLibrary.simpleMessage("Finish backup"),
    "finishSetup": MessageLookupByLibrary.simpleMessage("Finish setup"),
    "finishSetupSubCopy": MessageLookupByLibrary.simpleMessage(
      "Let\'s finish these tasks to get Onboard!",
    ),
    "finishWalletBackUp": MessageLookupByLibrary.simpleMessage(
      "Finish wallet backup",
    ),
    "finishWalletBackUpSubCopy": MessageLookupByLibrary.simpleMessage(
      "Backup your wallet to the cloud to regain access if you log out or switch devices",
    ),
    "firstName": MessageLookupByLibrary.simpleMessage("First name"),
    "firstPartyDeposits": MessageLookupByLibrary.simpleMessage(
      "1st party deposits",
    ),
    "fixHereToAvoidCardTermination": MessageLookupByLibrary.simpleMessage(
      "Fix here to avoid card termination",
    ),
    "flameEmoji": MessageLookupByLibrary.simpleMessage("🔥"),
    "flexibleFundingAndWithdrawal": MessageLookupByLibrary.simpleMessage(
      "Flexible funding & withdrawal",
    ),
    "forFasterLogins": MessageLookupByLibrary.simpleMessage(
      "For faster logins ⚡",
    ),
    "forMoreInformationRegardingUseOfThisAccount":
        MessageLookupByLibrary.simpleMessage(
          " For more information \nregarding your use of this account, view",
        ),
    "forMyself": MessageLookupByLibrary.simpleMessage("For myself"),
    "forSomeoneElse": MessageLookupByLibrary.simpleMessage("For someone else"),
    "forgettingPaymentsAreNowAThingOfThePast":
        MessageLookupByLibrary.simpleMessage(
          "Forgetting payments are now a thing of the past! 🤩",
        ),
    "forgotPasscode": MessageLookupByLibrary.simpleMessage("Forgot Passcode"),
    "forgotPin": MessageLookupByLibrary.simpleMessage("Forgot Passcode?"),
    "formattedCardRate": m36,
    "free": MessageLookupByLibrary.simpleMessage("Free"),
    "freeze": MessageLookupByLibrary.simpleMessage("Freeze"),
    "frequency": MessageLookupByLibrary.simpleMessage("Frequency"),
    "frequencyUpdated": MessageLookupByLibrary.simpleMessage(
      "Frequency updated",
    ),
    "from": MessageLookupByLibrary.simpleMessage("From"),
    "fromAnyCashAccount": MessageLookupByLibrary.simpleMessage(
      "From any cash account",
    ),
    "fromAnyExternalCryptoWallet": MessageLookupByLibrary.simpleMessage(
      "From any external crypto wallet",
    ),
    "fund": MessageLookupByLibrary.simpleMessage("Fund"),
    "fundAccount": MessageLookupByLibrary.simpleMessage("Fund account"),
    "fundCard": MessageLookupByLibrary.simpleMessage("Fund card"),
    "fundCardSelectAssetSubCopy": MessageLookupByLibrary.simpleMessage(
      "Fund your card with your wallet balance",
    ),
    "fundDaSelectAssetSubCopy": MessageLookupByLibrary.simpleMessage(
      "Fund your account with any of the supported \nstablecoins",
    ),
    "fundSource": MessageLookupByLibrary.simpleMessage("Source of funds"),
    "fundUsingYourLocalCurrency": MessageLookupByLibrary.simpleMessage(
      "Fund using your local currency",
    ),
    "fundViaExternalWalletWarning": MessageLookupByLibrary.simpleMessage(
      "to the address below.\nSending lower or from a different network may lead to loss of funds.",
    ),
    "fundVirtualAccountWithOnboardWallet": m37,
    "fundWallet": MessageLookupByLibrary.simpleMessage("Fund Wallet"),
    "fundWalletWithYourLocalCurrency": MessageLookupByLibrary.simpleMessage(
      "Fund wallet with your local currency",
    ),
    "fundYourCard": MessageLookupByLibrary.simpleMessage("Fund your card "),
    "fundYourUsdAccountWithOrWithdrawToOtherCurrencies":
        MessageLookupByLibrary.simpleMessage(
          "Fund your USD account with, or withdraw to, other currencies",
        ),
    "funding": MessageLookupByLibrary.simpleMessage("Funding"),
    "fundingAmount": MessageLookupByLibrary.simpleMessage("Funding amount"),
    "fundingAmountTooHigh": MessageLookupByLibrary.simpleMessage(
      "Funding amount too high.",
    ),
    "fundingAmountTooLow": MessageLookupByLibrary.simpleMessage(
      "Funding amount too low",
    ),
    "fundingCardWithXAmount": m38,
    "fundingInProgress": MessageLookupByLibrary.simpleMessage(
      "Funding in progress",
    ),
    "fundingMethodIsCurrentlyNotAvailableForTheSelectedCountry":
        MessageLookupByLibrary.simpleMessage(
          "Funding method is currently not available for the selected country",
        ),
    "fundingOptionsTitle": MessageLookupByLibrary.simpleMessage(
      "How would you like to fund?",
    ),
    "fundingRequired": MessageLookupByLibrary.simpleMessage("Funding required"),
    "fundingUnavailable": MessageLookupByLibrary.simpleMessage(
      "Funding unavailable",
    ),
    "fundingUnavailableDescription": MessageLookupByLibrary.simpleMessage(
      "Unfortunately, the selected asset cannot process your desired transaction. Please choose another one to proceed.",
    ),
    "fundingVirtualWithXAmount": m39,
    "fundingYourCard": MessageLookupByLibrary.simpleMessage(
      "Funding your card ",
    ),
    "fundwallet": MessageLookupByLibrary.simpleMessage("Fund wallet"),
    "fxFeeMayApply": MessageLookupByLibrary.simpleMessage("FX fees may apply"),
    "fxFeesMayApplySubCopy": MessageLookupByLibrary.simpleMessage(
      "When transactions are in a different currency from what your card is denominated in, an FX conversion fee may be charged separately.",
    ),
    "fxTransactionsSubtitle": m40,
    "gainers": MessageLookupByLibrary.simpleMessage("Gainers"),
    "gainersWithEmoji": MessageLookupByLibrary.simpleMessage("📈 Gainers"),
    "general": MessageLookupByLibrary.simpleMessage("General"),
    "generateATransactionStatement": MessageLookupByLibrary.simpleMessage(
      "Generate a transaction statement",
    ),
    "genericTransactionProviderSubtitle": m41,
    "getADedicatedUsBankAccount": MessageLookupByLibrary.simpleMessage(
      "Get a dedicated USD bank account",
    ),
    "getATradingPortfolio": MessageLookupByLibrary.simpleMessage(
      "Get a trading portfolio",
    ),
    "getAUsAccount": MessageLookupByLibrary.simpleMessage("Get a US account"),
    "getAUsBankAccount": MessageLookupByLibrary.simpleMessage(
      "Get a USD bank account",
    ),
    "getAUsdCard": m42,
    "getAnotherCard": MessageLookupByLibrary.simpleMessage("Get another card"),
    "getCardStatement": MessageLookupByLibrary.simpleMessage(
      "Get card statement",
    ),
    "getCryptoPortfolio": MessageLookupByLibrary.simpleMessage(
      "Get a crypto portfolio",
    ),
    "getMultipleCardThatJustWorksGlobally":
        MessageLookupByLibrary.simpleMessage(
          "Get a debit card that just work, globally",
        ),
    "getPaidByAnyone": MessageLookupByLibrary.simpleMessage(
      "Get paid by anyone",
    ),
    "getStarted": MessageLookupByLibrary.simpleMessage("Get started"),
    "getStartedOnTradableCoin": MessageLookupByLibrary.simpleMessage(
      "Get started on top tradable coins",
    ),
    "getStartedToTransact": MessageLookupByLibrary.simpleMessage(
      "SignIn/Signup below to transact",
    ),
    "getStartedToViewMarketData": MessageLookupByLibrary.simpleMessage(
      "SignIn/Signup to view market Data",
    ),
    "getTestFlightVersion": MessageLookupByLibrary.simpleMessage(
      "Get the TestFlight version",
    ),
    "getTheBestPersonalFinanceTips": MessageLookupByLibrary.simpleMessage(
      "Get the best personal finance tips",
    ),
    "getTheCode": MessageLookupByLibrary.simpleMessage("Get the code"),
    "getYourFriendsToSignUpAndEarn": MessageLookupByLibrary.simpleMessage(
      "Get your friends to sign up and earn",
    ),
    "gettingYourAccountNumberTakes": MessageLookupByLibrary.simpleMessage(
      "Getting your account number takes",
    ),
    "giftEmoji": MessageLookupByLibrary.simpleMessage("🎁"),
    "github": MessageLookupByLibrary.simpleMessage("github"),
    "giveAccessToTransact": MessageLookupByLibrary.simpleMessage(
      "You\'re giving access to transact with this token",
    ),
    "globalPaymentsMadeEasy": MessageLookupByLibrary.simpleMessage(
      "Global payments, made easy",
    ),
    "goBack": MessageLookupByLibrary.simpleMessage("Go back"),
    "goFromAnyCryptoTokenToCash": MessageLookupByLibrary.simpleMessage(
      "Go from any crypto token to \ncash",
    ),
    "goFromCryptoToCashAndBackWithSeamlessPaymentOptions":
        MessageLookupByLibrary.simpleMessage(
          "Go from crypto to cash and back with seamless payment options",
        ),
    "goToMySubscriptions": MessageLookupByLibrary.simpleMessage(
      "Go to my subscriptions",
    ),
    "googleAuthenticator": MessageLookupByLibrary.simpleMessage(
      "Google authenticator",
    ),
    "googleDrive": MessageLookupByLibrary.simpleMessage("Google Drive"),
    "gotGasElsewhere": m43,
    "gotIt": MessageLookupByLibrary.simpleMessage("Got it"),
    "gotoDashboard": MessageLookupByLibrary.simpleMessage("Go to dashboard"),
    "greatChoiceNoComma": MessageLookupByLibrary.simpleMessage("Great choice"),
    "greatChoiceWithComma": MessageLookupByLibrary.simpleMessage(
      "Great choice,",
    ),
    "growYourMoneyWhenYouSaveInADigitalDollarLikeUSDC": m44,
    "growYourWealthWithDigitalAssets": MessageLookupByLibrary.simpleMessage(
      "Grow your wealth with digital assets",
    ),
    "guidelines": MessageLookupByLibrary.simpleMessage("Guidelines"),
    "hash": MessageLookupByLibrary.simpleMessage("Hash"),
    "haveOtherWallets": MessageLookupByLibrary.simpleMessage(
      "Have other wallets?",
    ),
    "haveTransferredFundFromTradingWallet": MessageLookupByLibrary.simpleMessage(
      "I have transferred funds from my trading wallet to my spot wallet to avoid losing them",
    ),
    "havingIssuesReachOut": MessageLookupByLibrary.simpleMessage(
      "Having issues? Reach out to us via our",
    ),
    "headsUp": MessageLookupByLibrary.simpleMessage("Heads up!"),
    "headsUpYoureBeingRedirectedToAnExternalApplication":
        MessageLookupByLibrary.simpleMessage(
          "Heads up. You’re being redirected to an external application",
        ),
    "helpCenter": MessageLookupByLibrary.simpleMessage("Help center"),
    "helpUsBuildYourIdealApp": MessageLookupByLibrary.simpleMessage(
      "Help us build your ideal app",
    ),
    "helpfulTipsWithColon": MessageLookupByLibrary.simpleMessage(
      "Helpful Tips:",
    ),
    "hereIsWhatYouNeed": MessageLookupByLibrary.simpleMessage(
      "Here’s what you need",
    ),
    "hereIsYourWallet": MessageLookupByLibrary.simpleMessage(
      "Here’s your wallet!",
    ),
    "hereRecommendedPortfolio": MessageLookupByLibrary.simpleMessage(
      "Here’s your recommended portfolio",
    ),
    "heyWhatsYourEmail": MessageLookupByLibrary.simpleMessage(
      "Hey, what’s your email?",
    ),
    "hiddenCrypto": MessageLookupByLibrary.simpleMessage("Hidden crypto"),
    "hiddenNFTs": MessageLookupByLibrary.simpleMessage("Hidden NFTs"),
    "hiddenNFTsCount": m45,
    "hide": MessageLookupByLibrary.simpleMessage("Hide"),
    "hideBalance": MessageLookupByLibrary.simpleMessage("Hide \$0 balances"),
    "hideLowReputationTokens": MessageLookupByLibrary.simpleMessage(
      "Hide low reputation crypto",
    ),
    "hideNFT": MessageLookupByLibrary.simpleMessage("Hide NFT"),
    "hideNFTCollection": MessageLookupByLibrary.simpleMessage(
      "Hide NFT collection",
    ),
    "history": MessageLookupByLibrary.simpleMessage("History"),
    "home": MessageLookupByLibrary.simpleMessage("Home"),
    "homeAddress": MessageLookupByLibrary.simpleMessage("Home address"),
    "homeAddressSubCopy": MessageLookupByLibrary.simpleMessage(
      "We only use this to confirm your personal info",
    ),
    "hot": MessageLookupByLibrary.simpleMessage("🔥 Hot"),
    "hours": m46,
    "howItWorks": MessageLookupByLibrary.simpleMessage("How it works"),
    "howMuchExperienceCrypto": MessageLookupByLibrary.simpleMessage(
      "How much experience do you have in crypto?",
    ),
    "howOftenWillThisSubscriptionBeBilled":
        MessageLookupByLibrary.simpleMessage(
          "How often will this subscription be billed?",
        ),
    "howToConnectMessage": MessageLookupByLibrary.simpleMessage(
      "Look for the \'WalletConnect\' icon on any compatible service. Scan or copy the QR code and you\'re connected!",
    ),
    "howToUseWalletConnect": MessageLookupByLibrary.simpleMessage(
      "How to use wallet connect",
    ),
    "howWouldYouLikeToAddCrypto": MessageLookupByLibrary.simpleMessage(
      "How would you like to add crypto?",
    ),
    "howWouldYouLikeToFundYourCard": MessageLookupByLibrary.simpleMessage(
      "How would you like to fund your card?",
    ),
    "howYouWantToSell": MessageLookupByLibrary.simpleMessage(
      "How would you like to sell crypto?",
    ),
    "howYouWantToTransfer": MessageLookupByLibrary.simpleMessage(
      "How would you like to transfer crypto?",
    ),
    "howsYourExperienceSoFar": MessageLookupByLibrary.simpleMessage(
      "How’s your\n experience so far?",
    ),
    "hub": MessageLookupByLibrary.simpleMessage("Hub"),
    "hubFindUsefulAppsOnchain": MessageLookupByLibrary.simpleMessage(
      "Hub. Find useful apps,\nonchain",
    ),
    "hundredPercentEncrypted": MessageLookupByLibrary.simpleMessage(
      "100% encrypted",
    ),
    "iAccept": MessageLookupByLibrary.simpleMessage("I accept"),
    "iAmSendingToMyself": MessageLookupByLibrary.simpleMessage(
      "I\'m sending to myself",
    ),
    "iCanReactivateNowButAnotherFailureWillLeadToMyCardBeingTerminated":
        MessageLookupByLibrary.simpleMessage(
          "I can reactivate now, but another failure will lead to my card being terminated",
        ),
    "iCloud": MessageLookupByLibrary.simpleMessage("iCloud"),
    "iCloudBackupPending": MessageLookupByLibrary.simpleMessage(
      "Your wallet backup is not synced to icloud",
    ),
    "iCloudBackupPendingDescription": MessageLookupByLibrary.simpleMessage(
      "To avoid loss of funds, ensure your device is connected to the internet and you have some iCloud storage available. Open Settings > [Your Name] > iCloud to manage your storage.",
    ),
    "iHaveMadeExactPayment": MessageLookupByLibrary.simpleMessage(
      "I have made exact payment",
    ),
    "iHaveSent": MessageLookupByLibrary.simpleMessage("I have sent"),
    "iHaveTransferred": MessageLookupByLibrary.simpleMessage(
      "I\'ve transferred",
    ),
    "iUnderstand": MessageLookupByLibrary.simpleMessage("I understand"),
    "iUnderstandTheRisks": MessageLookupByLibrary.simpleMessage(
      "I understand the risks",
    ),
    "iWantToLearnAboutCrypto": MessageLookupByLibrary.simpleMessage(
      "I want to learn about crypto",
    ),
    "icloudBackupFailureTip": MessageLookupByLibrary.simpleMessage(
      "Ensure your device is connected to the internet and you have some iCloud storage available. Open Settings > [Your Name] > iCloud to manage your storage.",
    ),
    "idDocumentKycTips": MessageLookupByLibrary.simpleMessage(
      "ID document — clearly captured, all parts visible.",
    ),
    "idNumber": MessageLookupByLibrary.simpleMessage("ID number"),
    "idNumberKycTips": MessageLookupByLibrary.simpleMessage(
      "ID number — please make sure to enter it correctly.",
    ),
    "idOrCountryNotSupported": MessageLookupByLibrary.simpleMessage(
      "Your ID or country not is not supported",
    ),
    "idType": MessageLookupByLibrary.simpleMessage("ID Type"),
    "idTypeNumber": MessageLookupByLibrary.simpleMessage("ID type number"),
    "idValidationErrorMessage": MessageLookupByLibrary.simpleMessage(
      "This is an invalid ID number for this ID type",
    ),
    "identityDocument": MessageLookupByLibrary.simpleMessage(
      "Identity document",
    ),
    "identityIsVerified": MessageLookupByLibrary.simpleMessage(
      "Identity is verified",
    ),
    "ifTheDeactivationOccurredRecently": MessageLookupByLibrary.simpleMessage(
      "If the deactivation occurred recently, there may be a wait period before I can create a new card.",
    ),
    "illEnsureMyCardIsWellFundedForAnyTransactionsOnIt":
        MessageLookupByLibrary.simpleMessage(
          "I\'ll ensure my card is well funded for any transactions on it",
        ),
    "illPass": MessageLookupByLibrary.simpleMessage(" I\'ll pass"),
    "illTakeTheRisk": MessageLookupByLibrary.simpleMessage(
      "I’ll take the risk",
    ),
    "imHereToExploreTheApp": MessageLookupByLibrary.simpleMessage(
      "I’m here to explore the app",
    ),
    "importExistingWallet": MessageLookupByLibrary.simpleMessage(
      "Import Existing Wallet",
    ),
    "importMyWallet": MessageLookupByLibrary.simpleMessage("Import my wallet"),
    "importSuccessful": MessageLookupByLibrary.simpleMessage(
      "Import successful!",
    ),
    "importWallet": MessageLookupByLibrary.simpleMessage("Import wallet"),
    "importWalletSubCopy": MessageLookupByLibrary.simpleMessage(
      "Provide your 12 word recovery phrase",
    ),
    "importWalletSuccessMessage": MessageLookupByLibrary.simpleMessage(
      "Great! You can import this wallet ",
    ),
    "importWalletWith": MessageLookupByLibrary.simpleMessage(
      "Import wallet with?",
    ),
    "important": MessageLookupByLibrary.simpleMessage("Important"),
    "importedWalletLogoutSubCopy": MessageLookupByLibrary.simpleMessage(
      "To sign back in, you’ll be required to provide your recovery details to regain access to your account.",
    ),
    "in10Mins": MessageLookupByLibrary.simpleMessage("in 10mins"),
    "inAppBrowser": MessageLookupByLibrary.simpleMessage("In app browser"),
    "inLessThan10Mins": MessageLookupByLibrary.simpleMessage(
      "In less than 10mins",
    ),
    "inProgress": MessageLookupByLibrary.simpleMessage("In progress"),
    "inProgressCompletesWithin15Mins": MessageLookupByLibrary.simpleMessage(
      "Funding in progress. Completes within 15 mins",
    ),
    "inbox": MessageLookupByLibrary.simpleMessage("Inbox"),
    "inboxTooltipDescription": MessageLookupByLibrary.simpleMessage(
      "Your notification history and our help desk now lives here. Never miss any of the things that matter",
    ),
    "incorrectPasscode": MessageLookupByLibrary.simpleMessage(
      "Incorrect passcode. Please try again",
    ),
    "incorrectPasscodeXAttemptsLeft": m47,
    "incorrectPin": MessageLookupByLibrary.simpleMessage(
      "Incorrect pin. Please try again",
    ),
    "instantPaySubtitle": m48,
    "instantly": MessageLookupByLibrary.simpleMessage("Instantly"),
    "insufficientBalance": MessageLookupByLibrary.simpleMessage(
      "Insufficient balance",
    ),
    "insufficientBalanceSwapCopy": m49,
    "insufficientFundCharge": MessageLookupByLibrary.simpleMessage(
      "Insufficient fund charge",
    ),
    "insufficientFundChargeSubCopy": MessageLookupByLibrary.simpleMessage(
      "A charge may apply each time a card transaction fails due to not being funded",
    ),
    "insufficientGasCopy": m50,
    "insufficientGasFallBackCopy": m51,
    "insufficientGasSubCopy": MessageLookupByLibrary.simpleMessage(
      "Blockchain networks require fees to be paid in the native currency of the network. These fees aren\'t paid to Onboard, but to the network to securely validate your transaction.",
    ),
    "insufficientStorage": MessageLookupByLibrary.simpleMessage(
      "You have insufficient storage available on your Drive folder",
    ),
    "insufficientTokenBalance": m52,
    "interactWithCompatibleApps": MessageLookupByLibrary.simpleMessage(
      "Interact with compatible apps",
    ),
    "interactWithDappMessage": MessageLookupByLibrary.simpleMessage(
      "This feature enables your wallet to securely connect to a wide range of platforms and applications.",
    ),
    "intermediaryStatus": MessageLookupByLibrary.simpleMessage(
      "Intermediary status",
    ),
    "intermediaryStatusSubtitle": MessageLookupByLibrary.simpleMessage(
      "Are you receiving or sending funds on behalf of someone other than yourself?",
    ),
    "internalTransfer": MessageLookupByLibrary.simpleMessage(
      "Internal Transfer",
    ),
    "internationalMoneyMove": MessageLookupByLibrary.simpleMessage(
      "Move money to multiple destinations across the globe",
    ),
    "internationalMoneyTransfer": MessageLookupByLibrary.simpleMessage(
      "International money transfer",
    ),
    "internationalPayments": MessageLookupByLibrary.simpleMessage(
      "International Payments",
    ),
    "internationalPaymentsDesc": MessageLookupByLibrary.simpleMessage(
      "Receive USD, and send money globally",
    ),
    "introducingDirectAccount": MessageLookupByLibrary.simpleMessage(
      "Introducing Direct Account",
    ),
    "introducingOnboardVirtualCards": MessageLookupByLibrary.simpleMessage(
      "The USD card that always works",
    ),
    "introducingWalletConnection": MessageLookupByLibrary.simpleMessage(
      "Introducing WalletConnect",
    ),
    "introducingYourWallet": MessageLookupByLibrary.simpleMessage(
      "Introducing Your \nMerchat Wallet",
    ),
    "invalidAddress": MessageLookupByLibrary.simpleMessage("Invalid Address"),
    "invalidAmount": MessageLookupByLibrary.simpleMessage("Invalid Amount"),
    "invalidContractAddressCopy": m53,
    "invalidEmailAddressPleaseCheckAndTryAgain":
        MessageLookupByLibrary.simpleMessage(
          "Invalid Email Address. Please check and try again.",
        ),
    "invalidFirstName": MessageLookupByLibrary.simpleMessage(
      "Invalid name. Numbers and special characters are not allowed",
    ),
    "invalidFullName": MessageLookupByLibrary.simpleMessage(
      "Invalid full name, Numbers and special characters are not allowed",
    ),
    "invalidHomeAddressTryAgain": MessageLookupByLibrary.simpleMessage(
      "Invalid home address. Try again",
    ),
    "invalidLastName": MessageLookupByLibrary.simpleMessage(
      "Invalid name. Numbers and special characters are not allowed",
    ),
    "invalidPasskeySession": MessageLookupByLibrary.simpleMessage(
      "Passkey registration failed. Login again to try again.",
    ),
    "invalidPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Invalid phone number.",
    ),
    "invalidSessionId": MessageLookupByLibrary.simpleMessage(
      "Invalid session Id",
    ),
    "invalidUsername": MessageLookupByLibrary.simpleMessage(
      "Invalid username. Numbers and special characters are not allowed",
    ),
    "invalidWalletConnectUri": MessageLookupByLibrary.simpleMessage(
      "Missing or invalid. URI is not WalletConnect URI",
    ),
    "invalidWalletConnectUriTapToResume": MessageLookupByLibrary.simpleMessage(
      "Missing or invalid. URI is not WalletConnect URI. \nTap anywhere to resume scan.",
    ),
    "investInCrypto": MessageLookupByLibrary.simpleMessage("Invest in crypto"),
    "investor": MessageLookupByLibrary.simpleMessage("Investor"),
    "invite5Friends": MessageLookupByLibrary.simpleMessage("Invite 5 friends"),
    "inviteFriends": MessageLookupByLibrary.simpleMessage("Invite friends 👋"),
    "inviteFriendsEarnBonuses": MessageLookupByLibrary.simpleMessage(
      "Invite friends, earn bonuses",
    ),
    "isThisARecurringPayment": MessageLookupByLibrary.simpleMessage(
      "Is this a recurring payment?",
    ),
    "issueDate": MessageLookupByLibrary.simpleMessage("Issue date"),
    "itCosts": MessageLookupByLibrary.simpleMessage("It costs"),
    "itWillNowShowUpOnYourPortfolioList": MessageLookupByLibrary.simpleMessage(
      "It will now show up on your portfolio list.",
    ),
    "itsNotYouItsUs": MessageLookupByLibrary.simpleMessage(
      "It\'s not you, it\'s us",
    ),
    "join10kPeopleGainingFinancialKnowledge":
        MessageLookupByLibrary.simpleMessage(
          "Join 10k+ people gaining financial knowledge",
        ),
    "joinCommunity": MessageLookupByLibrary.simpleMessage("Join our community"),
    "justAMoment": MessageLookupByLibrary.simpleMessage("Just a moment..."),
    "justScanOrCopyIt": MessageLookupByLibrary.simpleMessage(
      "Just scan or copy it",
    ),
    "kcyInCompleteSubtitle": MessageLookupByLibrary.simpleMessage(
      "Thank you for starting the verification process. You have a few steps left to completion. It will take only 3 mins",
    ),
    "keepYourCardFundedWithAtLeast": MessageLookupByLibrary.simpleMessage(
      "Keep your card funded with at least this amount",
    ),
    "keyStats": MessageLookupByLibrary.simpleMessage("Key Stats"),
    "kycAttemptsInfo": MessageLookupByLibrary.simpleMessage(
      "To keep Onboard safe and secure, we require verified KYC for transactions involving cash.",
    ),
    "kycFailedDescription": MessageLookupByLibrary.simpleMessage(
      "Your KYC verification failed due to a data mismatch. Please try again.",
    ),
    "kycFailedSubCopy": MessageLookupByLibrary.simpleMessage(
      "Your KYC verification failed due to a data mismatch. Please try again.",
    ),
    "kycFailedToStart": MessageLookupByLibrary.simpleMessage(
      "KYC verification failed to start",
    ),
    "kycInProgressNotificationsCopy": MessageLookupByLibrary.simpleMessage(
      "Turn on notifications and we’ll update you on the\nstatus of your verification.",
    ),
    "kycInProgressStatusMessage": MessageLookupByLibrary.simpleMessage(
      "Thank you for submitting required docs. Should be complete within 10 minutes, but some can take up to 24 hours.\n\nTurn on notifications and we’ll update you on the status of your verification.",
    ),
    "kycInProgressStatusTitle": MessageLookupByLibrary.simpleMessage(
      "Docs submitted!\nVerification is in progress",
    ),
    "kycInProgressSubCopy": MessageLookupByLibrary.simpleMessage(
      "Should be complete within 10 minutes, but some can take up to 24 hours.",
    ),
    "kycInReview": MessageLookupByLibrary.simpleMessage(
      "KYC verification in review",
    ),
    "kycIncomplete": MessageLookupByLibrary.simpleMessage(
      "KYC verification incomplete",
    ),
    "kycInfoCopy": m54,
    "kycLoaderMessage": MessageLookupByLibrary.simpleMessage(
      "Redirecting to 3rd party ID service to complete your verification... ",
    ),
    "kycMaxAttemptsCopy": MessageLookupByLibrary.simpleMessage(
      "Despite multiple attempts, we were unable to verify your identity with the details you provided.",
    ),
    "kycRedirectCopy": MessageLookupByLibrary.simpleMessage(
      "Heads up, we’ll redirect you to an ID\nverification service.",
    ),
    "kycVerificationFailed": MessageLookupByLibrary.simpleMessage(
      "KYC verification failed",
    ),
    "kycVerificationInProgress": MessageLookupByLibrary.simpleMessage(
      "KYC verification in progress",
    ),
    "kycVerificationSuccessful": MessageLookupByLibrary.simpleMessage(
      "KYC verification successful",
    ),
    "kycVerifiedStatusMessage": MessageLookupByLibrary.simpleMessage(
      "Your details were successfully verified!\nNow, you\'re good to go.",
    ),
    "kycVerifiedStatusTitle": MessageLookupByLibrary.simpleMessage(
      "Identity is verified",
    ),
    "kycVerifiedSubCopy": MessageLookupByLibrary.simpleMessage(
      "Your details were successfully verified! Now, you\'re good to go.",
    ),
    "last1YearTrend": MessageLookupByLibrary.simpleMessage("Last 1 year trend"),
    "last90Days": MessageLookupByLibrary.simpleMessage("Last 90 days"),
    "lastCheckedDate": m55,
    "lastName": MessageLookupByLibrary.simpleMessage("Last name"),
    "lastOnboardingKycQuestionSubtitle": MessageLookupByLibrary.simpleMessage(
      "A few more details to close this out",
    ),
    "lastOne": MessageLookupByLibrary.simpleMessage("Last one"),
    "learnMore": MessageLookupByLibrary.simpleMessage("Learn more"),
    "learnMoreWithArrow": MessageLookupByLibrary.simpleMessage("Learn more >"),
    "leaveFeedback": MessageLookupByLibrary.simpleMessage("Leave Feedback"),
    "leavingSoonWithEmoji": MessageLookupByLibrary.simpleMessage(
      "Leaving so soon? 🤔",
    ),
    "legal": MessageLookupByLibrary.simpleMessage("Legal"),
    "lessOptions": MessageLookupByLibrary.simpleMessage("Less options"),
    "letGetStarted": MessageLookupByLibrary.simpleMessage("Let’s get started"),
    "letsFindOut": MessageLookupByLibrary.simpleMessage("Let’s find out"),
    "letsGetYouBackIn": MessageLookupByLibrary.simpleMessage(
      "Let’s get you back in. Verify your email to continue",
    ),
    "letsGetYouTheBestOfOnboard": MessageLookupByLibrary.simpleMessage(
      "Let\'s get you the best of Onboard",
    ),
    "letsGo": MessageLookupByLibrary.simpleMessage("Let’s go"),
    "letsPersonalizeYourCryptoJourney": MessageLookupByLibrary.simpleMessage(
      "Let\'s personalize your crypto journey",
    ),
    "letsSecureYourWallet": MessageLookupByLibrary.simpleMessage(
      "Let\'s secure your wallet",
    ),
    "letsVerifyYourAccount": MessageLookupByLibrary.simpleMessage(
      "Let\'s verify your account",
    ),
    "lifeStyle": MessageLookupByLibrary.simpleMessage("Lifestyle"),
    "limits": MessageLookupByLibrary.simpleMessage("Limits"),
    "links": MessageLookupByLibrary.simpleMessage("Links"),
    "loading": MessageLookupByLibrary.simpleMessage("Loading"),
    "localCurrency": MessageLookupByLibrary.simpleMessage("local currency"),
    "lockedBalance": MessageLookupByLibrary.simpleMessage("Locked balance"),
    "lockedBalanceDescription": MessageLookupByLibrary.simpleMessage(
      "This is the total value of your assets locked in your orders and ads. You’re unable to withdraw directly from this balance",
    ),
    "lockedInOrders": MessageLookupByLibrary.simpleMessage("Locked in orders"),
    "logout": MessageLookupByLibrary.simpleMessage("Log out"),
    "logoutSubCopy": MessageLookupByLibrary.simpleMessage(
      "You will be required to sign in and provide security next time ",
    ),
    "losers": MessageLookupByLibrary.simpleMessage("📉 Losers"),
    "maintenanceFee": MessageLookupByLibrary.simpleMessage("maintenance fee"),
    "makeCardInactive": MessageLookupByLibrary.simpleMessage(
      "Make card temporarily inactive",
    ),
    "makePaymentGlobally": MessageLookupByLibrary.simpleMessage(
      "Make payments globally",
    ),
    "makeSureYouSendOnlyTo": MessageLookupByLibrary.simpleMessage(
      "Make sure you send only to",
    ),
    "makeYourFirstDeposit": MessageLookupByLibrary.simpleMessage(
      "Make your first \ndeposit",
    ),
    "manage": MessageLookupByLibrary.simpleMessage("Manage"),
    "manageAds": MessageLookupByLibrary.simpleMessage("Manage Ads"),
    "manageAdsIntro": MessageLookupByLibrary.simpleMessage(
      "Control the multiple ads you’ve created and see the funds you can withdraw anytime",
    ),
    "manageAssets": MessageLookupByLibrary.simpleMessage("Manage assets"),
    "manageCrypto": MessageLookupByLibrary.simpleMessage("Manage crypto"),
    "manageNFTs": MessageLookupByLibrary.simpleMessage("Manage NFTs"),
    "manageTokens": MessageLookupByLibrary.simpleMessage("Manage tokens"),
    "manageYourAds": MessageLookupByLibrary.simpleMessage("Manage your ads"),
    "markASubscription": MessageLookupByLibrary.simpleMessage(
      "Mark a subscription",
    ),
    "markAllAsRead": MessageLookupByLibrary.simpleMessage("Mark all as read"),
    "markAsASubscription": MessageLookupByLibrary.simpleMessage(
      "Mark as a subscription",
    ),
    "marketCap": MessageLookupByLibrary.simpleMessage("Market Cap"),
    "marketCapRank": MessageLookupByLibrary.simpleMessage("Market Cap Rank"),
    "marketPricesCanChangeBetweenWhenYouPlace":
        MessageLookupByLibrary.simpleMessage(
          "Market prices can change between when you place an order and when it is executed.\n",
        ),
    "marketState": MessageLookupByLibrary.simpleMessage("Market State"),
    "masterCard": MessageLookupByLibrary.simpleMessage("mastercard"),
    "max": MessageLookupByLibrary.simpleMessage("Max"),
    "maxAmountRequired": m56,
    "maxDailySpend": MessageLookupByLibrary.simpleMessage("Max. daily spend"),
    "maxMonthlySpend": MessageLookupByLibrary.simpleMessage(
      "Max. monthly spend",
    ),
    "maxSlippage": MessageLookupByLibrary.simpleMessage("Max Slippage"),
    "maxSlippageInfo": MessageLookupByLibrary.simpleMessage(
      "Slippage can occur when market prices change between when you place an order and when it is executed.",
    ),
    "maximum": MessageLookupByLibrary.simpleMessage("Maximum"),
    "mayLeadToLossOfFunds": MessageLookupByLibrary.simpleMessage(
      "may lead to loss of funds.",
    ),
    "meetThisRequirement": MessageLookupByLibrary.simpleMessage(
      "I meet these requirements",
    ),
    "memo": MessageLookupByLibrary.simpleMessage("Memo"),
    "memoCopied": MessageLookupByLibrary.simpleMessage("Memo copied"),
    "merchant": MessageLookupByLibrary.simpleMessage("Merchant"),
    "merchantBalance": MessageLookupByLibrary.simpleMessage("Merchant balance"),
    "merchantCountry": MessageLookupByLibrary.simpleMessage("Merchant country"),
    "merchantSubCopy": MessageLookupByLibrary.simpleMessage(
      "I want to trade and exchange high volumes of digital assets",
    ),
    "merchantWallet": MessageLookupByLibrary.simpleMessage("Merchant wallet"),
    "message": MessageLookupByLibrary.simpleMessage("Message"),
    "min": MessageLookupByLibrary.simpleMessage("min"),
    "minAmountRequired": m57,
    "minFundingRequired": MessageLookupByLibrary.simpleMessage(
      "Min. funding required",
    ),
    "minNetworkAmountIs": m58,
    "minimum": MessageLookupByLibrary.simpleMessage("Minimum"),
    "minimumAmountIs": m59,
    "minimumCardBalance": MessageLookupByLibrary.simpleMessage(
      "Minimum card balance",
    ),
    "minimumConfirmations": MessageLookupByLibrary.simpleMessage(
      "Minimum confirmations",
    ),
    "minimumConfirmationsIs": m60,
    "minimumDeposit": MessageLookupByLibrary.simpleMessage("Minimum deposit"),
    "minutes": m61,
    "minutesAgo": m62,
    "missingOrIncorrectDataInYoureRequest":
        MessageLookupByLibrary.simpleMessage(
          "Missing or incorrect data in your request. Please check the details",
        ),
    "mobileNumber": MessageLookupByLibrary.simpleMessage("Mobile Number"),
    "money": MessageLookupByLibrary.simpleMessage("Money"),
    "moneyWingsEmoji": MessageLookupByLibrary.simpleMessage("💸"),
    "monthly": MessageLookupByLibrary.simpleMessage("Monthly"),
    "monthlyMaintenance": MessageLookupByLibrary.simpleMessage(
      "Monthly maintenance",
    ),
    "monthlyMaintenanceFee": MessageLookupByLibrary.simpleMessage(
      "Monthly maintenance fee",
    ),
    "monthlyMaintenanceSubCopy": MessageLookupByLibrary.simpleMessage(
      "This helps keep your card functioning at all times",
    ),
    "more": MessageLookupByLibrary.simpleMessage("More"),
    "moreCardMoreOptionsSubCopy": MessageLookupByLibrary.simpleMessage(
      "Don\'t limit yourself to just one. You can now create multiple Onboard Cards to manage your payments.",
    ),
    "moreCardsMoreOptions": MessageLookupByLibrary.simpleMessage(
      "More cards. More options",
    ),
    "moreOptions": MessageLookupByLibrary.simpleMessage("More options"),
    "moveBetweenStablecoinsAndUsdEurAtLowFees":
        MessageLookupByLibrary.simpleMessage(
          "Move between stablecoins and USD / EUR at low fees",
        ),
    "moveFundsToYourSpendingCard": m63,
    "moveToCryptoAndCash": MessageLookupByLibrary.simpleMessage(
      "Go from crypto to cash and \nback in minutes",
    ),
    "multiCurrency": MessageLookupByLibrary.simpleMessage(
      "Multi-currency payouts",
    ),
    "multipleActiveOrders": MessageLookupByLibrary.simpleMessage(
      "Multiple active orders",
    ),
    "multipleActiveOrdersCopy": MessageLookupByLibrary.simpleMessage(
      "Multiple active orders  •  Tap to review",
    ),
    "multipleCloudService": MessageLookupByLibrary.simpleMessage(
      "Back up with multiple cloud services for extra security",
    ),
    "multipleDeliveryMethod": MessageLookupByLibrary.simpleMessage(
      "Multiple delivery methods",
    ),
    "multipleWalletsFoundInYourCloudStorage":
        MessageLookupByLibrary.simpleMessage(
          "Multiple wallets found in your cloud storage. \nSelect one to recover",
        ),
    "my": MessageLookupByLibrary.simpleMessage("My"),
    "myAsset": MessageLookupByLibrary.simpleMessage("My Assets"),
    "myBalance": MessageLookupByLibrary.simpleMessage("My Balance"),
    "myBoardPass": MessageLookupByLibrary.simpleMessage("My Board Pass"),
    "myItem": MessageLookupByLibrary.simpleMessage("My Item"),
    "myItems": m64,
    "myNetworkAddress": m65,
    "myPortfolio": MessageLookupByLibrary.simpleMessage("My Portfolio"),
    "myTradingWallet": MessageLookupByLibrary.simpleMessage(
      "My Merchant Wallet",
    ),
    "myUsdAccountAddress": m66,
    "myVirtualCardAddress": MessageLookupByLibrary.simpleMessage(
      "My virtual card address",
    ),
    "myWallet": MessageLookupByLibrary.simpleMessage("My Wallet"),
    "myWallets": MessageLookupByLibrary.simpleMessage("My Wallets"),
    "name": MessageLookupByLibrary.simpleMessage("Name"),
    "nameYourCard": MessageLookupByLibrary.simpleMessage("Name your card"),
    "needTOConvertYourMoneyToALocalCurrency":
        MessageLookupByLibrary.simpleMessage(
          "Need to convert your money to local currency? We\'ve got you",
        ),
    "network": MessageLookupByLibrary.simpleMessage("Network"),
    "networkFee": MessageLookupByLibrary.simpleMessage("Network Fee"),
    "neverMissATransaction": MessageLookupByLibrary.simpleMessage(
      "Never miss a transaction",
    ),
    "neverMissATransactionSubCopy": MessageLookupByLibrary.simpleMessage(
      "Onboard sends notifications to keep you updated on all your \n account activity",
    ),
    "newAndImprovedDashboardSubCopy": MessageLookupByLibrary.simpleMessage(
      "Managing your money just got easier.\nYour card & crypto balances, now in one place",
    ),
    "newCard": MessageLookupByLibrary.simpleMessage("New card"),
    "newPasscodeCreated": MessageLookupByLibrary.simpleMessage(
      "New passcode created",
    ),
    "newPinCreated": MessageLookupByLibrary.simpleMessage("New pin created"),
    "newText": MessageLookupByLibrary.simpleMessage("New"),
    "newTextWithEmoji": MessageLookupByLibrary.simpleMessage("⭐ ️ New"),
    "next": MessageLookupByLibrary.simpleMessage("Next"),
    "nextOn": MessageLookupByLibrary.simpleMessage("next on"),
    "nextPayment": MessageLookupByLibrary.simpleMessage("Next payment"),
    "nextPaymentDateUpdated": MessageLookupByLibrary.simpleMessage(
      "Next payment date updated",
    ),
    "nftHidden": MessageLookupByLibrary.simpleMessage("NFT hidden!"),
    "nftHiddenDestription": MessageLookupByLibrary.simpleMessage(
      "This NFT will no longer show up on your portfolio. To unhide visit “Manage”",
    ),
    "nftIsHidden": MessageLookupByLibrary.simpleMessage("NFT is hidden"),
    "nftIsVisible": MessageLookupByLibrary.simpleMessage("NFT is visible"),
    "nftTipContent": MessageLookupByLibrary.simpleMessage(
      "Manage and interact with the NFTs you hold, right inside your wallet",
    ),
    "nftTransfer": MessageLookupByLibrary.simpleMessage("NFT Transfer"),
    "nfts": MessageLookupByLibrary.simpleMessage("NFTs"),
    "nftsInYourWallet": MessageLookupByLibrary.simpleMessage(
      "NFTs, in your wallet",
    ),
    "nickName": MessageLookupByLibrary.simpleMessage("Nickname"),
    "no": MessageLookupByLibrary.simpleMessage("No"),
    "noActiveAuthSession": MessageLookupByLibrary.simpleMessage(
      "No active auth session",
    ),
    "noAssetsFound": MessageLookupByLibrary.simpleMessage("No assets found"),
    "noAssetsSubCopy": MessageLookupByLibrary.simpleMessage(
      "No assets yet. Add crypto to get started",
    ),
    "noAttemptsLeftLoggingOutInXSeconds": m67,
    "noAuthMethodsAvailable": MessageLookupByLibrary.simpleMessage(
      "No Authorization methods available",
    ),
    "noBalancesAvailable": MessageLookupByLibrary.simpleMessage(
      "No balances available",
    ),
    "noCloudBackUpErrorMessage": m68,
    "noDefiWalletFoundOnDevice": MessageLookupByLibrary.simpleMessage(
      "No defi wallet found on device",
    ),
    "noFee": MessageLookupByLibrary.simpleMessage("No fee"),
    "noHiddenNFT": MessageLookupByLibrary.simpleMessage("No hidden nft"),
    "noInternetConnectionError": MessageLookupByLibrary.simpleMessage(
      "Please check your network connection and try again",
    ),
    "noMatchingOffers": MessageLookupByLibrary.simpleMessage(
      "No matching offers",
    ),
    "noMoreDelaysOrManualTransactions": MessageLookupByLibrary.simpleMessage(
      "No more delays or manual transactions",
    ),
    "noNetworkWithDepositAvailable": MessageLookupByLibrary.simpleMessage(
      "No network with deposit available",
    ),
    "noNetworkWithWithdrawalAvailable": MessageLookupByLibrary.simpleMessage(
      "No network with withdrawal available",
    ),
    "noOptionsAvailableForThisQuestion": MessageLookupByLibrary.simpleMessage(
      "No options available for this question",
    ),
    "noPasskeysDesc": MessageLookupByLibrary.simpleMessage(
      "Passkeys help you authenticate your wallet faster, using your device’s biometrics or security PIN.",
    ),
    "noPasskeysTitle": MessageLookupByLibrary.simpleMessage(
      "You don\'t have any passkeys",
    ),
    "noProviderCopy": MessageLookupByLibrary.simpleMessage(
      "Payment methods not currently available for this asset. Try another asset or network",
    ),
    "noResultsFound": MessageLookupByLibrary.simpleMessage("No results found"),
    "noRouteFound": MessageLookupByLibrary.simpleMessage("No route found"),
    "noSearchHistory": MessageLookupByLibrary.simpleMessage(
      "You haven’t searched any sites yet",
    ),
    "noSpecificAsset": MessageLookupByLibrary.simpleMessage(
      "No specific asset",
    ),
    "noTransactionFound": MessageLookupByLibrary.simpleMessage(
      "No Transactions found",
    ),
    "noTransactionsCopy": MessageLookupByLibrary.simpleMessage(
      "No assets yet. Add crypto to get started",
    ),
    "noTransactionsFound": MessageLookupByLibrary.simpleMessage(
      "No transactions found",
    ),
    "noTransactionsYet": MessageLookupByLibrary.simpleMessage(
      "No transactions yet",
    ),
    "non": MessageLookupByLibrary.simpleMessage("Non"),
    "nonStableCoinCardFundingNote": MessageLookupByLibrary.simpleMessage(
      "Cards can be funded with cash or crypto. You\'ve selected an asset which will first be swapped to a USD-based token in order to fund your card",
    ),
    "notAvailableInYourCountry": MessageLookupByLibrary.simpleMessage(
      "Not available in your\ncountry",
    ),
    "notAvailableInYourCountrySubCopy": MessageLookupByLibrary.simpleMessage(
      "We don\'t support cards in your location just yet, but we can reach out when we do, if you\'d like that.",
    ),
    "notEnoughToCoverFee": m69,
    "notEnoughTokensAvailableForThisSwapTryALowerAmount":
        MessageLookupByLibrary.simpleMessage(
          "Not enough tokens available for this swap. Try a lower amount.",
        ),
    "notSentTransactionStatusMessage": MessageLookupByLibrary.simpleMessage(
      "The transaction is in progress. We\'ll notify you once completed.",
    ),
    "notYou": MessageLookupByLibrary.simpleMessage("Not you?"),
    "noteTermForFrozenCard": MessageLookupByLibrary.simpleMessage(
      "Your card is now frozen, but please note the following terms",
    ),
    "notes": MessageLookupByLibrary.simpleMessage("Notes"),
    "notifications": MessageLookupByLibrary.simpleMessage("Notifications"),
    "notificationsSubtitle": MessageLookupByLibrary.simpleMessage(
      "View your recent notifications here",
    ),
    "notifyMe": MessageLookupByLibrary.simpleMessage("Notify me"),
    "notifyYouWhenDone": MessageLookupByLibrary.simpleMessage(
      "Don’t worry, we will notify you once successful",
    ),
    "nowText": MessageLookupByLibrary.simpleMessage("now"),
    "occupation": MessageLookupByLibrary.simpleMessage("Occupation"),
    "officialID": MessageLookupByLibrary.simpleMessage("official ID"),
    "okGotIt": MessageLookupByLibrary.simpleMessage("Ok, got it!"),
    "okay": MessageLookupByLibrary.simpleMessage("Okay"),
    "okayGotIt": MessageLookupByLibrary.simpleMessage("Okay, got it"),
    "on": MessageLookupByLibrary.simpleMessage("on"),
    "onGoingOrder": MessageLookupByLibrary.simpleMessage("Ongoing order"),
    "onGoingOrders": MessageLookupByLibrary.simpleMessage("Ongoing orders"),
    "onNetworkName": m70,
    "onboard": MessageLookupByLibrary.simpleMessage("Onboard"),
    "onboardAccounts": MessageLookupByLibrary.simpleMessage("Onboard accounts"),
    "onboardDotXyz": MessageLookupByLibrary.simpleMessage("Onboard.xyz"),
    "onboardExchange": MessageLookupByLibrary.simpleMessage("Onboard Exchange"),
    "onboardExchangeTM": MessageLookupByLibrary.simpleMessage(
      "Onboard Exchange®",
    ),
    "onboardFast": MessageLookupByLibrary.simpleMessage("OnboardFast®"),
    "onboardIsFeelingBrandNew": MessageLookupByLibrary.simpleMessage(
      "Onboard is feeling brand new!",
    ),
    "onboardIsNotAFinancialAdvisor": MessageLookupByLibrary.simpleMessage(
      "Onboard is not a financial advisor. Digital assets are volatile, so do your own research",
    ),
    "onboardPay": MessageLookupByLibrary.simpleMessage("Onboard pay"),
    "onboardPayOffRampSubtitle": m71,
    "onboardPayOnRampSubtitle": m72,
    "onboardPaySubtitle": m73,
    "onboardWallet": MessageLookupByLibrary.simpleMessage("Onboard Wallet"),
    "onboardingOptionsScreenSubtitle": MessageLookupByLibrary.simpleMessage(
      "Don\'t worry, you can still try out the other options later",
    ),
    "onboardingOptionsScreenTitle": MessageLookupByLibrary.simpleMessage(
      "How would you like to use Haven?",
    ),
    "onboardingSurveySubCopy": MessageLookupByLibrary.simpleMessage(
      "We\'re proud you\'re considering\nOnboard, but first - it\'s important to us to know if we can serve your needs.",
    ),
    "onboardsBetterWithFriends": MessageLookupByLibrary.simpleMessage(
      "Onboard\'s better with friends",
    ),
    "oneDayBefore": MessageLookupByLibrary.simpleMessage("1 Day before"),
    "oneLastThing": MessageLookupByLibrary.simpleMessage("One last thing..."),
    "oneSmallThing": MessageLookupByLibrary.simpleMessage("One small thing,"),
    "oneTimeFeeChargedOnFirstDeposit": m74,
    "oneTimeFeeToCreateCard": MessageLookupByLibrary.simpleMessage(
      "A one-time fee to create your card",
    ),
    "oneWeekBefore": MessageLookupByLibrary.simpleMessage("1 Week before"),
    "onlyFundAccountUsingAcceptedPartiesAndLimit":
        MessageLookupByLibrary.simpleMessage(
          "Only fund account using accepted parties and limits. Avoid reversals & fees.",
        ),
    "onlyRailTransfer": m75,
    "oopsCurrentCodeSeemsIncorrect": MessageLookupByLibrary.simpleMessage(
      "Current code seems incorrect",
    ),
    "oopsThisCodeSeemsIncorrect": MessageLookupByLibrary.simpleMessage(
      "Oops! This code seems incorrect",
    ),
    "oopsYouveGotItWrong": MessageLookupByLibrary.simpleMessage(
      "Oops! You’ve got it wrong",
    ),
    "open": MessageLookupByLibrary.simpleMessage("Open"),
    "optional": MessageLookupByLibrary.simpleMessage("Optional"),
    "or": MessageLookupByLibrary.simpleMessage("Or"),
    "orders": MessageLookupByLibrary.simpleMessage("Orders"),
    "otherAdditionalIdVerificationDescription":
        MessageLookupByLibrary.simpleMessage(
          "Please provide a description of the\ndocument type you want to provided",
        ),
    "others": MessageLookupByLibrary.simpleMessage("Others"),
    "overdueFees": MessageLookupByLibrary.simpleMessage("Overdue fees"),
    "overdueFeesExplainerCopy": MessageLookupByLibrary.simpleMessage(
      "To ensure your card always works, we require periodic service payments. Not paying this means we’ll no longer be able to provide the service to you.",
    ),
    "overview": MessageLookupByLibrary.simpleMessage("Overview"),
    "ownYourMoney": MessageLookupByLibrary.simpleMessage("OWN\nYOUR MONEY"),
    "p2pExchange": MessageLookupByLibrary.simpleMessage("P2P Exchange"),
    "p2pTransfers": MessageLookupByLibrary.simpleMessage("P2P transfers"),
    "partiallyFunded": MessageLookupByLibrary.simpleMessage("Partially funded"),
    "passcode": MessageLookupByLibrary.simpleMessage("Passcode"),
    "passcodeChanged": MessageLookupByLibrary.simpleMessage(
      "Passcode changed 🎉",
    ),
    "passcodeCreated": MessageLookupByLibrary.simpleMessage(
      "Passcode created 🎉",
    ),
    "passcodeDoesntMatch": MessageLookupByLibrary.simpleMessage(
      "Passcode doesn\'t match",
    ),
    "passcodeResetFailed": MessageLookupByLibrary.simpleMessage(
      "Passcode reset failed",
    ),
    "passcodeResetSuccessful": MessageLookupByLibrary.simpleMessage(
      "Passcode reset successful",
    ),
    "passkey": MessageLookupByLibrary.simpleMessage("Passkey"),
    "passkeyAuthCancelled": MessageLookupByLibrary.simpleMessage(
      "Passkey login cancelled",
    ),
    "passkeyAuthorizationFailed": MessageLookupByLibrary.simpleMessage(
      "Passkey authorization failed.",
    ),
    "passkeyCloseDesc": MessageLookupByLibrary.simpleMessage(
      "Creating a passkey is an essential security step that helps you easily authenticate your account. It takes 5 seconds to setup.",
    ),
    "passkeyCreated": MessageLookupByLibrary.simpleMessage("Passkeys created"),
    "passkeyCreatedDesc": MessageLookupByLibrary.simpleMessage(
      "You can now use your fingerprint, face scan, PIN or pattern to access to your wallet across your devices.",
    ),
    "passkeyCreatedFailed": MessageLookupByLibrary.simpleMessage(
      "Passkey registration failed",
    ),
    "passkeyCreatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Passkey created successfully",
    ),
    "passkeyDeleted": MessageLookupByLibrary.simpleMessage("Passkey deleted"),
    "passkeyDeletionError": MessageLookupByLibrary.simpleMessage(
      "Passkey couldn\'t be deleted due to an error",
    ),
    "passkeyDescOne": MessageLookupByLibrary.simpleMessage(
      "With passkeys, you don’t need to remember complex passwords.",
    ),
    "passkeyDescTwo": MessageLookupByLibrary.simpleMessage(
      "Instead, you can use your fingerprint, face, PIN or pattern to access your account across your devices.",
    ),
    "passkeyExistOnDevice": MessageLookupByLibrary.simpleMessage(
      "Passkey already exists on device",
    ),
    "passkeyListInfo": MessageLookupByLibrary.simpleMessage(
      "With passkeys, you can speed up accessing your Onboard account on this device using just your biometric data.",
    ),
    "passkeyRecoveryError": MessageLookupByLibrary.simpleMessage(
      "Passkey error. Please verify that this is the device associated with the registered passkey.",
    ),
    "passkeySetup": MessageLookupByLibrary.simpleMessage("Passkey setup"),
    "passkeys": MessageLookupByLibrary.simpleMessage("Passkeys"),
    "paste": MessageLookupByLibrary.simpleMessage("Paste"),
    "pasteFromClipboard": MessageLookupByLibrary.simpleMessage(
      "Paste from clipboard",
    ),
    "pasteHereOrType": MessageLookupByLibrary.simpleMessage(
      "Paste here or type...",
    ),
    "pasteOrScanAddress": MessageLookupByLibrary.simpleMessage(
      "Paste or scan address",
    ),
    "pasteOrTypePrivateKeyHere": MessageLookupByLibrary.simpleMessage(
      "Paste or type private key",
    ),
    "pasteYourRecoveryPhrase": MessageLookupByLibrary.simpleMessage(
      "Paste your recovery phrase",
    ),
    "pay": MessageLookupByLibrary.simpleMessage("Pay"),
    "payAnywhereWithAnOnboardCard": MessageLookupByLibrary.simpleMessage(
      "Pay anywhere with an Onboard \ncard",
    ),
    "payAnywhereWithOnboard": MessageLookupByLibrary.simpleMessage(
      "Pay anywhere with Onboard ",
    ),
    "payWith": MessageLookupByLibrary.simpleMessage("Pay with"),
    "payWithCash": MessageLookupByLibrary.simpleMessage("Pay with cash"),
    "payWithCashDepositsIsComingSoon": m76,
    "payWithCashTransfer": MessageLookupByLibrary.simpleMessage(
      "Pay with Cash Transfer",
    ),
    "payWithExternalWallet": MessageLookupByLibrary.simpleMessage(
      "Pay with External Wallet",
    ),
    "payWithLocalCurrency": MessageLookupByLibrary.simpleMessage(
      "Pay with Local currency",
    ),
    "payWithOnboardPay": MessageLookupByLibrary.simpleMessage(
      "Pay with Onboard Pay",
    ),
    "payWithWalletBalance": MessageLookupByLibrary.simpleMessage(
      "Pay with Wallet Balance",
    ),
    "paymentMethod": MessageLookupByLibrary.simpleMessage("Payment method"),
    "paymentMethodNotAvailable": MessageLookupByLibrary.simpleMessage(
      "Payment method not available",
    ),
    "paymentReminders": MessageLookupByLibrary.simpleMessage(
      "Payment reminders",
    ),
    "paymentSuccessful": MessageLookupByLibrary.simpleMessage(
      "Payment successful",
    ),
    "pending": MessageLookupByLibrary.simpleMessage("Pending"),
    "pendingDestinationChainTransactionStatusMessage": m77,
    "pendingSourceChainTransactionStatusMessage": m78,
    "pendingVia": MessageLookupByLibrary.simpleMessage("Pending via"),
    "perWithdrawal": MessageLookupByLibrary.simpleMessage("Per withdrawal"),
    "personalDetails": MessageLookupByLibrary.simpleMessage("Personal details"),
    "phoneNumber": MessageLookupByLibrary.simpleMessage("Phone number"),
    "phoneNumberViewSubCopy": MessageLookupByLibrary.simpleMessage(
      "Please provide your phone number - we\'ll only contact if you need us",
    ),
    "phoneSKipWarning": MessageLookupByLibrary.simpleMessage(
      "Phone numbers give us a direct means of helping out if you run into any issues. Sure about this?",
    ),
    "phoneVerification": MessageLookupByLibrary.simpleMessage(
      "Phone verification",
    ),
    "photoLibrary": MessageLookupByLibrary.simpleMessage("Photo Library"),
    "physicalCard": MessageLookupByLibrary.simpleMessage("Physical card"),
    "pickAColor": MessageLookupByLibrary.simpleMessage("Pick a color"),
    "pickMerchantThatOfferGreatRate": m79,
    "pictureOfIdIsRequired": MessageLookupByLibrary.simpleMessage(
      "Picture of the ID is required",
    ),
    "pinChangeFailed": MessageLookupByLibrary.simpleMessage(
      "Pin change failed",
    ),
    "pinCreated": MessageLookupByLibrary.simpleMessage("Pin created 🎉"),
    "pinCreationFailed": MessageLookupByLibrary.simpleMessage(
      "Pin creation failed",
    ),
    "pinDoesntMatch": MessageLookupByLibrary.simpleMessage(
      "PIN doesn\'t match",
    ),
    "pleaseAuthenticateToBackup": MessageLookupByLibrary.simpleMessage(
      "Please authenticate to secure your wallet",
    ),
    "pleaseConfirm": MessageLookupByLibrary.simpleMessage("Please confirm"),
    "pleaseConfirmThisTransaction": MessageLookupByLibrary.simpleMessage(
      "Please confirm this\ntransaction",
    ),
    "pleaseEnterTheCodeSentTo": MessageLookupByLibrary.simpleMessage(
      "Please enter the code sent to",
    ),
    "pleaseReadAndAccept": MessageLookupByLibrary.simpleMessage(
      "Please read and accept the following to use this card.",
    ),
    "pleaseSelectADifferentTokenFromTheSourceToken":
        MessageLookupByLibrary.simpleMessage(
          "Please select a different token from the source token to swap to.",
        ),
    "pleaseTryAnotherAsset": MessageLookupByLibrary.simpleMessage(
      "Funding not currently available for this asset. Please try another asset.",
    ),
    "pleaseWait24hrsBeforeSettingUpACard": m80,
    "poorInternetConnectionPleaseTryAgain": MessageLookupByLibrary.simpleMessage(
      "Poor internet connection. Please check your connection and try again.",
    ),
    "portfolio": MessageLookupByLibrary.simpleMessage("Portfolio"),
    "possibleSpam": MessageLookupByLibrary.simpleMessage("Possible spam"),
    "postalCode": MessageLookupByLibrary.simpleMessage("Postal code"),
    "postalCodeIsRequired": MessageLookupByLibrary.simpleMessage(
      "Postal code is required",
    ),
    "poweredBy": MessageLookupByLibrary.simpleMessage("Powered by"),
    "preferenceList": MessageLookupByLibrary.simpleMessage(
      "Payment method, notifications",
    ),
    "preferences": MessageLookupByLibrary.simpleMessage("Preferences"),
    "preventSpamToken": MessageLookupByLibrary.simpleMessage(
      "Prevents random, spam crypto from showing up in your wallet",
    ),
    "preview": MessageLookupByLibrary.simpleMessage("Preview"),
    "price": MessageLookupByLibrary.simpleMessage("Price"),
    "privacyPolicy": MessageLookupByLibrary.simpleMessage("Privacy Policy"),
    "privateKey": MessageLookupByLibrary.simpleMessage("Private key"),
    "privateKeyDescription": MessageLookupByLibrary.simpleMessage(
      "Your private key is a 64 character special kind of password. It is one way to access your account. Never share it with anyone!",
    ),
    "proTradingTipSubCopy": MessageLookupByLibrary.simpleMessage(
      "We suggest funding your merchant wallet first before creating an ad.",
    ),
    "proTradingTips": MessageLookupByLibrary.simpleMessage("Pro merchant tip"),
    "proceed": MessageLookupByLibrary.simpleMessage("Proceed"),
    "processingFee": MessageLookupByLibrary.simpleMessage("Processing fee"),
    "processingFeeInfoCopy": MessageLookupByLibrary.simpleMessage(
      "This covers the cost of your transaction - primarily for currency conversion and transaction fees.",
    ),
    "processingTime": MessageLookupByLibrary.simpleMessage("Processing time"),
    "processingYourOrder": MessageLookupByLibrary.simpleMessage(
      "Processing your order",
    ),
    "profile": MessageLookupByLibrary.simpleMessage("Profile"),
    "proofOfAddress": MessageLookupByLibrary.simpleMessage("Proof of address"),
    "proofOfAddressFileTooLarge": m81,
    "proofOfAddressSubtitle": MessageLookupByLibrary.simpleMessage(
      "Doc must match details to be accepted",
    ),
    "proofOfAddressTip": MessageLookupByLibrary.simpleMessage(
      "Proof of address — Legible & matches ID info",
    ),
    "properties": MessageLookupByLibrary.simpleMessage("Properties"),
    "protectTheValueOfYourMoney": MessageLookupByLibrary.simpleMessage(
      "Protect the value of your money",
    ),
    "protectYourMoney": MessageLookupByLibrary.simpleMessage(
      "Protect your money from devaluation",
    ),
    "protectYourWallet": MessageLookupByLibrary.simpleMessage(
      "Protect your wallet",
    ),
    "provideBasicDetails": MessageLookupByLibrary.simpleMessage(
      "Provide basic details",
    ),
    "provideConsistentInfo": MessageLookupByLibrary.simpleMessage(
      "Provide consistent & valid information",
    ),
    "provider": MessageLookupByLibrary.simpleMessage("provider"),
    "purchase": MessageLookupByLibrary.simpleMessage("Purchase"),
    "purposeOfAccount": MessageLookupByLibrary.simpleMessage(
      "Purpose of account",
    ),
    "purposeOfAccountSubtitle": MessageLookupByLibrary.simpleMessage(
      "What’s the primary purpose for your account?",
    ),
    "qrCode": MessageLookupByLibrary.simpleMessage("QR code"),
    "qualifyingMessages": MessageLookupByLibrary.simpleMessage(
      "Performs qualifying action. See terms",
    ),
    "quarterly": MessageLookupByLibrary.simpleMessage("Quarterly"),
    "quickActivation": MessageLookupByLibrary.simpleMessage("Quick activation"),
    "quickActivationAndFunding": MessageLookupByLibrary.simpleMessage(
      "Quick activation and fast funding",
    ),
    "quickQuestion": MessageLookupByLibrary.simpleMessage("Quick question"),
    "rank": MessageLookupByLibrary.simpleMessage("Rank"),
    "rate": MessageLookupByLibrary.simpleMessage("Rate"),
    "rateHasBeingUpdated": m82,
    "rateIsExpiredPleaseTryAgain": MessageLookupByLibrary.simpleMessage(
      "Rate is either expired or invalid, please try again.",
    ),
    "rateOurApp": MessageLookupByLibrary.simpleMessage("Rate our app"),
    "reachOutToUsForMoreInfo": MessageLookupByLibrary.simpleMessage(
      "Reach out to us for more info",
    ),
    "reactivateCard": MessageLookupByLibrary.simpleMessage("Reactivate card"),
    "receive": MessageLookupByLibrary.simpleMessage("Receive"),
    "receiveAsCash": MessageLookupByLibrary.simpleMessage("Receive as cash"),
    "receiveCashInYourLocalCurrency": MessageLookupByLibrary.simpleMessage(
      "Receive cash in local currency",
    ),
    "receiveCrypto": MessageLookupByLibrary.simpleMessage("Receive Crypto"),
    "receiveCurrencyInYourBankAccount": m83,
    "receiveDollarPayments": MessageLookupByLibrary.simpleMessage(
      "Receive dollar payments ",
    ),
    "receiveInternationalPayments": MessageLookupByLibrary.simpleMessage(
      "Receive international payments",
    ),
    "receivePayoutInYourBankAccount": m84,
    "receiveTokenFromExternalWallet": MessageLookupByLibrary.simpleMessage(
      "Receive tokens from external wallet",
    ),
    "received": MessageLookupByLibrary.simpleMessage("Received"),
    "receivingAccount": MessageLookupByLibrary.simpleMessage(
      "Receiving account",
    ),
    "receivingAddress": MessageLookupByLibrary.simpleMessage(
      "Receiving address",
    ),
    "receivingCurrency": MessageLookupByLibrary.simpleMessage(
      "Receiving currency",
    ),
    "recentlyAdded": MessageLookupByLibrary.simpleMessage("Recently added"),
    "recents": MessageLookupByLibrary.simpleMessage("Recents"),
    "recipientGets": MessageLookupByLibrary.simpleMessage("Recipient gets"),
    "recipientGot": MessageLookupByLibrary.simpleMessage("Recipient got"),
    "recommended": MessageLookupByLibrary.simpleMessage("Recommended"),
    "recoverAccountSubCopy1": MessageLookupByLibrary.simpleMessage(
      "Your Onboard account has an existing crypto wallet address attached to it ",
    ),
    "recoverAccountSubCopy2": MessageLookupByLibrary.simpleMessage(
      "Tap preferred button below to get started on wallet recovery",
    ),
    "recoverWithPasskey": MessageLookupByLibrary.simpleMessage(
      "Recover with Passkey",
    ),
    "recoverWithSMS": MessageLookupByLibrary.simpleMessage("Recover with SMS"),
    "recoveringYourAccount": MessageLookupByLibrary.simpleMessage(
      "Recovering your wallet",
    ),
    "recoveryOrSeedPhrase": MessageLookupByLibrary.simpleMessage(
      "Recovery or Seed phrase",
    ),
    "recoveryPhrase": MessageLookupByLibrary.simpleMessage("Recovery phrase"),
    "recoveryPhraseInfo": MessageLookupByLibrary.simpleMessage(
      "Your recovery phrase is a special kind of password. It is one way to access your wallet and must be kept\nprivate",
    ),
    "recoverySeedPhraseDescription": MessageLookupByLibrary.simpleMessage(
      "Your recovery or seed phrase is a 12 word special kind of password. It is another way to access your account and must be kept private",
    ),
    "reddit": MessageLookupByLibrary.simpleMessage("reddit"),
    "redirectingToOnboardExchange": MessageLookupByLibrary.simpleMessage(
      "Redirecting to Onboard\nExchange...",
    ),
    "redirectingToProvider": m85,
    "referAndEarnTitle": MessageLookupByLibrary.simpleMessage(
      "Earn \$\$ for each friend you invite!",
    ),
    "referral": MessageLookupByLibrary.simpleMessage("Referral"),
    "referralCode": MessageLookupByLibrary.simpleMessage("Referral code"),
    "referralCodeExplainer": MessageLookupByLibrary.simpleMessage(
      "This code is your referrer’s Board Pass (found in their \'Settings\' menu). Enter the code here, so perks or rewards can be assigned",
    ),
    "refreshBalance": MessageLookupByLibrary.simpleMessage("Refresh Balance"),
    "refund": MessageLookupByLibrary.simpleMessage("Refund"),
    "refundRequiredTransactionStatusMessage": MessageLookupByLibrary.simpleMessage(
      "Unfortunately, this transaction experienced an issue on the network. Please contact us for help with getting your token refunded.",
    ),
    "refunded": MessageLookupByLibrary.simpleMessage("Refunded"),
    "refundedTransactionStatusMessage": MessageLookupByLibrary.simpleMessage(
      "Funds have been returned to your wallet. Thanks for your understanding.",
    ),
    "reject": MessageLookupByLibrary.simpleMessage("Reject"),
    "remindMe": MessageLookupByLibrary.simpleMessage("Remind me"),
    "removedFromFavourites": MessageLookupByLibrary.simpleMessage(
      "Removed from favourites",
    ),
    "request": MessageLookupByLibrary.simpleMessage("Request"),
    "requestStatement": MessageLookupByLibrary.simpleMessage(
      "Request statement",
    ),
    "requestingCode": MessageLookupByLibrary.simpleMessage("Requesting code"),
    "required": MessageLookupByLibrary.simpleMessage("required"),
    "resend": MessageLookupByLibrary.simpleMessage("Resend"),
    "resendIn": m86,
    "resentCodeIn": m87,
    "reset": MessageLookupByLibrary.simpleMessage("Reset"),
    "resetFilters": MessageLookupByLibrary.simpleMessage("Reset filters"),
    "resetPasscode": MessageLookupByLibrary.simpleMessage("Reset Passcode"),
    "resetPin": MessageLookupByLibrary.simpleMessage("Reset pin"),
    "resources": MessageLookupByLibrary.simpleMessage("Resources"),
    "restore": MessageLookupByLibrary.simpleMessage("Restore"),
    "restoreWallet": MessageLookupByLibrary.simpleMessage("Restore wallet"),
    "restoreYourWallet": MessageLookupByLibrary.simpleMessage(
      "Restore your wallet",
    ),
    "resultsAreIn": MessageLookupByLibrary.simpleMessage("Results are in"),
    "retryBackup": MessageLookupByLibrary.simpleMessage("Retry backup"),
    "retryIcloud": MessageLookupByLibrary.simpleMessage("Retry iCloud"),
    "returnToConnectedToDapp": m88,
    "review": MessageLookupByLibrary.simpleMessage("Review"),
    "reviewInformation": MessageLookupByLibrary.simpleMessage(
      "Review information",
    ),
    "routingNumber": MessageLookupByLibrary.simpleMessage("Routing number"),
    "routingNumberCopied": MessageLookupByLibrary.simpleMessage(
      "Routing number copied!",
    ),
    "sampleEmail": MessageLookupByLibrary.simpleMessage("<EMAIL>"),
    "saveAddress": MessageLookupByLibrary.simpleMessage("Save address"),
    "saveInUsdc": m89,
    "saveToPhotos": MessageLookupByLibrary.simpleMessage("Save to photos"),
    "savedAddresses": MessageLookupByLibrary.simpleMessage("Saved addresses"),
    "savedPrivateKeySomewhere": MessageLookupByLibrary.simpleMessage(
      "I\'ve saved my private key somewhere. Failing to do this will lead me to loss of my funds forever.",
    ),
    "savedRecoveryKeySomewhere": MessageLookupByLibrary.simpleMessage(
      "I\'ve saved my recovery phrase somewhere. Failing to do this will lead me to loss of my funds forever.",
    ),
    "savedToGallery": MessageLookupByLibrary.simpleMessage("Saved to Gallery"),
    "savedToPhotos": MessageLookupByLibrary.simpleMessage("Saved to Photos"),
    "scanOrCopyQrCode": MessageLookupByLibrary.simpleMessage(
      "Scan or copy QR Code",
    ),
    "scanQrcode": MessageLookupByLibrary.simpleMessage("Scan QR Code"),
    "scheduledChatNotificationBody": MessageLookupByLibrary.simpleMessage(
      "We\'d love to help you get started with some tips - tap here to chat with us",
    ),
    "scheduledChatNotificationTitle": MessageLookupByLibrary.simpleMessage(
      "Thanks for getting Onboard! 🚀",
    ),
    "screenshotWarningCopy": MessageLookupByLibrary.simpleMessage(
      "Screenshots aren\'t a safe way to keep track of your Secret Recovery Phrase. Store it somewhere that isn\'t backed up online to keep your wallet safe.",
    ),
    "search": MessageLookupByLibrary.simpleMessage("Search"),
    "searchAndConnectToYourFaveAppsRightFromYourWallet":
        MessageLookupByLibrary.simpleMessage(
          "Search and connect to your fave apps, right from your Wallet",
        ),
    "searchWeb": MessageLookupByLibrary.simpleMessage("Search app or web"),
    "seconds": m90,
    "secureAccountWithPasscode": MessageLookupByLibrary.simpleMessage(
      "Set a 6-digit passcode for login",
    ),
    "secureWallet": MessageLookupByLibrary.simpleMessage("Secure wallet"),
    "secureYourWallet": MessageLookupByLibrary.simpleMessage(
      "Secure your wallet",
    ),
    "securedBy": MessageLookupByLibrary.simpleMessage("Secured by"),
    "securedByOnboardFast": MessageLookupByLibrary.simpleMessage(
      "Secured by OnboardFast®",
    ),
    "securingAccount": MessageLookupByLibrary.simpleMessage("Securing wallet"),
    "securingAccountSubCopy": MessageLookupByLibrary.simpleMessage(
      "We’re encrypting your details. Wallet is almost ready",
    ),
    "security": MessageLookupByLibrary.simpleMessage("Security"),
    "securityVerification": MessageLookupByLibrary.simpleMessage(
      "Security verification",
    ),
    "securityWarning": m91,
    "securityWarning1": m92,
    "securityWarning2": m93,
    "seeAll": MessageLookupByLibrary.simpleMessage("See all"),
    "seeConnections": MessageLookupByLibrary.simpleMessage("See Connections"),
    "seeGuidelines": MessageLookupByLibrary.simpleMessage("See guidelines"),
    "seeMore": MessageLookupByLibrary.simpleMessage("See more"),
    "seeWhatsNew": MessageLookupByLibrary.simpleMessage("See what\'s new"),
    "seeWhereToViewWalletSecurity": MessageLookupByLibrary.simpleMessage(
      "See where to view wallet security",
    ),
    "seeWhy": MessageLookupByLibrary.simpleMessage("See why"),
    "seedPhrase": MessageLookupByLibrary.simpleMessage("Seed phrase"),
    "select": MessageLookupByLibrary.simpleMessage("Select"),
    "selectAnAddress": MessageLookupByLibrary.simpleMessage(
      "Select an address",
    ),
    "selectAsset": MessageLookupByLibrary.simpleMessage("Select Asset"),
    "selectAssetToFund": MessageLookupByLibrary.simpleMessage(
      "Select asset to fund",
    ),
    "selectAssetToSend": MessageLookupByLibrary.simpleMessage(
      "Select asset to send",
    ),
    "selectConvertAsset": m94,
    "selectCurrency": MessageLookupByLibrary.simpleMessage("Select currency"),
    "selectDocument": MessageLookupByLibrary.simpleMessage("Select Document"),
    "selectDocumentSubtitle": MessageLookupByLibrary.simpleMessage(
      "You’ll be required to upload this document",
    ),
    "selectDocumentTitle": MessageLookupByLibrary.simpleMessage(
      "Select document with proof of your address",
    ),
    "selectFundingMethod": MessageLookupByLibrary.simpleMessage(
      "Select funding method",
    ),
    "selectIdSubCopy": MessageLookupByLibrary.simpleMessage(
      "You’ll be required to take a clear picture of the ID",
    ),
    "selectIdToVerify": MessageLookupByLibrary.simpleMessage(
      "Select ID to verify",
    ),
    "selectIdentityCard": MessageLookupByLibrary.simpleMessage("Select ID"),
    "selectNetwork": MessageLookupByLibrary.simpleMessage("Select network"),
    "selectSuitableNetwork": MessageLookupByLibrary.simpleMessage(
      "Select suitable network",
    ),
    "selectToken": MessageLookupByLibrary.simpleMessage("Select Token"),
    "selectWalletToImport": MessageLookupByLibrary.simpleMessage(
      "Select wallet to Import",
    ),
    "selfCustodyWallet": MessageLookupByLibrary.simpleMessage(
      "Self-custody wallet",
    ),
    "selfCustodyWalletExamples": MessageLookupByLibrary.simpleMessage(
      "e.g. MetaMask, Trust, Onboard",
    ),
    "selfieKycTips": MessageLookupByLibrary.simpleMessage(
      "Selfie — well-lit, face showing clearly.",
    ),
    "sell": MessageLookupByLibrary.simpleMessage("Sell"),
    "sellAnyCrypto": MessageLookupByLibrary.simpleMessage("Sell any crypto"),
    "sellAnyCryptoTokenForCash": MessageLookupByLibrary.simpleMessage(
      "Sell any crypto token for cash",
    ),
    "sellAssetOrAnyCryptoTokenForFiat": m95,
    "sellCrypto": MessageLookupByLibrary.simpleMessage("Sell crypto"),
    "sellToReceiveCash": MessageLookupByLibrary.simpleMessage(
      "Sell to receive cash",
    ),
    "sellToReceiveCrypto": MessageLookupByLibrary.simpleMessage(
      "Sell to receive crypto",
    ),
    "selling": MessageLookupByLibrary.simpleMessage("Selling"),
    "send": MessageLookupByLibrary.simpleMessage("Send"),
    "sendAsCash": MessageLookupByLibrary.simpleMessage("Send as cash"),
    "sendAtLeast": MessageLookupByLibrary.simpleMessage("Send at least"),
    "sendCode": MessageLookupByLibrary.simpleMessage("Send code"),
    "sendDefiSwapWarning": m96,
    "sendExactly": MessageLookupByLibrary.simpleMessage("Send exactly"),
    "sendFrom": MessageLookupByLibrary.simpleMessage("Send from"),
    "sendFromAnyBankOrMobileMoneyWallet": MessageLookupByLibrary.simpleMessage(
      "Send from any bank or mobile money wallet",
    ),
    "sendFromCryptoWallet": MessageLookupByLibrary.simpleMessage(
      "Send from crypto wallet",
    ),
    "sendFunds": MessageLookupByLibrary.simpleMessage("Send Funds"),
    "sendFundsToYourLocalBanks": MessageLookupByLibrary.simpleMessage(
      "Send funds to your local banks",
    ),
    "sendInternationalPayments": MessageLookupByLibrary.simpleMessage(
      "Send international payments",
    ),
    "sendMoneyAbroad": MessageLookupByLibrary.simpleMessage(
      "Send money abroad",
    ),
    "sendMoneyGlobally": MessageLookupByLibrary.simpleMessage(
      "Send money globally",
    ),
    "sendOnly": MessageLookupByLibrary.simpleMessage("Send only"),
    "sendTo": MessageLookupByLibrary.simpleMessage("Send to"),
    "sendToACryptoWallet": MessageLookupByLibrary.simpleMessage(
      "Send to a crypto wallet",
    ),
    "sendToBankOrMobileMoneyWallet": MessageLookupByLibrary.simpleMessage(
      "Send to bank or mobile money wallet",
    ),
    "sendToExternalWallet": MessageLookupByLibrary.simpleMessage(
      "Send to external wallet",
    ),
    "sendToLocalCurrency": MessageLookupByLibrary.simpleMessage(
      "Send to Local currency",
    ),
    "sendToOnboardPay": MessageLookupByLibrary.simpleMessage(
      "Send to Onboard Pay",
    ),
    "sendToOtherWallets": MessageLookupByLibrary.simpleMessage(
      "Send to other wallets",
    ),
    "sendToUSDBalance": m97,
    "sendToWalletBalance": MessageLookupByLibrary.simpleMessage(
      "Send to Wallet Balance",
    ),
    "sendViaSms": MessageLookupByLibrary.simpleMessage("Send via SMS"),
    "sendViaWhatsapp": MessageLookupByLibrary.simpleMessage(
      "Send via WhatsApp",
    ),
    "sendWithAssetSymbol": m98,
    "sending": MessageLookupByLibrary.simpleMessage("Sending"),
    "sendingFromADifferentNetwork": MessageLookupByLibrary.simpleMessage(
      "Sending from a different network",
    ),
    "sendingNetworkDesc": MessageLookupByLibrary.simpleMessage(
      "This is the network where your assets will be sent out on.",
    ),
    "sendingWallet": MessageLookupByLibrary.simpleMessage("Sending wallet"),
    "sendingWalletDescription": MessageLookupByLibrary.simpleMessage(
      "This is the wallet where your assets will be sent out from",
    ),
    "sendingYourOrder": MessageLookupByLibrary.simpleMessage(
      "Sending your order...",
    ),
    "sent": MessageLookupByLibrary.simpleMessage("Sent"),
    "sepa": MessageLookupByLibrary.simpleMessage("SEPA"),
    "sessionEnded": MessageLookupByLibrary.simpleMessage("Session ended"),
    "sessionError": MessageLookupByLibrary.simpleMessage("Session error"),
    "sessionLockDesc": MessageLookupByLibrary.simpleMessage(
      "Your account is waiting securely! \nLogin below to continue",
    ),
    "setAPassCode": MessageLookupByLibrary.simpleMessage("Set a passcode"),
    "setAPasskey": MessageLookupByLibrary.simpleMessage("Set a passkey"),
    "setEmailSubCopy": MessageLookupByLibrary.simpleMessage(
      "Confirm that you\'ve entered the correct email and hit continue",
    ),
    "setPassKey": MessageLookupByLibrary.simpleMessage("Set Passkey"),
    "setPasscode": MessageLookupByLibrary.simpleMessage("Set passcode"),
    "setUpASelfCustodyWallet": MessageLookupByLibrary.simpleMessage(
      "Setup a self custody wallet",
    ),
    "setUpAndFees": MessageLookupByLibrary.simpleMessage("Setup and fees"),
    "setUpAndStartReceivingPaymentsWithin1BusinessDay":
        MessageLookupByLibrary.simpleMessage(
          "Setup & start receiving payments within \n1 business day",
        ),
    "setUpCard": MessageLookupByLibrary.simpleMessage("Set up card"),
    "setUpNewCard": MessageLookupByLibrary.simpleMessage("Set up new card"),
    "setUpPasscodeToSecureAccount": MessageLookupByLibrary.simpleMessage(
      "Setup passcode to secure account",
    ),
    "setUpPaymentReminders": MessageLookupByLibrary.simpleMessage(
      "Setup payment reminders",
    ),
    "setUpTime": MessageLookupByLibrary.simpleMessage("Setup time"),
    "setUpWallet": MessageLookupByLibrary.simpleMessage("Set up  wallet"),
    "setUsernameTobeUsedAcrossOnboard": MessageLookupByLibrary.simpleMessage(
      "Set a username to be used across Onboard",
    ),
    "settingUpYourCard": MessageLookupByLibrary.simpleMessage(
      "Setting up your card 🎉",
    ),
    "settingUpYourWallet": MessageLookupByLibrary.simpleMessage(
      "Setting up your wallet  🎉",
    ),
    "settings": MessageLookupByLibrary.simpleMessage("Settings"),
    "settlementTime": MessageLookupByLibrary.simpleMessage("Settlement time"),
    "setup": MessageLookupByLibrary.simpleMessage("Set Up"),
    "setupPin": MessageLookupByLibrary.simpleMessage("Set up PIN"),
    "setupPinSubtitle": MessageLookupByLibrary.simpleMessage(
      "This allows you transact more securely on your device",
    ),
    "setupSixDigitPin": MessageLookupByLibrary.simpleMessage(
      "Set up 6-digit PIN",
    ),
    "setupWallet": MessageLookupByLibrary.simpleMessage("Set up Wallet"),
    "setupWalletProtection": MessageLookupByLibrary.simpleMessage(
      "Set up wallet protection",
    ),
    "setupYourSixDigitPin": MessageLookupByLibrary.simpleMessage(
      "Setup your 6-digit PIN",
    ),
    "share": MessageLookupByLibrary.simpleMessage("Share"),
    "shareFeedback": MessageLookupByLibrary.simpleMessage("Share feedback"),
    "shareReferralLinkCopy": m99,
    "shopping": MessageLookupByLibrary.simpleMessage("Shopping"),
    "show": MessageLookupByLibrary.simpleMessage("Show"),
    "showLess": MessageLookupByLibrary.simpleMessage("Show less"),
    "showMe": MessageLookupByLibrary.simpleMessage("Show me!"),
    "showMeText": MessageLookupByLibrary.simpleMessage("Show me"),
    "showMore": MessageLookupByLibrary.simpleMessage("Show more"),
    "showPrivateKey": MessageLookupByLibrary.simpleMessage("Show Private Key"),
    "showRecoveryPhrase": MessageLookupByLibrary.simpleMessage(
      "Show Recovery Phrase",
    ),
    "sign": MessageLookupByLibrary.simpleMessage("Sign"),
    "signAll": MessageLookupByLibrary.simpleMessage("Sign all"),
    "signOne": MessageLookupByLibrary.simpleMessage("Sign one"),
    "signToAuthenticateYourWallet": MessageLookupByLibrary.simpleMessage(
      "Sign to authenticate your wallet.",
    ),
    "signToAuthorizeAction": MessageLookupByLibrary.simpleMessage(
      "Sign to authorize this action",
    ),
    "signToProcessTransaction": MessageLookupByLibrary.simpleMessage(
      "Sign to process transaction",
    ),
    "signTransaction": MessageLookupByLibrary.simpleMessage("Sign transaction"),
    "signUpWithYourLink": MessageLookupByLibrary.simpleMessage(
      "Signs up with your link",
    ),
    "signatureRequest": MessageLookupByLibrary.simpleMessage(
      "Signature Request",
    ),
    "simpleCryptoInvesting": MessageLookupByLibrary.simpleMessage(
      "Simple crypto investing",
    ),
    "sitTightThisShouldBeDoneIn15mins": MessageLookupByLibrary.simpleMessage(
      "Sit tight, this should be done within \n15 minutes",
    ),
    "siteConnections": MessageLookupByLibrary.simpleMessage("Site connections"),
    "siteConnectionsText": MessageLookupByLibrary.simpleMessage(
      "This enables Onboard wallet and other sites to securely connect and interact by scanning or copying their QR code.",
    ),
    "sixMonths": MessageLookupByLibrary.simpleMessage("6 months"),
    "skip": MessageLookupByLibrary.simpleMessage("Skip"),
    "skipAccountBackUpWarningSubCopy": MessageLookupByLibrary.simpleMessage(
      "Backing up your wallet helps you recover your wallet in the future if you ever need to. It\'s an essential security step.",
    ),
    "skipPassCodeNote": MessageLookupByLibrary.simpleMessage(
      "Skip passcode, use your fingerprint or face ID to access your account across your devices",
    ),
    "slideToReview": MessageLookupByLibrary.simpleMessage("Slide to review"),
    "slippage": MessageLookupByLibrary.simpleMessage("Slippage"),
    "slippageCannotBeMoreThan": m100,
    "sms": MessageLookupByLibrary.simpleMessage("SMS"),
    "sortBy": MessageLookupByLibrary.simpleMessage("Sort by"),
    "spamTokenHiddenUndoInSettings": MessageLookupByLibrary.simpleMessage(
      "Spam crypto hidden. Undo in settings",
    ),
    "specifyTheAmountOfTokensYouWantToSwap":
        MessageLookupByLibrary.simpleMessage(
          "Specify the amount of tokens you want to swap",
        ),
    "specifyTheTokenYoureSwappingFrom": MessageLookupByLibrary.simpleMessage(
      "Specify the token you\'re swapping from",
    ),
    "specifyTheTokenYoureSwappingTo": MessageLookupByLibrary.simpleMessage(
      "Specify the token you\'re swapping to",
    ),
    "spendAnywhere": MessageLookupByLibrary.simpleMessage("SPEND\nANYWHERE"),
    "spendDollarAnywhere": MessageLookupByLibrary.simpleMessage(
      "Spend digital dollars anywhere with virtual cards",
    ),
    "spendGloballyWithUsdCard": MessageLookupByLibrary.simpleMessage(
      "Spend globally with USD card",
    ),
    "spendYourDollarsAnywhere": MessageLookupByLibrary.simpleMessage(
      "Spend your dollars\nanywhere",
    ),
    "spendYourDollarsAnywhereSubCopy": MessageLookupByLibrary.simpleMessage(
      "Fund your card anytime using your local currency or crypto, at the best possible rates.",
    ),
    "spot": MessageLookupByLibrary.simpleMessage("Spot"),
    "spotWallet": MessageLookupByLibrary.simpleMessage("Spot Wallet"),
    "spotWalletIntro": MessageLookupByLibrary.simpleMessage(
      "Holds all your assets",
    ),
    "start": MessageLookupByLibrary.simpleMessage("Start"),
    "startBridgeKycSubCopy": MessageLookupByLibrary.simpleMessage(
      "We need a little information to get started. This will help us secure your account and protect you from fraud.",
    ),
    "startByBuying": MessageLookupByLibrary.simpleMessage(
      "Start by buying crypto for \nas little as \$1",
    ),
    "startByFundingYourPortfolioWithAsLittleAsOneDollar":
        MessageLookupByLibrary.simpleMessage(
          "Start by funding your portfolio with as little as \$1",
        ),
    "startIdentityVerificationSubCopy": MessageLookupByLibrary.simpleMessage(
      "We need a little information to get\nstarted. This will help us secure your\ncard and protect you from fraud.",
    ),
    "startIdentityVerificationTitle": MessageLookupByLibrary.simpleMessage(
      "To use cards, let’s verify\nyour identity",
    ),
    "startKyc": MessageLookupByLibrary.simpleMessage("Start KYC"),
    "startKycFailedCopy": MessageLookupByLibrary.simpleMessage(
      "We apologize for the failed KYC verification and any inconvenience it may have caused during your onboarding process.",
    ),
    "startKycSubCopy": MessageLookupByLibrary.simpleMessage(
      "This will establish your identity, and prevents someone else from claiming your account.",
    ),
    "startKycTitle": MessageLookupByLibrary.simpleMessage(
      "Now, let’s verify your\nPhoto ID",
    ),
    "startMakingProfitOnOnboard": MessageLookupByLibrary.simpleMessage(
      "Start making profit on Onboard",
    ),
    "startSavingInDollars": MessageLookupByLibrary.simpleMessage(
      "Start saving in dollars",
    ),
    "startTrading": MessageLookupByLibrary.simpleMessage("Start trading"),
    "startTradingToday": MessageLookupByLibrary.simpleMessage(
      "Start trading today",
    ),
    "startTradingTodaySubtitle": MessageLookupByLibrary.simpleMessage(
      "Fund with USDT using P2P or from an external wallet",
    ),
    "startedOn": MessageLookupByLibrary.simpleMessage("Started on"),
    "state": MessageLookupByLibrary.simpleMessage("State"),
    "stateIsRequired": MessageLookupByLibrary.simpleMessage(
      "State is required",
    ),
    "stateOrRegion": MessageLookupByLibrary.simpleMessage("State/Region"),
    "statement": MessageLookupByLibrary.simpleMessage("Statement"),
    "stayOnTopOfYourSubscriptionsWhenYouSetUpPaymentReminders":
        MessageLookupByLibrary.simpleMessage(
          "Stay on top of your subscriptions when you set up payment reminders",
        ),
    "stepCloserToGettingCard": MessageLookupByLibrary.simpleMessage(
      "You\'re one step closer to getting your first Onboard debit card!",
    ),
    "stepsNeeded": MessageLookupByLibrary.simpleMessage("Steps needed"),
    "street": MessageLookupByLibrary.simpleMessage("Street"),
    "submit": MessageLookupByLibrary.simpleMessage("Submit"),
    "submittedTransactionStatusMessage": MessageLookupByLibrary.simpleMessage(
      "You\'ve signed this transaction and it has been submitted to the blockchain. We\'ll notify you once completed.",
    ),
    "submittingKycJob": MessageLookupByLibrary.simpleMessage(
      "Submitting your photo ID...",
    ),
    "subscriptionDetails": MessageLookupByLibrary.simpleMessage(
      "Subscription details",
    ),
    "subscriptionUnmarked": MessageLookupByLibrary.simpleMessage(
      "Subscription unmarked",
    ),
    "subscriptions": MessageLookupByLibrary.simpleMessage("Subscriptions"),
    "success": MessageLookupByLibrary.simpleMessage("Success"),
    "successful": MessageLookupByLibrary.simpleMessage("Successful"),
    "superChargeYourCryptoPortfolio": MessageLookupByLibrary.simpleMessage(
      "Supercharge your crypto portfolio 📈",
    ),
    "superChargeYourCryptoPortfolioSubCopy": MessageLookupByLibrary.simpleMessage(
      "You can now buy and sell any\ncrypto asset and keep track of\nprice changes over time",
    ),
    "superiorExchangeRate": MessageLookupByLibrary.simpleMessage(
      "Superior exchange rates",
    ),
    "swap": MessageLookupByLibrary.simpleMessage("Swap"),
    "swapFeeCopy": MessageLookupByLibrary.simpleMessage(
      "To process this transaction, a small network\n fee will be charged.",
    ),
    "swapGenericErrorMessage": MessageLookupByLibrary.simpleMessage(
      "We\'re unable to complete this swap. Please retry with a different amount, or contact support",
    ),
    "swapInsufficientBalanceError": m101,
    "swapSuccessful": MessageLookupByLibrary.simpleMessage("Swap successful"),
    "swapTokens": MessageLookupByLibrary.simpleMessage("Swap Tokens"),
    "swapped": MessageLookupByLibrary.simpleMessage("Swapped"),
    "swapping": MessageLookupByLibrary.simpleMessage("Swapping"),
    "switchBetweenAccount": MessageLookupByLibrary.simpleMessage(
      "Switch between accounts",
    ),
    "switchFromDigitalToRealWorldMoneyAndBack":
        MessageLookupByLibrary.simpleMessage(
          "Switch from digital to real-world money, and back",
        ),
    "switchNetwork": MessageLookupByLibrary.simpleMessage("Switch Network"),
    "switchNetworkSubCopy": MessageLookupByLibrary.simpleMessage(
      "Switch between various crypto networks to view your tokens held on each network",
    ),
    "switchNetworkSubtitle": MessageLookupByLibrary.simpleMessage(
      "This will switch the network within Onboard wallet to a previously added network",
    ),
    "switchNetworkTitle": MessageLookupByLibrary.simpleMessage(
      "Onboard Wallet wants to change the network",
    ),
    "switchText": MessageLookupByLibrary.simpleMessage("Switch®"),
    "switchToExchangeSupportedNetwork": MessageLookupByLibrary.simpleMessage(
      "Switch between supported crypto networks to trade",
    ),
    "switchVerificationMethod": MessageLookupByLibrary.simpleMessage(
      "Switch to another verification method",
    ),
    "switchedToNetwork": m102,
    "symbol": MessageLookupByLibrary.simpleMessage("Symbol"),
    "tab": MessageLookupByLibrary.simpleMessage("Tab"),
    "tabs": MessageLookupByLibrary.simpleMessage("Tabs"),
    "takeAQuickTourOfWhatsChangedInTheApp":
        MessageLookupByLibrary.simpleMessage(
          "Take a quick tour of what\'s changed\nin the app.",
        ),
    "takePhoto": MessageLookupByLibrary.simpleMessage("Take Photo"),
    "takes": MessageLookupByLibrary.simpleMessage("Takes"),
    "takes1to2Days": MessageLookupByLibrary.simpleMessage("Takes 1-2 days*"),
    "takingTooLong": MessageLookupByLibrary.simpleMessage("Taking too long?"),
    "tapBelowToSubmitRequiredInfo": MessageLookupByLibrary.simpleMessage(
      "Tap below to view and submit the required information",
    ),
    "tapButtonBelowToGetStartedOnRecovery":
        MessageLookupByLibrary.simpleMessage(
          "Tap button below to get started on wallet recovery",
        ),
    "tapHereForQuickAccess": MessageLookupByLibrary.simpleMessage(
      "Tap here for quick access to your accounts or to open a new one",
    ),
    "tapHereToDismiss": MessageLookupByLibrary.simpleMessage(
      "Tap here to dismiss",
    ),
    "tapIntoAnyOfTheseCategoriesToLearnMoreAboutThem":
        MessageLookupByLibrary.simpleMessage(
          "Tap into any of these categories to discover and learn more about them",
        ),
    "tapTheNew": MessageLookupByLibrary.simpleMessage("Tap the new "),
    "tapToActivateAccount": MessageLookupByLibrary.simpleMessage(
      "Tap to activate account",
    ),
    "tapToActivateCard": MessageLookupByLibrary.simpleMessage(
      "Tap to activate card",
    ),
    "tapToReview": MessageLookupByLibrary.simpleMessage("Tap to review"),
    "tapToSelectNetwork": MessageLookupByLibrary.simpleMessage(
      "Tap to select a network",
    ),
    "tapToToggleCurrency": MessageLookupByLibrary.simpleMessage(
      "Tap to toggle currency",
    ),
    "tapToUnlockFullFunctionality": MessageLookupByLibrary.simpleMessage(
      "Tap to unlock full app functionality  →",
    ),
    "tapToViewDetails": MessageLookupByLibrary.simpleMessage(
      "Tap to view details",
    ),
    "tapToViewMoreDetails": MessageLookupByLibrary.simpleMessage(
      "Tap to view more details",
    ),
    "tellUsABitAboutYou": MessageLookupByLibrary.simpleMessage(
      "Tell us a bit about you",
    ),
    "tellUsAboutYou": MessageLookupByLibrary.simpleMessage("Tell us about you"),
    "tellUsWhatYoudLikeToSee": MessageLookupByLibrary.simpleMessage(
      "Tell us what you\'d like to see!",
    ),
    "tellUsWhichOfYourPaymentsOccurOnARecurringBasisAndWellNotifyYouBeforeTheyComeDue":
        MessageLookupByLibrary.simpleMessage(
          "Tell us which of your card payments occur on a recurring basis and we\'ll notify you before they come due.",
        ),
    "termDot": MessageLookupByLibrary.simpleMessage("terms."),
    "terminated": MessageLookupByLibrary.simpleMessage("Terminated"),
    "termsOfService": MessageLookupByLibrary.simpleMessage("Terms of Service"),
    "termsOfUse": MessageLookupByLibrary.simpleMessage("Terms of Use"),
    "thanksForSharing": MessageLookupByLibrary.simpleMessage(
      "Thanks for sharing",
    ),
    "theirTermsOfService": MessageLookupByLibrary.simpleMessage(
      "their Terms of \nService.",
    ),
    "thereAreNoAssetsToDisplay": MessageLookupByLibrary.simpleMessage(
      "There are no assets to display",
    ),
    "thereforeACrossBorderFeeWillBeCharged":
        MessageLookupByLibrary.simpleMessage(
          "Therefore, a cross-border fee will be charged separately.",
        ),
    "thirdPartyDeposits": MessageLookupByLibrary.simpleMessage(
      "3rd party deposits",
    ),
    "thirdPartyService": MessageLookupByLibrary.simpleMessage(
      "3rd party service",
    ),
    "thisCardIsFrozen": MessageLookupByLibrary.simpleMessage(
      "This card is frozen!",
    ),
    "thisKeepsYourCardAboveTheMinimumBalanceRequired": m103,
    "thisPercentageIsCalledTheMaximumSlippage":
        MessageLookupByLibrary.simpleMessage(
          "This percentage is called the maximum slippage ",
        ),
    "thisPercentageIsTheMaximumSlippage": MessageLookupByLibrary.simpleMessage(
      "\nThis percentage is the maximum slippage",
    ),
    "thisServiceIsProvidedBy": MessageLookupByLibrary.simpleMessage(
      "This service is provided by",
    ),
    "thisSwapFailedTryIncreasingYourSlippage":
        MessageLookupByLibrary.simpleMessage(
          "This swap failed. Try increasing your slippage",
        ),
    "thisTransactionIsBeingProcessedByChargedByAmerchantOutsideTheUs":
        MessageLookupByLibrary.simpleMessage(
          "This transaction is being processed by the merchant, outside the US",
        ),
    "thisTransactionWasInitiatedByTheMerchantIn":
        MessageLookupByLibrary.simpleMessage(
          "This transaction was initiated by the merchant in",
        ),
    "thisWalletIsInWatchModeSubCopy": MessageLookupByLibrary.simpleMessage(
      "This wallet is in watch mode. Recover wallet to perform transactions",
    ),
    "thisWasCausedByMultipleFailedTransactionsOnMyCardDueToInsufficientFunds":
        MessageLookupByLibrary.simpleMessage(
          "This was caused by multiple failed transactions on my card due to insufficient funds",
        ),
    "thisWillBeAddedToYourOnboardWalletBalance":
        MessageLookupByLibrary.simpleMessage(
          "This will be added to your Onboard Wallet balance",
        ),
    "threeMonths": MessageLookupByLibrary.simpleMessage("3 months"),
    "to": MessageLookupByLibrary.simpleMessage("To"),
    "toAccessGlobalPayment": MessageLookupByLibrary.simpleMessage(
      "To access global payments",
    ),
    "toAnyCashAccount": MessageLookupByLibrary.simpleMessage(
      "To any cash account",
    ),
    "toAnyCryptoWallet": MessageLookupByLibrary.simpleMessage(
      "To any crypto wallet",
    ),
    "toCompleteThisTransactionSignTheMessageBelow":
        MessageLookupByLibrary.simpleMessage(
          "To complete this transaction, sign the message below",
        ),
    "toCreateAUSDAccountReviewAndAcceptTheseUsageTerms": m104,
    "toEnsureUpcomingPaymentsGoThrough": MessageLookupByLibrary.simpleMessage(
      "to ensure upcoming payments go through",
    ),
    "toGetAUsAccountLetsVerifyYourIdentity":
        MessageLookupByLibrary.simpleMessage(
          "To get a US account, let’s verify your identity",
        ),
    "toGetStartedSetupYOurDefiWallet": MessageLookupByLibrary.simpleMessage(
      "To get started, setup your DeFi wallet",
    ),
    "toGetUSDAccount": MessageLookupByLibrary.simpleMessage(
      "To get a USD account",
    ),
    "toGetUSDCard": MessageLookupByLibrary.simpleMessage("To get a USD card"),
    "toKeepOnboardSecureAndCompliantWeRequireKycForCashTx":
        MessageLookupByLibrary.simpleMessage(
          "To keep Onboard secure and compliant, we require verified KYC for transactions involving cash.",
        ),
    "toKickstartYourCrypto": MessageLookupByLibrary.simpleMessage(
      "To kickstart your crypto journey, please answer a few short questions. It’ll take less than 1 minute.",
    ),
    "toStartSending": MessageLookupByLibrary.simpleMessage("To start sending"),
    "toStartTrading": MessageLookupByLibrary.simpleMessage("To start trading"),
    "toThisDepositAddressToAvoidLosingYourFunds":
        MessageLookupByLibrary.simpleMessage(
          "to this deposit address to avoid losing your funds",
        ),
    "today": MessageLookupByLibrary.simpleMessage("Today"),
    "tokenApprovedCompleteYourSwap": MessageLookupByLibrary.simpleMessage(
      "Token approved. Complete your swap",
    ),
    "tokenHasLowReputation": MessageLookupByLibrary.simpleMessage(
      "This token has a low reputation",
    ),
    "tokenHidden": MessageLookupByLibrary.simpleMessage("Token hidden!"),
    "tokenHiddenSubCopy": MessageLookupByLibrary.simpleMessage(
      "It will no longer show up on your asset list. \nWant to hide all tokens like this? \nVisit Settings > Preferences > Manage tokens ",
    ),
    "tokenIsHidden": m105,
    "tokenIsNowHidden": MessageLookupByLibrary.simpleMessage(
      "Token is now hidden",
    ),
    "tokenIsVisible": m106,
    "tokenTransfer": MessageLookupByLibrary.simpleMessage("Token Transfer"),
    "tooManyAttemptsTryAgainLater": MessageLookupByLibrary.simpleMessage(
      "Too many attempts. Try again later",
    ),
    "top": MessageLookupByLibrary.simpleMessage("Top"),
    "total": MessageLookupByLibrary.simpleMessage("Total"),
    "totalAmount": MessageLookupByLibrary.simpleMessage("Total Amount"),
    "totalBalance": MessageLookupByLibrary.simpleMessage("Total Balance"),
    "totalConverted": MessageLookupByLibrary.simpleMessage("Total converted"),
    "totalWeConvert": MessageLookupByLibrary.simpleMessage(
      "Total we’ll convert",
    ),
    "totalWellConvert": MessageLookupByLibrary.simpleMessage(
      "Total we’ll convert",
    ),
    "trade": MessageLookupByLibrary.simpleMessage("Trade"),
    "tradeAndManageAssets": MessageLookupByLibrary.simpleMessage(
      "Trade and Manage Assets",
    ),
    "tradeCrypto": MessageLookupByLibrary.simpleMessage("Trade crypto"),
    "tradeCryptoOnAMarketPlace": MessageLookupByLibrary.simpleMessage(
      "Trade crypto on a marketplace",
    ),
    "tradeDigitalAssets": MessageLookupByLibrary.simpleMessage(
      "Trade digital assets",
    ),
    "tradeDigitalAssetsDesc": MessageLookupByLibrary.simpleMessage(
      "Deposit and swap BTC, SOL and more",
    ),
    "tradeIntroHeader": MessageLookupByLibrary.simpleMessage(
      "Buy, Sell and Swap Crypto with Ease",
    ),
    "tradeIntroSubCopy": MessageLookupByLibrary.simpleMessage(
      "With Onboard Exchange®, we make it easy to go from crypto to your local currency, and back",
    ),
    "tradeNotAvailable": MessageLookupByLibrary.simpleMessage(
      "Trade not available",
    ),
    "tradeToken": m107,
    "trading": MessageLookupByLibrary.simpleMessage("Trading"),
    "tradingAccount": MessageLookupByLibrary.simpleMessage("Trading account"),
    "tradingBalance": MessageLookupByLibrary.simpleMessage("Trading balance"),
    "tradingWallet": MessageLookupByLibrary.simpleMessage("Trading Wallet"),
    "tradingWalletCreated": MessageLookupByLibrary.simpleMessage(
      "Merchant wallet created!",
    ),
    "tradingWalletCreationError": MessageLookupByLibrary.simpleMessage(
      "Something went wrong.\\nBut not to worry, please retry",
    ),
    "tradingWalletIntro": MessageLookupByLibrary.simpleMessage(
      "Start high volume trades",
    ),
    "tradingWalletNotAllowedForExternalSend": MessageLookupByLibrary.simpleMessage(
      "For extra security, your trading wallet cannot be used for transfers to external wallets.",
    ),
    "transaction": MessageLookupByLibrary.simpleMessage("Transaction"),
    "transactionBandSubtitle": MessageLookupByLibrary.simpleMessage(
      "How much do you intend to send and/or receive with this account monthly?",
    ),
    "transactionCancelled": MessageLookupByLibrary.simpleMessage(
      "Transaction Cancelled",
    ),
    "transactionCompleted": MessageLookupByLibrary.simpleMessage(
      "Transaction completed",
    ),
    "transactionFailed": MessageLookupByLibrary.simpleMessage(
      "Transaction failed",
    ),
    "transactionFailedTryAgain": MessageLookupByLibrary.simpleMessage(
      "Transaction Failed, try again!",
    ),
    "transactionFee": MessageLookupByLibrary.simpleMessage("Transaction Fee"),
    "transactionFeeCopy": MessageLookupByLibrary.simpleMessage(
      " Our payment partners may charge a small fee to process this transaction\nThe receiving amount is only an estimate.",
    ),
    "transactionId": MessageLookupByLibrary.simpleMessage("Transaction ID"),
    "transactionIdCopied": MessageLookupByLibrary.simpleMessage(
      "Transaction ID copied!",
    ),
    "transactionLimit": MessageLookupByLibrary.simpleMessage(
      "Transaction limits",
    ),
    "transactionOrder": MessageLookupByLibrary.simpleMessage(
      "Transaction Order",
    ),
    "transactionPin": MessageLookupByLibrary.simpleMessage("Transaction Pin"),
    "transactionSent": MessageLookupByLibrary.simpleMessage("Transaction sent"),
    "transactionSentSubtitle": MessageLookupByLibrary.simpleMessage(
      "We\'ll notify you when this transaction is completed",
    ),
    "transactionSentTitle": MessageLookupByLibrary.simpleMessage(
      "Transaction sent",
    ),
    "transactionSuccessful": MessageLookupByLibrary.simpleMessage(
      "Transaction Successful",
    ),
    "transactionType": MessageLookupByLibrary.simpleMessage("Transaction type"),
    "transactions": MessageLookupByLibrary.simpleMessage("Transactions"),
    "transfer": MessageLookupByLibrary.simpleMessage("Transfer"),
    "transferAmount": MessageLookupByLibrary.simpleMessage("Transfer amount"),
    "transferAssetsTitle": MessageLookupByLibrary.simpleMessage(
      "How would you like to transfer assets?",
    ),
    "transferBetweenOnboardWallet": MessageLookupByLibrary.simpleMessage(
      "Transfer between wallets on Onboard",
    ),
    "transferCryptoGlobally": MessageLookupByLibrary.simpleMessage(
      "Transfer crypto globally, swiftly converted to EUR or USD",
    ),
    "transferCryptoToOtherWallet": MessageLookupByLibrary.simpleMessage(
      "Transfer crypto to other wallets",
    ),
    "transferDirectlyToYourBankAccount": MessageLookupByLibrary.simpleMessage(
      "Transfer directly to your bank account",
    ),
    "transferTokenToExternalWallet": MessageLookupByLibrary.simpleMessage(
      "Transfer crypto to other wallets",
    ),
    "transferWarning": MessageLookupByLibrary.simpleMessage(
      "Ensure that the address is correct and on the same network. Transactions cannot be reversed",
    ),
    "transferWithTokenSymbol": m108,
    "travel": MessageLookupByLibrary.simpleMessage("Travel"),
    "tryAgain": MessageLookupByLibrary.simpleMessage("Try again"),
    "tryAnotherMethod": MessageLookupByLibrary.simpleMessage(
      "Try another method",
    ),
    "tryExtraBackup": MessageLookupByLibrary.simpleMessage(
      "Try other backup options",
    ),
    "tryOtherBackupOption": MessageLookupByLibrary.simpleMessage(
      "Or try other backup options",
    ),
    "turnOnNotification": MessageLookupByLibrary.simpleMessage(
      "Turn on notification",
    ),
    "turnOnNotifications": MessageLookupByLibrary.simpleMessage(
      "Turn on notifications",
    ),
    "twelveMonths": MessageLookupByLibrary.simpleMessage("12 months"),
    "twentyFourHourVolume": MessageLookupByLibrary.simpleMessage("24h Volume"),
    "twitter": MessageLookupByLibrary.simpleMessage("twitter"),
    "twoDaysBefore": MessageLookupByLibrary.simpleMessage("2 Days before"),
    "typeManually": MessageLookupByLibrary.simpleMessage("Type manually"),
    "uKycIdTip": MessageLookupByLibrary.simpleMessage(
      "Your ID — Upload a clear, untampered version",
    ),
    "unHideCollection": MessageLookupByLibrary.simpleMessage(
      "Unhide collection",
    ),
    "unMarkSubscription": MessageLookupByLibrary.simpleMessage(
      "Unmark subscription",
    ),
    "unPaidFeesMayLeadToYourCardBeingDeactivated":
        MessageLookupByLibrary.simpleMessage(
          "Unpaid fees may lead to your card being deactivated",
        ),
    "unableToBackupOnIcloud": MessageLookupByLibrary.simpleMessage(
      "Your wallet is unable to backup to iCloud",
    ),
    "unableToGetAssetDetails": MessageLookupByLibrary.simpleMessage(
      "Unable to get asset details",
    ),
    "unableToGetFee": MessageLookupByLibrary.simpleMessage("Unable to get fee"),
    "unableToGetPaymentMethod": MessageLookupByLibrary.simpleMessage(
      "Unable to get payment method",
    ),
    "unableToGetToken": MessageLookupByLibrary.simpleMessage(
      "Unable to get token",
    ),
    "unableToRecognizeAddressErrorMessage": MessageLookupByLibrary.simpleMessage(
      "\'We aren’t able to fetch details for the contract address entered. Please pre-fill manually\'",
    ),
    "undo": MessageLookupByLibrary.simpleMessage("Undo"),
    "unfreeze": MessageLookupByLibrary.simpleMessage("Unfreeze"),
    "unhideNFT": MessageLookupByLibrary.simpleMessage("Unhide NFT"),
    "unhideNftDescription": MessageLookupByLibrary.simpleMessage(
      "It will now show up on your portfolio list.",
    ),
    "unitedSatesDollars": MessageLookupByLibrary.simpleMessage(
      "United Stated Dollars",
    ),
    "universalAddress": MessageLookupByLibrary.simpleMessage(
      "UNIVERSAL ADDRESS",
    ),
    "universalAddressExplainer": MessageLookupByLibrary.simpleMessage(
      "A universal address is a single address that can receive all tokens that match its network. Think of it like an ‘account number’ for receiving funds. \nEg: Your Onboard address",
    ),
    "universalAddresses": MessageLookupByLibrary.simpleMessage(
      "For Universal addresses. ",
    ),
    "unknownMethod": MessageLookupByLibrary.simpleMessage("unknown method"),
    "unlock": MessageLookupByLibrary.simpleMessage("Unlock"),
    "unlockFullAppFunctionality": MessageLookupByLibrary.simpleMessage(
      "Unlock full app functionality",
    ),
    "unlockToGainAccess": MessageLookupByLibrary.simpleMessage(
      "Unlock wallet to gain access to the app",
    ),
    "unlockWallet": MessageLookupByLibrary.simpleMessage("Unlock wallet"),
    "unlockWithFaceId": MessageLookupByLibrary.simpleMessage(
      "Unlock with Face ID",
    ),
    "unlockWithFingerPrint": MessageLookupByLibrary.simpleMessage(
      "Unlock with Finger print",
    ),
    "unlockWithPasscode": MessageLookupByLibrary.simpleMessage(
      "Unlock with Passcode",
    ),
    "unmarkSubscriptionSubCopy": MessageLookupByLibrary.simpleMessage(
      "This will only be removed from your Onboard subscription list. It won\'t cancel any future payments to the merchant",
    ),
    "unsupportedNetworkSubCopy": MessageLookupByLibrary.simpleMessage(
      "To buy assets, please switch to a supported network.",
    ),
    "unsupportedTradingNetwork": MessageLookupByLibrary.simpleMessage(
      "To start trading and managing ads, please switch to a supported network",
    ),
    "upcomingPayments": MessageLookupByLibrary.simpleMessage(
      "Upcoming payments",
    ),
    "updateAmount": MessageLookupByLibrary.simpleMessage("Update amount"),
    "updateApp": MessageLookupByLibrary.simpleMessage("Update App"),
    "updateYourPasscodeAnytime": MessageLookupByLibrary.simpleMessage(
      "Update your passcode anytime",
    ),
    "uploadDoc": MessageLookupByLibrary.simpleMessage("Upload doc"),
    "uploadProofOfDocument": MessageLookupByLibrary.simpleMessage(
      "Upload proof of address",
    ),
    "uploading": MessageLookupByLibrary.simpleMessage("Uploading"),
    "usAccount": MessageLookupByLibrary.simpleMessage("US Account"),
    "usd": MessageLookupByLibrary.simpleMessage("USD"),
    "usdAccount": MessageLookupByLibrary.simpleMessage("USD account"),
    "usdAccountGets": m109,
    "usdBalance": MessageLookupByLibrary.simpleMessage("USD Balance"),
    "usdVirtualAccount": MessageLookupByLibrary.simpleMessage(
      "USD Virtual card",
    ),
    "usdcEqualsUSd": m110,
    "usdcIsUsRegulated": m111,
    "usdcRegulationSubCopy": m112,
    "useDifferentDoc": MessageLookupByLibrary.simpleMessage(
      "Use a different doc",
    ),
    "useDifferentWallet": MessageLookupByLibrary.simpleMessage(
      "Use a different wallet?",
    ),
    "username": MessageLookupByLibrary.simpleMessage("Username"),
    "usernameTaken": MessageLookupByLibrary.simpleMessage(
      "This username has already been taken",
    ),
    "usernameValidationError": MessageLookupByLibrary.simpleMessage(
      "Must be at least 5 characters",
    ),
    "vaFunding": MessageLookupByLibrary.simpleMessage(
      "\$6 minimum. Only ACH transfers. ",
    ),
    "vacation": MessageLookupByLibrary.simpleMessage("Vacation"),
    "validationFailedIncorrectOtp": MessageLookupByLibrary.simpleMessage(
      "failed, Incorrect otp",
    ),
    "validationFailedIncorrectPin": MessageLookupByLibrary.simpleMessage(
      "Oops! Incorrect pin",
    ),
    "value": MessageLookupByLibrary.simpleMessage("Value"),
    "verificationAttemptLeft": m113,
    "verificationCancelled": MessageLookupByLibrary.simpleMessage(
      "Verification cancelled",
    ),
    "verificationFailed": MessageLookupByLibrary.simpleMessage(
      "Verification failed",
    ),
    "verificationIsInProgress": MessageLookupByLibrary.simpleMessage(
      "Verification is in progress",
    ),
    "verificationOngoingSubtitle": MessageLookupByLibrary.simpleMessage(
      "Verification ongoing, completes in 24hrs",
    ),
    "verified": MessageLookupByLibrary.simpleMessage("Verified"),
    "verifiedAddressNotCorrect": MessageLookupByLibrary.simpleMessage(
      "The verified address on this account doesn\'t match backup address",
    ),
    "verifiesIdentity": MessageLookupByLibrary.simpleMessage(
      "Verifies their identity",
    ),
    "verify": MessageLookupByLibrary.simpleMessage("Verify"),
    "verifyBackupStatus": MessageLookupByLibrary.simpleMessage(
      "Verify your backup status",
    ),
    "verifyIdToStartTrading": MessageLookupByLibrary.simpleMessage(
      "Verify ID to start trading",
    ),
    "verifyMeAgain": MessageLookupByLibrary.simpleMessage("Verify me again"),
    "verifyPhoneNumberToSecureAccount": MessageLookupByLibrary.simpleMessage(
      "We’ll verify this number to secure your account.",
    ),
    "verifyTransaction": MessageLookupByLibrary.simpleMessage(
      "Verify Transaction",
    ),
    "verifyWith": m114,
    "verifyWithAuthenticatorApp": MessageLookupByLibrary.simpleMessage(
      "Verify with authenticatorApp",
    ),
    "verifyWithDefiWallet": MessageLookupByLibrary.simpleMessage(
      "Verify with Defi Wallet",
    ),
    "verifyWithPasscode": MessageLookupByLibrary.simpleMessage(
      "Verify with passcode",
    ),
    "verifyWithPasskey": MessageLookupByLibrary.simpleMessage(
      "Verify with passkey",
    ),
    "verifyWithPasskeyDesc": MessageLookupByLibrary.simpleMessage(
      "Verify using your fingerprint, face scan, PIN or pattern on your device.",
    ),
    "verifying": MessageLookupByLibrary.simpleMessage("Verifying"),
    "verifyingOtp": MessageLookupByLibrary.simpleMessage("Verifying otp"),
    "verifyingYourOnboardWallet": MessageLookupByLibrary.simpleMessage(
      "Verifying your Onboard Wallet ",
    ),
    "veryAggressive": MessageLookupByLibrary.simpleMessage("Very Aggressive"),
    "viaSMS": MessageLookupByLibrary.simpleMessage("Via SMS"),
    "viaSms": MessageLookupByLibrary.simpleMessage("Via SMS"),
    "viaWhatsApp": MessageLookupByLibrary.simpleMessage("Via WhatsApp"),
    "viaWhatsapp": MessageLookupByLibrary.simpleMessage("Via WhatsApp"),
    "viewAd": MessageLookupByLibrary.simpleMessage("View ad"),
    "viewAll": MessageLookupByLibrary.simpleMessage("View all"),
    "viewBroadcast": MessageLookupByLibrary.simpleMessage("View broadcast"),
    "viewCardUsageTerms": MessageLookupByLibrary.simpleMessage(
      "View card usage terms",
    ),
    "viewCredentialsWarning": m115,
    "viewDetails": MessageLookupByLibrary.simpleMessage("View details"),
    "viewLimits": MessageLookupByLibrary.simpleMessage("View limits"),
    "viewMessage": MessageLookupByLibrary.simpleMessage("View message"),
    "viewNetwork": MessageLookupByLibrary.simpleMessage("View network"),
    "viewOffer": MessageLookupByLibrary.simpleMessage("View offer"),
    "viewOrder": MessageLookupByLibrary.simpleMessage("View order"),
    "viewRequest": MessageLookupByLibrary.simpleMessage("View request"),
    "viewTransaction": MessageLookupByLibrary.simpleMessage("View transaction"),
    "viewWalletAddressToSendCrypto": MessageLookupByLibrary.simpleMessage(
      "View wallet address to send crypto ",
    ),
    "viewYourAccounts": MessageLookupByLibrary.simpleMessage(
      "View your accounts",
    ),
    "vipAccess": MessageLookupByLibrary.simpleMessage("VIP Access"),
    "vipAccessDesc": MessageLookupByLibrary.simpleMessage(
      "All of the above + premium benefits",
    ),
    "virtualAccount": MessageLookupByLibrary.simpleMessage("Virtual account"),
    "virtualAccountAddressCopied": MessageLookupByLibrary.simpleMessage(
      "Virtual account address copied",
    ),
    "virtualCard": MessageLookupByLibrary.simpleMessage("Virtual card"),
    "visaVirtual": MessageLookupByLibrary.simpleMessage("Visa virtual"),
    "visitWebsite": MessageLookupByLibrary.simpleMessage("Visit website"),
    "wallet": MessageLookupByLibrary.simpleMessage("Wallet"),
    "walletAddress": MessageLookupByLibrary.simpleMessage("Wallet Address"),
    "walletAuthorizationMessage": MessageLookupByLibrary.simpleMessage(
      "Please sign to confirm authorization.\nYour signature proves that you are the rightful owner of this DeFi wallet.",
    ),
    "walletBackup": MessageLookupByLibrary.simpleMessage("Wallet backup"),
    "walletBalance": MessageLookupByLibrary.simpleMessage("Wallet balance"),
    "walletConfirmed": MessageLookupByLibrary.simpleMessage(
      "Wallet confirmed!",
    ),
    "walletConfirmedSubCopy": MessageLookupByLibrary.simpleMessage(
      "Yes, you’re the proud owner of this wallet address.",
    ),
    "walletConfirmedSubCopy2": MessageLookupByLibrary.simpleMessage(
      "Think of it as an ‘account number’ for receiving funds ",
    ),
    "walletConnect": MessageLookupByLibrary.simpleMessage("WalletConnect"),
    "walletConnectWarning": MessageLookupByLibrary.simpleMessage(
      "This connection will view your wallet balance, activity and request approval for transactions",
    ),
    "walletConnected": MessageLookupByLibrary.simpleMessage("Wallet connected"),
    "walletCreated": MessageLookupByLibrary.simpleMessage("Wallet created!"),
    "walletCreatedMessage": MessageLookupByLibrary.simpleMessage(
      "You\'re all set to create ads. Start by funding your wallet.",
    ),
    "walletCreatedSubCopy": MessageLookupByLibrary.simpleMessage(
      "Here\'s your Onboard wallet address. Think of it like an \'account number\' for receiving funds.",
    ),
    "walletDetails": MessageLookupByLibrary.simpleMessage("Wallet details"),
    "walletDisconnected": MessageLookupByLibrary.simpleMessage(
      "Wallet disconnected",
    ),
    "walletInfo": MessageLookupByLibrary.simpleMessage("Wallet info"),
    "walletSecurity": MessageLookupByLibrary.simpleMessage("Wallet security"),
    "wantADifferentPaymentMethod": MessageLookupByLibrary.simpleMessage(
      "Want a different payment method? ",
    ),
    "wantToEditDetailsReachOutToUsViaOur": MessageLookupByLibrary.simpleMessage(
      "Want to edit details? Reach out to us via our",
    ),
    "wantToFund": MessageLookupByLibrary.simpleMessage("Want to fund?"),
    "wantToFundCardSubCopy": MessageLookupByLibrary.simpleMessage(
      "To fund your card, please switch to a supported network",
    ),
    "wantToFundDaSubCopy": MessageLookupByLibrary.simpleMessage(
      "To fund your account, please switch to a supported network",
    ),
    "wantToHideSpamTokens": MessageLookupByLibrary.simpleMessage(
      "Want to hide all crypto like this?",
    ),
    "wantToLevelUp": MessageLookupByLibrary.simpleMessage("Want to level up?"),
    "wantToTrade": MessageLookupByLibrary.simpleMessage("Want to trade?"),
    "wantToWithdraw": MessageLookupByLibrary.simpleMessage("Want to withdraw?"),
    "wantToWithdrawSubCopy": MessageLookupByLibrary.simpleMessage(
      "To withdraw from your card, please switch to a supported network",
    ),
    "waveEmoji": MessageLookupByLibrary.simpleMessage("👋"),
    "weCanContactIfYouNeedUs": MessageLookupByLibrary.simpleMessage(
      "We can contact if you need us",
    ),
    "weEncounteredATemporaryIssuePleaseRetryThisTransaction":
        MessageLookupByLibrary.simpleMessage(
          "We encountered a temporary issue. Please retry this transaction\"",
        ),
    "weEncounteredAnErrorUnmarkingYourSubscription":
        MessageLookupByLibrary.simpleMessage(
          "We encountered an error unmarking your subscription",
        ),
    "weNeedToVerifyItsReallyYouBehindTheScreen":
        MessageLookupByLibrary.simpleMessage(
          "We need to verify that it’s really you behind the screen",
        ),
    "weOfferTheBestRatesPossible": MessageLookupByLibrary.simpleMessage(
      "We offer the best rates possible for almost any currency. No hidden fees",
    ),
    "weOnlyRecommendWalletDesc": MessageLookupByLibrary.simpleMessage(
      "We only recommend this wallet if you already have some knowledge on DeFi and blockchain networks.",
    ),
    "weVeMovedThingsAround": MessageLookupByLibrary.simpleMessage(
      "We’ve moved things around",
    ),
    "weWillRecommendAPortfolioBasedOnYourAnswers":
        MessageLookupByLibrary.simpleMessage(
          "We’ll recommend a portfolio based on your answers",
        ),
    "weWillRedirect": MessageLookupByLibrary.simpleMessage(
      "We’ll redirect you to",
    ),
    "weWillVerifyPhoneNumberForRecovery": MessageLookupByLibrary.simpleMessage(
      "We’ll verify this number for account recovery",
    ),
    "web3AuthFailureCopy": MessageLookupByLibrary.simpleMessage(
      "Looks like we ran into a temporary issue connecting this account. Please try again!",
    ),
    "weekly": MessageLookupByLibrary.simpleMessage("Weekly"),
    "weeklyLimit": MessageLookupByLibrary.simpleMessage("Weekly limit"),
    "welcome": MessageLookupByLibrary.simpleMessage("Welcome"),
    "welcomeBack": MessageLookupByLibrary.simpleMessage("Welcome back"),
    "welcomeBackSubCopy": MessageLookupByLibrary.simpleMessage(
      "You have an existing account on Onboard.\nChoose a sign in option",
    ),
    "welcomeBackWithEmoji": MessageLookupByLibrary.simpleMessage(
      "Welcome back! 👋",
    ),
    "welcomeOnboard": MessageLookupByLibrary.simpleMessage("Welcome Onboard!"),
    "welcomeToYourDigitalMoneyApp": MessageLookupByLibrary.simpleMessage(
      "Welcome\nto your\ndigital money\napp",
    ),
    "wellChargeThisOneTimeFeeOnYourFirstDeposit":
        MessageLookupByLibrary.simpleMessage(
          "We\'ll charge this one-time fee on your first deposit",
        ),
    "wellFinishThisAndNotifyYouOnceComplete":
        MessageLookupByLibrary.simpleMessage(
          "We\'ll finish this and notify you once complete.",
        ),
    "wellLoveToKnow": MessageLookupByLibrary.simpleMessage(
      "We’d love to know!",
    ),
    "wellNotifyYouDurationCopy": m116,
    "wellRedirectYouTo": MessageLookupByLibrary.simpleMessage(
      "We’ll redirect you to ",
    ),
    "wellRedirectYouToA3rdPartyService": MessageLookupByLibrary.simpleMessage(
      "We’ll redirect you to 3rd party service.",
    ),
    "wellSendYouACodeToConfirmPhoneNumber":
        MessageLookupByLibrary.simpleMessage(
          "We’ll send you a code to confirm phone number",
        ),
    "wellUpdateYouOnTheStatusOfYourVerification":
        MessageLookupByLibrary.simpleMessage(
          "We’ll update you on the status of your verification.",
        ),
    "wereGoingToKeepWorkingToImproveYourOnboardExperience":
        MessageLookupByLibrary.simpleMessage(
          "We\'re going to keep working hard \nto improve your Onboard experience",
        ),
    "wereSettingThingsUpForYouThisShouldBeQuick":
        MessageLookupByLibrary.simpleMessage(
          "We\'re setting things up for you. This should be quick!",
        ),
    "wereSoExcitedToHaveYouSubCopy": MessageLookupByLibrary.simpleMessage(
      "We\'re so excited to have you Onboard. Let\'s set your wallet up",
    ),
    "weveCuratedGreatAppsToInspireYou": MessageLookupByLibrary.simpleMessage(
      "We\'ve curated great apps to inspire and entertain you",
    ),
    "whatCurrencyDoYouWantToReceiveFundsIn":
        MessageLookupByLibrary.simpleMessage(
          "What currency do you want to receive funds in?",
        ),
    "whatCurrencyDoYouWantToSendFundsIn": MessageLookupByLibrary.simpleMessage(
      "What currency do you want to send funds in?",
    ),
    "whatDoeThisMean": MessageLookupByLibrary.simpleMessage(
      "What does this mean?",
    ),
    "whatDoesThisMeanForMe": MessageLookupByLibrary.simpleMessage(
      "What does this mean for me?",
    ),
    "whatIsARecoveryPhrase": MessageLookupByLibrary.simpleMessage(
      "What is a recovery phrase?",
    ),
    "whatIsTheDestinationWallet": MessageLookupByLibrary.simpleMessage(
      "What is the destination wallet?",
    ),
    "whatIsYourName": MessageLookupByLibrary.simpleMessage("What’s your name?"),
    "whatLocationDoYouLive": MessageLookupByLibrary.simpleMessage(
      "What location do you live in most often?",
    ),
    "whatWouldYouLikeToDo": MessageLookupByLibrary.simpleMessage(
      "What would you like to do?",
    ),
    "whatWouldYouLikeToDoNow": MessageLookupByLibrary.simpleMessage(
      "What would you like to do now?",
    ),
    "whatYouNeedToKnow": MessageLookupByLibrary.simpleMessage(
      "What you need to know",
    ),
    "whatYourPreferredInvestment": MessageLookupByLibrary.simpleMessage(
      "What’s your preferred investment style?",
    ),
    "whenYouSendStableCoinsToYourDedicatedWalletAddress":
        MessageLookupByLibrary.simpleMessage(
          "When you send stablecoins to your dedicated wallet address, it\'s converted to cash in your bank account in minutes.",
        ),
    "whereAreYouSendingMoneyFrom": MessageLookupByLibrary.simpleMessage(
      "Where are you sending money from?",
    ),
    "whereDoYouCurrentlyLive": MessageLookupByLibrary.simpleMessage(
      "Where do you live most of the time?",
    ),
    "whereDoYouWantToReceiveFundsIn": MessageLookupByLibrary.simpleMessage(
      "Where do you want to receive funds in?",
    ),
    "whereToTransferFrom": MessageLookupByLibrary.simpleMessage(
      "Where to transfer from",
    ),
    "whereWouldYouLikeToSendMoneyTo": MessageLookupByLibrary.simpleMessage(
      "Where would you like to send money to?",
    ),
    "whereWouldYouLikeToTransferYourFundsTo":
        MessageLookupByLibrary.simpleMessage(
          "Where would you like to transfer funds to?",
        ),
    "whereYouHeardAboutOnboard": MessageLookupByLibrary.simpleMessage(
      "Where did you hear about Onboard?",
    ),
    "whereYouHeardAboutUs": MessageLookupByLibrary.simpleMessage(
      "Where did you hear about us?",
    ),
    "whereYouWantToReceivePay": MessageLookupByLibrary.simpleMessage(
      "Where do you want to receive funds in?",
    ),
    "whichBestDescribeYou": MessageLookupByLibrary.simpleMessage(
      "Which of these best describe you?",
    ),
    "whichOfTheseBestDescribeYou": MessageLookupByLibrary.simpleMessage(
      "Which of these best describe you?",
    ),
    "whoAccountFor": MessageLookupByLibrary.simpleMessage(
      "Who\'s this account for?",
    ),
    "whoAreYouSendingTo": MessageLookupByLibrary.simpleMessage(
      "Who are you sending to?",
    ),
    "why": MessageLookupByLibrary.simpleMessage("Why?"),
    "whyDidThisHappen": MessageLookupByLibrary.simpleMessage(
      "Why did this happen?",
    ),
    "whyDoINeedKycToTrade": MessageLookupByLibrary.simpleMessage(
      "Why do I need KYC to trade",
    ),
    "whyDoINeedKycToTradeWithQuestionMark":
        MessageLookupByLibrary.simpleMessage("Why do I need KYC to trade?"),
    "whyHaven": MessageLookupByLibrary.simpleMessage("Why Haven?"),
    "whyHavenFour": MessageLookupByLibrary.simpleMessage(
      "Premium: curated investments, concierge services, exclusive events.",
    ),
    "whyHavenOne": MessageLookupByLibrary.simpleMessage(
      "Personalized financial & investment guidance from a dedicated assistant.",
    ),
    "whyHavenThree": MessageLookupByLibrary.simpleMessage(
      "Coming soon: USD/EUR accounts, card cashback, payments in 30+ countries.",
    ),
    "whyHavenTwo": MessageLookupByLibrary.simpleMessage(
      "Diverse investments in USD and 50+ digital assets like Bitcoin & Solana.",
    ),
    "whyWasThisFeeCharged": MessageLookupByLibrary.simpleMessage(
      "Why was this fee charged?",
    ),
    "willIncurAFee": MessageLookupByLibrary.simpleMessage("will incur a fee."),
    "wire": MessageLookupByLibrary.simpleMessage("Wire"),
    "wireDepositFee": MessageLookupByLibrary.simpleMessage("Wire deposit fee"),
    "withdraw": MessageLookupByLibrary.simpleMessage("Withdraw"),
    "withdrawFromCard": MessageLookupByLibrary.simpleMessage(
      "Withdraw from card",
    ),
    "withdrawFromCardOptionsCopy": MessageLookupByLibrary.simpleMessage(
      "Where would you like to send funds to?",
    ),
    "withdrawYourDollarsAnytime": MessageLookupByLibrary.simpleMessage(
      "Withdraw your dollars anytime",
    ),
    "withdrawal": MessageLookupByLibrary.simpleMessage("Withdrawal"),
    "withdrawalAmount": MessageLookupByLibrary.simpleMessage(
      "Withdrawal amount ",
    ),
    "withdrawalAmountTooHigh": MessageLookupByLibrary.simpleMessage(
      "Withdrawal amount too high",
    ),
    "withdrawalAmountTooLow": MessageLookupByLibrary.simpleMessage(
      "Withdrawal amount too low",
    ),
    "withdrawalLimitsExceeded": MessageLookupByLibrary.simpleMessage(
      "Withdrawal limit exceeded.",
    ),
    "withdrawalMethodIsCurrentlyNotAvailableForThisCountry":
        MessageLookupByLibrary.simpleMessage(
          "Withdrawal method is currently not available for the selected country",
        ),
    "withdrawalNotEnabledForNetwork": MessageLookupByLibrary.simpleMessage(
      "Withdrawal not enabled for network",
    ),
    "withdrawalsUnavailablePleaseTryAgainSoon": m117,
    "withdrew": MessageLookupByLibrary.simpleMessage("Withdrew"),
    "work": MessageLookupByLibrary.simpleMessage("Work"),
    "wouldLikeToConnect": MessageLookupByLibrary.simpleMessage(
      "would like to connect",
    ),
    "xWantToConnect": m118,
    "yesCancel": MessageLookupByLibrary.simpleMessage("Yes, cancel"),
    "yesDelete": MessageLookupByLibrary.simpleMessage("Yes, delete"),
    "yesDeleteMyAccount": MessageLookupByLibrary.simpleMessage(
      "Yes, delete my account",
    ),
    "yesDeleteThisAddress": MessageLookupByLibrary.simpleMessage(
      "Yes, delete this address",
    ),
    "yesExitTheExchange": MessageLookupByLibrary.simpleMessage(
      "Yes, exit the exchange",
    ),
    "yesIUnderstand": MessageLookupByLibrary.simpleMessage("Yes, I understand"),
    "yesProceed": MessageLookupByLibrary.simpleMessage("Yes, proceed"),
    "yesSetUpANewCard": MessageLookupByLibrary.simpleMessage(
      "Yes, set up a new card",
    ),
    "yesSkip": MessageLookupByLibrary.simpleMessage("Yes, skip"),
    "yesterday": MessageLookupByLibrary.simpleMessage("Yesterday"),
    "youACryptoMaxi": MessageLookupByLibrary.simpleMessage(
      "You’re a crypto maxi",
    ),
    "youAreA": MessageLookupByLibrary.simpleMessage("You\'re a"),
    "youAreAn": MessageLookupByLibrary.simpleMessage("You’re an"),
    "youAreBuying": MessageLookupByLibrary.simpleMessage("You’re buying"),
    "youAreSigningTheMessageBelow": MessageLookupByLibrary.simpleMessage(
      "You are signing the message below",
    ),
    "youBuy": MessageLookupByLibrary.simpleMessage("You buy"),
    "youCanNowSearchAndConnectToYourFavoriteApps":
        MessageLookupByLibrary.simpleMessage(
          "You can now search and connect to your favourite apps",
        ),
    "youCanSendMoneyToABankMobileMoney": MessageLookupByLibrary.simpleMessage(
      "You can send money to a bank, mobile money or even a crypto wallet",
    ),
    "youCanSwitchNetworksHere": MessageLookupByLibrary.simpleMessage(
      "You can switch networks here 👋",
    ),
    "youDontHaveAnAccountsInThisCurrency": MessageLookupByLibrary.simpleMessage(
      "You don’t have accounts in this currency",
    ),
    "youDontHaveAnyDirectAccounts": m119,
    "youEarnBonus": MessageLookupByLibrary.simpleMessage(
      "You earn a bonus when your friend",
    ),
    "youGet": MessageLookupByLibrary.simpleMessage("You get"),
    "youGot": MessageLookupByLibrary.simpleMessage("You got"),
    "youHaveAPreviouslyTerminatedCardSubCopy": m120,
    "youHaveNoMoreAttemptsLeft": MessageLookupByLibrary.simpleMessage(
      "You have no more attempts left",
    ),
    "youHaveNotAddedAUsdOrEurAccountYet": m121,
    "youHaveOverdueFees": MessageLookupByLibrary.simpleMessage(
      "You have overdue fees!",
    ),
    "youHaveOverdueFeesSubCopy": MessageLookupByLibrary.simpleMessage(
      "You need to fund your card for the items below to keep it active.",
    ),
    "youHaveReachedAMax": MessageLookupByLibrary.simpleMessage(
      "You have reached a max",
    ),
    "youHaveReachedMaxSubCopy": m122,
    "youHaveTotalControl": MessageLookupByLibrary.simpleMessage(
      "It’s self-custody, which means you have total control over your funds.",
    ),
    "youHaveXRetriesLeft": m123,
    "youPaid": MessageLookupByLibrary.simpleMessage("You paid"),
    "youPay": MessageLookupByLibrary.simpleMessage("You pay"),
    "youPayExactly": MessageLookupByLibrary.simpleMessage("You pay exactly"),
    "youReInCopy": m124,
    "youReceive": MessageLookupByLibrary.simpleMessage("You receive"),
    "youReceived": MessageLookupByLibrary.simpleMessage("You received"),
    "youReceived1380": MessageLookupByLibrary.simpleMessage(
      "You received \$1,380",
    ),
    "youSell": MessageLookupByLibrary.simpleMessage("You sell"),
    "youSend": MessageLookupByLibrary.simpleMessage("You send"),
    "youSent250USDT": MessageLookupByLibrary.simpleMessage("You sent 205 USDT"),
    "youSwap": MessageLookupByLibrary.simpleMessage("You swap"),
    "youTransfer": MessageLookupByLibrary.simpleMessage("You transfer"),
    "youWereHereForAWhile": MessageLookupByLibrary.simpleMessage(
      "You were away for a while",
    ),
    "youWillGet": MessageLookupByLibrary.simpleMessage("You will get"),
    "youWillPay": MessageLookupByLibrary.simpleMessage("You will pay"),
    "youWillReceive": MessageLookupByLibrary.simpleMessage("You will receive"),
    "youWithdraw": MessageLookupByLibrary.simpleMessage("You withdraw"),
    "youWithdrew": MessageLookupByLibrary.simpleMessage("You withdrew"),
    "youllBeNotified": MessageLookupByLibrary.simpleMessage(
      "You’ll be notified",
    ),
    "youllNeedToGrantYourApproval": MessageLookupByLibrary.simpleMessage(
      "You\'ll need to grant approval to spend your",
    ),
    "yourAccountHaveBeenFunded": MessageLookupByLibrary.simpleMessage(
      "Your account has being funded",
    ),
    "yourAccountIsReady": MessageLookupByLibrary.simpleMessage(
      "Your account is ready!",
    ),
    "yourAccountIsSecure": MessageLookupByLibrary.simpleMessage(
      "Your account is secure! Tap the icon below to unlock using your device",
    ),
    "yourAccountWillBeReadySoon": MessageLookupByLibrary.simpleMessage(
      "Your account will be ready soon!",
    ),
    "yourCardGets": MessageLookupByLibrary.simpleMessage("Your card gets"),
    "yourCardHasBeingFunded": MessageLookupByLibrary.simpleMessage(
      "Your card has being funded",
    ),
    "yourCardHasBeingSuspendedTemporarilyPleaseNoteTheFollowing":
        MessageLookupByLibrary.simpleMessage(
          "Your card has been suspended temporarily. Please note the following",
        ),
    "yourCardIsBelowMinimumBalance": MessageLookupByLibrary.simpleMessage(
      "Your card is below minimum balance",
    ),
    "yourCompleteFinancialLife": MessageLookupByLibrary.simpleMessage(
      "Your complete financial life, all in one place.",
    ),
    "yourDataIsEncrypted": MessageLookupByLibrary.simpleMessage(
      "Your data is encrypted",
    ),
    "yourDepositIsBeingProcessed": MessageLookupByLibrary.simpleMessage(
      "Your deposit is being processed",
    ),
    "yourIdIsRequired": MessageLookupByLibrary.simpleMessage(
      "Your ID is required",
    ),
    "yourIdMayBeRequired": MessageLookupByLibrary.simpleMessage(
      "Your ID may be required. ",
    ),
    "yourInboxIsHere": MessageLookupByLibrary.simpleMessage(
      "Your Inbox is here",
    ),
    "yourKeysYourCoins": MessageLookupByLibrary.simpleMessage(
      "Your keys, your coins",
    ),
    "yourKycIsInReview": MessageLookupByLibrary.simpleMessage(
      "Your verification is in review",
    ),
    "yourKycIsIncomplete": MessageLookupByLibrary.simpleMessage(
      "Your KYC verification is incomplete",
    ),
    "yourOrderNeedsAttention": m125,
    "yourPasskeyIsCreated": MessageLookupByLibrary.simpleMessage(
      "Your passkey is created!",
    ),
    "yourPersonalisedPortfolio": MessageLookupByLibrary.simpleMessage(
      "Your personalized portfolio is ready - fund your wallet to start investing!",
    ),
    "yourRecoveryDetailsAreBackedUp": MessageLookupByLibrary.simpleMessage(
      "Your recovery details are backed up",
    ),
    "yourSubscriptionWasAdded": MessageLookupByLibrary.simpleMessage(
      "Your subscription was added",
    ),
    "yourSwapWillNotContinueIfTheRateChangesMoreThan":
        MessageLookupByLibrary.simpleMessage(
          "Your swap will not continue if the rate changes more than this percentage",
        ),
    "yourTransactionMayBeFrontRunAndResultInAnUnfavourableTrade":
        MessageLookupByLibrary.simpleMessage(
          "Transaction may be front run and result in an unfavorable trade",
        ),
    "yourWalletAddress": MessageLookupByLibrary.simpleMessage(
      "Your wallet address",
    ),
    "yourWalletIsNotBackedUp": MessageLookupByLibrary.simpleMessage(
      "Your wallet is not backed up",
    ),
    "yourWalletIsReady": MessageLookupByLibrary.simpleMessage(
      "Your wallet is ready!",
    ),
    "yourWalletIsSecure": MessageLookupByLibrary.simpleMessage(
      "Your wallet is secure!",
    ),
    "yourWalletIsSecureSubCopy": MessageLookupByLibrary.simpleMessage(
      "All backed up. Remember, no one else can access this wallet, not even us \nat Onboard",
    ),
    "yourWalletRecoveryDetails": MessageLookupByLibrary.simpleMessage(
      "Your wallet recovery details",
    ),
    "yourWillingToAcceptBeforeWeStopTheTransaction":
        MessageLookupByLibrary.simpleMessage(
          "you\'re willing to accept before we stop the transaction",
        ),
    "yourWithdrawalLimits": MessageLookupByLibrary.simpleMessage(
      "Your withdrawal limits",
    ),
    "youreBuying": m126,
    "youreInFullControlOfThisWalletWithYourPrivateKeys":
        MessageLookupByLibrary.simpleMessage(
          "You’re in full control of this wallet, with your private keys.",
        ),
    "youreSelling": m127,
    "youreWillingToAcceptBeforeWeStopTheTransaction":
        MessageLookupByLibrary.simpleMessage(
          "you\'re willing to accept before we stop the transaction.",
        ),
    "youveReadAndAcceptedTheFollowingToUseThisAccount":
        MessageLookupByLibrary.simpleMessage(
          "You’ve read and accepted the following to use this account",
        ),
    "youveReadAndAcceptedTheFollowingToUseThisCard":
        MessageLookupByLibrary.simpleMessage(
          "You\'ve read and accepted the following to use this card.",
        ),
    "zip": MessageLookupByLibrary.simpleMessage("ZIP"),
  };
}
