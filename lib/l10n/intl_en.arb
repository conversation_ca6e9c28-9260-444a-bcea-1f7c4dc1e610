{"destinationWallet": "Destination Wallet", "orders": "Orders", "my": "My", "allNetworks": "All networks", "keyStats": "Key Stats", "settings": "Settings", "wallet": "Wallet", "onboardWallet": "Onboard Wallet", "secureYourWallet": "Secure your wallet", "authViewHeader": "The simplest wallet to\nbuy, sell, store and \nsend crypto", "cancel": "Cancel", "onboard": "Onboard", "available": "Available", "seedPhrase": "Seed phrase", "buyGasAndGetStarted": "Buy {tokenSymbol} and get started!", "depositGasAndGetStarted": "Deposit {tokenSymbol} and get started!", "avoidFailedTransactions": "Avoid failed transactions", "buyGasSubCopy": "{tokenSymbol} token is required to pay network fees for transactions. There's no {tokenSymbol} in your wallet, so we suggest adding some now.", "oopsThisCodeSeemsIncorrect": "Oops! This code seems incorrect", "creatingYourOnboardWallet": "Creating your Onboard Wallet", "tooManyAttemptsTryAgainLater": "Too many attempts. Try again later", "signatureRequest": "Signature Request", "youAreSigningTheMessageBelow": "You are signing the message below", "coinTransfer": "Coin Transfer", "iUnderstand": "I understand", "feesMayApply": "Fees may apply", "feesInfoCopy": " Our payment partners may charge a small fee to process this transaction\nThe receiving amount is only an estimate.", "fetchingRates": "Fetching rates...", "swapFeeCopy": "To process this transaction, a small network\n fee will be charged.", "gotGasElsewhere": "Got {tokenSymbol} elsewhere? Deposit", "unableToRecognizeAddressErrorMessage": "'We aren’t able to fetch details for the contract address entered. Please pre-fill manually'", "tokenTransfer": "Token Transfer", "nftTransfer": "NFT Transfer", "youSwap": "You swap", "internalTransfer": "Internal Transfer", "contractCall": "Contract Call", "depositCrypto": "Deposit Crypto", "destination": "Destination", "receiveTokenFromExternalWallet": "Receive tokens from external wallet", "backupWalletReason": "Please authenticate to secure your wallet", "unlockWallet": "Unlock wallet", "cryptoIsVisible": "Crypto is visible", "itWillNowShowUpOnYourPortfolioList": "It will now show up on your portfolio list.", "unlockToGainAccess": "Unlock wallet to gain access to the app", "createWallet": "Create Wallet", "continueWith": "Continue with {Google}", "switchedToNetwork": "Switched to {networkName}", "onboardDotXyz": "Onboard.xyz", "destinationWalletInfo": "This is the wallet where your assets will  automatically be sent to once we have received them", "importWallet": "Import wallet", "importWalletSuccessMessage": "Great! You can import this wallet ", "importWalletSubCopy": "Provide your 12 word recovery phrase", "pasteHereOrType": "Paste here or type...", "important": "Important", "paste": "Paste", "done": "Done", "email": "Email", "confirmSwap": "Confirm swap", "myWallet": "My Wallet", "tokenIsVisible": "{symbol} is visible", "tokenIsHidden": "{symbol} is hidden", "authenticatingYourEmail": "Authenticating your email", "letsVerifyYourAccount": "Let's verify your account", "rate": "Rate", "spotWallet": "Spot Wallet", "maxSlippage": "<PERSON> Slippage", "request": "Request", "contractAddress": "Contract Address", "approveTokenSubCubCopy": "You're giving access to transact with this token", "setEmailSubCopy": "Confirm that you've entered the correct email and hit continue", "whatIsARecoveryPhrase": "What is a recovery phrase?", "didYouKnow": "Did you know?", "networkFee": "Network Fee", "swapSuccessful": "Swap successful", "customerSubCopy": "I want to store and manage my digital assets", "tellUsABitAboutYou": "Tell us a bit about you", "accountTypeSubCopy": "Which of the options below best describes\nyou", "merchantSubCopy": "I want to trade and exchange high volumes of digital assets", "recoveryPhraseInfo": "Your recovery phrase is a special kind of password. It is one way to access your wallet and must be kept\nprivate", "securityWarning": "Please confirm that you read below before viewing your {credentialType}", "importExistingWallet": "Import Existing Wallet", "enterOnboardEmail": "Enter onboard email", "failedToSignTransaction": "Failed to Sign Transaction", "youReceive": "You receive", "errorSwitchingChain": "Error Switching Chain", "maxSlippageInfo": "Slippage can occur when market prices change between when you place an order and when it is executed.", "transactionCancelled": "Transaction Cancelled", "authorizeTransaction": "Authorize Transaction", "thisPercentageIsTheMaximumSlippage": "\nThis percentage is the maximum slippage", "yourWillingToAcceptBeforeWeStopTheTransaction": "you're willing to accept before we stop the transaction", "confirmTransaction": "Confirm Transaction", "transactionOrder": "Transaction Order", "continueText": "Continue", "loading": "Loading", "showPrivateKey": "Show Private Key", "recoveryPhrase": "Recovery phrase", "privateKey": "Private key", "yourWalletRecoveryDetails": "Your wallet recovery details", "yourRecoveryDetailsAreBackedUp": "Your recovery details are backed up", "showRecoveryPhrase": "Show Recovery Phrase", "transactionFee": "Transaction Fee", "totalAmount": "Total Amount", "walletCreated": "Wallet created!", "walletCreatedSubCopy": "Here's your Onboard wallet address. Think of it like an 'account number' for receiving funds.", "switchNetwork": "Switch Network", "switchNetworkTitle": "Onboard Wallet wants to change the network", "switchNetworkSubtitle": "This will switch the network within Onboard wallet to a previously added network", "sampleEmail": "<EMAIL>", "pleaseEnterTheCodeSentTo": "Please enter the code sent to", "changeEmail": "Change email", "didntReceiveCode": "Didn’t receive code?", "resend": "Resend", "wantToTrade": "Want to trade?", "unsupportedNetworkSubCopy": "To buy assets, please switch to a supported network.", "resentCodeIn": "Resend in {time}", "tradeIntroHeader": "<PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON> Crypto with <PERSON><PERSON>", "tradeIntroSubCopy": "With Onboard Exchange®, we make it easy to go from crypto to your local currency, and back", "express": "Express", "welcomeOnboard": "Welcome Onboard!", "received": "Received", "skip": "<PERSON><PERSON>", "swap": "<PERSON><PERSON><PERSON>", "secureWallet": "Secure wallet", "encourageBackup": "Enable biometric to safeguard your fund!", "verifyingYourOnboardWallet": "Verifying your Onboard Wallet ", "redirectingToOnboardExchange": "Redirecting to Onboard\nExchange...", "pleaseAuthenticateToBackup": "Please authenticate to secure your wallet", "logout": "Log out", "logoutSubCopy": "You will be required to sign in and provide security next time ", "unlock": "Unlock", "send": "Send", "receive": "Receive", "dontShowThisAgain": "Don't show this again", "buy": "Buy", "dataNotProvided": "Data not provided", "chainIdNotProvided": "Chain Id not provided", "failedToSignMessage": "Failed to sign message", "sign": "Sign", "signToAuthenticateYourWallet": "Sign to authenticate your wallet.", "chainNotAdded": "Chain not added", "authSignMessageCopy": "Welcome to Onboard!\n\nPlease sign this message to complete authentication. Your signature verifies that you’re the owner of this wallet.\n This request will not trigger a transaction or cost you anything.\n{timeStamp}", "sell": "<PERSON>ll", "network": "Network", "selling": "Selling", "address": "Address", "myBalance": "My Balance", "copy": "Copy", "copied": "<PERSON>pied", "profile": "Profile", "paymentMethod": "Payment method", "notifications": "Notifications", "faqs": "FAQs", "home": "Home", "ads": "Ads", "resources": "Resources", "leaveFeedback": "Leave Feedback", "securityWarning1": "Your {credentialType} grants total access to your wallet. Never share it with\nanyone!", "securityWarning2": "Onboard will never ask you to share your {credentialType}", "viewCredentialsWarning": "Your {credentialType} grants access to your entire wallet. Use securely!", "proceed": "Proceed", "wellRedirectYouTo": "We’ll redirect you to ", "onboardExchangeTM": "Onboard Exchange®", "walletAddress": "Wallet Address", "iUnderstandTheRisks": "I understand the risks", "walletSecurity": "Wallet security", "backupCompleted": "Your wallet is now protected", "sendTo": "Send to", "walletInfo": "Wallet info", "general": "General", "from": "From", "to": "To", "amount": "Amount", "max": "Max", "confirm": "Confirm", "confidential": "Confidential", "fee": "Fee", "selectAsset": "Select Asset", "customer": "Customer", "merchant": "Merchant", "failed": "Failed", "pending": "Pending", "success": "Success", "approve": "Approve", "swapTokens": "S<PERSON>p <PERSON>", "transactions": "Transactions", "noTransactionsCopy": "No assets yet. Add crypto to get started", "approving": "Approving", "getStarted": "Get started", "poweredBy": "Powered by", "onboardExchange": "Onboard Exchange", "kycMaxAttemptsCopy": "Despite multiple attempts, we were unable to verify your identity with the details you provided.", "receiveCrypto": "Receive Crypto", "transactionSuccessful": "Transaction Successful", "transactionFailed": "Transaction failed", "tradeToken": "Trade {token}", "swapping": "Swapping", "setupWalletProtection": "Set up wallet protection", "enableBiometricForLogin": "Enable biometrics for login", "exchange": "Exchange", "date": "Date", "transactionId": "Transaction ID", "myAsset": "My Assets", "youSell": "You sell", "insufficientBalance": "Insufficient balance", "chooseNetwork": "Choose Network", "depositTokenName": "Deposit {name}", "sendOnly": "Send only", "toThisDepositAddressToAvoidLosingYourFunds": "to this deposit address to avoid losing your funds", "myNetworkAddress": "My {network} Address", "share": "Share", "assets": "Assets", "balance": "Balance", "selectToken": "Select Token", "invalidAddress": "Invalid Address", "preview": "Preview", "sending": "Sending", "value": "Value", "findOffers": "Find Offers", "sent": "<PERSON><PERSON>", "youBuy": "You buy", "itCosts": "It costs", "transactionSentSubtitle": "We'll notify you when this transaction is completed", "gotIt": "Got it", "confirmed": "Confirmed", "transactionSentTitle": "Transaction sent", "completed": "Completed", "addressCopied": "Address Copied!", "appVersionCopied": "App Version Copied!", "overview": "Overview", "spot": "Spot", "trading": "Trading", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdraw": "Withdraw", "trade": "Trade", "tradingWallet": "Trading Wallet", "spotWalletIntro": "Holds all your assets", "tradingWalletIntro": "Start high volume trades", "myWallets": "My Wallets", "totalBalance": "Total Balance", "availableBalance": "Available Balance", "lockedInOrders": "Locked in orders", "exchangeMerchantSubtitle": "Create ads, manage existing orders from customers", "setup": "Set Up", "moveToCryptoAndCash": "Go from crypto to cash and \nback in minutes", "introducingYourWallet": "Introducing Your \nMerchat Wallet", "createAds": "Create Ads", "addCustomToken": "Add custom token", "addCustomTokens": "Add custom tokens", "customTokens": "Custom Tokens", "name": "Name", "symbol": "Symbol", "add": "Add", "invalidContractAddressCopy": "Oops! This is an invalid {networkName}\ncontract address. Please check again", "customTokensSubCopy": "When a token does not appear on your wallet, it can be manually added", "search": "Search", "createAdsIntro": "Make profits with your merchant wallet by creating buy and sell ads.", "next": "Next", "seeAll": "See all", "manageAds": "Manage Ads", "manageAdsIntro": "Control the multiple ads you’ve created and see the funds you can withdraw anytime", "tradeAndManageAssets": "Trade and Manage Assets", "tradingWalletCreated": "Merchant wallet created!", "walletCreatedMessage": "You're all set to create ads. Start by funding your wallet.", "myTradingWallet": "My Merchant <PERSON>", "setupWallet": "Set up Wallet", "creatingTradingWalletLoadingMessage": "We’re creating a merchant wallet for you. It’ll only take a few moments", "depositFunds": "Deposit funds", "lockedBalance": "Locked balance", "lockedBalanceDescription": "This is the total value of your assets locked in your orders and ads. You’re unable to withdraw directly from this balance", "availableBalanceDescription": "The total amount of crypto you can use to create new ads. You can top up or withdraw from this balance anytime.", "manageYourAds": "Manage your ads", "proTradingTips": "Pro merchant tip", "proTradingTipSubCopy": "We suggest funding your merchant wallet first before creating an ad.", "continueToAds": "Continue to <PERSON>s", "fundWallet": "Fund Wallet", "importSuccessful": "Import successful!", "useDifferentWallet": "Use a different wallet?", "accountWithNumber": "Account {number}", "selectWalletToImport": "Select wallet to Import", "associatedAccounts": "These are accounts associated with your seedphrase.", "welcomeBackWithEmoji": "Welcome back! 👋", "letsGetYouBackIn": "Let’s get you back in. Verify your email to continue", "sendToExternalWallet": "Send to external wallet", "transferBetweenOnboardWallet": "Transfer between wallets on Onboard", "transferWithTokenSymbol": "Transfer {symbol}", "invalidAmount": "<PERSON><PERSON><PERSON>", "pasteOrScanAddress": "Paste or scan address", "ensureNetworkMatches": "Ensure the network matches the recipient address or your assets may be lost", "ensureAddressIsCorrect": "Ensure that the address is correct and on the same network. Transactions cannot be reversed", "sendingWallet": "Sending wallet", "sendingWalletDescription": "This is the wallet where your assets will be sent out from", "tradingWalletNotAllowedForExternalSend": "For extra security, your trading wallet cannot be used for transfers to external wallets.", "sendFrom": "Send from", "notEnoughToCoverFee": "Insufficient {BNB} balance to cover fees", "scanQrcode": "Scan QR Code", "confirmItIsYou": "Hey,\nWe'd like to confirm that its you! Your signature verifies that you're the owner of this wallet:", "confirmToSignMessage": "Tap confirm to sign this message.", "notYou": "Not you?", "confirmingWallet": "Confirming your Onboard Wallet", "enterEmail": "<PERSON><PERSON>", "confirmEmailUsedInOnboard": "Confirm the email linked to your Onboard account and tap continue", "welcome": "Welcome", "waveEmoji": "👋", "appVersion": "Version {appVersion} (Build {buildNumber})", "walletConfirmed": "Wallet confirmed!", "deleteAccount": "Delete account", "deleteAccountWarning": "Please read the following requirements before deleting your Onboard account", "meetThisRequirement": "I meet these requirements", "accountWillBeDeletedPermanently": "You will permanently lose all your profile information. Your wallet will remain usable once backed up", "yesDeleteMyAccount": "Yes, delete my account", "deletingYourOnboardWallet": "Deleting your Onboard Wallet", "savedPrivateKeySomewhere": "I've saved my private key somewhere. Failing to do this will lead me to loss of my funds forever.", "savedRecoveryKeySomewhere": "I've saved my recovery phrase somewhere. Failing to do this will lead me to loss of my funds forever.", "haveTransferredFundFromTradingWallet": "I have transferred funds from my trading wallet to my spot wallet to avoid losing them", "no": "No", "confirmAccountDelete": "Confirm account deletion", "walletConfirmedSubCopy": "Yes, you’re the proud owner of this wallet address.", "walletConfirmedSubCopy2": "Think of it as an ‘account number’ for receiving funds ", "existingAccountSubCopy": "This email address is already linked to an existing account on Onboard. To proceed, please import the seed phrase of the wallet in use on that account", "passcode": "Passcode", "passcodeDoesntMatch": "Passcode doesn't match", "security": "Security", "changePasscode": "Change Passcode", "unlockWithFaceId": "Unlock with Face ID", "unlockWithFingerPrint": "Unlock with <PERSON>ger print", "incorrectPasscode": "Incorrect passcode. Please try again", "newPasscodeCreated": "New passcode created", "passcodeCreated": "Passcode created 🎉", "passcodeChanged": "Passcode changed 🎉", "created": "Created", "createANewPasscode": "Create a new 6 digit passcode ", "confirmYourNewPasscode": "Confirm your new 6 digit passcode", "confirmYourCurrentPasscode": "Confirm your current 6 digit passcode", "secureAccountWithPasscode": "Set a 6-digit passcode for login", "confirmPasscode": "Confirm your 6 digit passcode", "setPasscode": "Set passcode", "unlockWithPasscode": "Unlock with Passcode", "enterYourPasscode": "Enter your passcode", "helpCenter": "Help center", "chatWithUs": "Chat with us", "havingIssuesReachOut": "Having issues? Reach out to us via our", "today": "Today", "yesterday": "Yesterday", "hide": "<PERSON>de", "preferences": "Preferences", "manageTokens": "Manage tokens", "hideLowReputationTokens": "Hide low reputation crypto", "preventSpamToken": "Prevents random, spam crypto from showing up in your wallet", "allSpamTokenHidden": "All spam crypto hidden", "undo": "Undo", "spamTokenHiddenUndoInSettings": "Spam crypto hidden. Undo in settings", "tokenHasLowReputation": "This token has a low reputation", "appearToBeSpamToken": "Appears to be a spam crypto. Proceed with caution", "wantToHideSpamTokens": "Want to hide all crypto like this?", "hours": "{count, plural, =0{} =1{ 1 hour} other{ {count} hours}} ", "minutes": "{count, plural, =0{} =1{ 1 minute} other{ {count} minutes}} ", "seconds": "{count, plural, =0{} =1{ 1 second} other{ {count} seconds}} ", "incorrectPasscodeXAttemptsLeft": "Incorrect passcode. {count, plural, =0{} =1{1 attempt} other{{count} attempts}} left ", "youHaveXRetriesLeft": "You have {count, plural, =0{no retry} =1{1 retry} other{{count} retries}} left", "@incorrectPasscodeXAttemptsLeft": {"description": "A plural message", "placeholders": {"count": {"type": "num", "format": "compact"}}}, "noAttemptsLeftLoggingOutInXSeconds": "You have no more attempts. Logging out {count, plural, =0{} =1{in 1 sec..} other{in {count} secs..}}", "@noAttemptsLeftLoggingOutInXSeconds": {"description": "A plural message", "placeholders": {"count": {"type": "num", "format": "compact"}}}, "preferenceList": "Payment method, notifications", "fundwallet": "Fund wallet", "description": "Description", "pleaseConfirmThisTransaction": "Please confirm this\ntransaction", "deny": "<PERSON><PERSON>", "allowTransaction": "Allow Transaction", "youllNeedToGrantYourApproval": "You'll need to grant approval to spend your", "tokenApprovedCompleteYourSwap": "<PERSON><PERSON> approved. Complete your swap", "beforeConfirmingSwap": "before confirming swap", "approvingToken": "Approving token", "confirmingSwap": "Confirming swap", "unsupportedTradingNetwork": "To start trading and managing ads, please switch to a supported network", "oneSmallThing": "One small thing,", "phoneNumberViewSubCopy": "Please provide your phone number - we'll only contact if you need us", "finish": "Finish", "yourDataIsEncrypted": "Your data is encrypted", "beforeYouGo": "Before you go", "phoneSKipWarning": "Phone numbers give us a direct means of helping out if you run into any issues. Sure about this?", "illPass": " I'll pass", "addNumber": "Add number", "failedToGetTransaction": "Failed to fetch transaction", "joinCommunity": "Join our community", "visitWebsite": "Visit website", "chooseCountry": "Choose country", "yourIdMayBeRequired": "Your ID may be required. ", "why": "Why?", "kycInfoCopy": "{appName} only requires ID Verification when buying or selling assets with local currencies. This is to deter bad actors from having access and using it for harmful reasons.", "tokenIsNowHidden": "Token is now hidden", "introducingWalletConnection": "Introducing WalletConnect", "interactWithCompatibleApps": "Interact with compatible apps", "interactWithDappMessage": "This feature enables your wallet to securely connect to a wide range of platforms and applications.", "howToUseWalletConnect": "How to use wallet connect", "justScanOrCopyIt": "Just scan or copy it", "howToConnectMessage": "Look for the 'WalletConnect' icon on any compatible service. Scan or copy the QR code and you're connected!", "newText": "New", "seeConnections": "See Connections", "scanOrCopyQrCode": "Scan or copy QR Code", "xWantToConnect": "{x} wants to connect to your wallet", "connectToOnlySiteYouTrust": "Only connect to sites you trust", "reject": "Reject", "connect": "Connect", "walletConnected": "Wallet connected", "returnToConnectedToDapp": "Return to {Uniswap} to continue", "invalidWalletConnectUri": "Missing or invalid. URI is not WalletConnect URI", "sessionEnded": "Session ended", "sessionError": "Session error", "connectedSites": "Connected sites", "connected": "Connected", "siteConnections": "Site connections", "siteConnectionsText": "This enables Onboard wallet and other sites to securely connect and interact by scanning or copying their QR code.", "connectionDetails": "Connection details", "connectedOn": "Connected on", "walletDisconnected": "Wallet disconnected", "emptyWalletConnectState": "Your wallet connections with other sites will appear here", "connectedWallet": "Connected Wallet", "disconnect": "Disconnect", "disconnectSite": "Disconnect site", "disconnectSiteWarning": "Disconnecting will end your session with this site. You'll need to setup a new connection if you want to reconnect later", "connectingMayTakeLonger": "Connecting may take a few seconds", "connectionFailedRefreshDapp": "Connection failed. Refresh the Dapp", "typeManually": "Type manually", "pasteYourRecoveryPhrase": "Paste your recovery phrase", "clear": "Clear", "importMyWallet": "Import my wallet", "enterYourRecoveryPhrase": "Enter your recovery phrase", "oopsYouveGotItWrong": "Oops! You’ve got it wrong", "editManually": "Edit manually", "headsUp": "Heads up!", "editsHereWillOnlyAlterWhatYouSeeOnTheOnboardAppAndNotYourSubscriptionWithTheMerchant": "Edits here will only alter what you see on the Onboard app and not your subscription with the merchant", "exitingExchangeWarningSubCopy": "This will exit the Exchange. You'll be taken back to your Wallet, but any ongoing transactions will continue as normal.", "appUpdateAvailable": "App update available!", "isThisARecurringPayment": "Is this a recurring payment?", "markAsASubscription": "Mark as a subscription", "appUpdateSubcopy": "Your app version is out of date.\nUpdate to get the best of\nOnboard ", "updateApp": "Update App", "yesExitTheExchange": "Yes, exit the exchange", "switchNetworkSubCopy": "Switch between various crypto networks to view your tokens held on each network", "settingUpYourWallet": "Setting up your wallet  🎉", "creatingYourPrivateKey": "Creating your private key  🔑", "assigningYouFullCustody": "Assigning you full custody 🔐", "hereIsYourWallet": "Here’s your wallet!", "backUpYourWallet": "Backup your wallet", "noResultsFound": "No results found", "fundWalletWithYourLocalCurrency": "Fund wallet with your local currency", "noAssetsSubCopy": "No assets yet. Add crypto to get started", "walletConnectWarning": "This connection will view your wallet balance, activity and request approval for transactions", "application": "Application", "signTransaction": "Sign transaction", "web3AuthFailureCopy": "Looks like we ran into a temporary issue connecting this account. Please try again!", "cards": "Cards", "card": "Card", "featuresForYou": "Features for you", "addToAppleOrGooglePay": "Add to Apple or Google pay", "setUpCard": "Set up card", "fastAffordableFunding": "Fast, affordable funding", "acceptedGlobalFunding": "Accepted globally, online", "spendDollarAnywhere": "Spend digital dollars anywhere with virtual cards", "notAvailableInYourCountry": "Not available in your\ncountry", "notifyMe": "Notify me", "pasteFromClipboard": "Paste from clipboard", "notAvailableInYourCountrySubCopy": "We don't support cards in your location just yet, but we can reach out when we do, if you'd like that.", "countryOfResidence": "Country of residence", "country": "Country", "kycRedirectCopy": "Heads up, we’ll redirect you to an ID\nverification service.", "identityIsVerified": "Identity is verified", "kycVerifiedSubCopy": "Your details were successfully verified! Now, you're good to go.", "verificationIsInProgress": "Verification is in progress", "turnOnNotifications": "Turn on notifications", "youllBeNotified": "You’ll be notified", "verificationFailed": "Verification failed", "verifyMeAgain": "Verify me again", "startKycFailedCopy": "We apologize for the failed KYC verification and any inconvenience it may have caused during your onboarding process.", "kycInProgressNotificationsCopy": "Turn on notifications and we’ll update you on the\nstatus of your verification.", "selectFundingMethod": "Select funding method", "cashTransfer": "Cash transfer", "payWithWalletBalance": "Pay with Wallet Balance", "fundUsingYourLocalCurrency": "Fund using your local currency", "externalWallet": "External wallet", "depositFundsFromExternalWallet": "Deposit funds from an external wallet", "asset": "asset", "select": "Select", "fundCardSelectAssetSubCopy": "Fund your card with your wallet balance", "fundDaSelectAssetSubCopy": "Fund your account with any of the supported \nstablecoins", "fundCard": "Fund card", "total": "Total", "cardCreation": "Card creation", "easyGlobalPayments": "Easy global payments", "cardIntroVirtualCardsSubCopy": "Make fast payments online and in-store, anywhere in the world, with your own {currency} virtual card.", "processingFee": "Processing fee", "youWillPay": "You will pay", "fundingYourCard": "Funding your card ", "youPay": "You pay", "virtualCard": "Virtual card", "fundYourCard": "Fund your card ", "spendYourDollarsAnywhere": "Spend your dollars\nanywhere", "spendYourDollarsAnywhereSubCopy": "Fund your card anytime using your local currency or crypto, at the best possible rates.", "cardRequestSuccessful": "Card Request Successful", "confirmingYourTransaction": "Confirming your transaction..", "confirmingTransactionSubCopy": "We'll keep checking and let you know once received. You can leave this page anytime.", "minimum": "Minimum", "fundingAmountTooLow": "Funding amount too low", "cardRequestSuccessSubCopy": "You're all done! We'll notify you soon once your card is activated and ready for use.", "kycLoaderMessage": "Redirecting to 3rd party ID service to complete your verification... ", "wellUpdateYouOnTheStatusOfYourVerification": "We’ll update you on the status of your verification.", "activateUSDCard": "Activate your {currency} card", "stepCloserToGettingCard": "You're one step closer to getting your first Onboard debit card!", "details": "Details", "cardName": "Card name", "cardType": "Card type", "cardCreationFee": "Card creation fee", "oneTimeFeeToCreateCard": "A one-time fee to create your card", "visaVirtual": "Visa virtual", "viewCardUsageTerms": "View card usage terms", "cardUsageAndTerms": "Card usage terms", "pleaseReadAndAccept": "Please read and accept the following to use this card.", "addCardAppleGooglePay": "Add card to Apple / Google Pay", "addCardAppleGooglePaySubCopy": "When added, your card can be used for contactless payments", "minimumCardBalance": "Minimum card balance", "keepYourCardFundedWithAtLeast": "Keep your card funded with at least this amount", "fees": "Fees", "insufficientFundCharge": "Insufficient fund charge", "insufficientFundChargeSubCopy": "A charge may apply each time a card transaction fails due to not being funded", "monthlyMaintenance": "Monthly maintenance", "monthlyMaintenanceSubCopy": "This helps keep your card functioning at all times", "transactionLimit": "Transaction limits", "maxDailySpend": "Max. daily spend", "maxMonthlySpend": "Max. monthly spend", "iAccept": "I accept", "payWithExternalWallet": "Pay with External Wallet", "pay": "Pay", "sendAtLeast": "Send at least", "myVirtualCardAddress": "My virtual card address", "qrCode": "QR code", "iHaveMadeExactPayment": "I have made exact payment", "cardAddressCopied": "Card address copied", "yesCancel": "Yes, cancel", "payWithCashTransfer": "Pay with Cash Transfer", "cancelPaymentSubCopy": "Please only cancel this payment if you have not yet initiated a transfer.", "amountCopied": "Amount copied", "processingFeeInfoCopy": "This covers the cost of your transaction - primarily for currency conversion and transaction fees.", "cancelPayment": "Cancel payment", "cardStatus": "Card status", "fund": "Fund", "noTransactionsYet": "No transactions yet", "cardActions": "Card actions", "addToGooglePay": "Add to Google Pay", "cardDetails": "Card details", "cardHolderName": "Cardholder name", "cardNumber": "Card number", "expiryDate": "Expiry date", "cvc": "CVC", "billingAddress": "Billing address", "show": "Show", "street": "Street", "city": "City", "state": "State", "zip": "ZIP", "coutry": "Country", "copyVirtualCardAddressCopy": "Tap to copy your wallet address or scan the QR code.", "wantADifferentPaymentMethod": "Want a different payment method? ", "hubFindUsefulAppsOnchain": "Hub. Find useful apps,\nonchain", "discoverAppsYouNeed": "Discover apps you need", "weveCuratedGreatAppsToInspireYou": "We've curated great apps to inspire and entertain you", "fundViaExternalWalletWarning": "to the address below.\nSending lower or from a different network may lead to loss of funds.", "more": "More", "freeze": "Freeze", "makeCardInactive": "Make card temporarily inactive", "deleteCard": "Delete card", "deleteCardWarning": "Deleting your card is final. There may be a  wait period before you can create a new one. Any unused balance will be sent to your Onboard Wallet.", "yesDelete": "Yes, delete", "cardFrozen": "Card frozen", "cardUnfrozen": "Card unfrozen", "cardTransactions": "Card transactions", "verifyingOtp": "Verifying otp", "thisCardIsFrozen": "This card is frozen!", "unfreeze": "Unfreeze", "free": "Free", "maximum": "Maximum", "fundingAmountTooHigh": "Funding amount too high.", "wantToFund": "Want to fund?", "transfer": "Transfer", "wantToFundCardSubCopy": "To fund your card, please switch to a supported network", "wantToFundDaSubCopy": "To fund your account, please switch to a supported network", "withdrawFromCard": "Withdraw from card", "withdrawFromCardOptionsCopy": "Where would you like to send funds to?", "sendToWalletBalance": "Send to Wallet Balance", "withdrawalAmount": "Withdrawal amount ", "youWillReceive": "You will receive", "sendToLocalCurrency": "Send to Local currency", "securedByOnboardFast": "Secured by OnboardFast®", "receivingAccount": "Receiving account", "change": "Change", "moveFundsToYourSpendingCard": "Move funds to your {cardCurrency} card", "overdueFees": "Overdue fees", "fixHereToAvoidCardTermination": "Fix here to avoid card termination", "youHaveOverdueFees": "You have overdue fees!", "cardWillDeactivateIn": "Card will deactivate in", "fundingRequired": "Funding required", "cardMaintenanceFeeAndMonth": "Card maintenance fee", "cardTopUp": "Card top-up", "cardDeactivated": "Card deactivated", "tapToViewMoreDetails": "Tap to view more details", "cardDeActivated": "Card deactivated!", "overdueFeesExplainerCopy": "To ensure your card always works, we require periodic service payments. Not paying this means we’ll no longer be able to provide the service to you.", "cardTopUpInfoCopy": "Your card requires a minimum balance to remain active. Top ups are needed when your balance falls below this amount.", "youHaveOverdueFeesSubCopy": "You need to fund your card for the items below to keep it active.", "whyDidThisHappen": "Why did this happen?", "setUpNewCard": "Set up new card", "featureNotAvailable": "Feature not available", "cardFeatureNotAvailableSubCopy": "We can't offer a card to you just yet, but please get in touch with us if you'd like to learn more.", "pleaseWait24hrsBeforeSettingUpACard": "Please wait{timer}before setting up a new card", "addToAppleWallet": "Add to Apple Wallet", "signToAuthorizeAction": "Sign to authorize this action", "authorizeUnfreezeCard": "I authorize unfreezing my card to reactivate it for transactions", "cardReactivated": "Card reactivated", "noteTermForFrozenCard": "Your card is now frozen, but please note the following terms", "attemptedTransactionWillFail": "While frozen, any attempted transactions on this card will fail", "canUnfreezeAnytime": "You can unfreeze card anytime", "cardFundingRequired": "Card funding required!", "yourCardIsBelowMinimumBalance": "Your card is below minimum balance", "cardBalanceIsLow": "Card balance is low!", "addFundsToAvoidCardCharges": "Add funds to avoid card charges", "toEnsureUpcomingPaymentsGoThrough": "to ensure upcoming payments go through", "willIncurAFee": "will incur a fee.", "currentBalance": "Current balance - {balance}", "minFundingRequired": "Min. funding required", "thisKeepsYourCardAboveTheMinimumBalanceRequired": "This keeps your card above the {minimumBalance} minimum balance required to stay active", "cardFrozenSubCopy": "Your card has been frozen. To fix, fund your card to meet the minimum balance required.", "unPaidFeesMayLeadToYourCardBeingDeactivated": "Unpaid fees may lead to your card being deactivated", "aFailedTransactionDueToInsufficientFunds": "A failed transaction on your card due to insufficient funds ", "chooseAccount": "Choose account", "whereYouWantToReceivePay": "Where do you want to receive funds in?", "cancelled": "Cancelled", "tryAgain": "Try again", "rateIsExpiredPleaseTryAgain": "Rate is either expired or invalid, please try again.", "whatIsYourName": "What’s your name?", "enterFullName": "Enter your full name just as it is on your", "officialID": "official ID", "firstName": "First name", "lastName": "Last name", "username": "Username", "setUsernameTobeUsedAcrossOnboard": "Set a username to be used across Onboard", "verifyPhoneNumberToSecureAccount": "We’ll verify this number to secure your account.", "changeNumber": "Change number", "verified": "Verified", "addPhoneNumber": "Add phone number", "startIdentityVerificationSubCopy": "We need a little information to get\nstarted. This will help us secure your\ncard and protect you from fraud.", "startIdentityVerificationTitle": "To use cards, let’s verify\nyour identity", "startKycTitle": "Now, let’s verify your\nPhoto ID", "startKycSubCopy": "This will establish your identity, and prevents someone else from claiming your account.", "authorizeCardTermination": "I authorize terminating my card.", "sendToOtherWallets": "Send to other wallets", "yourCardHasBeingFunded": "Your card has being funded", "selectNetwork": "Select network", "wantToWithdraw": "Want to withdraw?", "addYourAccount": "Add your account", "withdrawalAmountTooHigh": "Withdrawal amount too high", "withdrawalAmountTooLow": "Withdrawal amount too low", "cardWithdrawalMinimumBalanceWarning": "<PERSON><PERSON><PERSON> will set card below the min. balance of {minBalance}", "wantToWithdrawSubCopy": "To withdraw from your card, please switch to a supported network", "cardWithdrawalSelectAssetSubCopy": "Which token do you want to receive at your destination wallet?", "cardCreationFeeSubCopy": "A one-time fee to create your card", "selectIdToVerify": "Select ID to verify", "selectIdSubCopy": "You’ll be required to take a clear picture of the ID", "idType": "ID Type", "selectIdentityCard": "Select ID", "fundingAmount": "Funding amount", "cardDeactivatedSubCopy": "Your card ({label}**{maskedNumber}) has been terminated, and can no longer be used for transactions. If applicable, your balance will be sent to your wallet.", "whatLocationDoYouLive": "What location do you live in most often?", "invalidFirstName": "Invalid name. Numbers and special characters are not allowed", "invalidLastName": "Invalid name. Numbers and special characters are not allowed", "usernameValidationError": "Must be at least 5 characters", "usernameTaken": "This username has already been taken", "funding": "Funding", "purchase": "Purchase", "refund": "Refund", "charge": "Charge", "inProgress": "In progress", "successful": "Successful", "addAccount": "Add your account", "inLessThan10Mins": "In less than 10mins", "homeAddress": "Home address", "homeAddressSubCopy": "We only use this to confirm your personal info", "addressLineOne": "Address line 1", "addressLineTwo": "Address line 2 (optional)", "stateOrRegion": "State/Region", "postalCode": "Postal code", "addressIsRequired": "Address is required", "stateIsRequired": "State is required", "countryIsRequired": "Country is required", "postalCodeIsRequired": "Postal code is required", "cityIsRequired": "City is required", "complete": "Complete", "rateHasBeingUpdated": "Rate has being updated to {formattedRate}", "cardFunding": "Card funding", "partiallyFunded": "Partially funded", "pendingVia": "Pending via", "youveReadAndAcceptedTheFollowingToUseThisCard": "You've read and accepted the following to use this card.", "activateProfile": "Activate profile ", "tradeCrypto": "Trade crypto", "protectYourWallet": "Protect your wallet", "enablePasscodeOrBiometrics": "Enable passcode or biometrics", "addAPhoneNumber": "Add a phone number", "weCanContactIfYouNeedUs": "We can contact if you need us", "tellUsAboutYou": "Tell us about you", "letsGetYouTheBestOfOnboard": "Let's get you the best of Onboard", "comingSoon": "Coming soon", "createYourFirstAd": "Create your first ad", "startMakingProfitOnOnboard": "Start making profit on Onboard", "helpUsBuildYourIdealApp": "Help us build your ideal app", "addCryptoToGetStarted": "Add crypto to get started", "dob": "Date of birth", "dobSubCopy": "Enter your date of birth as it is on your official ID", "enterYourIdNumber": "Enter your {document} number.", "idNumber": "ID number", "idOrCountryNotSupported": "Your ID or country not is not supported", "countryNotSupported": "Your country is not supported", "others": "Others", "manage": "Manage", "manageAssets": "Manage assets", "addedToFavourites": "Added to favourites", "removedFromFavourites": "Removed from favourites", "tokenHidden": "Token hidden!", "tokenHiddenSubCopy": "It will no longer show up on your asset list. \nWant to hide all tokens like this? \nVisit Settings > Preferences > Manage tokens ", "settingUpYourCard": "Setting up your card 🎉", "cardActivationSubCopy": "It’ll be ready for you to use within 24 hours", "scheduledChatNotificationTitle": "Thanks for getting Onboard! 🚀", "formattedCardRate": "{cardCurrencySymbol}1.00 = {conversionRate} {assetCode}", "scheduledChatNotificationBody": "We'd love to help you get started with some tips - tap here to chat with us", "possibleSpam": "Possible spam", "cardControlsForTotalSecurity": "Card controls for total security", "savedAddresses": "Saved addresses", "saveAddress": "Save address", "addAddress": "Add address", "egMyFirstWallet": "Eg. My first wallet", "noSpecificAsset": "No specific asset", "universalAddresses": "For Universal addresses. ", "learnMoreWithArrow": "Learn more >", "learnMore": "Learn more", "circulatingSupply": "Circulating Supply", "twentyFourHourVolume": "24h Volume", "rank": "Rank", "marketCap": "Market Cap", "selectSuitableNetwork": "Select suitable network", "addressSaved": "Address saved", "addressDeleted": "Address deleted", "selectAnAddress": "Select an address", "addWalletAddress": "Add wallet address", "delete": "Delete", "edit": "Edit", "cardBalance": "Card balance", "thisWillBeAddedToYourOnboardWalletBalance": "This will be added to your Onboard Wallet balance", "cardTerminationFeeExplainer": "This covers the cost of your transaction - primarily for currency conversion and transaction fees.", "universalAddress": "UNIVERSAL ADDRESS", "universalAddressExplainer": "A universal address is a single address that can receive all tokens that match its network. Think of it like an ‘account number’ for receiving funds. \nEg: Your Onboard address", "deleteAddress": "Delete address?", "deleteAddressSubCopy": "Are you sure you want to delete this address? This can't be reversed", "yesDeleteThisAddress": "Yes, delete this address", "youPaid": "You paid", "youReceived": "You received", "deleteCardSubCopy": "This action is final. There may be a wait period before you can create a new card.", "amountFunded": "Amount funded", "amountWithdrawn": "Amount withdrawn", "connectingMayTakeAFewSeconds": "Connecting may take a few seconds", "walletConnect": "WalletConnect", "switchToExchangeSupportedNetwork": "Switch between supported crypto networks to trade", "fxTransactionsSubtitle": "Transactions in other currencies (or at non-{countryCode} merchants) incur currency conversion fees", "non": "Non", "screenshotWarningCopy": "Screenshots aren't a safe way to keep track of your Secret Recovery Phrase. Store it somewhere that isn't backed up online to keep your wallet safe.", "heyWhatsYourEmail": "Hey, what’s your email?", "haveOtherWallets": "Have other wallets?", "yourWalletAddress": "Your wallet address", "letsSecureYourWallet": "Let's secure your wallet", "stepsNeeded": "Steps needed", "setAPassCode": "Set a passcode", "finishWalletBackUp": "Finish wallet backup", "flameEmoji": "🔥", "letsGo": "Let’s go", "hundredPercentEncrypted": "100% encrypted", "yourWalletIsSecure": "Your wallet is secure!", "yourWalletIsSecureSubCopy": "All backed up. Remember, no one else can access this wallet, not even us \nat Onboard", "finishWalletBackUpSubCopy": "Backup your wallet to the cloud to regain access if you log out or switch devices", "a6DigitPasscodeForLoginOnThisDevice": "A 6-digit passcode for login on this device", "welcomeBackSubCopy": "You have an existing account on Onboard.\nChoose a sign in option", "invalidEmailAddressPleaseCheckAndTryAgain": "Invalid Email Address. Please check and try again.", "enable2fa": "Enable 2FA", "enable2faSubCopy": "To securely regain access to your account when you logout or lose device", "phoneVerification": "Phone verification", "googleAuthenticator": "Google authenticator", "endToEndEncrypted": "End to End Encrypted", "backUpErrorMessage": "Something went wrong with this backup.\nBut not to worry, let’s retry this", "neverMissATransaction": "Never miss a transaction", "neverMissATransactionSubCopy": "Onboard sends notifications to keep you updated on all your \n account activity", "youReceived1380": "You received $1,380", "paymentSuccessful": "Payment successful", "retryBackup": "Retry backup", "creditCardEmoji": "💳", "youSent250USDT": "You sent 205 USDT", "moneyWingsEmoji": "💸", "nowText": "now", "minutesAgo": "{minute}m ago", "cloudBackUp": "Cloud backup", "min": "min", "backUpNow": "Back up now", "doThisLater": "Do this later", "backingUpWallet": "Backing up wallet", "areYouSure": "Are you sure?", "illTakeTheRisk": "I’ll take the risk", "finishBackUp": "Finish backup", "walletBackup": "Wallet backup", "backUpDetails": "Backup details", "googleDrive": "Google Drive", "iCloud": "iCloud", "lastCheckedDate": "Last checked {date}", "restoreWallet": "Restore wallet", "tapButtonBelowToGetStartedOnRecovery": "Tap button below to get started on wallet recovery", "recoverAccountSubCopy1": "Your Onboard account has an existing crypto wallet address attached to it ", "recoverAccountSubCopy2": "Tap preferred button below to get started on wallet recovery", "noCloudBackUpErrorMessage": "We couldn't find a recovery file on\nthis account. Please retry by connecting another {Drive} account.", "skipAccountBackUpWarningSubCopy": "Backing up your wallet helps you recover your wallet in the future if you ever need to. It's an essential security step.", "backingUpAccountSubCopy": "We're safely backing up your wallet to your {iCloud}. This should be quick", "securingAccount": "Securing wallet", "chooseWalletToRecover": "Choose wallet to recover", "confirmingAccountBackUp": "Confirming wallet backup", "multipleWalletsFoundInYourCloudStorage": "Multiple wallets found in your cloud storage. \nSelect one to recover", "securingAccountSubCopy": "We’re encrypting your details. Wall<PERSON> is almost ready", "finishAccountBackUpSubCopy1": "Back up your wallet recovery details to help you regain access when you log out or switch devices", "finishAccountBackUpSubCopy2": "No one else can access this wallet, \nnot even us at Onboard", "yourWalletIsReady": "Your wallet is ready!", "addressActLikeAccount": "Address above acts like an 'account number' for receiving funds.", "youHaveTotalControl": "It’s self-custody, which means you have total control over your funds.", "onGoingOrders": "Ongoing orders", "multipleActiveOrdersCopy": "Multiple active orders  •  Tap to review", "tapToReview": "Tap to review", "youreSelling": "You’re selling {token}  •", "howWouldYouLikeToAddCrypto": "How would you like to add crypto?", "youreBuying": "You’re buying {token}  •", "dropbox": "Dropbox", "extraBackupOptions": "Extra backup options", "multipleCloudService": "Back up with multiple cloud services for extra security", "backup": "Back up", "backedUpTo": "Backed up to {drive_name}", "backUpDetailDialogSubCopy": "Your account recovery file is backed up to your cloud storage", "recoveringYourAccount": "Recovering your wallet", "backupLogoutNudgeSubCopy": "To sign back in, we'll require authentication to regain access to your account.", "importedWalletLogoutSubCopy": "To sign back in, you’ll be required to provide your recovery details to regain access to your account.", "selectCurrency": "Select currency", "phoneNumber": "Phone number", "wantToEditDetailsReachOutToUsViaOur": "Want to edit details? Reach out to us via our", "invalidPhoneNumber": "Invalid phone number.", "youHaveNoMoreAttemptsLeft": "You have no more attempts left", "inProgressCompletesWithin15Mins": "Funding in progress. Completes within 15 mins", "cardCryptoFundingInProgressSubCopy": "We’ve received a deposit of {amount} for your virtual card and are now funding it.", "sitTightThisShouldBeDoneIn15mins": "Sit tight, this should be done within \n15 minutes", "viewWalletAddressToSendCrypto": "View wallet address to send crypto ", "payWithCash": "Pay with cash", "payWithCashDepositsIsComingSoon": "{countryName} {countryCurrency} deposits is coming\nsoon", "changeCountry": "Change country", "fastest": "Fastest", "recommended": "Recommended", "sendAsCash": "Send as cash", "howYouWantToTransfer": "How would you like to transfer crypto?", "howYouWantToSell": "How would you like to sell crypto?", "transferCryptoToOtherWallet": "Transfer crypto to other wallets", "transferTokenToExternalWallet": "Transfer crypto to other wallets", "featuredApps": "Featured apps", "discoverAppsWeSpotlight": "Discover apps we spotlight. Curated by experts. Handpicked for you.", "onboardPaySubtitle": "Send {NGN} to a designated account number and receive {BNB} instantly", "cryptoToCashFast": "Crypto to Cash, fast", "transferCryptoGlobally": "Transfer crypto globally, swiftly converted to EUR or USD", "onboardPayOnRampSubtitle": "Pay USD or EUR to a dedicated account and receive {crypto}", "onboardPayOffRampSubtitle": "Send {crypto} to an address and receive USD or EUR in your account.", "instantPaySubtitle": "Send {NGN} to a designated account number and receive {BNB} instantly", "pickMerchantThatOfferGreatRate": "Pick from a selection of merchants that offer you great {USD} rates", "genericTransactionProviderSubtitle": "Select this provider to complete your {asset} transaction", "receivePayoutInYourBankAccount": "Receive {NGN} payout in your bank account within minutes", "redirectingToProvider": "Redirecting to {provider}", "convertToYour": "Convert to your ", "localCurrency": "local currency", "swapGenericErrorMessage": "We're unable to complete this swap. Please retry with a different amount, or contact support", "accountWasBackedUpToDriveWhichIsNotSupportedToThisDevice": "Account was backed up to {Drive_Type}, which is not supported on this device.", "aDrive": "a drive", "poorInternetConnectionPleaseTryAgain": "Poor internet connection. Please check your connection and try again.", "invalidHomeAddressTryAgain": "Invalid home address. Try again", "backupRecoveryItemSubCopy": "Account recovery details are backed up to your {cloud} storage. It helps you regain account access if you log\nout or switch devices", "withdrawalsUnavailablePleaseTryAgainSoon": "{NGN} withdrawals unavailable. Please try again soon.", "depositsUnavailablePleaseTryAgainSoon": "{NGN} deposits unavailable. Please try again soon.", "cloud": "cloud", "cardSuspended": "Card suspended!", "reactivateCard": "Reactivate card", "cardIsSuspended": "Card is suspended", "cryptoAddress": "Crypto address", "yourCardHasBeingSuspendedTemporarilyPleaseNoteTheFollowing": "Your card has been suspended temporarily. Please note the following", "thisWasCausedByMultipleFailedTransactionsOnMyCardDueToInsufficientFunds": "This was caused by multiple failed transactions on my card due to insufficient funds", "illEnsureMyCardIsWellFundedForAnyTransactionsOnIt": "I'll ensure my card is well funded for any transactions on it", "iCanReactivateNowButAnotherFailureWillLeadToMyCardBeingTerminated": "I can reactivate now, but another failure will lead to my card being terminated", "referAndEarnTitle": "Earn $$ for each friend you invite!", "youEarnBonus": "You earn a bonus when your friend", "signUpWithYourLink": "Signs up with your link", "verifiesIdentity": "Verifies their identity", "qualifyingMessages": "Performs qualifying action. See terms", "inviteFriends": "Invite friends 👋", "giftEmoji": "🎁", "getAUsdCard": "Get a {cardCurrency} card", "payAnywhereWithOnboard": "Pay anywhere with Onboard ", "payAnywhereWithAnOnboardCard": "Pay anywhere with an Onboard \ncard", "earnForEveryInvitee": "Earn $$ for every invitee ", "sellAnyCrypto": "Sell any crypto", "goFromAnyCryptoTokenToCash": "Go from any crypto token to \ncash", "getYourFriendsToSignUpAndEarn": "Get your friends to sign up and earn", "anyPendingCardFeesWillStillBeCharged": "Any pending card fees will still be charged.", "account": "Account", "personalDetails": "Personal details", "walletDetails": "Wallet details", "rateOurApp": "Rate our app", "weVeMovedThingsAround": "We’ve moved things around", "onboardIsFeelingBrandNew": "Onboard is feeling brand new!", "seeWhereToViewWalletSecurity": "See where to view wallet security", "sellAssetOrAnyCryptoTokenForFiat": "Sell {asset} or any crypto token for {fiat_currency}", "chooseFromAVarietyOfPaymentMethodsAndProviders": "Choose from a variety of payment \nmethods and providers", "connectAnotherAccount": "Connect another account", "provider": "provider", "howsYourExperienceSoFar": "How’s your\n experience so far?", "wellLoveToKnow": "We’d love to know!", "thanksForSharing": "Thanks for sharing", "aNewAndImprovedDashboard": "A new and improved \ndashboard ✨", "superChargeYourCryptoPortfolio": "Supercharge your crypto portfolio 📈", "superChargeYourCryptoPortfolioSubCopy": "You can now buy and sell any\ncrypto asset and keep track of\nprice changes over time", "tapTheNew": "Tap the new ", "aUnifiedViewForYourCrypto": "A unified view for your crypto", "aUnifiedViewForYourCryptoSubCopy": "We now show your balances across\nall networks, and a simplified view\nfor each token you hold", "noMatchingOffers": "No matching offers", "deliveryMethodNotAvailable": "Delivery method not available", "paymentMethodNotAvailable": "Payment method not available", "deliveryMethodNotAvailableSubCopy": "Unfortunately, the selected provider cannot process your desired transaction. Please choose a new one to proceed.", "changePaymentProvider": "Change payment provider", "changeDeliveryMethod": "Change delivery method", "newAndImprovedDashboardSubCopy": "Managing your money just got easier.\nYour card & crypto balances, now in one place", "takeAQuickTourOfWhatsChangedInTheApp": "Take a quick tour of what's changed\nin the app.", "wereGoingToKeepWorkingToImproveYourOnboardExperience": "We're going to keep working hard \nto improve your Onboard experience", "deliveryMethodsNotCurrentlySupportedForThisAsset": "Delivery methods not currently available for this asset", "shareReferralLinkCopy": "Join me and many others using Onboard to make their money borderless. Sign up with my link, earn a reward: {URL}", "idValidationErrorMessage": "This is an invalid ID number for this ID type", "transactionIdCopied": "Transaction ID copied!", "introducingDirectAccount": "Introducing Direct Account", "cryptoToCashInMinutes": "Crypto to cash in minutes", "howItWorks": "How it works", "receiveAsCash": "Receive as cash", "showMe": "Show me!", "youDontHaveAnyDirectAccounts": "You don’t have any {currency} direct\naccount", "whenYouSendStableCoinsToYourDedicatedWalletAddress": "When you send stablecoins to your dedicated wallet address, it's converted to cash in your bank account in minutes.", "noMoreDelaysOrManualTransactions": "No more delays or manual transactions", "directGivesYouDedicatedWalletAddressLinkedToAccount": "Direct gives you dedicated \nwallet addresses linked to your bank account", "hub": "<PERSON><PERSON>", "apps": "Apps", "debitCard": "Debit Card", "lifeStyle": "Lifestyle", "sellCrypto": "Sell crypto", "directAccount": "Direct account", "p2pExchange": "P2P Exchange", "sellAnyCryptoTokenForCash": "Sell any crypto token for cash", "tradeCryptoOnAMarketPlace": "Trade crypto on a marketplace", "earnRewards": "Earn rewards", "compass": "<PERSON>mp<PERSON>", "yesProceed": "Yes, proceed", "headsUpYoureBeingRedirectedToAnExternalApplication": "Heads up. You’re being redirected to an external application", "inviteFriendsEarnBonuses": "Invite friends, earn bonuses", "getTheBestPersonalFinanceTips": "Get the best personal finance tips", "addNewAccount": "Add new account", "yourOrderNeedsAttention": "Your {count, plural, =0{order} =1{order} other{orders}} needs attention", "verificationCancelled": "Verification cancelled", "howWouldYouLikeToFundYourCard": "How would you like to fund your card?", "fundingMethodIsCurrentlyNotAvailableForTheSelectedCountry": "Funding method is currently not available for the selected country", "withdrawalMethodIsCurrentlyNotAvailableForThisCountry": "Withdrawal method is currently not available for the selected country", "whereWouldYouLikeToTransferYourFundsTo": "Where would you like to transfer funds to?", "deliveryMethod": "Delivery method", "bankTransfer": "Bank Transfer", "noInternetConnectionError": "Please check your network connection and try again", "thisTransactionIsBeingProcessedByChargedByAmerchantOutsideTheUs": "This transaction is being processed by the merchant, outside the US", "thereforeACrossBorderFeeWillBeCharged": "Therefore, a cross-border fee will be charged separately.", "asThisIsANonUSDTransaction": "As this is a non-{currency} transaction", "anFXConversionFeeWillBeChargedSeparately": "an FX conversion fee will be charged separately.", "whyWasThisFeeCharged": "Why was this fee charged?", "currencyConversionFees": "Currency conversion fees", "okay": "Okay", "actionCouldNotBeCompletedPleaseContactSupport": "Action could not be completed. Please contact support", "kycFailedDescription": "Your KYC verification failed due to a data mismatch. Please try again.", "wantToLevelUp": "Want to level up?", "transferDirectlyToYourBankAccount": "Transfer directly to your bank account", "join10kPeopleGainingFinancialKnowledge": "Join 10k+ people gaining financial knowledge", "whatWouldYouLikeToDoNow": "What would you like to do now?", "investInCrypto": "Invest in crypto", "startSavingInDollars": "Start saving in dollars", "iWantToLearnAboutCrypto": "I want to learn about crypto", "dontWorryYouCanStillTryOutOtherOptionsLater": "Don't worry, you can still try out the other options later", "imHereToExploreTheApp": "I’m here to explore the app", "introducingOnboardVirtualCards": "The USD card that always works", "gotoDashboard": "Go to dashboard", "notifyYouWhenDone": "Don’t worry, we will notify you once successful", "protectTheValueOfYourMoney": "Protect the value of your money", "saveInUsdc": "Save in {tokenSymbol}", "chooseCrypto": "Choose crypto", "top": "Top", "gainers": "Gaine<PERSON>", "growYourMoneyWhenYouSaveInADigitalDollarLikeUSDC": "Grow your money when you save in a digital dollar like {tokenSymbol}", "usdcEqualsUSd": "1 {tokenSymbol} ≈ {rate} USD", "usdcIsUsRegulated": "{tokenSymbol} is US regulated", "withdrawYourDollarsAnytime": "Withdraw your dollars anytime", "easyAndFastWithdrawal247": "Easy & fast withdrawals, 24/7", "money": "Money", "earn": "<PERSON><PERSON><PERSON>", "crypto": "Crypto", "usdcRegulationSubCopy": "{tokenSymbol} is a digital dollar that is regulated and fully reserved. Reserves are held at US financial institutions", "choosePaymentMethod": "Choose payment method", "on": "on", "youCanSwitchNetworksHere": "You can switch networks here 👋", "needTOConvertYourMoneyToALocalCurrency": "Need to convert your money to local currency? We've got you", "featured": "Featured", "discoverCrypto": "Discover crypto", "seeMore": "See more", "connectOtherApplication": "Connect to other applications", "balanceHidden": "Balance hidden!", "balanceHiddenSubCopy": "Your balances will no longer be visible. \n Tap the icon beside your balance to unhide", "start": "Start", "finishSetup": "Finish setup", "finishSetupSubCopy": "Let's finish these tasks to get Onboard!", "myBoardPass": "My Board Pass", "importWalletWith": "Import wallet with?", "chooseHowToImport": "Choose how you’d like to import an \nexisting wallet ", "recoveryOrSeedPhrase": "Recovery or Seed phrase", "whatDoeThisMean": "What does this mean?", "recoverySeedPhraseDescription": "Your recovery or seed phrase is a 12 word special kind of password. It is another way to access your account and must be kept private", "privateKeyDescription": "Your private key is a 64 character special kind of password. It is one way to access your account. Never share it with anyone!", "enterYourPrivateKey": "Enter your private key", "pasteOrTypePrivateKeyHere": "Paste or type private key", "yourWalletIsNotBackedUp": "Your wallet is not backed up", "verifyBackupStatus": "Verify your backup status", "confirmBackup": "Confirm backup", "signToProcessTransaction": "Sign to process transaction", "toCompleteThisTransactionSignTheMessageBelow": "To complete this transaction, sign the message below", "currencyConversionFeesExplainer": "A fee applies for non-{currency} transactions or where the payment is processed by the merchant outside the {country}. This fee is from the card network, not Onboard.", "makePaymentGlobally": "Make payments globally", "cryptoBalance": "Crypto Balance", "myPortfolio": "My Portfolio", "discoverCryptoGem": "Discover crypto gems", "noProviderCopy": "Payment methods not currently available for this asset. Try another asset or network", "about": "About", "price": "Price", "getCryptoPortfolio": "Get a crypto portfolio", "startByBuying": "Start by buying crypto for \nas little as $1", "SetPasscodeAccountBackup": "Set passcode, account backup", "IncreaseYourSecurity": "Increase your security", "chooseDeliveryMethod": "Choose delivery method", "getStartedOnTradableCoin": "Get started on top tradable coins", "onNetworkName": "on {networkName}", "invalidWalletConnectUriTapToResume": "Missing or invalid. URI is not WalletConnect URI. \nTap anywhere to resume scan.", "review": "Review", "createYourNewCard": "Create your new card", "subscriptions": "Subscriptions", "shopping": "Shopping", "vacation": "Vacation", "cardCurrency": "Card currency", "cardBrand": "Card brand", "nameYourCard": "Name your card", "masterCard": "mastercard", "giveAccessToTransact": "You're giving access to transact with this token", "iCloudBackupPending": "Your wallet backup is not synced to icloud", "iCloudBackupPendingDescription": "To avoid loss of funds, ensure your device is connected to the internet and you have some iCloud storage available. Open Settings > [Your Name] > iCloud to manage your storage.", "or": "Or", "tryExtraBackup": "Try other backup options", "generateATransactionStatement": "Generate a transaction statement", "statement": "Statement", "nickName": "Nickname", "pickAColor": "Pick a color", "getCardStatement": "Get card statement", "requestStatement": "Request statement", "chooseYourTimeRange": "Choose your time range", "createANewCard": "Create a new card", "threeMonths": "3 months", "sixMonths": "6 months", "twelveMonths": "12 months", "turnOnNotification": "Turn on notification", "bridgeOnAnyNetwork": "Bridge on any network", "bridgeOnAnyNetworkSubCopy": "Move your crypto across networks. You can now swap crypto between our supported networks", "cardStatementRequestSuccessful": "Card statement request \nsuccessful", "allSetACopyOfYourCardStatementWillBeSentToYourMailInAFewMins": "All set. A copy of your requested\ncard statement will be sent to your\nemail in a few minutes", "insufficientStorage": "You have insufficient storage available on your Drive folder", "verifiedAddressNotCorrect": "The verified address on this account doesn't match backup address", "physicalCard": "Physical card", "createMultipleCards": "Create multiple cards", "youHaveReachedAMax": "You have reached a max", "okGotIt": "Ok, got it!", "youHaveReachedMaxSubCopy": "You can only have {max} Onboard virtual cards\nactive at a time. Deactivate any of your active\ncards to create a new one", "createMultipleCardsSubCopy": "You now have more options! Tap on the\nplus icon to add a new card", "kycInProgressSubCopy": "Should be complete within 10 minutes, but some can take up to 24 hours.", "yesSetUpANewCard": "Yes, set up a new card", "pleaseConfirm": "Please confirm", "creatingANewCardWillReplaceMyDeactivatedCard": "Creating a new card will replace my deactivated card", "ifTheDeactivationOccurredRecently": "If the deactivation occurred recently, there may be a wait period before I can create a new card.", "youHaveAPreviouslyTerminatedCardSubCopy": "You have a previous card ({brand} {maskedPan}) that was deactivated. ", "noRouteFound": "No route found", "refreshBalance": "Refresh Balance", "buyWithCash": "Buy with cash", "buyTokenWithYourLocalCurrency": "Buy {tokenSymbol} with your local currency", "buyWithCrypto": "Buy with crypto", "exchangeWithOtherCryptoPairs": "Exchange with other crypto pairs", "sellToReceiveCash": "Sell to receive cash", "sellToReceiveCrypto": "Sell to receive crypto", "receiveCashInYourLocalCurrency": "Receive cash in local currency", "inbox": "Inbox", "markAllAsRead": "Mark all as read", "notificationsSubtitle": "View your recent notifications here", "chatSubtitle": "Need some help? Leave us a message", "viewTransaction": "View transaction", "viewOrder": "View order", "yourInboxIsHere": "Your Inbox is here", "inboxTooltipDescription": "Your notification history and our help desk now lives here. Never miss any of the things that matter", "receiveCurrencyInYourBankAccount": "Receive {currency} in your bank account", "whatDoesThisMeanForMe": "What does this mean for me?", "kycVerificationFailed": "KYC verification failed", "kycVerificationInProgress": "KYC verification in progress", "kycVerificationSuccessful": "KYC verification successful", "emptyNotifications": "No notifications yet", "fundingUnavailable": "Funding unavailable", "fundingUnavailableDescription": "Unfortunately, the selected asset cannot process your desired transaction. Please choose another one to proceed.", "changeAsset": "Change asset", "pleaseTryAnotherAsset": "Funding not currently available for this asset. Please try another asset.", "HeadsUp": "Heads up", "nonStableCoinCardFundingNote": "Cards can be funded with cash or crypto. You've selected an asset which will first be swapped to a USD-based token in order to fund your card", "yesIUnderstand": "Yes, I understand", "fundingCardWithXAmount": "We're funding your card with {x} and will notify you when complete", "travel": "Travel", "work": "Work", "letsFindOut": "Let’s find out", "wereSoExcitedToHaveYouSubCopy": "We're so excited to have you Onboard. Let's set your wallet up", "setUpWallet": "Set up  wallet", "youReInCopy": "{name}, you're in!", "itsNotYouItsUs": "It's not you, it's us", "badFitSurveySubCopy": "It doesn't appear our current product is the best fit for your needs right now. We'd love to let you know if this changes in future.", "tapToActivateCard": "Tap to activate card", "kycAttemptsInfo": "To keep Onboard safe and secure, we require verified KYC for transactions involving cash.", "onboardingSurveySubCopy": "We're proud you're considering\nOnboard, but first - it's important to us to know if we can serve your needs.", "noTransactionFound": "No Transactions found", "swapped": "Swapped", "unknownMethod": "unknown method", "searchWeb": "Search app or web", "disconnected": "Disconnected", "dappRedirectToBrowser": "Do you want to be redirected to this\nexternal app?", "open": "Open", "unableToGetFee": "Unable to get fee", "youGet": "You get", "transferAmount": "Transfer amount", "showLess": "Show less", "noSearchHistory": "You haven’t searched any sites yet", "history": "History", "moreCardsMoreOptions": "More cards. More options", "getAnotherCard": "Get another card", "moreCardMoreOptionsSubCopy": "Don't limit yourself to just one. You can now create multiple Onboard Cards to manage your payments.", "swapInsufficientBalanceError": "You don't have enough of the {tokenSymbol} to swap", "deleteAllTab": "Delete all tabs?", "deleteAllTabWarning": "This will clear all your currently active tabs", "close": "Close", "emptyTabMessage": "No open tabs yet", "tabs": "Tabs", "tab": "Tab", "slippage": "Slippage", "manageCrypto": "Manage crypto", "hiddenCrypto": "Hidden crypto", "confirmSlippage": "Confirm slippage", "yourTransactionMayBeFrontRunAndResultInAnUnfavourableTrade": "Transaction may be front run and result in an unfavorable trade", "slippageCannotBeMoreThan": "Slippage cannot be more than {amount}", "marketPricesCanChangeBetweenWhenYouPlace": "Market prices can change between when you place an order and when it is executed.\n", "thisPercentageIsCalledTheMaximumSlippage": "This percentage is called the maximum slippage ", "youreWillingToAcceptBeforeWeStopTheTransaction": "you're willing to accept before we stop the transaction.", "yourSwapWillNotContinueIfTheRateChangesMoreThan": "Your swap will not continue if the rate changes more than this percentage", "signOne": "Sign one", "signAll": "Sign all", "message": "Message", "sendInternationalPayments": "Send international payments", "wouldLikeToConnect": "would like to connect", "welcomeToYourDigitalMoneyApp": "Welcome\nto your\ndigital money\napp", "spendAnywhere": "SPEND\nANYWHERE", "exchangeFast": "EXCHANGE\nFAST", "ownYourMoney": "OWN\nYOUR MONEY", "acceptEurAndUsdGloballySwiftlyConvertedToDigitalDollars": "Accept EUR & USD globally, swiftly converted to digital dollars", "getMultipleCardThatJustWorksGlobally": "Get a debit card that just work, globally", "switchFromDigitalToRealWorldMoneyAndBack": "Switch from digital to real-world money, and back", "aWalletThatKeepsYouInFullControl": "A wallet that keeps you (and only you!) in full control", "cashToCryptoFast": "Cash to Crypto, fast", "unableToGetPaymentMethod": "Unable to get payment method", "unableToGetToken": "Unable to get token", "youHaveNotAddedAUsdOrEurAccountYet": "You have not added a {currency} account", "receiveInternationalPayments": "Receive international payments", "moveBetweenStablecoinsAndUsdEurAtLowFees": "Move between stablecoins and USD / EUR at low fees", "globalPaymentsMadeEasy": "Global payments, made easy", "thisSwapFailedTryIncreasingYourSlippage": "This swap failed. Try increasing your slippage", "insufficientBalanceSwapCopy": "You have insufficient {symbol} to pay the fee for this transaction. Top up your {symbol} balance and try again.", "nftsInYourWallet": "NFTs, in your wallet", "nftTipContent": "Manage and interact with the NFTs you hold, right inside your wallet", "emptyNftDescription": "You don’t own any NFTs yet", "exploreNftShop": "Explore NFT shops", "nfts": "NFTs", "showMeText": "Show me", "recents": "Recents", "sortBy": "Sort by", "recentlyAdded": "Recently added", "collectionNameOrder": "Collection name (A - Z)", "aToz": "A - Z", "merchantCountry": "Merchant country", "fxFeeMayApply": "FX fees may apply", "inAppBrowser": "In app browser", "discoverMoreApps": "Discover more apps", "exploreHub": "Explore hub", "browseTheWeb": "Browse the web", "searchAndConnectToYourFaveAppsRightFromYourWallet": "Search and connect to your fave apps, right from your Wallet", "tapIntoAnyOfTheseCategoriesToLearnMoreAboutThem": "Tap into any of these categories to discover and learn more about them", "youCanNowSearchAndConnectToYourFavoriteApps": "You can now search and connect to your favourite apps", "specifyTheTokenYoureSwappingFrom": "Specify the token you're swapping from", "specifyTheTokenYoureSwappingTo": "Specify the token you're swapping to", "weEncounteredATemporaryIssuePleaseRetryThisTransaction": "We encountered a temporary issue. Please retry this transaction\"", "missingOrIncorrectDataInYoureRequest": "Missing or incorrect data in your request. Please check the details", "pleaseSelectADifferentTokenFromTheSourceToken": "Please select a different token from the source token to swap to.", "specifyTheAmountOfTokensYouWantToSwap": "Specify the amount of tokens you want to swap", "notEnoughTokensAvailableForThisSwapTryALowerAmount": "Not enough tokens available for this swap. Try a lower amount.", "thisTransactionWasInitiatedByTheMerchantIn": "This transaction was initiated by the merchant in", "wire": "Wire", "sepa": "SEPA", "ach": "ACH", "collection": "Collection", "properties": "Properties", "saveToPhotos": "Save to photos", "hideNFT": "Hide NFT", "savedToGallery": "Saved to Gallery", "savedToPhotos": "Saved to Photos", "myItems": "My Items {number}", "myItem": "My Item", "hideNFTCollection": "Hide NFT collection", "unHideCollection": "Unhide collection", "unhideNFT": "Unhide NFT", "nftIsVisible": "NFT is visible", "unhideNftDescription": "It will now show up on your portfolio list.", "hiddenNFTsCount": "Hidden NFTs {count}", "manageNFTs": "Manage NFTs", "nftHidden": "NFT hidden!", "nftHiddenDestription": "This NFT will no longer show up on your portfolio. To unhide visit “Manage”", "nftIsHidden": "NFT is hidden", "hiddenNFTs": "Hidden NFTs", "noHiddenNFT": "No hidden nft", "paymentReminders": "Payment reminders", "editFrequency": "Edit frequency", "amountUpdated": "Amount updated", "frequencyUpdated": "Frequency updated", "nextPaymentDateUpdated": "Next payment date updated", "wellNotifyYouDurationCopy": "We’ll notify you {duration}", "setUpPaymentReminders": "Setup payment reminders", "findActiveSubscriptions": "Find active subscriptions", "tellUsWhichOfYourPaymentsOccurOnARecurringBasisAndWellNotifyYouBeforeTheyComeDue": "Tell us which of your card payments occur on a recurring basis and we'll notify you before they come due.", "howOftenWillThisSubscriptionBeBilled": "How often will this subscription be billed?", "subscriptionDetails": "Subscription details", "addAsSubscription": "Add as subscription", "frequency": "Frequency", "startedOn": "Started on", "nextPayment": "Next payment", "remindMe": "Remind me", "cardUsed": "Card used", "oneWeekBefore": "1 Week before", "twoDaysBefore": "2 Days before", "oneDayBefore": "1 Day before", "annually": "Annually", "everySixMonths": "Every 6 months", "quarterly": "Quarterly", "monthly": "Monthly", "addNew": "Add new", "everyTwoWeeks": "Every 2 weeks", "weekly": "Weekly", "updateAmount": "Update amount", "activity": "Activity", "nextOn": "next on", "upcomingPayments": "Upcoming payments", "unMarkSubscription": "Unmark subscription", "subscriptionUnmarked": "Subscription unmarked", "markASubscription": "Mark a subscription", "yourSubscriptionWasAdded": "Your subscription was added", "addAnother": "Add another", "goToMySubscriptions": "Go to my subscriptions", "forgettingPaymentsAreNowAThingOfThePast": "Forgetting payments are now a thing of the past! 🤩", "weEncounteredAnErrorUnmarkingYourSubscription": "We encountered an error unmarking your subscription", "unmarkSubscriptionSubCopy": "This will only be removed from your Onboard subscription list. It won't cancel any future payments to the merchant", "editsHereWillOnlyAlterWhatYouSeeOnTheOnboardApp": "Edits here will only alter what you see on the \nOnboard app and not your subscription with the site", "stayOnTopOfYourSubscriptionsWhenYouSetUpPaymentReminders": "Stay on top of your subscriptions when you set up payment reminders", "fxFeesMayApplySubCopy": "When transactions are in a different currency from what your card is denominated in, an FX conversion fee may be charged separately.", "unableToBackupOnIcloud": "Your wallet is unable to backup to iCloud", "icloudBackupFailureTip": "Ensure your device is connected to the internet and you have some iCloud storage available. Open Settings > [Your Name] > iCloud to manage your storage.", "retryIcloud": "Retry iCloud", "tryOtherBackupOption": "Or try other backup options", "passkeyCloseDesc": "Creating a passkey is an essential security step that helps you easily authenticate your account. It takes 5 seconds to setup.", "passkeyCreated": "Passkeys created", "setAPasskey": "Set a passkey", "passkeys": "Passkeys", "passkey": "Passkey", "createAPassKey": "Create a passkey", "deletePasskey": "Delete your passkey?", "deletePasskeyWarning": "You will no longer be able to use this passkey to sign in. You can create a new passkey for this device at any time.", "addPasskey": "Add <PERSON>key", "passkeyDeleted": "Passkey deleted", "passkeyDeletionError": "<PERSON>key couldn't be deleted due to an error", "passkeyCreatedFailed": "Passkey registration failed", "passkeyListInfo": "With passkeys, you can speed up accessing your Onboard account on this device using just your biometric data.", "passkeyExistOnDevice": "Passkey already exists on device", "add2FA": "Add 2FA", "add2FASubCopy": "For extra account security, we recommend enabling at least one 2FA method", "recoverWithPasskey": "Recover with <PERSON><PERSON>", "recoverWithSMS": "Recover with SMS", "moreOptions": "More options", "emptyPasskeyDescription": "Use your fingerprint and face ID to speed up login. With passkeys, you can more securely complete transaction on Onboard.", "passkeyRecoveryError": "Passkey error. Please verify that this is the device associated with the registered passkey.", "lessOptions": "Less options", "kycFailedToStart": "KYC verification failed to start", "chooseVirtualCard": "Choose virtual card", "tapToUnlockFullFunctionality": "Tap to unlock full app functionality  →", "unlockFullAppFunctionality": "Unlock full app functionality", "appStoreVersionFunctionality": "This App Store version of the Onboard app lacks a few features, pending approval from Apple. While that's ongoing, we recommend accessing our full app functionality via TestFlight", "getTestFlightVersion": "Get the TestFlight version", "weWillVerifyPhoneNumberForRecovery": "We’ll verify this number for account recovery", "receivingCurrency": "Receiving currency", "estimatedRate": "Est. Rate", "chooseCurrency": "Choose currency", "tapToToggleCurrency": "Tap to toggle currency", "whatCurrencyDoYouWantToReceiveFundsIn": "What currency do you want to receive funds in?", "whatCurrencyDoYouWantToSendFundsIn": "What currency do you want to send funds in?", "receivingAddress": "Receiving address", "euro": "Euro", "unitedSatesDollars": "United Stated Dollars", "fundingInProgress": "Funding in progress", "yourCardGets": "Your card gets", "youPayExactly": "You pay exactly", "youWillGet": "You will get", "payWith": "Pay with", "youSend": "You send", "totalWellConvert": "Total we’ll convert", "getAUsBankAccount": "Get a USD bank account", "getAUsAccount": "Get a US account", "receiveDollarPayments": "Receive dollar payments ", "cash": "Cash", "aDedicatedUsVirtualAccount": "A dedicated USD virtual account", "quickActivationAndFunding": "Quick activation and fast funding", "toGetAUsAccountLetsVerifyYourIdentity": "To get a US account, let’s verify your identity", "wellRedirectYouToA3rdPartyService": "We’ll redirect you to 3rd party service.", "yourIdIsRequired": "Your ID is required", "accountStatus": "Account status", "tapToViewDetails": "Tap to view details", "addMoney": "Add money", "onlyFundAccountUsingAcceptedPartiesAndLimit": "Only fund account using accepted parties and limits. Avoid reversals & fees.", "seeGuidelines": "See guidelines", "accountName": "Account name", "accountNumber": "Account number", "routingNumber": "Routing number", "bankName": "Bank name", "bankAddress": "Bank address", "vaFunding": "$6 minimum. Only ACH transfers. ", "takes1to2Days": "Takes 1-2 days*", "takes": "Takes", "guidelines": "Guidelines", "acceptedPayments": "Accepted payments", "limits": "Limits", "firstPartyDeposits": "1st party deposits", "thirdPartyDeposits": "3rd party deposits", "onlyRailTransfer": "Only {rail} transfers", "accountDetails": "Account details", "congratsHereIsYourVaAccount": "Congrats! \nHere's your {currency} account", "startBridgeKycSubCopy": "We need a little information to get started. This will help us secure your account and protect you from fraud.", "accountTermsOfUse": "Account terms of use", "youveReadAndAcceptedTheFollowingToUseThisAccount": "You’ve read and accepted the following to use this account", "setUpAndFees": "Setup and fees", "setUpTime": "Setup time", "gettingYourAccountNumberTakes": "Getting your account number takes", "monthlyMaintenanceFee": "Monthly maintenance fee", "settlementTime": "Settlement time", "depositsReceivedWithin": "Deposits received within", "depositsBelowThisWillLeadToDelays": "Deposits below this will lead to delays", "chargedOnEachDepositToYourAccount": "Charged on each deposit to your account", "activationFee": "Activation fee", "minimumDeposit": "Minimum deposit", "thisServiceIsProvidedBy": "This service is provided by", "legal": "Legal", "forMoreInformationRegardingUseOfThisAccount": " For more information \nregarding your use of this account, view", "theirTermsOfService": "their Terms of \nService.", "wellChargeThisOneTimeFeeOnYourFirstDeposit": "We'll charge this one-time fee on your first deposit", "toCreateAUSDAccountReviewAndAcceptTheseUsageTerms": "To create a {currency} account, review & accept these usage terms", "accountNameCopied": "Account name copied!", "accountNumberCopied": "Account number copied!", "routingNumberCopied": "Routing number copied!", "bankNameCopied": "Bank name copied!", "bankAddressCopied": "Bank address copied!", "tapToActivateAccount": "Tap to activate account", "makeYourFirstDeposit": "Make your first \ndeposit", "fundAccount": "Fund account", "days": "days", "maintenanceFee": "maintenance fee", "creatingYourAccount": "Creating your account", "takingTooLong": "Taking too long?", "wellFinishThisAndNotifyYouOnceComplete": "We'll finish this and notify you once complete.", "tapHereToDismiss": "Tap here to dismiss", "activationInProgress": "Activation in progress", "yourAccountWillBeReadySoon": "Your account will be ready soon!", "youWithdraw": "You withdraw", "accountWithdrawalMinimumBalanceWarning": "<PERSON><PERSON><PERSON> will set account below the min. balance of {minBalance}'", "wereSettingThingsUpForYouThisShouldBeQuick": "We're setting things up for you. This should be quick!", "oneTimeFeeChargedOnFirstDeposit": "One-time {fee} fee charged on 1st deposit", "youGot": "You got", "youWithdrew": "You withdrew", "totalConverted": "Total converted", "cardGot": "Card got", "cardGets": "Card gets", "addHomeAddress": "Add a home address", "createNewAd": "Create new ad", "viewDetails": "View details", "viewAd": "View ad", "viewMessage": "View message", "viewNetwork": "View network", "viewOffer": "View offer", "viewRequest": "View request", "viewBroadcast": "View broadcast", "depositInProgress": "Deposit in progress", "depositCancelled": "Deposit cancelled", "withdrawal": "<PERSON><PERSON><PERSON>", "deposited": "Deposited", "withdrew": "Withdrew", "fundingOptionsTitle": "How would you like to fund?", "currencyBankTransfer": "{currency} bank transfer", "fiatTransferOptionSubtitle": "Add dollars with your details", "fundVirtualAccountWithOnboardWallet": "Fund your {USD} account with your wallet balance", "totalWeConvert": "Total we’ll convert", "usdAccountGets": "{currency} account gets", "payWithLocalCurrency": "Pay with Local currency", "yourAccountHaveBeenFunded": "Your account has being funded", "insufficientTokenBalance": "Insufficient {tokenSymbol} balance", "seeWhy": "See why", "insufficientGasFallBackCopy": "To cover network fees for this transaction, you'll need {BNB} token.", "insufficientGasCopy": "To cover network fees for this transaction, you'll need {fiatTokenValue} worth of {tokenSymbol} token.", "insufficientGasSubCopy": "Blockchain networks require fees to be paid in the native currency of the network. These fees aren't paid to Onboard, but to the network to securely validate your transaction.", "accountGets": "Account gets", "currencyBalance": "{currency} Balance", "fundingVirtualWithXAmount": "We're funding your account with {x} and will notify you when complete", "virtualAccountAddressCopied": "Virtual account address copied", "payWithOnboardPay": "Pay with Onboard Pay", "onboardPay": "Onboard pay", "sendToUSDBalance": "Send to {currency} balance", "quickQuestion": "Quick question", "whereYouHeardAboutUs": "Where did you hear about us?", "referralCode": "Referral code", "optional": "Optional", "whereYouHeardAboutOnboard": "Where did you hear about Onboard?", "exampleOfOtherChannel": "Eg In a community call", "sendToOnboardPay": "Send to Onboard Pay", "pendingSourceChainTransactionStatusMessage": "This transaction has been submitted to {network} and is awaiting confirmation. Sit tight, it should be done soon.", "pendingDestinationChainTransactionStatusMessage": "The transaction is awaiting confirmation on {network}'s network. We'll notify you once completed.", "refundRequiredTransactionStatusMessage": "Unfortunately, this transaction experienced an issue on the network. Please contact us for help with getting your token refunded.", "refundedTransactionStatusMessage": "Funds have been returned to your wallet. Thanks for your understanding.", "claimRequiredTransactionStatusMessage": "This transaction is still in progress, but requires a claim step to complete the transfer on {network}. Please contact us for help with this.", "notSentTransactionStatusMessage": "The transaction is in progress. We'll notify you once completed.", "actionRequired": "Action required", "refunded": "Refunded", "claimRequired": "Claim required", "submittedTransactionStatusMessage": "You've signed this transaction and it has been submitted to the blockchain. We'll notify you once completed.", "virtualAccount": "Virtual account", "walletBalance": "Wallet balance", "tradingBalance": "Trading balance", "usdBalance": "USD Balance", "usd": "USD", "defiWallet": "<PERSON><PERSON><PERSON>", "merchantBalance": "Merchant balance", "allAccounts": "All Accounts", "chooseYourSource": "Choose your source", "simpleCryptoInvesting": "Simple crypto investing", "yourKeysYourCoins": "Your keys, your coins", "newCard": "New card", "invite5Friends": "Invite 5 friends", "onboardsBetterWithFriends": "Onboard's better with friends", "seeWhatsNew": "See what's new", "checkOutOurLatestUpdates": "Check out our latest updates", "discover": "Discover", "shareFeedback": "Share feedback", "chooseCard": "Choose card", "whereWouldYouLikeToSendMoneyTo": "Where would you like to send money to?", "toAnyCryptoWallet": "To any crypto wallet", "tellUsWhatYoudLikeToSee": "Tell us what you'd like to see!", "featuresAccessDependsOnLocation": "The features you have access to depend on your location. You may change this, but be sure to only select a country where you can provide documents that prove you're legally authorized to be there.", "featuresAvailable": "Features available", "whichOfTheseBestDescribeYou": "Which of these best describe you?", "setPassKey": "Set Passkey", "forFasterLogins": "For faster logins ⚡", "skipPassCodeNote": "Skip passcode, use your fingerprint or face ID to access your account across your devices", "yourCompleteFinancialLife": "Your complete financial life, all in one place.", "doMoneyBetter": "DO MONEY\nBETTER", "whereDoYouCurrentlyLive": "Where do you live most of the time?", "passkeyAuthCancelled": "Passkey login cancelled", "anyCashAccount": "Any cash account", "whereAreYouSendingMoneyFrom": "Where are you sending money from?", "externalCryptoWallet": "External crypto wallet", "active": "Active", "terminated": "Terminated", "currency": "<PERSON><PERSON><PERSON><PERSON>", "youTransfer": "You transfer", "switchText": "Switch®", "activeOverdue": "Active overdue", "oneLastThing": "One last thing...", "whichBestDescribeYou": "Which of these best describe you?", "yourAccountIsReady": "Your account is ready!", "allSetExcitedToHaveYouWelcome": "All set. We're so excited to have you here. \nWelcome to Onboard ✨", "byTappingYouAgree": "By tapping ‘{param}’, you agree to Onboard’s", "and": "and", "privacyPolicy": "Privacy Policy", "termsOfUse": "Terms of Use", "createPasskey": "Create passkey", "toAnyCashAccount": "To any cash account", "sendToBankOrMobileMoneyWallet": "Send to bank or mobile money wallet", "whereToTransferFrom": "Where to transfer from", "sendMoneyAbroad": "Send money abroad", "usdAccount": "USD account", "usdVirtualAccount": "USD Virtual card", "multiCurrency": "Multi-currency payouts", "p2pTransfers": "P2P transfers", "tradingAccount": "Trading account", "toGetUSDAccount": "To get a USD account", "hereIsWhatYouNeed": "Here’s what you need", "provideBasicDetails": "Provide basic details", "identityDocument": "Identity document", "proofOfAddress": "Proof of address", "acceptTermsOfService": "Accept terms of service", "additionalQuestions": "Additional questions", "toGetUSDCard": "To get a USD card", "toStartSending": "To start sending", "kycInProgressStatusTitle": "Docs submitted!\nVerification is in progress", "kycInProgressStatusMessage": "Thank you for submitting required docs. Should be complete within 10 minutes, but some can take up to 24 hours.\n\nTurn on notifications and we’ll update you on the status of your verification.", "kycVerifiedStatusTitle": "Identity is verified", "kycVerifiedStatusMessage": "Your details were successfully verified!\nNow, you're good to go.", "dateOfBirthSubtitle": "Enter your date of birth as it is on your official ID", "pictureOfIdIsRequired": "Picture of the ID is required", "weWillRedirect": "We’ll redirect you to", "thirdPartyService": "3rd party service", "selectDocumentTitle": "Select document with proof of your address", "selectDocumentSubtitle": "You’ll be required to upload this document", "documentType": "Document type", "selectDocument": "Select Document", "uploadProofOfDocument": "Upload proof of address", "proofOfAddressSubtitle": "Doc must match details to be accepted", "issueDate": "Issue date", "uploadDoc": "Upload doc", "last90Days": "Last 90 days", "addressDocumentFormat": "pdf, jpg, jpeg & png format. 5 MB Max", "photoLibrary": "Photo Library", "takePhoto": "Take Photo", "chooseFile": "Choose <PERSON>", "useDifferentDoc": "Use a different doc", "uploading": "Uploading", "fillTheForm": "Fill the form below", "fillTheFormSubtitle": "Let us know how you plan on using your USD account", "employmentStatus": "Employment status", "occupation": "Occupation", "fundSource": "Source of funds", "chooseStatus": "Choose status", "chooseStatusSubtitle": "What's your current professional status?", "forMyself": "For myself", "forSomeoneElse": "For someone else", "lastOne": "Last one", "purposeOfAccount": "Purpose of account", "amountToBeSentOrReceived": "Amounts to be sent or received", "intermediaryStatus": "Intermediary status", "whoAccountFor": "Who's this account for?", "intermediaryStatusSubtitle": "Are you receiving or sending funds on behalf of someone other than yourself?", "chooseBand": "Choose transaction band", "transactionBandSubtitle": "How much do you intend to send and/or receive with this account monthly?", "purposeOfAccountSubtitle": "What’s the primary purpose for your account?", "browseAssets": "<PERSON><PERSON><PERSON>", "kycFailedSubCopy": "Your KYC verification failed due to a data mismatch. Please try again.", "invalidUsername": "Invalid username. Numbers and special characters are not allowed", "startKyc": "Start KYC", "portfolio": "Portfolio", "convert": "Convert", "assetPrices": "Asset Prices", "hot": "🔥 Hot", "losers": "📉 Losers", "newTextWithEmoji": "⭐ ️ New", "gainersWithEmoji": "📈 Gainers", "links": "Links", "twitter": "twitter", "reddit": "reddit", "coinGecko": "coingecko", "facebook": "facebook", "github": "github", "explorer": "explorer", "marketState": "Market State", "marketCapRank": "Market Cap Rank", "showMore": "Show more", "addAssetsTitle": "How would you like to add assets?", "whatYouNeedToKnow": "What you need to know", "externalDepositInfoOne": "Only deposit {USDC} from the {network} network to not lose funds.", "externalDepositInfoTwo": "Deposit at least {amount} {asset}. Sending below this amount may lead to a loss of funds or delays.", "confirmations": "confirmations", "minimumConfirmationsIs": "Minimum block confirmation is {min}", "minimumConfirmations": "Minimum confirmations", "processingTime": "Processing time", "blockConfirmations": "block confirmation(s)", "enterAnAmount": "Enter an amount", "memo": "Memo", "memoCopied": "Memo copied", "balances": "Balances", "transactionFailedTryAgain": "Transaction Failed, try again!", "transactionSent": "Transaction sent", "transactionFeeCopy": " Our payment partners may charge a small fee to process this transaction\nThe receiving amount is only an estimate.", "processingYourOrder": "Processing your order", "tradeNotAvailable": "Trade not available", "sendFunds": "Send Funds", "externalWalletTransferDesc": "Transfer crypto to other wallets", "transferAssetsTitle": "How would you like to transfer assets?", "sendWithAssetSymbol": "Send {tokenSymbol}", "tapToSelectNetwork": "Tap to select a network", "transferWarning": "Ensure that the address is correct and on the same network. Transactions cannot be reversed", "securityVerification": "Security verification", "sendingYourOrder": "Sending your order...", "blocks": "block(s)", "minNetworkAmountIs": "Min. network amount is {min}.", "withdrawalNotEnabledForNetwork": "<PERSON><PERSON><PERSON> not enabled for network", "enterTheSixDigitCodeSentTo": "Enter the 6-digit code sent to", "getTheCode": "Get the code", "submit": "Submit", "describleYourPurpose": "Describe your purpose", "youDontHaveAnAccountsInThisCurrency": "You don’t have accounts in this currency", "securedBy": "Secured by", "onboardFast": "OnboardFast®", "arrives": "Arrives", "instantly": "Instantly", "verificationOngoingSubtitle": "Verification ongoing, completes in 24hrs", "toGetStartedSetupYOurDefiWallet": "To get started, setup your DeFi wallet", "fastOnAndOffRamps": "Fast on & off ramps", "easyOnchainAccess": "Easy onchain access", "exploreDefiAppsAndConnectUpTo10PlusNetworksWithEase": "Explore DeFi apps & connect to up to 10+ networks with ease", "goFromCryptoToCashAndBackWithSeamlessPaymentOptions": "Go from crypto to cash and back with seamless payment options", "youreInFullControlOfThisWalletWithYourPrivateKeys": "You’re in full control of this wallet, with your private keys.", "chooseAuthenticator": "Choose authenticator", "sms": "SMS", "transactionPin": "Transaction Pin", "authenticatorApp": "Authenticator App", "switchVerificationMethod": "Switch to another verification method", "codeSentSuccessfully": "Code sent successfully", "allTransactions": "All transactions", "transaction": "Transaction", "minAmountRequired": "Min amount is {amount}", "maxAmountRequired": "Max amount is {amount}", "selectConvertAsset": "Select {from} Asset", "notes": "Notes", "cashWithdrawal": "<PERSON>", "cashWithdrawalWarning": "Withdrawing {BNB} or any other crypto asset to cash will require USD conversion.", "mobileNumber": "Mobile Number", "chipperCashTag": "Chipper Cash Tag", "whereDoYouWantToReceiveFundsIn": "Where do you want to receive funds in?", "chooseAPreferredMethod": "Choose a preferred method", "sendFundsToYourLocalBanks": "Send funds to your local banks", "noFee": "No fee", "confirmingYourVerificationStatus": "Confirming your verification status..", "unableToGetAssetDetails": "Unable to get asset details", "transactionCompleted": "Transaction completed", "iHaveTransferred": "I've transferred", "yourDepositIsBeingProcessed": "Your deposit is being processed", "confirmingYourDepositDescription": "We'll keep checking and let you know once received. You can leave this page anytime.", "getStartedToTransact": "SignIn/Signup below to transact", "getStartedToViewMarketData": "SignIn/Signup to view market Data", "required": "required", "checkingDepositStatus": "Checking deposit status...", "submittingKycJob": "Submitting your photo ID...", "whyHaven": "Why Haven?", "okayGotIt": "Okay, got it", "whyHavenOne": "Personalized financial & investment guidance from a dedicated assistant.", "whyHavenTwo": "Diverse investments in USD and 50+ digital assets like Bitcoin & Solana.", "whyHavenThree": "Coming soon: USD/EUR accounts, card cashback, payments in 30+ countries.", "whyHavenFour": "Premium: curated investments, concierge services, exclusive events.", "discoverMembershipBenefits": "Discover membership benefits", "confirmingDeposit": "Confirming deposit", "cancelConfirmation": "Cancel confirmation", "depositConfirmed": "deposit confirmed", "depositConfirmedDesc": "Get ready to explore a world of financial opportunities tailored just for you.", "createAPasskey": "Create a passkey", "passkeyDescOne": "With passkeys, you don’t need to remember complex passwords.", "passkeyDescTwo": "Instead, you can use your fingerprint, face, PIN or pattern to access your account across your devices.", "passkeyCreatedSuccessfully": "Passkey created successfully", "passkeySetup": "Passkey setup", "yourPasskeyIsCreated": "Your passkey is created!", "passkeyCreatedDesc": "You can now use your fingerprint, face scan, PIN or pattern to access to your wallet across your devices.", "encrypted": "encrypted", "passkeyAuthorizationFailed": "Passkey authorization failed.", "authorizing": "Authorizing...", "emailVerification": "Email verification...", "verifyWithPasskey": "Verify with passkey", "verifyWithPasskeyDesc": "Verify using your fingerprint, face scan, PIN or pattern on your device.", "verify": "Verify", "authenticateVia": "Authenticate via {auth}", "verifying": "Verifying", "authVerificationCode": "{auth} verification code", "verifyWith": "Verify with {auth}", "requestingCode": "Requesting code", "codeSent": "Code sent", "authorizationFailedTryAgain": "Authorization failed, try again", "noNetworkWithWithdrawalAvailable": "No network with withdrawal available", "noNetworkWithDepositAvailable": "No network with deposit available", "added": "Added", "noPasskeysTitle": "You don't have any passkeys", "noPasskeysDesc": "Passkeys help you authenticate your wallet faster, using your device’s biometrics or security PIN.", "deletePasskeyConfirmation": "By deleting your passkey, you'll lose access to secure and easy authentication of your wallet", "goBack": "Go back", "buyUsingNgnViaPtoP": "Buy using NGN via P2P", "selectAssetToFund": "Select asset to fund", "startTradingToday": "Start trading today", "startTradingTodaySubtitle": "Fund with USDT using P2P or from an external wallet", "depositUSDT": "Deposit USDT", "onboardingOptionsScreenTitle": "How would you like to use Haven?", "onboardingOptionsScreenSubtitle": "Don't worry, you can still try out the other options later", "tradeDigitalAssets": "Trade digital assets", "tradeDigitalAssetsDesc": "Deposit and swap BTC, SOL and more", "convertCryptoToCash": "Convert crypto to cash", "convertCryptoToCashDesc": "Near-instant withdrawals to bank", "USDCardsAndRewards": "USD Cards & Rewards", "USDCardsAndRewardsDesc": "Get cashback on every purchase", "internationalPayments": "International Payments", "internationalPaymentsDesc": "Receive USD, and send money globally", "vipAccess": "VIP Access", "vipAccessDesc": "All of the above + premium benefits", "selectAssetToSend": "Select asset to send", "beneficiaryNetwork": "Beneficiary Network", "sendingNetworkDesc": "This is the network where your assets will be sent out on.", "filter": "Filter", "filters": "Filters", "allAssets": "All Assets", "transactionType": "Transaction type", "resetFilters": "Reset filters", "applyFilters": "Apply filters", "viewAll": "View all", "setupSixDigitPin": "Set up 6-digit PIN", "setupPinSubtitle": "This allows you transact more securely on your device", "createYourPin": "Create your PIN", "setupPin": "Set up PIN", "setupYourSixDigitPin": "Setup your 6-digit PIN", "confirmYourSixDigitPin": "Confirm your 6-digit PIN", "changePin": "Change PIN", "createNewSixDigitPin": "Create a new 6-digit pin", "confirmYourNewSixDigitPin": "Confirm your new 6-digit pin", "confirmYourCurrentSixDigitPin": "Confirm your current 6-digit pin", "pinCreated": "<PERSON><PERSON> created 🎉", "pinDoesntMatch": "PIN doesn't match", "incorrectPin": "Incorrect pin. Please try again", "newPinCreated": "New pin created", "pinCreationFailed": "Pin creation failed", "pinChangeFailed": "Pin change failed", "verifyTransaction": "Verify Transaction", "enterYourSixDigitPin": "Enter your 6-digit PIN", "authMethodNotAvailable": "Authorization channel not available", "validationFailedIncorrectPin": "Oops! Incorrect pin", "validationFailedIncorrectOtp": "failed, Incorrect otp", "authorizationFailed": "authorization failed, try again", "oopsCurrentCodeSeemsIncorrect": "Current code seems incorrect", "noAssetsFound": "No assets found", "hideBalance": "Hide $0 balances", "noTransactionsFound": "No transactions found", "termsOfService": "Terms of Service", "andSign": "&", "enterOrPasteMemo": "Enter or Paste Memo", "whatWouldYouLikeToDo": "What would you like to do?", "growYourWealthWithDigitalAssets": "Grow your wealth with digital assets", "protectYourMoney": "Protect your money from devaluation", "greatChoiceNoComma": "Great choice", "greatChoiceWithComma": "Great choice,", "toKickstartYourCrypto": "To kickstart your crypto journey, please answer a few short questions. It’ll take less than 1 minute.", "letGetStarted": "Let’s get started", "howMuchExperienceCrypto": "How much experience do you have in crypto?", "whatYourPreferredInvestment": "What’s your preferred investment style?", "justAMoment": "Just a moment...", "weWillRecommendAPortfolioBasedOnYourAnswers": "We’ll recommend a portfolio based on your answers", "analyzingYourInterests": "Analyzing your interests", "analyzingYourExperience": "Analyzing your experience", "analyzingYourGoals": "Analyzing your goals", "youACryptoMaxi": "You’re a crypto maxi", "failToLoadData": "Failed to load data. Swipe down to refresh!", "last1YearTrend": "Last 1 year trend", "onboardIsNotAFinancialAdvisor": "Onboard is not a financial advisor. Digital assets are volatile, so do your own research", "byProceedingYouAgree": "By proceeding, you agree to the", "disclaimerColon": "Disclaimer:", "termDot": "terms.", "depositToStartTrading": "Deposit to start trading", "hereRecommendedPortfolio": "Here’s your recommended portfolio", "resultsAreIn": "Results are in", "investor": "Investor", "youAreA": "You're a", "youAreAn": "You’re an", "reviewInformation": "Review information", "actionRequiredTapHere": "Action required. Tap here", "actionRequiredDesc": "In order to process this transaction, we’ll need additional information from you.", "tapBelowToSubmitRequiredInfo": "Tap below to view and submit the required information", "iAmSendingToMyself": "I'm sending to myself", "beneficiaryFullName": "Beneficiary full name", "whoAreYouSendingTo": "Who are you sending to?", "beneficiaryInfoDesc": "We need this to comply with regulations. Incorrect details could delay or cancel your transaction", "invalidFullName": "Invalid full name, Numbers and special characters are not allowed", "beneficiaryInformation": "Beneficiary Information", "whatIsTheDestinationWallet": "What is the destination wallet?", "selfCustodyWallet": "Self-custody wallet", "selfCustodyWalletExamples": "e.g. MetaMask, Trust, Onboard", "exchangeExamples": "e.g. Binance, Coinbase, CEX.io", "welcomeBack": "Welcome back", "sessionLockDesc": "Your account is waiting securely! \nLogin below to continue", "youWereHereForAWhile": "You were away for a while", "yourAccountIsSecure": "Your account is secure! Tap the icon below to unlock using your device", "onGoingOrder": "Ongoing order", "youAreBuying": "You’re buying", "multipleActiveOrders": "Multiple active orders", "slideToReview": "Slide to review", "exitOnboardingNote": "You’ll need to fund your wallet to access savings and trading on Onboard.", "yesSkip": "Yes, skip", "veryAggressive": "Very Aggressive", "completingWantToContinue": "Completing this step helps us tailor the best experience for you. Want to continue?", "leavingSoonWithEmoji": "Leaving so soon? 🤔", "doNotMissOut": "Don’t miss out! 💰", "yourPersonalisedPortfolio": "Your personalized portfolio is ready - fund your wallet to start investing!", "yourWithdrawalLimits": "Your withdrawal limits", "perWithdrawal": "Per withdrawal", "dailyLimit": "Daily limit", "weeklyLimit": "Weekly limit", "withdrawalLimitsExceeded": "Withdrawal limit exceeded.", "viewLimits": "View limits", "minimumAmountIs": "Min. amount is {amount}.", "resetPin": "Reset pin", "passcodeResetSuccessful": "Passcode reset successful", "passcodeResetFailed": "Passcode reset failed", "forgotPin": "Forgot Passcode?", "reset": "Reset", "internationalMoneyTransfer": "International money transfer", "internationalMoneyMove": "Move money to multiple destinations across the globe", "superiorExchangeRate": "Superior exchange rates", "weOfferTheBestRatesPossible": "We offer the best rates possible for almost any currency. No hidden fees", "multipleDeliveryMethod": "Multiple delivery methods", "youCanSendMoneyToABankMobileMoney": "You can send money to a bank, mobile money or even a crypto wallet", "sendToACryptoWallet": "Send to a crypto wallet", "letsPersonalizeYourCryptoJourney": "Let's personalize your crypto journey", "verifyIdToStartTrading": "Verify ID to start trading", "noOptionsAvailableForThisQuestion": "No options available for this question", "whyDoINeedKycToTrade": "Why do I need KYC to trade", "whyDoINeedKycToTradeWithQuestionMark": "Why do I need KYC to trade?", "toKeepOnboardSecureAndCompliantWeRequireKycForCashTx": "To keep Onboard secure and compliant, we require verified KYC for transactions involving cash.", "dontWantKyc": "Don't want KY<PERSON>?", "setUpASelfCustodyWallet": "Setup a self custody wallet", "weOnlyRecommendWalletDesc": "We only recommend this wallet if you already have some knowledge on DeFi and blockchain networks.", "startTrading": "Start trading", "below18YearsError": "Sorry, your age does not meet our minimum requirement of 18 years.", "toStartTrading": "To start trading", "wireDepositFee": "Wire deposit fee", "chargedOnEachWireDepositToYourAccount": "Charged on each wire deposit to your account", "referralCodeExplainer": "This code is your referrer’s Board Pass (found in their 'Settings' menu). Enter the code here, so perks or rewards can be assigned", "kycIncomplete": "KYC verification incomplete", "getADedicatedUsBankAccount": "Get a dedicated USD bank account", "quickActivation": "Quick activation", "setUpAndStartReceivingPaymentsWithin1BusinessDay": "Setup & start receiving payments within \n1 business day", "getPaidByAnyone": "Get paid by anyone", "acceptAchAndWirePaymentsFromDeelAndMore": "Accept ACH & Wire payments from Deel, Payoneer, Upwork, Paypal & more", "flexibleFundingAndWithdrawal": "Flexible funding & withdrawal", "fundYourUsdAccountWithOrWithdrawToOtherCurrencies": "Fund your USD account with, or withdraw to, other currencies", "thereAreNoAssetsToDisplay": "There are no assets to display", "lastOnboardingKycQuestionSubtitle": "A few more details to close this out", "referral": "Referral", "spendGloballyWithUsdCard": "Spend globally with USD card", "usAccount": "US Account", "getATradingPortfolio": "Get a trading portfolio", "onboardAccounts": "Onboard accounts", "externalAccounts": "External accounts", "fromAnyCashAccount": "From any cash account", "sendFromAnyBankOrMobileMoneyWallet": "Send from any bank or mobile money wallet", "fromAnyExternalCryptoWallet": "From any external crypto wallet", "sendFromCryptoWallet": "Send from crypto wallet", "merchantWallet": "Merchant wallet", "beginYourCryptoInvesting": "Begin your simple crypto investing", "tapHereForQuickAccess": "Tap here for quick access to your accounts or to open a new one", "viewYourAccounts": "View your accounts", "switchBetweenAccount": "Switch between accounts", "aQuickSwipeAccount": "A quick swipe takes you to each account that you have ", "sendMoneyGlobally": "Send money globally", "failToLoadDataPleaseRefresh": "Failed to load data. Please refresh", "accessFastAffordableTransferMoney": "Access fast, affordable transfers to send money anywhere", "kycInReview": "KYC verification in review", "yourKycIsInReview": "Your verification is in review", "WeNeedMoreDetails": "We need a few more details", "basicIdVerification": "Basic ID verification", "additionalIdVerification": "Additional ID verification", "chooseYourPreferredCryptoBalance": "Choose your preferred crypto balance", "completeVerification": "Complete verification", "yourKycIsIncomplete": "Your KYC verification is incomplete", "kcyInCompleteSubtitle": "Thank you for starting the verification process. You have a few steps left to completion. It will take only 3 mins", "proofOfAddressTip": "Proof of address — Legible & matches ID info", "uKycIdTip": "Your ID — Upload a clear, untampered version", "provideConsistentInfo": "Provide consistent & valid information", "helpfulTipsWithColon": "Helpful Tips:", "selfieKycTips": "<PERSON><PERSON> — well-lit, face showing clearly.", "idDocumentKycTips": "ID document — clearly captured, all parts visible.", "idNumberKycTips": "ID number — please make sure to enter it correctly.", "tradingWalletCreationError": "Something went wrong.\\nBut not to worry, please retry", "errorOccurredTryAgain": "error occurred, try again", "startByFundingYourPortfolioWithAsLittleAsOneDollar": "Start by funding your portfolio with as little as $1", "recipientGets": "Recipient gets", "cryptoMaxi": "Crypto Maxi ✨", "cardTerminatedSuccessfully": "Card terminated successfully", "verificationAttemptLeft": "You have {kycRetriesLeft} retries left", "wellSendYouACodeToConfirmPhoneNumber": "We’ll send you a code to confirm phone number", "sendViaWhatsapp": "Send via WhatsApp", "sendViaSms": "Send via SMS", "viaWhatsapp": "Via WhatsApp", "viaSms": "Via SMS", "proofOfAddressFileTooLarge": "File is too large. Please upload a doc {size} or less", "invalidSessionId": "Invalid session Id", "anErrorOccurredPleaseTryAgain": "An error occurred, please try again", "noAuthMethodsAvailable": "No Authorization methods available", "noActiveAuthSession": "No active auth session", "verifyWithPasscode": "Verify with passcode", "verifyWithAuthenticatorApp": "Verify with authenticatorApp", "enterTheSixDigitCodeGenerated": "Enter the 6-digit code generated", "verifyWithDefiWallet": "Verify with <PERSON><PERSON>", "UnsupportedAuthenticationMethod": "Unsupported authentication method", "additionalIdVerificationPageTitle": "Provide ID for additional verification", "additionalIdVerificationPageSubtitle": "Choose ID type and provide ID number", "chooseIdType": "Choose ID type", "chooseIdTypeSubtitle": "You will be required to provide ID number", "idTypeNumber": "ID type number", "otherAdditionalIdVerificationDescription": "Please provide a description of the\ndocument type you want to provided", "describeDocument": "Describe document", "tryAnotherMethod": "Try another method", "confirmWithPasskey": "Confirm with passkey", "confirmWithEmail": "Confirm with email", "confirmWithPhoneNumber": "Confirm with phone number", "weNeedToVerifyItsReallyYouBehindTheScreen": "We need to verify that it’s really you behind the screen", "confirmViaPasskey": "Confirm via passkey", "confirmViaEmail": "Confirm via email", "confirmViaAccountPin": "Confirm via account pin", "confirmViaSMS": "Confirm via SMS", "confirmViaAuthenticatorApp": "Confirm via authenticator app", "confirmViaWallet": "Confirm via wallet", "chooseMethod": "Choose method", "confirmWithPasscode": "Confirm with passcode", "confirmWithAuthenticator": "Confirm with Authenticator", "enterTheCodeSentTo": "Enter the code sent to", "sendCode": "Send code", "resendIn": "Resend in {timer}", "viaWhatsApp": "Via WhatsApp", "viaSMS": "Via SMS", "emailMeACode": "Email me a code", "confirmWithWallet": "Confirm with wallet", "noDefiWalletFoundOnDevice": "No defi wallet found on device", "chooseYourPreferredAsset": "Choose your preferred asset ", "walletAuthorizationMessage": "Please sign to confirm authorization.\nYour signature proves that you are the rightful owner of this DeFi wallet.", "bank": "Bank", "toAccessGlobalPayment": "To access global payments", "sendExactly": "Send exactly", "iHaveSent": "I have sent", "thisWalletIsInWatchModeSubCopy": "This wallet is in watch mode. Recover wallet to perform transactions", "restoreYourWallet": "Restore your wallet", "restore": "Rest<PERSON>", "makeSureYouSendOnlyTo": "Make sure you send only to", "sendingFromADifferentNetwork": "Sending from a different network", "mayLeadToLossOfFunds": "may lead to loss of funds.", "sendDefiSwapWarning": "You're sending {BNB} which will first be converted to USD, and may cause slight variations in the final received {NGN} amount", "in10Mins": "in 10mins", "recipientGot": "Recipient got", "accountRestricted": "Account restricted", "accountDeactivated": "Account deactivated", "reachOutToUsForMoreInfo": "Reach out to us for more info", "accountDeactivatedSubCopy": "Your account ({bankName}, {maskedAccountNumber}) has been deactivated and cannot be used for transactions. Incoming deposits will be reversed.", "accountRestrictedSubCopy": "Your account ({bankName}, {maskedAccountNumber}) has been restricted and cannot be used for transactions. Incoming deposits will be reversed until resolved.", "accounts": "Accounts", "invalidPasskeySession": "Passkey registration failed. <PERSON><PERSON> again to try again.", "myUsdAccountAddress": "My {currency} account address", "resetPasscode": "Reset Passcode", "forgotPasscode": "Forgot Passcode", "updateYourPasscodeAnytime": "Update your passcode anytime", "setUpPasscodeToSecureAccount": "Setup passcode to secure account", "cantRememberYourCurrentPasscode": "Can't remember your current passcode?", "blockchainWithdrawal": "Blockchain <PERSON>"}