// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// StackedLocatorGenerator
// **************************************************************************

// ignore_for_file: public_member_api_docs, implementation_imports, depend_on_referenced_packages

import 'package:stacked_services/src/bottom_sheet/bottom_sheet_service.dart';
import 'package:stacked_services/src/dialog/dialog_service.dart';
import 'package:stacked_services/src/navigation/navigation_service.dart';
import 'package:stacked_services/src/snackbar/snackbar_service.dart';
import 'package:stacked_shared/stacked_shared.dart';

import '../api/onboard_wallet_client.dart';
import '../haven/services/assessment_lite_service.dart';
import '../haven/services/passkey_service.dart';
import '../haven/services/trading_account_service.dart';
import '../haven/services/trading_activation_service.dart';
import '../haven/services/trading_assets_service.dart';
import '../haven/services/trading_database_service.dart';
import '../haven/services/trading_deposits_service.dart';
import '../haven/services/trading_market_service.dart';
import '../haven/services/trading_portfolio_service.dart';
import '../haven/services/trading_trade_service.dart';
import '../haven/services/trading_transfer_service.dart';
import '../manager/toast.dart';
import '../services/analytics_service.dart';
import '../services/app_settings_service.dart';
import '../services/authentication_service.dart';
import '../services/authorization_service.dart';
import '../services/biometric_service.dart';
import '../services/card_service.dart';
import '../services/card_subscription_service.dart';
import '../services/cloud_backup/cloud_backup_service.dart';
import '../services/cloud_backup/google_drive.dart';
import '../services/cloud_backup/icloud_drive.dart';
import '../services/config_service.dart';
import '../services/crashlytics_service.dart';
import '../services/cross_swap_service.dart';
import '../services/crypto_service.dart';
import '../services/dapp_service.dart';
import '../services/database_service.dart';
import '../services/decryption_service.dart';
import '../services/direct_account_service.dart';
import '../services/dynamic_link_service.dart';
import '../services/encryption_service.dart';
import '../services/ethereum_service.dart';
import '../services/factory_ethereum_service.dart';
import '../services/feature_service.dart';
import '../services/fire_store_service.dart';
import '../services/inbox_service.dart';
import '../services/kyc_service.dart';
import '../services/life_cycle_service.dart';
import '../services/location_service.dart';
import '../services/nestcoin_rpc_database_service.dart';
import '../services/network_database_service.dart';
import '../services/network_service.dart';
import '../services/nfts_service.dart';
import '../services/onboard_exchange_service.dart';
import '../services/onboard_pay_service.dart';
import '../services/onboard_provider_service.dart';
import '../services/ongoing_order_service.dart';
import '../services/partner_database_service.dart';
import '../services/partners_service.dart';
import '../services/passcode_service.dart';
import '../services/passkey_service.dart';
import '../services/preference_service.dart';
import '../services/push_notification_service.dart';
import '../services/rails_service.dart';
import '../services/rates_service.dart';
import '../services/remote_config_service.dart';
import '../services/secure_app_service.dart';
import '../services/secure_storage_service.dart';
import '../services/send_service.dart';
import '../services/swap_service.dart';
import '../services/token_service.dart';
import '../services/transaction_db_service.dart';
import '../services/transaction_hash_monitoring_service.dart';
import '../services/user_service.dart';
import '../services/virtual_account_service.dart';
import '../services/wallet_auth_service.dart';
import '../services/wallet_bridge_service.dart';
import '../services/wallet_connect/wallet_connect_service.dart';
import '../services/wallet_navigation_service.dart';
import '../services/wallet_service.dart';
import '../services/web3_service.dart';
import '../services/web_view_service.dart';
import '../ui/views/cards/active_card_view/active_card_view_model.dart';
import '../ui/views/dashboard/main_dashboard/main_dashboard_viewmodel.dart';
import '../ui/views/home/<USER>';
import '../ui/views/hub/hub_viewmodel.dart';
import '../ui/views/notifications/notifications_viewmodel.dart';

final locator = StackedLocator.instance;

Future<void> setupLocator({
  String? environment,
  EnvironmentFilter? environmentFilter,
}) async {
// Register environments
  locator.registerEnvironment(
      environment: environment, environmentFilter: environmentFilter);

// Register dependencies
  locator.registerLazySingleton(() => NavigationService());
  locator.registerLazySingleton(() => DialogService());
  locator.registerLazySingleton(() => BottomSheetService());
  final preferenceService = PreferenceService();
  await preferenceService.init();
  locator.registerSingleton(preferenceService);

  locator.registerLazySingleton(() => AnalyticsService());
  locator.registerLazySingleton(() => Web3Service());
  locator.registerLazySingleton(() => WalletAuthService());
  locator.registerLazySingleton(() => SecureStorageService());
  locator.registerLazySingleton(() => WalletService());
  locator.registerLazySingleton(() => AuthenticationService());
  locator.registerLazySingleton(() => UserService());
  locator.registerLazySingleton(() => EthereumService());
  locator.registerLazySingleton(() => WebViewService());
  locator.registerLazySingleton(() => OnboardProviderService());
  locator.registerLazySingleton(() => DatabaseService());
  locator.registerLazySingleton(() => NetworkService());
  locator.registerLazySingleton(() => NetworkDatabaseService());
  locator.registerLazySingleton(() => SnackbarService());
  locator.registerLazySingleton(() => SecureAppService());
  locator.registerLazySingleton(() => CryptoService());
  locator.registerLazySingleton(() => TransactionDbService());
  locator.registerFactory(() => OnboardWalletClient());
  locator.registerLazySingleton(() => CardSubscriptionService());
  locator.registerLazySingleton(() => NestcoinRpcDatabaseService());
  locator.registerLazySingleton(() => PushNotificationService());
  locator.registerLazySingleton(() => ActiveCardViewModel());
  locator.registerLazySingleton(() => SwapService());
  locator.registerLazySingleton(() => ToastManager());
  locator.registerLazySingleton(() => PartnersService());
  locator.registerLazySingleton(() => AppSettingsService());
  locator.registerLazySingleton(() => PartnerDatabaseService());
  locator.registerLazySingleton(() => RatesService());
  locator.registerLazySingleton(() => ConfigService());
  locator.registerLazySingleton(() => TransactionHashMonitoringService());
  locator.registerLazySingleton(() => DynamicLinkService());
  locator.registerLazySingleton(() => RemoteConfigService());
  locator.registerLazySingleton(() => WalletNavigationService());
  locator.registerLazySingleton(() => WalletConnectService());
  locator.registerLazySingleton(() => CrashlyticsService());
  locator.registerLazySingleton(() => CardService());
  locator.registerLazySingleton(() => KycService());
  locator.registerLazySingleton(() => EncryptionService());
  locator.registerLazySingleton(() => FireStoreService());
  locator.registerLazySingleton(() => HomeViewModel());
  locator.registerLazySingleton(() => GoogleDrive());
  locator.registerLazySingleton(() => ICloudDrive());
  locator.registerLazySingleton(() => CloudBackupService());
  locator.registerLazySingleton(() => OnboardExchangeService());
  locator.registerLazySingleton(() => LocationService());
  locator.registerLazySingleton(() => RailsService());
  locator.registerLazySingleton(() => DecryptionService());
  locator.registerLazySingleton(() => DirectAccountService());
  locator.registerLazySingleton(() => WalletBridgeService());
  locator.registerLazySingleton(() => TokenService());
  locator.registerLazySingleton(() => CrossSwapService());
  locator.registerLazySingleton(() => FeatureService());
  locator.registerLazySingleton(() => InboxService());
  locator.registerLazySingleton(() => NotificationsViewModel());
  locator.registerLazySingleton(() => HubViewModel());
  locator.registerLazySingleton(() => LifeCycleService());
  locator.registerFactory(() => FactoryEthereumService());
  locator.registerLazySingleton(() => OngoingOrderService());
  locator.registerLazySingleton(() => OnboardPayService());
  locator.registerLazySingleton(() => DappService());
  locator.registerLazySingleton(() => NftsService());
  locator.registerLazySingleton(() => PasskeyService());
  locator.registerLazySingleton(() => VirtualAccountService());
  locator.registerLazySingleton(() => BiometricService());
  locator.registerLazySingleton(() => MainDashboardViewModel());
  locator.registerLazySingleton(() => PasscodeService());
  locator.registerLazySingleton(() => AuthorizationService());
  locator.registerLazySingleton(() => SendService());
  locator.registerLazySingleton(() => TradingAccountService());
  locator.registerLazySingleton(() => TradingAssetsService());
  locator.registerLazySingleton(() => TradingDepositsService());
  locator.registerLazySingleton(() => TradingMarketService());
  locator.registerLazySingleton(() => TradingPortfolioService());
  locator.registerLazySingleton(() => TradeService());
  locator.registerLazySingleton(() => TransferService());
  locator.registerLazySingleton(() => AssessmentLiteService());
  locator.registerLazySingleton(() => HavenPasskeyService());
  locator.registerLazySingleton(() => TradingActivationService());
  locator.registerLazySingleton(() => TradingDatabaseService());
}
