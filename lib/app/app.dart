import 'package:onboard_wallet/api/api.dart';
import 'package:onboard_wallet/haven/services/services.dart';
import 'package:onboard_wallet/haven/ui/views/haven_views.dart' as haven;
import 'package:onboard_wallet/manager/manager.dart';
import 'package:onboard_wallet/services/send_service.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:onboard_wallet/ui/views/account_backup/account_backup_view.dart';
import 'package:onboard_wallet/ui/views/account_setup/account_setup.dart';
import 'package:onboard_wallet/ui/views/accounts_list/accounts_list_view.dart';
import 'package:onboard_wallet/ui/views/add_custom_tokens/add_custom_tokens_view.dart';
import 'package:onboard_wallet/ui/views/app_guide/app_guide_view.dart';
import 'package:onboard_wallet/ui/views/app_update_nudge/app_update_nudge_view.dart';
import 'package:onboard_wallet/ui/views/auth/auth_view.dart';
import 'package:onboard_wallet/ui/views/auth_sign_message/auth_sign_message_view.dart';
import 'package:onboard_wallet/ui/views/authorization/auth_method_view.dart';
import 'package:onboard_wallet/ui/views/buy_gas_nudge/buy_gas_nudge_view.dart';
import 'package:onboard_wallet/ui/views/card_creation_warning/card_creation_warning_view.dart';
import 'package:onboard_wallet/ui/views/cards/active_card_view/active_card_view_model.dart';
import 'package:onboard_wallet/ui/views/cards/card_intro/card_intro_view.dart';
import 'package:onboard_wallet/ui/views/cards/fund/card_fund_via_virtual_account/card_fund_via_virtual_account_preview/card_fund_via_virtual_account_preview.dart';
import 'package:onboard_wallet/ui/views/cards/fund/card_fund_via_virtual_account/card_fund_via_virtual_account_view.dart';
import 'package:onboard_wallet/ui/views/cards/transfer/card_transfer_preview_view.dart';
import 'package:onboard_wallet/ui/views/cards/withdraw/withdraw_via_virtual_account/card_withdrawal_via_virtual_account_view.dart';
import 'package:onboard_wallet/ui/views/cards/withdraw/withdraw_via_virtual_account/preview/card_withdrawal_via_virtual_account_preview.dart';
import 'package:onboard_wallet/ui/views/choose_crypto/choose_crypto_view.dart';
import 'package:onboard_wallet/ui/views/choose_tokens/choose_tokens_view.dart';
import 'package:onboard_wallet/ui/views/complete_profile/complete_profile_view.dart';
import 'package:onboard_wallet/ui/views/country_of_residence/country_of_residence_view.dart';
import 'package:onboard_wallet/ui/views/country_of_residence_auth/country_of_residence_auth.dart';
import 'package:onboard_wallet/ui/views/create_card/create_card_view.dart';
import 'package:onboard_wallet/ui/views/dapp_category_detail/dapp_category_detail_view.dart';
import 'package:onboard_wallet/ui/views/dapp_tabs/dapp_tabs_view.dart';
import 'package:onboard_wallet/ui/views/dashboard/cash_dashboard/cash_dashboard_view.dart';
import 'package:onboard_wallet/ui/views/dashboard/main_dashboard/main_dashboard_viewmodel.dart';
import 'package:onboard_wallet/ui/views/delete_account/delete_account_view.dart';
import 'package:onboard_wallet/ui/views/direct_account/direct_account_intro/direct_account_intro_view.dart';
import 'package:onboard_wallet/ui/views/enable_2fa/enable_2fa_view.dart';
import 'package:onboard_wallet/ui/views/enable_notifs/enable_notifs_view.dart';
import 'package:onboard_wallet/ui/views/fund_options/fund_options_view.dart';
import 'package:onboard_wallet/ui/views/get_card_statement/get_card_statement_view.dart';
import 'package:onboard_wallet/ui/views/get_started/get_started_view.dart';
import 'package:onboard_wallet/ui/views/help_center/help_center_view.dart';
import 'package:onboard_wallet/ui/views/hidden_crypto/hidden_crypto_view.dart';
import 'package:onboard_wallet/ui/views/hidden_nfts/hidden_nfts_view.dart';
import 'package:onboard_wallet/ui/views/home/<USER>';
import 'package:onboard_wallet/ui/views/home/<USER>';
import 'package:onboard_wallet/ui/views/how_it_works/how_it_works.dart';
import 'package:onboard_wallet/ui/views/hub/hub_view.dart';
import 'package:onboard_wallet/ui/views/hub/hub_viewmodel.dart';
import 'package:onboard_wallet/ui/views/import_wallet/import_wallet_options/import_wallet_options_view.dart';
import 'package:onboard_wallet/ui/views/inbox/inbox_view.dart';
import 'package:onboard_wallet/ui/views/invest_crypto_intro/invest_crypto_intro_view.dart';
import 'package:onboard_wallet/ui/views/kyc/kyc_phone_verification/kyc_choose_phone_otp_view.dart';
import 'package:onboard_wallet/ui/views/lock/lock_view.dart';
import 'package:onboard_wallet/ui/views/manage_assets/manage_assets_view.dart';
import 'package:onboard_wallet/ui/views/manage_nfts/manage_nfts_view.dart';
import 'package:onboard_wallet/ui/views/manage_tokens/manage_tokens_view.dart';
import 'package:onboard_wallet/ui/views/my_assets/my_assets_view.dart';
import 'package:onboard_wallet/ui/views/nft_collection/nft_collection_view.dart';
import 'package:onboard_wallet/ui/views/nft_details/nft_details_view.dart';
import 'package:onboard_wallet/ui/views/nft_transactions/nft_transactions_view.dart';
import 'package:onboard_wallet/ui/views/notification_details/notification_details_view.dart';
import 'package:onboard_wallet/ui/views/notifications/notifications_view.dart';
import 'package:onboard_wallet/ui/views/onboard-pay/onboard-pay-intro/onboard_pay_intro_view.dart';
import 'package:onboard_wallet/ui/views/onboard-pay/onboard_pay_transfer_preview/onboard_pay_transfer_preview_view.dart';
import 'package:onboard_wallet/ui/views/onboard_web/onboard_web_view.dart';
import 'package:onboard_wallet/ui/views/passcode_settings/passcode_settings_view.dart';
import 'package:onboard_wallet/ui/views/passkeys/create_passkey_view.dart';
import 'package:onboard_wallet/ui/views/passcode/passcode_view.dart';
import 'package:onboard_wallet/ui/views/passcode_and_biometric/passcode_and_biometric_view.dart';
import 'package:onboard_wallet/ui/views/passcode_unlock/passcode_unlock_view.dart';
import 'package:onboard_wallet/ui/views/passkeys/passkeys_view.dart';
import 'package:onboard_wallet/ui/views/phone_number/widgets/countries_modal.dart';
import 'package:onboard_wallet/ui/views/preferences/preferences_view.dart';
import 'package:onboard_wallet/ui/views/profile/profile_view.dart';
import 'package:onboard_wallet/ui/views/recover_account/recover_account_view.dart';
import 'package:onboard_wallet/ui/views/refer_and_earn/refer_and_earn_view.dart';
import 'package:onboard_wallet/ui/views/save_in_dollars_intro/save_in_dollars_intro_view.dart';
import 'package:onboard_wallet/ui/views/secure_wallet/secure_wallet_view.dart';
import 'package:onboard_wallet/ui/views/security/security_view.dart';
import 'package:onboard_wallet/ui/views/security_warning/security_warning_view.dart';
import 'package:onboard_wallet/ui/views/select_attribution_channel/select_attribute_persona.dart';
import 'package:onboard_wallet/ui/views/select_wallet_address/select_wallet_address_view.dart';
import 'package:onboard_wallet/ui/views/send/choose_fiat/choose_fiat_view.dart';
import 'package:onboard_wallet/ui/views/send/select_send_source/select_send_source_view.dart';
import 'package:onboard_wallet/ui/views/send/send.dart';
import 'package:onboard_wallet/ui/views/send/send_with_cash_to_bank_preview/send_with_cash_to_bank_preview_view.dart';
import 'package:onboard_wallet/ui/views/send/send_select_payment_method/send_select_payment_method_view.dart';
import 'package:onboard_wallet/ui/views/send/send_with_cash_to_crypto/send_with_cash_to_crypto_view.dart'
    show SendWithCashToCryptoView;
import 'package:onboard_wallet/ui/views/send/transaction_success_view.dart';
import 'package:onboard_wallet/ui/views/set_email/set_email_view.dart';
import 'package:onboard_wallet/ui/views/set_up_defi_wallet/set_up_defi_wallet_view.dart';
import 'package:onboard_wallet/ui/views/settings/settings_view.dart';
import 'package:onboard_wallet/ui/views/show_credentials/show_credentials_view.dart';
import 'package:onboard_wallet/ui/views/sign_transaction/sign_transaction_view.dart';
import 'package:onboard_wallet/ui/views/sms_auth/sms_auth_view.dart';
import 'package:onboard_wallet/ui/views/start_virtual_account_kyc/start_virtual_account_kyc_view.dart';
import 'package:onboard_wallet/ui/views/startup/startup_view.dart';
import 'package:onboard_wallet/ui/views/swap/swap_view.dart';
import 'package:onboard_wallet/ui/views/token_details/token_details_view.dart';
import 'package:onboard_wallet/ui/views/token_network_details/token_network_details_view.dart';
import 'package:onboard_wallet/ui/views/token_networks/token_networks_view.dart';
import 'package:onboard_wallet/ui/views/token_transactions/token_transactions_view.dart';
import 'package:onboard_wallet/ui/views/transaction_detail/transaction_detail_view.dart';
import 'package:onboard_wallet/ui/views/transfer/card/card_internal_transfer_view.dart';
import 'package:onboard_wallet/ui/views/verify_email/verify_email_view.dart';
import 'package:onboard_wallet/ui/views/verify_otp/verify_otp_view.dart';
import 'package:onboard_wallet/ui/views/virtual-accounts/fund_virtual_account_via_cash_transfer/fund_virtual_account_via_cash_transfer_view.dart';
import 'package:onboard_wallet/ui/views/virtual-accounts/set_up_virtual_account/set_up_virtual_account_view.dart';
import 'package:onboard_wallet/ui/views/virtual-accounts/views.dart';
import 'package:onboard_wallet/ui/views/virtual-accounts/virtual_account_cash_transfer_preview/virtual_account_cash_transfer_preview_view.dart';
import 'package:onboard_wallet/ui/views/virtual-accounts/virtual_account_funding_option/virtual_account_funding_option_view.dart';
import 'package:onboard_wallet/ui/views/virtual-accounts/virtual_account_transaction_detail/widgets/virtual_account_transaction_details.dart';
import 'package:onboard_wallet/ui/views/virtual-accounts/virtual_account_transactions_view/virtual_account_transactions_view.dart';
import 'package:onboard_wallet/ui/views/virtual-accounts/virtual_account_transfer_options/virtual_account_transfer_options_view.dart';
import 'package:onboard_wallet/ui/views/virtual-accounts/virtual_account_transfer_preview/virtual_account_transfer_preview_view.dart';
import 'package:onboard_wallet/ui/views/virtual-accounts/virtual_account_transfer_via_external_wallet/virtual_account_transfer_via_external_wallet_view.dart';
import 'package:onboard_wallet/ui/views/virtual-accounts/virtual_account_transfer_via_local_currency/virtual_account_transfer_via_local_currency_view.dart';
import 'package:onboard_wallet/ui/views/virtual-accounts/virtual_account_transfer_via_onboard_wallet/virtual_account_transfer_via_onboard_wallet_view.dart';
import 'package:onboard_wallet/ui/views/virtual_account_creation_loader/virtual_account_creation_loader_view.dart';
import 'package:onboard_wallet/ui/views/wallet_connect/connection_modal/connection_modal.dart';
import 'package:onboard_wallet/ui/views/wallet_connect/scanner/wallet_connect_scanner.dart';
import 'package:onboard_wallet/ui/views/wallet_connect/wallet_connect.dart';
import 'package:onboard_wallet/ui/views/wallet_connect/wallet_connect_intro/wallet_connect_intro_view.dart';
import 'package:onboard_wallet/ui/views/wallet_info/wallet_info_view.dart';
import 'package:onboard_wallet/ui/views/wallet_security/wallet_security_view.dart';
import 'package:onboard_wallet/ui/views/web_sign_message/web_sign_message_view.dart';
import 'package:onboard_wallet/ui/views/welcome/welcome_view.dart';
import 'package:onboard_wallet/ui/views/welcome_back/sso/welcome_back_sso_view.dart';
import 'package:onboard_wallet/utils/custom_route.dart';
import 'package:stacked/stacked_annotations.dart';
import 'package:stacked_services/stacked_services.dart';

import '../ui/views/dashboard/cash_dashboard/cash_active_dashboard/virtual_account_restriction_view.dart';
import '../ui/views/deposit/select_assets/select_assets_view.dart';
import '../ui/views/onboard-pay/onboard_pay_amount_entry/onboard_pay_amount_entry_view.dart';
import '../ui/views/scanner/qr_code_scanner.dart';
import '../ui/views/send/send_from_defi/send_from_defi_view.dart';
import '../ui/views/send/send_from_ext_wallet_to_bank/send_from_ext_wallet_to_bank_view.dart';
import '../ui/views/send/send_with_cash/send_with_cash_view.dart';
import '../ui/views/trading_wallet_setup/intros/create_trading_wallet_success_view.dart';
import '../ui/views/views.dart';
import 'package:onboard_wallet/ui/views/attribution_question/attribution_question_view.dart';
import 'package:onboard_wallet/ui/views/select_attribution_channel/select_attribution_channel_view.dart';
// @stacked-import

@StackedApp(
  routes: [
    MaterialRoute(page: StartupView),
    MaterialRoute(page: HomeView),
    MaterialRoute(page: SettingsView),
    MaterialRoute(page: AuthView),
    MaterialRoute(page: VerifyEmailView),
    MaterialRoute(page: SetEmailView),
    MaterialRoute(page: ImportWalletView),
    MaterialRoute(page: OnboardWebView),
    MaterialRoute(page: WelcomeView),
    MaterialRoute(page: LockView),
    MaterialRoute(page: DepositView),
    MaterialRoute(page: SignTransactionView),
    MaterialRoute(page: WebSignMessageView),
    MaterialRoute(page: ShowCredentialsView),
    MaterialRoute(page: TokenDetailsView),
    MaterialRoute(page: AuthSignMessageView),
    MaterialRoute(page: SwapView),
    MaterialRoute(page: WalletInfoView),
    MaterialRoute(page: SecurityWarningView),
    MaterialRoute(page: TransactionSuccessView),
    MaterialRoute(page: TransactionDetailView),
    MaterialRoute(page: TransferSelectTokenView),
    MaterialRoute(page: TransferPreviewView),
    MaterialRoute(page: QrCodeScanner),
    MaterialRoute(page: ExternalTransferAmountView),
    MaterialRoute(page: InternalTransferAmountView),
    MaterialRoute(page: AddCustomTokensView),
    MaterialRoute(page: BuyGasNudgeView),
    MaterialRoute(page: DeleteAccountView),
    MaterialRoute(page: PasscodeView),
    MaterialRoute(page: SecurityView),
    MaterialRoute(page: PasscodeUnlockView),
    MaterialRoute(page: HelpCenterView),
    MaterialRoute(page: PreferencesView),
    MaterialRoute(page: ManageTokensView),
    MaterialRoute(page: PasscodeAndBiometricView),
    MaterialRoute(page: ConnectedSitesView),
    MaterialRoute(page: ConnectedSiteDetailView),
    MaterialRoute(page: PasteSeedPhraseView),
    MaterialRoute(page: TypeSeedPhraseView),
    MaterialRoute(page: ViewTypedSeedPhraseView),
    MaterialRoute(page: AppUpdateNudgeView),
    MaterialRoute(page: CountryOfResidenceView),
    MaterialRoute(page: StartKycView),
    MaterialRoute(page: CardNotAvailableView),
    MaterialRoute(page: KycSuccessfulView),
    MaterialRoute(page: KycInProgressView),
    MaterialRoute(page: KycFailedView),
    MaterialRoute(page: ActivateUSDCard),
    MaterialRoute(page: CardUsageAndTerms),
    MaterialRoute(page: CardFundOptionsView),
    MaterialRoute(page: CardFundSelectAssetView),
    MaterialRoute(page: CardFundViaOnboardWalletView),
    MaterialRoute(page: ConfirmCardPurchaseView),
    MaterialRoute(page: FundCardViaExternalWalletView),
    MaterialRoute(page: FundCardViaExternalWalletPreviewView),
    MaterialRoute(page: FundCardViaExternalWalletConfirmView),
    MaterialRoute(page: FundCardViaCashTransferView),
    MaterialRoute(page: CardDetailsView),
    MaterialRoute(page: CardTransactionDetailView),
    MaterialRoute(page: CardTransactionsView),
    MaterialRoute(page: CardWithdrawalOptionsView),
    MaterialRoute(page: CardWithdrawalViaExternalWalletView),
    MaterialRoute(page: CardWithdrawalViaOnboardWalletView),
    MaterialRoute(page: CardWithdrawalViaCashTransferView),
    MaterialRoute(page: CardWithdrawalPreviewView),
    MaterialRoute(page: CardWithdrawalSelectAssetView),
    MaterialRoute(page: CardWithdrawalCashTransferPreviewView),
    MaterialRoute(page: SignActionRequestView),
    MaterialRoute(page: CardOverdueFeesView),
    MaterialRoute(page: CardFrozenView),
    MaterialRoute(page: VerifyOtpView),
    MaterialRoute(page: CardTerminatedView),
    MaterialRoute(page: CardFeatureUnavailableView),
    MaterialRoute(page: LowCardBalanceWarningView),
    MaterialRoute(page: MinimumCardBalanceWarningView),
    MaterialRoute(page: KycPhoneVerificationView),
    MaterialRoute(page: KycFullNameView),
    MaterialRoute(page: KycPhoneView),
    MaterialRoute(page: StartIdentityVerificationView),
    MaterialRoute(page: CardIntroView),
    MaterialRoute(page: SelectIDView),
    MaterialRoute(page: UserAddressView),
    MaterialRoute(page: CompleteProfileView),
    MaterialRoute(page: IdNumberView),
    MaterialRoute(page: MyAssetsView),
    MaterialRoute(page: ManageAssetsView),
    MaterialRoute(page: ImportSuccessView),
    MaterialRoute(page: SelectWalletAddressView),
    MaterialRoute(page: AddWalletAddressView),
    MaterialRoute(page: WalletAddressSelectAssetView),
    MaterialRoute(page: SelectAssetsView),
    MaterialRoute(page: FundOptionsView),
    MaterialRoute(page: CryptoCashAmountEntryView),
    MaterialRoute(page: GetStartedView),
    MaterialRoute(page: WelcomeBackSSOView),
    MaterialRoute(page: WalletSecurityView),
    MaterialRoute(page: SecureWalletView),
    MaterialRoute(page: Enable2faView),
    MaterialRoute(page: EnableNotifsView),
    MaterialRoute(page: AccountBackupView),
    MaterialRoute(page: RecoverAccountView),
    MaterialRoute(page: SmsAuthView),
    MaterialRoute(page: DeleteCardView),
    MaterialRoute(page: CardView),
    MaterialRoute(page: ProfileView),
    MaterialRoute(page: CardSuspendedView),
    MaterialRoute(page: ReferAndEarnView),
    MaterialRoute(page: HubView),
    MaterialRoute(page: DirectAccountAmountEntryView),
    MaterialRoute(page: DirectAccountTransferPreviewView),
    MaterialRoute(page: DirectAccountSelectAssetView),
    MaterialRoute(page: ImportPrivateKeyView),
    MaterialRoute(page: ImportWalletOptionsView),
    MaterialRoute(page: AppGuideView),
    MaterialRoute(page: SaveInDollarsIntroView),
    MaterialRoute(page: OnboardPayIntroView),
    MaterialRoute(page: OnboardPayAmountEntryView),
    MaterialRoute(page: OnboardPayTransferPreviewView),
    MaterialRoute(page: DirectAccountIntroView),
    MaterialRoute(page: CreatePasskeyView),
    MaterialRoute(page: DashboardRouteView, children: [
      MaterialRoute(page: MainDashboardView),
      CustomRoute(
        page: CardView,
        transitionsBuilder: CustomTransitions.slideTransitionBuilder,
      ),
      CustomRoute(
        page: MerchantDashboardView,
        transitionsBuilder: CustomTransitions.slideTransitionBuilder,
      ),
      CustomRoute(
        page: CashDashboardView,
        transitionsBuilder: CustomTransitions.slideTransitionBuilder,
      ),
    ]),
    MaterialRoute(page: ChooseTokensView),
    MaterialRoute(page: CustomerDashboardView),
    MaterialRoute(page: InvestInCryptoIntroView),
    MaterialRoute(page: MyPortfolioView),
    MaterialRoute(page: TokenNetworksView),
    MaterialRoute(page: TokenNetworkDetailsView),
    MaterialRoute(page: TokenTransactionsView),
    MaterialRoute(page: ChooseCryptoView),
    MaterialRoute(page: CreateTradingWalletSuccessView),
    MaterialRoute(page: CreateCardView),
    MaterialRoute(page: GetCardStatementView),
    MaterialRoute(page: InboxView),
    MaterialRoute(page: NotificationsView),
    MaterialRoute(page: NotificationDetailsView),
    MaterialRoute(page: CardCreationWarningView),
    MaterialRoute(page: DappBrowserView),
    MaterialRoute(page: DappCategoryDetailView),
    MaterialRoute(page: CardTransferPreviewView),
    MaterialRoute(page: CardInternalTransferView),
    MaterialRoute(page: DappTabsView),
    MaterialRoute(page: HiddenCryptoView),
    MaterialRoute(page: NftDetailsView),
    MaterialRoute(page: ManageNftsView),
    MaterialRoute(page: NftTransactionsView),
    MaterialRoute(page: NftCollectionView),
    MaterialRoute(page: HiddenNftsView),
    MaterialRoute(page: MarkCardSubscriptionsView),
    MaterialRoute(page: CardSubscriptionDetailView),
    MaterialRoute(page: AddCardSubscriptionView),
    MaterialRoute(page: UpdateCardSubscriptionAmountView),
    MaterialRoute(page: CardSubscriptionListView),
    MaterialRoute(page: PasskeysView),
    MaterialRoute(page: CardSubscriptionsTransactionsView),
    MaterialRoute(page: AccountsListView),
    MaterialRoute(page: CountriesListView),
    MaterialRoute(page: StartVirtualAccountKycView),
    MaterialRoute(page: VirtualAccountUsageTermsView),
    MaterialRoute(page: VirtualAccountDetailsView),
    MaterialRoute(page: VirtualAccountCreationSuccessView),
    MaterialRoute(page: VirtualAccountTransactionDetailView),
    MaterialRoute(page: VirtualAccountSelectAssetView),
    MaterialRoute(page: VirtualAccountCreationLoaderView),
    MaterialRoute(page: CashDashboardView),
    MaterialRoute(page: VirtualAccountTransferOptionsView),
    MaterialRoute(page: VirtualAccountTransferViaExternalWalletView),
    MaterialRoute(page: VirtualAccountTransferViaOnboardWalletView),
    MaterialRoute(page: VirtualAccountTransferViaPreviewView),
    MaterialRoute(page: VirtualAccountTransferViaLocalCurrencyView),
    MaterialRoute(page: VirtualAccountCashTransferPreviewView),
    MaterialRoute(page: VirtualAccountTransactionsView),
    MaterialRoute(page: VirtualAccountTransactionDetails),
    MaterialRoute(page: FundVirtualAccountViaCashTransferView),
    MaterialRoute(page: VirtualAccountFundingOptionView),
    MaterialRoute(page: AttributionQuestionView),
    MaterialRoute(page: SelectAttributionChannelView),
    MaterialRoute(page: SelectAttributePersonaView),
    MaterialRoute(page: CardFundViaVirtualAccountView),
    MaterialRoute(page: CardFundViaVirtualAccountPreviewView),
    MaterialRoute(page: CardWithdrawalViaVirtualAccountView),
    MaterialRoute(page: CardWithdrawalViaVirtualAccountPreview),
    MaterialRoute(page: SendSelectPaymentMethodView),
    MaterialRoute(page: SelectSendSourceView),
    MaterialRoute(page: AccountSetupView),
    MaterialRoute(page: CountryOfResidenceAuthView),
    MaterialRoute(page: SendWithCashView),
    MaterialRoute(page: ChooseFiatView),
    MaterialRoute(page: SendToBankPreviewView),
    MaterialRoute(page: HowItWorks),
    MaterialRoute(page: SetUpDefiWalletView),
    MaterialRoute(page: SetUpVirtualAccountView),
    MaterialRoute(page: AuthMethodView),
    MaterialRoute(page: SendWithCashToCryptoView),
    MaterialRoute(page: WalletConnectQrCodeScanner),
    MaterialRoute(page: WalletConnectIntroView),
    MaterialRoute(page: VirtualAccountRestrictionView),
    MaterialRoute(page: WalletConnectionModal),

    //-------------------------------HAVEN Routes Configs---------------------------------
    MaterialRoute(
        page: haven.HavenHomeView, name: 'havenHomeView', path: '/haven/home'),
    MaterialRoute(
        page: haven.HavenCountryOfResidenceView,
        name: 'havenCountryOfResidenceView',
        path: '/haven/country-of-residence'),
    MaterialRoute(
        page: haven.LoaderView, name: 'havenLoaderView', path: '/haven/loader'),
    MaterialRoute(
        page: haven.HavenSelectAssetsView,
        name: 'havenSelectAssetsView',
        path: '/haven/select-assets'),
    MaterialRoute(
        page: haven.HavenFundOptionsView,
        name: 'havenFundOptionsView',
        path: '/haven/fund-options'),
    MaterialRoute(
        page: haven.HavenDepositView,
        name: 'havenDepositView',
        path: '/haven/deposit'),
    MaterialRoute(
        page: haven.DepositAmountInputView,
        name: 'havenDepositAmountInputView',
        path: '/haven/deposit-amount-input'),
    MaterialRoute(
        page: haven.PortfolioAssetDetailView,
        name: 'havenPortfolioAssetDetailView',
        path: '/haven/portfolio-asset-detail'),
    MaterialRoute(
        page: haven.BuyAssetView,
        name: 'havenBuyAssetView',
        path: '/haven/buy-asset'),
    MaterialRoute(
        page: haven.TransactionResultView,
        name: 'havenTransactionResultView',
        path: '/haven/transaction-result'),
    MaterialRoute(
        page: haven.SellAssetView,
        name: 'havenSellAssetView',
        path: '/haven/sell-asset'),
    MaterialRoute(
        page: haven.ConvertAssetView,
        name: 'havenConvertAssetView',
        path: '/haven/convert-asset'),
    MaterialRoute(
        page: haven.SelectUserAssetsView,
        name: 'havenSelectUserAssetsView',
        path: '/haven/select-user-assets'),
    MaterialRoute(
        page: haven.TransferOptionsView,
        name: 'havenTransferOptionsView',
        path: '/haven/transfer-options'),
    MaterialRoute(
        page: haven.CryptoTransferInputView,
        name: 'havenCryptoTransferInputView',
        path: '/haven/crypto-transfer-input'),
    MaterialRoute(
        page: haven.HavenTransferPreviewView,
        name: 'havenTransferPreviewView',
        path: '/haven/transfer-preview'),
    MaterialRoute(
        page: haven.TransactionLoaderView,
        name: 'havenTransactionLoaderView',
        path: '/haven/transaction-loader'),
    MaterialRoute(
        page: haven.TransactionsView,
        name: 'havenTransactionsView',
        path: '/haven/transactions'),
    MaterialRoute(
        page: haven.SelectSwapAssetsView,
        name: 'havenSelectSwapAssetsView',
        path: '/haven/select-swap-assets'),
    MaterialRoute(
        page: haven.ConvertPreview,
        name: 'havenConvertPreviewView',
        path: '/haven/convert-preview'),
    MaterialRoute(
        page: haven.HavenTransactionDetailView,
        name: 'havenTransactionDetailView',
        path: '/haven/transaction-detail'),
    MaterialRoute(
        page: haven.CashWithdrawalView,
        name: 'havenCashWithdrawalView',
        path: '/haven/cash-withdrawal'),
    MaterialRoute(
        page: haven.CashWithdrawalPreview,
        name: 'havenCashWithdrawalPreviewView',
        path: '/haven/cash-withdrawal-preview'),
    MaterialRoute(
        page: haven.CashWithdrawalConfirmView,
        name: 'havenCashWithdrawalConfirmView',
        path: '/haven/cash-withdrawal-confirm'),
    MaterialRoute(
        page: haven.PortfolioTransactionsView,
        name: 'havenPortfolioTransactionsView',
        path: '/haven/portfolio-transactions'),
    MaterialRoute(
        page: haven.PortfolioAssetList,
        name: 'havenPortfolioAssetListView',
        path: '/haven/portfolio-asset-list'),
    MaterialRoute(
        page: haven.SearchBalanceView,
        name: 'havenSearchBalanceView',
        path: '/haven/search-balance'),
    MaterialRoute(
        page: haven.RecommendedPortfolioTypeView,
        name: 'havenRecommendedPortfolioTypeView',
        path: '/haven/recommended-portfolio-type'),
    MaterialRoute(
        page: haven.CryptoBeneficiaryView,
        name: 'havenCryptoBeneficiaryView',
        path: '/haven/crypto-beneficiary'),
    MaterialRoute(
        page: haven.PhoneVerificationView,
        name: 'havenPhoneVerificationView',
        path: '/haven/phone-verification'),

    MaterialRoute(page: KycChoosePhoneOtpView),
    MaterialRoute(page: SendFromDefiView),
    MaterialRoute(page: SendFromExtWalletToBankView),
    MaterialRoute(page: PasscodeSettingsView)
// @stacked-route
  ],
  dependencies: [
    LazySingleton(classType: NavigationService),
    LazySingleton(classType: DialogService),
    LazySingleton(classType: BottomSheetService),
    InitializableSingleton(classType: PreferenceService),
    LazySingleton(classType: AnalyticsService),
    LazySingleton(classType: Web3Service),
    LazySingleton(classType: WalletAuthService),
    LazySingleton(classType: SecureStorageService),
    LazySingleton(classType: WalletService),
    LazySingleton(classType: AuthenticationService),
    LazySingleton(classType: UserService),
    LazySingleton(classType: EthereumService),
    LazySingleton(classType: WebViewService),
    LazySingleton(classType: OnboardProviderService),
    LazySingleton(classType: DatabaseService),
    LazySingleton(classType: NetworkService),
    LazySingleton(classType: NetworkDatabaseService),
    LazySingleton(classType: SnackbarService),
    LazySingleton(classType: SecureAppService),
    LazySingleton(classType: CryptoService),
    LazySingleton(classType: TransactionDbService),
    Factory(classType: OnboardWalletClient),
    LazySingleton(classType: CardSubscriptionService),
    LazySingleton(classType: NestcoinRpcDatabaseService),
    LazySingleton(classType: PushNotificationService),
    LazySingleton(classType: ActiveCardViewModel),
    LazySingleton(classType: SwapService),
    LazySingleton(classType: ToastManager),
    LazySingleton(classType: PartnersService),
    LazySingleton(classType: AppSettingsService),
    LazySingleton(classType: PartnerDatabaseService),
    LazySingleton(classType: RatesService),
    LazySingleton(classType: ConfigService),
    LazySingleton(classType: TransactionHashMonitoringService),
    LazySingleton(classType: DynamicLinkService),
    LazySingleton(classType: RemoteConfigService),
    LazySingleton(classType: WalletNavigationService),
    LazySingleton(classType: WalletConnectService),
    LazySingleton(classType: CrashlyticsService),
    LazySingleton(classType: CardService),
    LazySingleton(classType: KycService),
    LazySingleton(classType: EncryptionService),
    LazySingleton(classType: FireStoreService),
    LazySingleton(classType: HomeViewModel),
    LazySingleton(classType: GoogleDrive),
    LazySingleton(classType: ICloudDrive),
    LazySingleton(classType: CloudBackupService),
    LazySingleton(classType: OnboardExchangeService),
    LazySingleton(classType: LocationService),
    LazySingleton(classType: RailsService),
    LazySingleton(classType: DecryptionService),
    LazySingleton(classType: DirectAccountService),
    LazySingleton(classType: WalletBridgeService),
    LazySingleton(classType: TokenService),
    LazySingleton(classType: CrossSwapService),
    LazySingleton(classType: FeatureService),
    LazySingleton(classType: InboxService),
    LazySingleton(classType: NotificationsViewModel),
    LazySingleton(classType: HubViewModel),
    LazySingleton(classType: LifeCycleService),
    Factory(classType: FactoryEthereumService),
    LazySingleton(classType: OngoingOrderService),
    LazySingleton(classType: OnboardPayService),
    LazySingleton(classType: DappService),
    LazySingleton(classType: NftsService),
    LazySingleton(classType: PasskeyService),
    LazySingleton(classType: VirtualAccountService),
    LazySingleton(classType: BiometricService),
    LazySingleton(classType: MainDashboardViewModel),
    LazySingleton(classType: PasscodeService),
    LazySingleton(classType: AuthorizationService),
    LazySingleton(classType: SendService),

//----------------------------Haven Services------------------------
    LazySingleton(classType: TradingAccountService),
    LazySingleton(classType: TradingAssetsService),
    LazySingleton(classType: TradingDepositsService),
    LazySingleton(classType: TradingMarketService),
    LazySingleton(classType: TradingPortfolioService),
    LazySingleton(classType: TradeService),
    LazySingleton(classType: TransferService),
    LazySingleton(classType: AssessmentLiteService),
    LazySingleton(classType: HavenPasskeyService),
    LazySingleton(classType: TradingActivationService),
    LazySingleton(classType: TradingDatabaseService)

// @stacked-service
  ],
  logger: StackedLogger(),
)
class App {}
