import 'package:hive_flutter/hive_flutter.dart';
import 'package:nestcoinco_onboard_api_gateway_lite/nestcoinco_onboard_api_gateway_lite.dart';
import 'package:onboard_wallet/constants/hive_type_id.dart'
    show kLocalTradingBalance;

part 'local_trading_balance.g.dart';

@HiveType(typeId: kLocalTradingBalance)
class LocalTradingBalance {
  @HiveField(0)
  String? currency;

  @HiveField(1)
  num? availableBalance;

  @HiveField(2)
  num? totalBalance;

  @HiveField(3)
  num? lockedBalance;

  @HiveField(4)
  int? nonZeroAssets;

  @HiveField(5)
  bool? accountExists;

  LocalTradingBalance({
    this.currency,
    this.availableBalance,
    this.totalBalance,
    this.lockedBalance,
    this.nonZeroAssets,
    this.accountExists,
  });

  factory LocalTradingBalance.fromPortfolioBalanceSnapshot(
      PortfolioBalanceSnapshot portfolioBalance) {
    return LocalTradingBalance(
      currency: portfolioBalance.currency,
      availableBalance: portfolioBalance.balance.availableBalance,
      lockedBalance: portfolioBalance.balance.lockedBalance,
      totalBalance: portfolioBalance.balance.totalBalance,
      nonZeroAssets: portfolioBalance.nonZeroAssets,
      accountExists: portfolioBalance.accountExists,
    );
  }

  //to avoid disrupting what we have now, I will be converting back to plug into what we have currently
  PortfolioBalanceSnapshot? toPortfolioBalanceSnapshot() {
    if (accountExists == null ||
        totalBalance == null ||
        availableBalance == null ||
        lockedBalance == null) {
      return null;
    }

    return PortfolioBalanceSnapshot((b) => b
      ..currency = currency
      ..nonZeroAssets = nonZeroAssets
      ..accountExists = accountExists
      ..balance = AssetBalance((a) => a
        ..totalBalance = totalBalance
        ..availableBalance = availableBalance
        ..lockedBalance = lockedBalance).toBuilder());
  }
}
