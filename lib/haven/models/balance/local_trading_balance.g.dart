// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'local_trading_balance.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LocalTradingBalanceAdapter extends TypeAdapter<LocalTradingBalance> {
  @override
  final int typeId = 222;

  @override
  LocalTradingBalance read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LocalTradingBalance(
      currency: fields[0] as String?,
      availableBalance: fields[1] as num?,
      totalBalance: fields[2] as num?,
      lockedBalance: fields[3] as num?,
      nonZeroAssets: fields[4] as int?,
      accountExists: fields[5] as bool?,
    );
  }

  @override
  void write(BinaryWriter writer, LocalTradingBalance obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.currency)
      ..writeByte(1)
      ..write(obj.availableBalance)
      ..writeByte(2)
      ..write(obj.totalBalance)
      ..writeByte(3)
      ..write(obj.lockedBalance)
      ..writeByte(4)
      ..write(obj.nonZeroAssets)
      ..writeByte(5)
      ..write(obj.accountExists);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LocalTradingBalanceAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
