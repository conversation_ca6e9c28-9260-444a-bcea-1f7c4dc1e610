// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'local_asset_info.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LocalAssetInfoAdapter extends TypeAdapter<LocalAssetInfo> {
  @override
  final int typeId = 219;

  @override
  LocalAssetInfo read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LocalAssetInfo(
      symbol: fields[0] as String?,
      name: fields[1] as String?,
      logoUrl: fields[2] as String?,
      multiToken: fields[3] as bool?,
      displayDecimals: fields[4] as int?,
      depositAvailable: fields[5] as bool?,
      withdrawalAvailable: fields[6] as bool?,
      transferAvailable: fields[7] as bool?,
      tradingAvailable: fields[8] as bool?,
      tokens: (fields[9] as List?)?.cast<String>(),
    );
  }

  @override
  void write(BinaryWriter writer, LocalAssetInfo obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.symbol)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.logoUrl)
      ..writeByte(3)
      ..write(obj.multiToken)
      ..writeByte(4)
      ..write(obj.displayDecimals)
      ..writeByte(5)
      ..write(obj.depositAvailable)
      ..writeByte(6)
      ..write(obj.withdrawalAvailable)
      ..writeByte(7)
      ..write(obj.transferAvailable)
      ..writeByte(8)
      ..write(obj.tradingAvailable)
      ..writeByte(9)
      ..write(obj.tokens);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LocalAssetInfoAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
