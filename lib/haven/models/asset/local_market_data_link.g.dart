// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'local_market_data_link.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LocalMarketDataLinkAdapter extends TypeAdapter<LocalMarketDataLink> {
  @override
  final int typeId = 220;

  @override
  LocalMarketDataLink read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LocalMarketDataLink(
      url: fields[0] as String?,
      type: fields[1] as String?,
      label: fields[2] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, LocalMarketDataLink obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.url)
      ..writeByte(1)
      ..write(obj.type)
      ..writeByte(2)
      ..write(obj.label);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LocalMarketDataLinkAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
