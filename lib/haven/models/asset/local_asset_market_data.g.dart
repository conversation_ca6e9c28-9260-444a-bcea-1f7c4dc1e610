// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'local_asset_market_data.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LocalAssetMarketDataAdapter extends TypeAdapter<LocalAssetMarketData> {
  @override
  final int typeId = 221;

  @override
  LocalAssetMarketData read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LocalAssetMarketData(
      assetInfo: fields[0] as LocalAssetInfo?,
      price: fields[1] as num?,
      last24HrChange: fields[2] as num?,
      last24HrVol: fields[3] as num?,
      about: fields[4] as String?,
      rank: fields[5] as int?,
      totalSupply: fields[6] as int?,
      marketCap: fields[7] as num?,
      last24HrHigh: fields[8] as num?,
      last24HrLow: fields[9] as num?,
      allTimeReturn: fields[10] as num?,
      links: (fields[11] as List?)?.cast<LocalMarketDataLink>(),
    );
  }

  @override
  void write(BinaryWriter writer, LocalAssetMarketData obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.assetInfo)
      ..writeByte(1)
      ..write(obj.price)
      ..writeByte(2)
      ..write(obj.last24HrChange)
      ..writeByte(3)
      ..write(obj.last24HrVol)
      ..writeByte(4)
      ..write(obj.about)
      ..writeByte(5)
      ..write(obj.rank)
      ..writeByte(6)
      ..write(obj.totalSupply)
      ..writeByte(7)
      ..write(obj.marketCap)
      ..writeByte(8)
      ..write(obj.last24HrHigh)
      ..writeByte(9)
      ..write(obj.last24HrLow)
      ..writeByte(10)
      ..write(obj.allTimeReturn)
      ..writeByte(11)
      ..write(obj.links);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LocalAssetMarketDataAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
