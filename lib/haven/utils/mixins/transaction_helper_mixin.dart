import 'package:flutter/material.dart';
import 'package:nestcoinco_onboard_api_gateway_lite/nestcoinco_onboard_api_gateway_lite.dart';
import 'package:onboard_wallet/extensions/date_time.dart';
import 'package:onboard_wallet/haven/extensions/extensions.dart';
import 'package:onboard_wallet/gen/assets.gen.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/haven/models/asset/local_asset_market_data.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';

///TODO: Change this to an extension function later for transaction
mixin TransactionHelperMixin {
  String? getTransactionIcon({
    required PortfolioTransactionDirection direction,
    required PortfolioTransactionActivityType type,
    required PortfolioTransactionStatus status,
  }) {
    switch (status) {
      case PortfolioTransactionStatus.FAILED:
        switch (type) {
          case PortfolioTransactionActivityType.DEPOSIT:
          case PortfolioTransactionActivityType.TRADE:
          case PortfolioTransactionActivityType.INTERNAL_CREDIT:
            if (direction == PortfolioTransactionDirection.INFLOW) {
              return Assets.haven.svg.txInflowFailed.path;
            } else {
              return Assets.haven.svg.txOutflowFailed.path;
            }
          case PortfolioTransactionActivityType.WITHDRAWAL:
          case PortfolioTransactionActivityType.INTERNAL_DEBIT:
            return Assets.haven.svg.txOutflowFailed.path;
        }
        break;
      case PortfolioTransactionStatus.IN_PROGRESS:
      case PortfolioTransactionStatus.PENDING:
        switch (type) {
          case PortfolioTransactionActivityType.DEPOSIT:
          case PortfolioTransactionActivityType.INTERNAL_CREDIT:
          case PortfolioTransactionActivityType.TRADE:
            if (direction == PortfolioTransactionDirection.INFLOW) {
              return Assets.haven.svg.txInflowPending.path;
            } else {
              return Assets.haven.svg.txOutflowPending.path;
            }
          case PortfolioTransactionActivityType.WITHDRAWAL:
          case PortfolioTransactionActivityType.INTERNAL_DEBIT:
            return Assets.haven.svg.txOutflowPending.path;
        }
        break;
      case PortfolioTransactionStatus.SUCCESS:
        switch (type) {
          case PortfolioTransactionActivityType.DEPOSIT:
          case PortfolioTransactionActivityType.INTERNAL_CREDIT:
          case PortfolioTransactionActivityType.TRADE:
            if (direction == PortfolioTransactionDirection.INFLOW) {
              return Assets.haven.svg.txInflowSuccess.path;
            } else {
              return Assets.haven.svg.txOutflowSuccess.path;
            }
          case PortfolioTransactionActivityType.WITHDRAWAL:
          case PortfolioTransactionActivityType.INTERNAL_DEBIT:
            return Assets.haven.svg.txOutflowSuccess.path;
        }
        break;
    }
    return null;
  }

  String getTransactionTitle(
      {required PortfolioTransactionActivityType? type,
      required String? tag}) {
    const tagBlockchainWithdrawal = "TX_WITHDRAWAL_BLOCKCHAIN";
    bool isBlockchainTag = tag == tagBlockchainWithdrawal;

    switch (type) {
      case PortfolioTransactionActivityType.DEPOSIT:
      case PortfolioTransactionActivityType.INTERNAL_CREDIT:
        return S.current.deposit;
      case PortfolioTransactionActivityType.TRADE:
        return S.current.trade;
      case PortfolioTransactionActivityType.INTERNAL_DEBIT:
      case PortfolioTransactionActivityType.WITHDRAWAL:
        return isBlockchainTag
            ? S.current.blockchainWithdrawal
            : S.current.cashWithdrawal;
      default:
        return S.current.transaction;
    }
  }

  String getTransactionTitleNotif({required String? type}) {
    switch (type) {
      case 'FIAT':
      case "INTERNAL_DEBIT":
      case 'WITHDRAWAL':
        return S.current.withdrawal;
      case "TRADE":
        return S.current.trade;
      case "INTERNAL_CREDIT":
      case "DEPOSIT":
        return S.current.deposit;
      default:
        return S.current.transaction;
    }
  }

  String getTransactionStatusText(
      {required PortfolioTransactionStatus status}) {
    switch (status) {
      case PortfolioTransactionStatus.SUCCESS:
        return S.current.success;
      case PortfolioTransactionStatus.IN_PROGRESS:
        return S.current.inProgress;
      case PortfolioTransactionStatus.PENDING:
        return S.current.pending;
      case PortfolioTransactionStatus.FAILED:
        return S.current.failed;
      default:
        return S.current.pending;
    }
  }

  Color getTransactionStatusColor(
      {required PortfolioTransactionStatus status}) {
    switch (status) {
      case PortfolioTransactionStatus.SUCCESS:
        return SuccessColor.success500;
      case PortfolioTransactionStatus.IN_PROGRESS:
      case PortfolioTransactionStatus.PENDING:
        return WarningColor.warning500;
      case PortfolioTransactionStatus.FAILED:
        return ErrorColor.error500;
      default:
        return WarningColor.warning50;
    }
  }

  Color getTransactionAmountColor(
      {required PortfolioTransactionDirection type}) {
    switch (type) {
      case PortfolioTransactionDirection.INFLOW:
        return SuccessColor.success500;
      case PortfolioTransactionDirection.OUTFLOW:
        return ErrorColor.error500;
      default:
        return WarningColor.warning500;
    }
  }

  Color getTransactionStatusBgColor(
      {required PortfolioTransactionStatus status}) {
    switch (status) {
      case PortfolioTransactionStatus.SUCCESS:
        return SuccessColor.success50;
      case PortfolioTransactionStatus.IN_PROGRESS:
      case PortfolioTransactionStatus.PENDING:
        return WarningColor.warning50;
      case PortfolioTransactionStatus.FAILED:
        return ErrorColor.error50;
      default:
        return WarningColor.warning50;
    }
  }

  num transactionAmount(PortfolioTransaction transaction) {
    num totalAmount = transaction.amount;

    if (transaction.feeInclusive == false) {
      totalAmount += transaction.feeAmount;
    }

    return totalAmount;
  }

  String formattedAmount(
      PortfolioTransaction transaction, LocalAssetMarketData? assetData) {
    final amount = transactionAmount(transaction);

    if (transaction.direction == PortfolioTransactionDirection.INFLOW) {
      return "+ ${amount.currencyFormat(decimalDigits: assetData?.assetInfo?.displayDecimals ?? 8).removeTrailingZerosMaintainingDecimalPlaces()}";
    } else {
      return "- ${amount.currencyFormat(decimalDigits: assetData?.assetInfo?.displayDecimals ?? 8).removeTrailingZerosMaintainingDecimalPlaces()}";
    }
  }

  String amountInDollars(
      PortfolioTransaction transaction, LocalAssetMarketData? assetData) {
    return transaction.usdValue?.currencyFormat() ?? "";
  }

  String formattedDate(PortfolioTransaction transaction) {
    final date = transaction.createdAt.toLocal();

    return date.chartDateTimeFormat;
  }
}
