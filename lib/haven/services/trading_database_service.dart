import 'package:hive_flutter/hive_flutter.dart';
import 'package:nestcoinco_onboard_api_gateway_lite/nestcoinco_onboard_api_gateway_lite.dart';
import 'package:onboard_wallet/app/app.logger.dart';
import 'package:onboard_wallet/constants/hive_box_name.dart';
import 'package:onboard_wallet/haven/models/balance/local_trading_balance.dart';

class TradingDatabaseService {
  Box<LocalTradingBalance> get tradingWalletBalanceBox =>
      Hive.box<LocalTradingBalance>(HiveBoxName.tradingWalletBalanceBoxName);

  Future saveTradingBalance(PortfolioBalanceSnapshot? balance) async {
    if (balance == null) return;
    try {
      await tradingWalletBalanceBox.clear();
      tradingWalletBalanceBox
          .add(LocalTradingBalance.fromPortfolioBalanceSnapshot(balance));
    } catch (e) {
      getLogger(toString()).e("Database error === $e");
    }
  }

  PortfolioBalanceSnapshot? getLocalBalance() {
   return tradingWalletBalanceBox.values.isNotEmpty
        ? tradingWalletBalanceBox.values.first.toPortfolioBalanceSnapshot()
        : null;
  }
}