import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:nestcoinco_onboard_api_gateway_lite/nestcoinco_onboard_api_gateway_lite.dart';
import 'package:onboard_wallet/api/api.dart';
import 'package:onboard_wallet/api/onboard_exception.dart';
import 'package:onboard_wallet/app/app.locator.dart';
import 'package:onboard_wallet/app/app.logger.dart';
import 'package:onboard_wallet/constants/string_constants.dart';
import 'package:onboard_wallet/haven/services/trading_database_service.dart';
import 'package:onboard_wallet/models/result.dart';
import 'package:onboard_wallet/services/preference_service.dart';
import 'package:stacked/stacked.dart';

class TradingPortfolioService with ListenableServiceMixin {
  TradingPortfolioService() {
    listenToReactiveValues([balanceSnapshot, portfolioAssetsBalances]);
  }

  final _tradingPortfolioClient =
      getOnboardLiteApiGateway.getTradingPortfolioClient();
  final _tradingDBService = locator<TradingDatabaseService>();

  PortfolioBalanceSnapshot? balanceSnapshot;
  List<UserPortfolioAssetBalance> portfolioAssetsBalances = [];

  List<PortfolioTransaction> transactions = [];

  //fetch last updated balance
  PortfolioBalanceSnapshot? get lastUpdatedBalance =>
      _tradingDBService.getLocalBalance();

  //fetch the account status from local cache
  bool doesTradingWalletExist() {
    return locator<PreferenceService>().getBool(key: kTradingWalletExist) ??
        false;
  }

  Future<Result<PortfolioBalanceSnapshot?>> getUserBalance() async {
    try {
      final response = await _tradingPortfolioClient.getPortfolioSnapshot();
      balanceSnapshot = response.data;
      _tradingDBService.saveTradingBalance(balanceSnapshot);

      //save the flag locally for fast lookup
      if (balanceSnapshot?.accountExists == true) {
        locator<PreferenceService>()
            .setBool(key: kTradingWalletExist, value: true);
      }
      notifyListeners();
      return Result.success(data: response.data);
    } on DioException catch (e) {
      getLogger(toString()).d("======= $e");
      final dioException = OnboardExceptions.fromDioError(e);
      return Result.failure(error: dioException);
    }
  }

  Future<Result<List<UserPortfolioAssetBalance>>> getUserPortfolio() async {
    try {
      final response = await _tradingPortfolioClient.getPortfolio();
      portfolioAssetsBalances = response.data?.assets.toList() ?? [];
      notifyListeners();
      return Result.success(data: portfolioAssetsBalances);
    } on DioException catch (e) {
      getLogger(toString()).d("======= $e");
      final dioException = OnboardExceptions.fromDioError(e);
      return Result.failure(error: dioException);
    }
  }

  Future<Result<UserPortfolioAssetDetails?>> getPortfolioAssetBalance(
      String assetSymbol) async {
    try {
      final response = await _tradingPortfolioClient.getPortfolioAssetBalance(
          assetSymbol: assetSymbol);
      return Result.success(data: response.data!);
    } on DioException catch (e) {
      getLogger(toString()).d("======= $e");
      final dioException = OnboardExceptions.fromDioError(e);
      return Result.failure(error: dioException);
    }
  }

  Future<Result<PortfolioTransaction?>> getTransactionById(
      String transactionId) async {
    try {
      final response = await _tradingPortfolioClient.getTransactionById(
          transactionId: transactionId);
      return Result.success(data: response.data);
    } on DioException catch (e) {
      getLogger(toString()).d("======= $e");
      final dioException = OnboardExceptions.fromDioError(e);
      return Result.failure(error: dioException);
    }
  }

  Future<Result<PortfolioTransactionsHistoryList?>> getTransactions(
      {String? asset,
      PortfolioTransactionActivityType? type,
      int page = 1,
      int size = 20}) async {
    try {
      final response = await _tradingPortfolioClient.getTransactions(
          asset: asset, type: type, page: page, size: size);

      if (asset == null && response.data != null) {
        transactions = response.data!.transactions.toList();
        notifyListeners();
      }
      return Result.success(data: response.data);
    } on DioException catch (e) {
      getLogger(toString()).d("======= $e");
      final dioException = OnboardExceptions.fromDioError(e);
      return Result.failure(error: dioException);
    }
  }

  Future<bool> hasMadeDeposit({bool showLoader = false}) async {
    if (showLoader) EasyLoading.show();
    final result = await getTransactions(
            type: PortfolioTransactionActivityType.DEPOSIT, size: 2)
        .whenComplete(() {
      if (showLoader) EasyLoading.dismiss();
    });

    result.whenOrNull(success: (list) {
      if (list?.transactions.isNotEmpty == true) {
        return true;
      }
    });
    return false;
  }

  clearCache() {
    balanceSnapshot = null;
    portfolioAssetsBalances.clear();
  }
}
