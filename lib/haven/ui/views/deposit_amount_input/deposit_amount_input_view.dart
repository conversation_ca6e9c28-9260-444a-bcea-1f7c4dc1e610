import 'package:flutter/material.dart';
import 'package:nestcoinco_onboard_api_gateway_lite/nestcoinco_onboard_api_gateway_lite.dart';
import 'package:onboard_wallet/haven/enums/funding_enums.dart';
import 'package:onboard_wallet/haven/extensions/extensions.dart';
import 'package:onboard_wallet/gen/assets.gen.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/haven/extensions/funding_flow_type.dart';
import 'package:onboard_wallet/haven/models/asset/local_asset_market_data.dart';
import 'package:onboard_wallet/haven/ui/widgets/input_field/input_field.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';
import 'package:onboard_wallet/ui/common/ui_helpers.dart';
import 'package:onboard_wallet/haven/ui/views/deposit_amount_input/deposit_amount_input_viewmodel.dart';
import 'package:onboard_wallet/ui/widgets/buttons/onbw_back_button.dart';
import 'package:onboard_wallet/ui/widgets/scaffold/loader_wrapper.dart';
import 'package:onboard_wallet/haven/ui/views/deposit_amount_input/widgets/rail_provider_widget.dart';
import 'package:onboard_wallet/ui/widgets/onbw_shimmer.dart';
import 'package:onboard_wallet/ui/widgets/widgets.dart';
import 'package:stacked/stacked.dart';

class DepositAmountInputView extends StackedView<DepositAmountInputViewModel> {
  const DepositAmountInputView({
    super.key,
    required this.localAssetData,
    this.assetBalance,
    required this.provider,
    required this.fundOptionType,
    required this.countryCode,
    required this.providers,
  });

  final LocalAssetMarketData localAssetData;
  final UserPortfolioAssetBalance? assetBalance;
  final RailsProvider provider;
  final HavenFundOptionFlowType fundOptionType;
  final String countryCode;
  final List<RailsProvider> providers;

  @override
  Widget builder(BuildContext context, DepositAmountInputViewModel viewModel,
      Widget? child) {
    final textTheme = Theme.of(context).textTheme;
    return LoaderWrapper(
      showLoader: viewModel.initiatingOrder,
      loaderMessage: S.current.redirectingToProvider(
          viewModel.provider.name),
      child: Scaffold(
        appBar: TransparentAppBar(
          leading: OnbwBackButton(
            onPressed: viewModel.handlePop,
          ),
          title: Text(
            viewModel.title,
            style:
            Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: Grey.grey90,
            ),
          ),
        ),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                verticalSpaceMedium,
                CashCryptoInputField(
                  inputFieldCrossAxisAlignment: CrossAxisAlignment.start,
                  textEditingController: viewModel.tokenTextEditingController,
                  label: S.current.amount,
                  labelStyle: textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w400,
                    color: Grey.grey500,
                    letterSpacing: 0.25,
                  ),
                  isEnabled: true,
                  currency: viewModel.currencyName,
                  inputFormatters: viewModel.inputFormatters,
                  onChanged: (value) => viewModel.onTokenTextChanged(value),
                  bottomText: viewModel.bottomText,
                  onCurrencyTapped: viewModel.switchInputMode,
                  // onMaxSelected:
                  balanceColor: viewModel.amountErrorMessage.isEmpty
                      ? Grey.grey400
                      : ErrorColor.error500,
                ),
                const verticalSpace(40),
                Padding(
                  padding: const EdgeInsets.only(left: 16.0),
                  child: Text(
                    viewModel.fundOptionType.isFunding
                        ? S.current.paymentMethod
                        : S.current.deliveryMethod,
                    style: textTheme.bodyMedium?.copyWith(
                      color: Grey.grey400,
                      fontSize: 14.fontSize,
                      height: 1.57,
                    ),
                  ),
                ),
                const verticalSpace(16),
                if (viewModel.isBusy) ...[
                  OnbwShimmer(
                    height: 60,
                    width: double.infinity,
                    borderRadius: BorderRadius.circular(8),
                  ),
                ] else ...[

                  RailProviderWidget(
                    provider: viewModel.provider,
                    subtitle: viewModel.fundOptionType.isFunding
                        ? S.current.payWithCash
                        : S.current.sendAsCash,
                    icon: Assets.svg.bank.svg(),
                    onTap: viewModel.toFundOptions,
                  ),
                ],
                const Spacer(),
                Container(
                  color: Grey.grey50,
                  child: PrimaryButton.common(
                    enabled: viewModel.buttonEnabled ||
                        viewModel.errorMessage.isNotEmpty,
                    bgColor: viewModel.errorMessage.isEmpty
                        ? kcBrandPurple
                        : ErrorColor.error50,
                    onPressed: viewModel.onContinuePressed,
                    child: Text(
                      viewModel.errorMessage.isEmpty
                          ? S.current.continueText
                          : viewModel.errorMessage,
                      style: Theme.of(context).textTheme.labelLarge?.copyWith(
                            color: viewModel.errorMessage.isEmpty
                                ? kcBaseWhite
                                : ErrorColor.error600,
                          ),
                    ),
                  ),
                ),
                const verticalSpace(20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  DepositAmountInputViewModel viewModelBuilder(BuildContext context) =>
      DepositAmountInputViewModel(localAssetData, assetBalance, provider,
          fundOptionType, countryCode, providers);

  @override
  void onViewModelReady(DepositAmountInputViewModel viewModel) =>
      viewModel.onViewModelReady();
}
