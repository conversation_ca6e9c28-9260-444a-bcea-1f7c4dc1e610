import 'package:flutter/cupertino.dart';
import 'package:onboard_wallet/app/app.locator.dart';
import 'package:onboard_wallet/app/app.logger.dart';
import 'package:onboard_wallet/app/app.router.dart';
import 'package:onboard_wallet/haven/constants/analytic_event.dart';
import 'package:onboard_wallet/haven/enums/enums.dart';
import 'package:onboard_wallet/haven/models/asset/local_asset_market_data.dart';
import 'package:onboard_wallet/haven/services/trading_assets_service.dart';
import 'package:onboard_wallet/models/onboard_user/onboard_user.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';

class HavenSelectAssetsViewModel extends BaseViewModel {
  final _analyticsService = locator<AnalyticsService>();

  final Function(LocalAssetMarketData)? onAssetSelected;
  final AssetSelectionType selectionType;
  final String? assetToOmit;
  final List<LocalAssetMarketData>? assetPrices;

  HavenSelectAssetsViewModel(this.onAssetSelected, this.selectionType,
      this.assetToOmit, this.assetPrices);

  final _tradingAssetService = locator<TradingAssetsService>();
  final _navigationService = locator<NavigationService>();
  final _userService = locator<UserService>();

  OnboardUser? get currentUser => _userService.getCurrentUser();

  final searchController = TextEditingController();
  bool _searchFieldNotEmpty = false;

  List<LocalAssetMarketData> _searchResultTokens = [];

  List<LocalAssetMarketData> get searchResultTokens => _searchResultTokens;

  set searchResultTokens(List<LocalAssetMarketData> value) {
    _searchResultTokens = value;
    notifyListeners();
  }

  bool hasReachedMax = true;

  bool get searchFieldNotEmpty => _searchFieldNotEmpty;

  List<LocalAssetMarketData> _allAssets = [];

  List<LocalAssetMarketData> get allAssets => _allAssets;

  set allAssets(List<LocalAssetMarketData> value) {
    _allAssets = value;
    notifyListeners();
  }

  set searchFieldNotEmpty(bool value) {
    _searchFieldNotEmpty = value;
    notifyListeners();
  }

  onModelReady() {
    _analyticsService.logEvent(
        eventName: HavenAnalyticsEvent.fundSelectAssetsScreen);

    if (assetPrices != null && assetPrices!.isNotEmpty) {
      _allAssets = assetPrices!;
      searchResultTokens = _allAssets;
    } else {
      fetchAssets();
    }
  }

  void fetchAssets() async {
    if (_tradingAssetService.cachedAssetList.isNotEmpty) {
      updateAssetsBaseOnSelectionType(_tradingAssetService.cachedAssetList);
    } else {
      setBusy(true);
    }

    final res = await _tradingAssetService
        .getAssetsConfig()
        .whenComplete(() => setBusy(false));

    res.when(success: (success) {
      if (success != null && success.isNotEmpty) {
        updateAssetsBaseOnSelectionType(success);
      }
    }, failure: (error) {
      getLogger(toString()).e(error.message);
    });
  }

  updateAssetsBaseOnSelectionType(List<LocalAssetMarketData> assetDataList) {
    switch (selectionType) {
      case AssetSelectionType.firstTimeDeposit:
      case AssetSelectionType.deposit:
        _allAssets = assetDataList
            .where((element) => element.assetInfo?.depositAvailable == true)
            .toList();
        break;
      case AssetSelectionType.trading:
        _allAssets = assetDataList
            .where((element) => element.assetInfo?.tradingAvailable == true)
            .toList();
        break;
      case AssetSelectionType.withdrawal:
        _allAssets = assetDataList
            .where((element) => element.assetInfo?.transferAvailable == true)
            .toList();
        break;
    }

    if (assetToOmit != null) {
      _allAssets = _allAssets
          .where((element) => element.assetInfo?.symbol != assetToOmit)
          .toList();
    }

    searchResultTokens = _allAssets;
  }

  void onChanged(String value) {
    if (value.isNotEmpty) {
      searchResultTokens = _allAssets.where((element) {
        final nameMatch = element.assetInfo?.name
                ?.toLowerCase()
                .contains(value.toLowerCase()) ??
            false;

        final symbolMatch = element.assetInfo?.symbol
                ?.toLowerCase()
                .contains(value.toLowerCase()) ??
            false;

        return nameMatch || symbolMatch;
      }).toList();
    } else {
      searchResultTokens = _allAssets;
    }
  }

  void clearField() {
    searchController.clear();
    notifyListeners();
  }

  onTokenSelect(LocalAssetMarketData asset) {
    _analyticsService.logEvent(
        eventName: HavenAnalyticsEvent.fundAssetSelected,
        parameters: {
          HavenEventParameterKey.tokenSymbol: asset.assetInfo?.symbol ?? ""
        });

    if (onAssetSelected != null) {
      onAssetSelected!(asset);
    } else {
      _navigationService.navigateToHavenFundOptionsView(
          asset: asset, fundOptionType: HavenFundOptionFlowType.add);
    }
  }

  void lowReputationWarningTap() {}

  back() {
    _analyticsService.logEvent(eventName: HavenAnalyticsEvent.fundSelectAssetClose);

    if (selectionType == AssetSelectionType.firstTimeDeposit) {
      _navigationService.clearStackAndShow(Routes.homeView);
    } else {
      _navigationService.back();
    }
  }

  bool showMoreInfoTag() {
    return currentUser?.country?.toLowerCase() == CountryCodeEnum.ng.name;
  }
}
