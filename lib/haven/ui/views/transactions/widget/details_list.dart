import 'package:flutter/material.dart';
import 'package:onboard_wallet/gen/assets.gen.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';
import 'package:onboard_wallet/ui/common/ui_helpers.dart';
import 'package:onboard_wallet/haven/ui/views/transfer/widgets/send_widgets.dart';
import 'package:onboard_wallet/ui/widgets/cards/card_surface.dart';
import 'package:stacked/stacked.dart';
import '../transaction_detail/transaction_detail_viewmodel.dart';
import 'txn_card.dart';

class DetailsList extends ViewModelWidget<HavenTransactionDetailViewModel> {
  const DetailsList({super.key});

  @override
  Widget build(
      BuildContext context, HavenTransactionDetailViewModel viewModel) {
    return Column(
      children: [
        CardSurface(
          child: Column(
            children: [
              if (viewModel.transaction.blockchainInfo != null) ...[
                TxnToCard(
                  address:
                      viewModel.transaction.blockchainInfo?.recipient.address,
                ),
                const Divider(
                  height: 34,
                  color: Grey.grey100,
                ),
              ] else if (viewModel.isWithdrawal &&
                  viewModel.isCashTag &&
                  viewModel.transaction.paymentMethod != null) ...[
                SendConfirmationItems(
                  icon: Assets.svg.bank.svg(
                      colorFilter: const ColorFilter.mode(
                          kcBrandPurple, BlendMode.srcIn)),
                  leading: S.current.bankName,
                  trailing: viewModel.transaction.paymentMethod?.bankName ?? "",
                ),
                const Divider(
                  height: 34,
                  color: Grey.grey100,
                ),
                SendConfirmationItems(
                  icon: Assets.svg.checked.svg(),
                  leading: S.current.accountName,
                  trailing:
                      viewModel.transaction.paymentMethod?.accountName ?? "",
                ),
                const Divider(
                  height: 34,
                  color: Grey.grey100,
                ),
                SendConfirmationItems(
                  icon: Assets.svg.checked.svg(),
                  leading: S.current.accountNumber,
                  trailing:
                      viewModel.transaction.paymentMethod?.accountIdentifier ??
                          "",
                ),
                if (viewModel.transaction.paymentMethod?.country != null) ...[
                  const Divider(
                    height: 34,
                    color: Grey.grey100,
                  ),
                  SendConfirmationItems(
                    icon: Assets.svg.checked.svg(),
                    leading: S.current.country,
                    trailing:
                        viewModel.transaction.paymentMethod?.country ?? "",
                  ),
                ],
                const Divider(
                  height: 34,
                  color: Grey.grey100,
                ),
                SendConfirmationItems(
                  icon: Assets.haven.svg.coinsSwap.svg(
                      colorFilter: const ColorFilter.mode(
                          kcPrimaryColor, BlendMode.srcIn)),
                  leading: S.current.rate,
                  trailing: viewModel.getRate(),
                ),
                const Divider(
                  height: 34,
                  color: Grey.grey100,
                ),
              ],
              TxnFeeCard(
                fee: viewModel.transaction.feeAmount,
                asset: viewModel.getAssetSymbol(),
              ),
              const Divider(
                height: 34,
                color: Grey.grey100,
              ),
              if (viewModel.transaction.notes != null) ...[
                SendConfirmationItems(
                  icon: Assets.svg.checked.svg(),
                  leading: S.current.notes,
                  trailing:
                      viewModel.transaction.notes ?? viewModel.transaction.tag,
                ),
                const Divider(
                  height: 34,
                  color: Grey.grey100,
                ),
              ],
              SendConfirmationItems(
                icon: Assets.svg.timeCircle.svg(
                    colorFilter: const ColorFilter.mode(
                        kcPrimaryColor, BlendMode.srcIn)),
                leading: S.current.date,
                trailing: viewModel.dateAndTime() ?? '',
              ),
            ],
          ),
        ),
        const verticalSpace(37),
        GestureDetector(
          onTap: () => viewModel.viewTransaction(),
          behavior: HitTestBehavior.translucent,
          child: CardSurface(
              child: Row(
            children: [
              IconContainer(
                size: 30,
                child: Padding(
                  padding: const EdgeInsets.all(2.0),
                  child: Assets.svg.lock.svg(),
                ),
              ),
              const horizontalSpace(12),
              Text(
                S.current.transactionId,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                      fontSize: 14,
                    ),
              ),
              Expanded(
                child: Text(
                  viewModel.transactionID(),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: kcPrimaryColor,
                        fontSize: 14,
                        decoration: TextDecoration.underline,
                      ),
                  textAlign: TextAlign.right,
                ),
              ),
              const horizontalSpace(8),
              Assets.svg.arrowUpRight.svg(
                colorFilter:
                    const ColorFilter.mode(kcPrimaryColor, BlendMode.srcIn),
              )
            ],
          )),
        ),
      ],
    );
  }
}
