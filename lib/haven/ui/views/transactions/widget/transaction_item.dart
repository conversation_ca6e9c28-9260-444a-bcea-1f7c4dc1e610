import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nestcoinco_onboard_api_gateway_lite/nestcoinco_onboard_api_gateway_lite.dart';
import 'package:onboard_wallet/extensions/extensions.dart';
import 'package:onboard_wallet/haven/models/asset/local_asset_market_data.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';
import 'package:onboard_wallet/ui/common/ui_helpers.dart';
import 'package:onboard_wallet/haven/utils/mixins/transaction_helper_mixin.dart';

class TransactionItem extends StatelessWidget with TransactionHelperMixin {
  const TransactionItem(
      {super.key,
      required this.transaction,
      required this.onItemClicked,
      required this.assetMarketData,
      this.isPortfolioPage = false});

  final PortfolioTransaction transaction;
  final LocalAssetMarketData? assetMarketData;
  final VoidCallback onItemClicked;
  final bool isPortfolioPage;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: onItemClicked,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 11),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SvgPicture.asset(getTransactionIcon(
                    direction: transaction.direction,
                    type: transaction.type,
                    status: transaction.status) ??
                ""),
            const horizontalSpace(18),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                        child: Text(
                          getTransactionTitle(
                              type: transaction.type,
                              tag: transaction.tag),
                          overflow: TextOverflow.ellipsis,
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontSize: 14.fontSize,
                                    fontWeight: FontWeight.w500,
                                    color: Grey.grey900,
                                  ),
                        ),
                      ),
                      const horizontalSpace(20),
                      Text(
                          "${formattedAmount(transaction, assetMarketData)} ${transaction.asset}",
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontSize: 14.fontSize,
                                    fontWeight: FontWeight.w400,
                                    color: getTransactionAmountColor(
                                        type: transaction.direction),
                                  )),
                    ],
                  ),
                  const verticalSpace(3),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                        child: Text(
                          isPortfolioPage
                              ? formattedDate(transaction)
                              : getTransactionStatusText(
                                  status: transaction.status),
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    fontSize: 11.fontSize,
                                    fontWeight: FontWeight.w400,
                                    color: Grey.grey400,
                                  ),
                        ),
                      ),
                      const horizontalSpace(20),
                      Text(
                        amountInDollars(transaction, assetMarketData),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              fontSize: 11.fontSize,
                              fontWeight: FontWeight.w400,
                              color: Grey.grey400,
                            ),
                      ),
                    ],
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
