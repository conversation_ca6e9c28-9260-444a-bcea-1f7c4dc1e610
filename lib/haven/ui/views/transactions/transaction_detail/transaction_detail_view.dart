import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nestcoinco_onboard_api_gateway_lite/nestcoinco_onboard_api_gateway_lite.dart';
import 'package:onboard_wallet/extensions/num.dart';
import 'package:onboard_wallet/generated/l10n.dart';
import 'package:onboard_wallet/haven/models/asset/local_asset_market_data.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';
import 'package:onboard_wallet/ui/common/ui_helpers.dart';
import 'package:onboard_wallet/haven/ui/views/transactions/transaction_detail/transaction_detail_viewmodel.dart';
import 'package:onboard_wallet/haven/ui/views/transactions/widget/details_list.dart';
import 'package:onboard_wallet/ui/widgets/app_bar/app_bar.dart';
import 'package:onboard_wallet/ui/widgets/progress/onbw_progress_indicator.dart';
import 'package:onboard_wallet/haven/utils/mixins/transaction_helper_mixin.dart';
import 'package:stacked/stacked.dart';

class HavenTransactionDetailView extends StackedView<HavenTransactionDetailViewModel>
    with TransactionHelperMixin {
  const HavenTransactionDetailView(
      {super.key,
      this.transaction,
      this.assetMarketData,
      this.isFromNotification = false,
      this.transactionType,
      this.assetSymbol, this.transactionIdFromNotif});

  final PortfolioTransaction? transaction;
  final LocalAssetMarketData? assetMarketData;
  final bool isFromNotification;
  final String? transactionIdFromNotif;
  final String? transactionType;
  final String? assetSymbol;

  @override
  Widget builder(BuildContext context, HavenTransactionDetailViewModel viewModel,
      Widget? child) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      appBar: TransparentAppBar(
        centerTitle: true,
        title: Text(
          isFromNotification
              ? getTransactionTitleNotif(type: transactionType)
              : getTransactionTitle(
                  type: transaction?.type,
                  tag: transaction?.tag),
          style: textTheme.titleMedium?.copyWith(
              color: Grey.grey90, fontSize: 20.fontSize, letterSpacing: -0.05),
        ),
      ),
      body: SafeArea(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 22),
          child: viewModel.isBusy
              ? const Center(
                  child: SizedBox.square(
                    dimension: 100,
                    child: OnbwProgressIndicator(
                      color: kcPrimaryColor,
                      strokeWidth: 5,
                      backgroundColor: kcPrimaryColor,
                    ),
                  ),
                )
              : viewModel.hasError
                  ? Center(
                      child: Text(
                        S.current.failedToGetTransaction,
                        style: textTheme.bodyMedium
                            ?.copyWith(color: ErrorColor.error50),
                      ),
                    )
                  : SingleChildScrollView(
                      child: Column(
                        children: [
                          const verticalSpace(20),
                          Center(
                            child: SvgPicture.asset(
                              getTransactionIcon(
                                      direction:
                                          viewModel.transaction.direction,
                                      type: viewModel.transaction.type,
                                      status: viewModel.transaction.status) ??
                                  "",
                              height: 66.h,
                              width: 66.w,
                            ),
                          ),
                          const verticalSpace(20),
                          Text(
                              "${formattedAmount(viewModel.transaction, viewModel.assetMarketData)} ${viewModel.getAssetSymbol()}",
                              style: Theme.of(context)
                                  .textTheme
                                  .titleLarge
                                  ?.copyWith(
                                      fontSize: 22.fontSize,
                                      fontWeight: FontWeight.w600,
                                      color: Grey.grey900,
                                      letterSpacing: -0.5)),
                          const verticalSpace(10),
                          Text(
                            amountInDollars(
                                viewModel.transaction, assetMarketData),
                            style: Theme.of(context)
                                .textTheme
                                .bodySmall
                                ?.copyWith(
                                    fontSize: 14.fontSize,
                                    fontWeight: FontWeight.w400,
                                    color: Grey.grey500,
                                    letterSpacing: -0.2),
                          ),
                          const verticalSpace(20),
                          GestureDetector(
                            onTap: () {
                              viewModel.showActionRequiredDialog();
                            },
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 14.w, vertical: 8.h),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(16.r),
                                  color: getTransactionStatusBgColor(
                                      status: viewModel.transaction.status)),
                              child: Text(
                                getTransactionStatusText(
                                    status: viewModel.transaction.status),
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.copyWith(
                                        fontSize: 14.fontSize,
                                        fontWeight: FontWeight.w500,
                                        color: getTransactionStatusColor(
                                            status:
                                                viewModel.transaction.status),
                                        letterSpacing: -0.2),
                              ),
                            ),
                          ),
                          const verticalSpace(27),
                          const DetailsList()
                        ],
                      ),
                    ),
        ),
      ),
    );
  }

  @override
  HavenTransactionDetailViewModel viewModelBuilder(BuildContext context) {
    return HavenTransactionDetailViewModel(
        assetMarketData, isFromNotification, transactionIdFromNotif);
  }

  @override
  void onViewModelReady(HavenTransactionDetailViewModel viewModel) =>
      viewModel.onViewModelReady(transaction);
}
