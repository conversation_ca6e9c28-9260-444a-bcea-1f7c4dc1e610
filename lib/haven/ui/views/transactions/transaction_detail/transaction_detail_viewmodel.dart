import 'package:flutter/material.dart';
import 'package:flutter_multi_formatter/flutter_multi_formatter.dart';
import 'package:intl/intl.dart';
import 'package:nestcoinco_onboard_api_gateway_lite/nestcoinco_onboard_api_gateway_lite.dart';
import 'package:onboard_wallet/app/app.locator.dart';
import 'package:onboard_wallet/app/app.logger.dart';
import 'package:onboard_wallet/app/app.router.dart';
import 'package:onboard_wallet/haven/constants/analytic_event.dart';
import 'package:onboard_wallet/extensions/extensions.dart';
import 'package:onboard_wallet/manager/manager.dart';
import 'package:onboard_wallet/haven/models/asset/local_asset_market_data.dart';
import 'package:onboard_wallet/services/analytics_service.dart';
import 'package:onboard_wallet/haven/services/trading_portfolio_service.dart';
import 'package:onboard_wallet/ui/dialogs/info_alert/external_close_dialog.dart';
import 'package:onboard_wallet/haven/ui/views/transactions/widget/action_required_dialog.dart';
import 'package:onboard_wallet/haven/utils/mixins/copy_address_mixin.dart';
import 'package:onboard_wallet/haven/utils/mixins/transaction_helper_mixin.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';
import 'package:collection/collection.dart';

class HavenTransactionDetailViewModel extends BaseViewModel
    with CopyAddressMixin, TransactionHelperMixin {
  final _navigationService = locator<NavigationService>();
  final _portfolioService = locator<TradingPortfolioService>();
  final _toastManager = locator<ToastManager>();
  final _analyticService = locator<AnalyticsService>();

  late PortfolioTransaction transaction;
  final LocalAssetMarketData? assetMarketData;
  final bool fromNotification;
  final String? transactionIdFromNotif;

  HavenTransactionDetailViewModel(
      this.assetMarketData, this.fromNotification, this.transactionIdFromNotif);

  final tagCashWithdrawal = "TX_WITHDRAWAL_CASH";
  final tagBlockchainWithdrawal = "TX_WITHDRAWAL_BLOCKCHAIN";

  bool get isTrade =>
      transaction.type == PortfolioTransactionActivityType.TRADE;

  bool get isDeposit =>
      transaction.type == PortfolioTransactionActivityType.DEPOSIT;

  bool get isWithdrawal =>
      transaction.type == PortfolioTransactionActivityType.WITHDRAWAL;

  bool get isBlockchainTag => transaction.tag == tagBlockchainWithdrawal;

  bool get isCashTag => transaction.tag == tagCashWithdrawal;

  onViewModelReady(PortfolioTransaction? transaction) async {
    _analyticService.logEvent(
        eventName: HavenAnalyticsEvent.transactionDetailsView,
        parameters: {
          HavenEventParameterKey.tokenSymbol: assetMarketData?.assetInfo?.symbol,
          HavenEventParameterKey.trxType: transaction?.type.name,
        });

    await _fetchTransactionDetails(transaction);

    if (fromNotification) {
      _logDepositAndTransferStatusEvent();

      _portfolioService.getUserBalance();
      _portfolioService.getUserPortfolio();
    }
  }

  _fetchTransactionDetails(PortfolioTransaction? lTransaction) async {
    setBusy(true);

    final result = await _portfolioService
        .getTransactionById(
            fromNotification ? transactionIdFromNotif! : lTransaction?.id ?? "")
        .whenComplete(() => setBusy(false));

    result.when(success: (success) {
      if (success != null) {
        transaction = success;
        notifyListeners();
      } else if (lTransaction != null) {
        transaction = lTransaction;
      }
    }, failure: (failure) {
      _toastManager.showErrorToast(text: failure.message);
    });
  }

  viewTransaction() {
    if (transaction.blockchainInfo != null) {
      _analyticService.logEvent(
          eventName: HavenAnalyticsEvent.transactionHashOnTokenScanner,
          parameters: {
            HavenEventParameterKey.tokenSymbol: assetMarketData?.assetInfo?.symbol,
            HavenEventParameterKey.trxType: transaction.type.name,
          });

      String? txUrl = transaction.blockchainInfo?.txUrl;
      if (txUrl != null) {
        _navigationService.navigateToOnboardWebView(urlPath: txUrl);
      }
    } else {
      copyTransactionRef(txRef: transaction.reference);
    }
  }

  String getAssetSymbol() {
    Metadata? tokenMetadata = transaction.metadata
        .firstWhereOrNull((item) => item.name == 'tokenSymbol');

    if (tokenMetadata != null && transaction.asset != "USD") {
      return tokenMetadata.value;
    } else {
      return transaction.asset;
    }
  }

  String getRate() {
    Metadata? quoteCurrencyMetadata = transaction.metadata
        .firstWhereOrNull((item) => item.name == 'quoteCurrency');

    Metadata? quote = transaction.metadata
        .firstWhereOrNull((item) => item.name == 'quoteRate');

    if (quote != null) {
      //add a check for quoteCurrencyMetadata
      return "1 ${getAssetSymbol()} = ${quote.value.toCurrencyString(trailingSymbol: quoteCurrencyMetadata?.value ?? "")}";
    } else {
      return "";
    }
  }

  String? dateAndTime() {
    var formatter = DateFormat("dd/MM/yyyy, hh.mm aaa");
    return formatter.format(transaction.createdAt.toLocal());
  }

  String transactionID() {
    if (transaction.isOnchain == true && transaction.blockchainInfo != null) {
      return transaction.blockchainInfo?.txHash
              .middleOverflow(prefixCharacter: 7, suffixCharacter: 3) ??
          '';
    } else {
      return transaction.reference
          .middleOverflow(prefixCharacter: 7, suffixCharacter: 3);
    }
  }

  void showActionRequiredDialog() {
    final context = StackedService.navigatorKey?.currentContext;

    if (context != null) {
      ExternalCloseDialog.showExternalDialog(
        context,
        child: ActionRequiredDialog(
          onReviewInfoClicked: () {
            //TODO: implement review info
            _navigationService.back();
          },
        ),
        alignment: const Alignment(0, -0.2),
      );
    }
  }

  void _logDepositAndTransferStatusEvent() {
    try {
      String eventName = "";

      if (transaction.status == PortfolioTransactionStatus.SUCCESS) {
        eventName = isDeposit
            ? HavenAnalyticsEvent.fundAssetDepositSuccess
            : isCashTag
                ? HavenAnalyticsEvent.transferCashTransferSuccessScreen
                : HavenAnalyticsEvent.transferAssetSuccessScreen;
      }

      _analyticService.logEvent(eventName: eventName, parameters: {
        HavenEventParameterKey.tokenSymbol: assetMarketData?.assetInfo?.symbol,
        HavenEventParameterKey.trxType: transaction.type.name,
      });
    } catch (e) {
      getLogger(toString()).e(e.toString());
    }
  }
}
