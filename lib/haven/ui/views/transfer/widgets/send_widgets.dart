import 'package:flutter/material.dart';
import 'package:onboard_wallet/extensions/num.dart';
import 'package:onboard_wallet/ui/common/app_colors.dart';
import 'package:onboard_wallet/ui/common/ui_helpers.dart';

class SendConfirmationItems extends StatelessWidget {
  final String leading;
  final String trailing;
  final Widget icon;

  const SendConfirmationItems(
      {super.key,
      required this.leading,
      required this.trailing,
      required this.icon});

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        IconContainer(
            size: 30,
            child: Padding(
              padding: const EdgeInsets.all(1.0),
              child: icon,
            )),
        const horizontalSpace(12),
        Text(
          leading,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w500,
                color: Colors.black,
                fontSize: 12.fontSize,
              ),
        ),
        const horizontalSpace(50),
        Expanded(
          child: Text(
            trailing,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Grey.grey500,
                  fontSize: 12.fontSize,
                ),
            textAlign: TextAlign.right,
          ),
        )
      ],
    );
  }
}

class IconContainer extends StatelessWidget {
  final Widget child;
  final Color bgColor;
  final double size;
  final Color? borderColor;

  const IconContainer({
    super.key,
    required this.child,
    this.bgColor = Grey.grey100,
    this.size = 38,
    this.borderColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: size,
      width: size,
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(30),
        border: Border.all(color: borderColor ?? purple6point6),
      ),
      child: Center(
        child: SizedBox(
          height: 0.6 * size,
          width: 0.6 * size,
          child: child,
        ),
      ),
    );
  }
}
