/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsFontsGen {
  const $AssetsFontsGen();
}

class $AssetsGifGen {
  const $AssetsGifGen();

  /// File path: assets/gif/coin-rotation.gif
  AssetGenImage get coinRotation =>
      const AssetGenImage('assets/gif/coin-rotation.gif');

  /// List of all assets
  List<AssetGenImage> get values => [coinRotation];
}

class $AssetsHavenGen {
  const $AssetsHavenGen();

  /// Directory path: assets/haven/json
  $AssetsHavenJsonGen get json => const $AssetsHavenJsonGen();

  /// Directory path: assets/haven/png
  $AssetsHavenPngGen get png => const $AssetsHavenPngGen();

  /// Directory path: assets/haven/svg
  $AssetsHavenSvgGen get svg => const $AssetsHavenSvgGen();
}

class $AssetsJavascriptGen {
  const $AssetsJavascriptGen();

  /// File path: assets/javascript/trust.js
  String get trust => 'assets/javascript/trust.js';

  /// List of all assets
  List<String> get values => [trust];
}

class $AssetsJsonGen {
  const $AssetsJsonGen();

  /// File path: assets/json/ICON_ANIMATED.json
  String get iconAnimated => 'assets/json/ICON_ANIMATED.json';

  /// File path: assets/json/ONBOARD_V1.json
  String get onboardV1 => 'assets/json/ONBOARD_V1.json';

  /// File path: assets/json/abi.json
  String get abi => 'assets/json/abi.json';

  /// File path: assets/json/country-codes.json
  String get countryCodes => 'assets/json/country-codes.json';

  /// File path: assets/json/green-checkmark.json
  String get greenCheckmark => 'assets/json/green-checkmark.json';

  /// File path: assets/json/handshake.json
  String get handshake => 'assets/json/handshake.json';

  /// File path: assets/json/loader.json
  String get loader => 'assets/json/loader.json';

  /// File path: assets/json/smile_config.json
  String get smileConfig => 'assets/json/smile_config.json';

  /// File path: assets/json/smile_config_stage.json
  String get smileConfigStage => 'assets/json/smile_config_stage.json';

  /// File path: assets/json/trading_wallet_abi.json
  String get tradingWalletAbi => 'assets/json/trading_wallet_abi.json';

  /// File path: assets/json/wallet-created.json
  String get walletCreated => 'assets/json/wallet-created.json';

  /// List of all assets
  List<String> get values => [
        iconAnimated,
        onboardV1,
        abi,
        countryCodes,
        greenCheckmark,
        handshake,
        loader,
        smileConfig,
        smileConfigStage,
        tradingWalletAbi,
        walletCreated
      ];
}

class $AssetsPngGen {
  const $AssetsPngGen();

  /// File path: assets/png/add-money.png
  AssetGenImage get addMoney => const AssetGenImage('assets/png/add-money.png');

  /// File path: assets/png/app-loader.png
  AssetGenImage get appLoader =>
      const AssetGenImage('assets/png/app-loader.png');

  /// File path: assets/png/blue_logo.png
  AssetGenImage get blueLogo => const AssetGenImage('assets/png/blue_logo.png');

  /// File path: assets/png/blue_plus.png
  AssetGenImage get bluePlus => const AssetGenImage('assets/png/blue_plus.png');

  /// File path: assets/png/card-intro-illos.png
  AssetGenImage get cardIntroIllos =>
      const AssetGenImage('assets/png/card-intro-illos.png');

  /// File path: assets/png/card-intro-ilos.png
  AssetGenImage get cardIntroIlos =>
      const AssetGenImage('assets/png/card-intro-ilos.png');

  /// File path: assets/png/card-stack.png
  AssetGenImage get cardStack =>
      const AssetGenImage('assets/png/card-stack.png');

  /// File path: assets/png/card_splash.png
  AssetGenImage get cardSplash =>
      const AssetGenImage('assets/png/card_splash.png');

  /// File path: assets/png/cards-merchants.png
  AssetGenImage get cardsMerchants =>
      const AssetGenImage('assets/png/cards-merchants.png');

  /// File path: assets/png/connected-sites-illos.png
  AssetGenImage get connectedSitesIllos =>
      const AssetGenImage('assets/png/connected-sites-illos.png');

  /// File path: assets/png/crypto_balance_background.png
  AssetGenImage get cryptoBalanceBackground =>
      const AssetGenImage('assets/png/crypto_balance_background.png');

  /// File path: assets/png/dapp-illos.png
  AssetGenImage get dappIllos =>
      const AssetGenImage('assets/png/dapp-illos.png');

  /// File path: assets/png/delete-account-warning.png
  AssetGenImage get deleteAccountWarning =>
      const AssetGenImage('assets/png/delete-account-warning.png');

  /// File path: assets/png/discover-icon.png
  AssetGenImage get discoverIcon =>
      const AssetGenImage('assets/png/discover-icon.png');

  /// File path: assets/png/drive logo.png
  AssetGenImage get driveLogo =>
      const AssetGenImage('assets/png/drive logo.png');

  /// File path: assets/png/effect bg blue.png
  AssetGenImage get effectBgBlue =>
      const AssetGenImage('assets/png/effect bg blue.png');

  /// File path: assets/png/frozen card content.png
  AssetGenImage get frozenCardContent =>
      const AssetGenImage('assets/png/frozen card content.png');

  /// File path: assets/png/get-a-us-bank-account-ilos-white.png
  AssetGenImage get getAUsBankAccountIlosWhite =>
      const AssetGenImage('assets/png/get-a-us-bank-account-ilos-white.png');

  /// File path: assets/png/get-a-us-bank-account-ilos.png
  AssetGenImage get getAUsBankAccountIlos =>
      const AssetGenImage('assets/png/get-a-us-bank-account-ilos.png');

  /// File path: assets/png/get-a-usd-account.png
  AssetGenImage get getAUsdAccount =>
      const AssetGenImage('assets/png/get-a-usd-account.png');

  /// File path: assets/png/gift_box.png
  AssetGenImage get giftBox => const AssetGenImage('assets/png/gift_box.png');

  /// File path: assets/png/google logo.png
  AssetGenImage get googleLogo =>
      const AssetGenImage('assets/png/google logo.png');

  /// File path: assets/png/home.png
  AssetGenImage get home => const AssetGenImage('assets/png/home.png');

  /// File path: assets/png/hub-intro-ilos.png
  AssetGenImage get hubIntroIlos =>
      const AssetGenImage('assets/png/hub-intro-ilos.png');

  /// File path: assets/png/hub.png
  AssetGenImage get hub => const AssetGenImage('assets/png/hub.png');

  /// File path: assets/png/icloud logo.png
  AssetGenImage get icloudLogo =>
      const AssetGenImage('assets/png/icloud logo.png');

  /// File path: assets/png/info-blue.png
  AssetGenImage get infoBlue => const AssetGenImage('assets/png/info-blue.png');

  /// File path: assets/png/info.png
  AssetGenImage get info => const AssetGenImage('assets/png/info.png');

  /// File path: assets/png/intro-four.png
  AssetGenImage get introFour =>
      const AssetGenImage('assets/png/intro-four.png');

  /// File path: assets/png/intro-one.png
  AssetGenImage get introOne => const AssetGenImage('assets/png/intro-one.png');

  /// File path: assets/png/intro-three.png
  AssetGenImage get introThree =>
      const AssetGenImage('assets/png/intro-three.png');

  /// File path: assets/png/intro-three2x.png
  AssetGenImage get introThree2x =>
      const AssetGenImage('assets/png/intro-three2x.png');

  /// File path: assets/png/intro-two.png
  AssetGenImage get introTwo => const AssetGenImage('assets/png/intro-two.png');

  /// File path: assets/png/intro_trade_wallet_one.png
  AssetGenImage get introTradeWalletOne =>
      const AssetGenImage('assets/png/intro_trade_wallet_one.png');

  /// File path: assets/png/intro_trade_wallet_two.png
  AssetGenImage get introTradeWalletTwo =>
      const AssetGenImage('assets/png/intro_trade_wallet_two.png');

  /// File path: assets/png/lifi.png
  AssetGenImage get lifi => const AssetGenImage('assets/png/lifi.png');

  /// File path: assets/png/lightbulb.png
  AssetGenImage get lightbulb =>
      const AssetGenImage('assets/png/lightbulb.png');

  /// File path: assets/png/logo.png
  AssetGenImage get logo => const AssetGenImage('assets/png/logo.png');

  /// File path: assets/png/manage-ads.png
  AssetGenImage get manageAds =>
      const AssetGenImage('assets/png/manage-ads.png');

  /// File path: assets/png/onboard-pay-ilos.png
  AssetGenImage get onboardPayIlos =>
      const AssetGenImage('assets/png/onboard-pay-ilos.png');

  /// File path: assets/png/pawel.png
  AssetGenImage get pawel => const AssetGenImage('assets/png/pawel.png');

  /// File path: assets/png/phone_star_illos.png
  AssetGenImage get phoneStarIllos =>
      const AssetGenImage('assets/png/phone_star_illos.png');

  /// File path: assets/png/pink-locker.png
  AssetGenImage get pinkLocker =>
      const AssetGenImage('assets/png/pink-locker.png');

  /// File path: assets/png/pink_lock.png
  AssetGenImage get pinkLock => const AssetGenImage('assets/png/pink_lock.png');

  /// File path: assets/png/promo.png
  AssetGenImage get promo => const AssetGenImage('assets/png/promo.png');

  /// File path: assets/png/purple-gradient.png
  AssetGenImage get purpleGradient =>
      const AssetGenImage('assets/png/purple-gradient.png');

  /// File path: assets/png/qr-code-illos.png
  AssetGenImage get qrCodeIllos =>
      const AssetGenImage('assets/png/qr-code-illos.png');

  /// File path: assets/png/red-cube.png
  AssetGenImage get redCube => const AssetGenImage('assets/png/red-cube.png');

  /// File path: assets/png/refer-and-earn-ilos.png
  AssetGenImage get referAndEarnIlos =>
      const AssetGenImage('assets/png/refer-and-earn-ilos.png');

  /// File path: assets/png/referral-icon.png
  AssetGenImage get referralIcon =>
      const AssetGenImage('assets/png/referral-icon.png');

  /// File path: assets/png/save-in-dollars-ilos-one.png
  AssetGenImage get saveInDollarsIlosOne =>
      const AssetGenImage('assets/png/save-in-dollars-ilos-one.png');

  /// File path: assets/png/save-in-dollars-ilos-three.png
  AssetGenImage get saveInDollarsIlosThree =>
      const AssetGenImage('assets/png/save-in-dollars-ilos-three.png');

  /// File path: assets/png/save-in-dollars-ilos-two.png
  AssetGenImage get saveInDollarsIlosTwo =>
      const AssetGenImage('assets/png/save-in-dollars-ilos-two.png');

  /// File path: assets/png/security.png
  AssetGenImage get security => const AssetGenImage('assets/png/security.png');

  /// File path: assets/png/selfie-sample-group.png
  AssetGenImage get selfieSampleGroup =>
      const AssetGenImage('assets/png/selfie-sample-group.png');

  /// File path: assets/png/setting.png
  AssetGenImage get setting => const AssetGenImage('assets/png/setting.png');

  /// File path: assets/png/spend-card-ilos.png
  AssetGenImage get spendCardIlos =>
      const AssetGenImage('assets/png/spend-card-ilos.png');

  /// File path: assets/png/star rate 1.png
  AssetGenImage get starRate1 =>
      const AssetGenImage('assets/png/star rate 1.png');

  /// File path: assets/png/star rate 2.png
  AssetGenImage get starRate2 =>
      const AssetGenImage('assets/png/star rate 2.png');

  /// File path: assets/png/star rate 3.png
  AssetGenImage get starRate3 =>
      const AssetGenImage('assets/png/star rate 3.png');

  /// File path: assets/png/star rate 4.png
  AssetGenImage get starRate4 =>
      const AssetGenImage('assets/png/star rate 4.png');

  /// File path: assets/png/star rate 5.png
  AssetGenImage get starRate5 =>
      const AssetGenImage('assets/png/star rate 5.png');

  /// File path: assets/png/swing.png
  AssetGenImage get swing => const AssetGenImage('assets/png/swing.png');

  /// File path: assets/png/tbc-blks.png
  AssetGenImage get tbcBlks => const AssetGenImage('assets/png/tbc-blks.png');

  /// File path: assets/png/top-tokens-icon.png
  AssetGenImage get topTokensIcon =>
      const AssetGenImage('assets/png/top-tokens-icon.png');

  /// File path: assets/png/top-tokens-ilos.png
  AssetGenImage get topTokensIlos =>
      const AssetGenImage('assets/png/top-tokens-ilos.png');

  /// File path: assets/png/trading_balance_background.png
  AssetGenImage get tradingBalanceBackground =>
      const AssetGenImage('assets/png/trading_balance_background.png');

  /// File path: assets/png/wallet-connect-symbol.png
  AssetGenImage get walletConnectSymbol =>
      const AssetGenImage('assets/png/wallet-connect-symbol.png');

  /// File path: assets/png/wallet-toggle.png
  AssetGenImage get walletToggle =>
      const AssetGenImage('assets/png/wallet-toggle.png');

  /// File path: assets/png/wallet.png
  AssetGenImage get wallet => const AssetGenImage('assets/png/wallet.png');

  /// File path: assets/png/wc.png
  AssetGenImage get wc => const AssetGenImage('assets/png/wc.png');

  /// File path: assets/png/world-send-icon.png
  AssetGenImage get worldSendIcon =>
      const AssetGenImage('assets/png/world-send-icon.png');

  /// List of all assets
  List<AssetGenImage> get values => [
        addMoney,
        appLoader,
        blueLogo,
        bluePlus,
        cardIntroIllos,
        cardIntroIlos,
        cardStack,
        cardSplash,
        cardsMerchants,
        connectedSitesIllos,
        cryptoBalanceBackground,
        dappIllos,
        deleteAccountWarning,
        discoverIcon,
        driveLogo,
        effectBgBlue,
        frozenCardContent,
        getAUsBankAccountIlosWhite,
        getAUsBankAccountIlos,
        getAUsdAccount,
        giftBox,
        googleLogo,
        home,
        hubIntroIlos,
        hub,
        icloudLogo,
        infoBlue,
        info,
        introFour,
        introOne,
        introThree,
        introThree2x,
        introTwo,
        introTradeWalletOne,
        introTradeWalletTwo,
        lifi,
        lightbulb,
        logo,
        manageAds,
        onboardPayIlos,
        pawel,
        phoneStarIllos,
        pinkLocker,
        pinkLock,
        promo,
        purpleGradient,
        qrCodeIllos,
        redCube,
        referAndEarnIlos,
        referralIcon,
        saveInDollarsIlosOne,
        saveInDollarsIlosThree,
        saveInDollarsIlosTwo,
        security,
        selfieSampleGroup,
        setting,
        spendCardIlos,
        starRate1,
        starRate2,
        starRate3,
        starRate4,
        starRate5,
        swing,
        tbcBlks,
        topTokensIcon,
        topTokensIlos,
        tradingBalanceBackground,
        walletConnectSymbol,
        walletToggle,
        wallet,
        wc,
        worldSendIcon
      ];
}

class $AssetsSvgGen {
  const $AssetsSvgGen();

  /// File path: assets/svg/Icon.svg
  SvgGenImage get icon => const SvgGenImage('assets/svg/Icon.svg');

  /// File path: assets/svg/Inbox w red indicator.svg
  SvgGenImage get inboxWRedIndicator =>
      const SvgGenImage('assets/svg/Inbox w red indicator.svg');

  /// File path: assets/svg/Paper.svg
  SvgGenImage get paper => const SvgGenImage('assets/svg/Paper.svg');

  /// File path: assets/svg/Star.svg
  SvgGenImage get star => const SvgGenImage('assets/svg/Star.svg');

  /// File path: assets/svg/Swap.svg
  SvgGenImage get swap => const SvgGenImage('assets/svg/Swap.svg');

  /// File path: assets/svg/Wallet.svg
  SvgGenImage get wallet => const SvgGenImage('assets/svg/Wallet.svg');

  /// File path: assets/svg/activate-profile-ilos.svg
  SvgGenImage get activateProfileIlos =>
      const SvgGenImage('assets/svg/activate-profile-ilos.svg');

  /// File path: assets/svg/ad.svg
  SvgGenImage get ad => const SvgGenImage('assets/svg/ad.svg');

  /// File path: assets/svg/add-thick.svg
  SvgGenImage get addThick => const SvgGenImage('assets/svg/add-thick.svg');

  /// File path: assets/svg/additionIdVerificiation.svg
  SvgGenImage get additionIdVerificiation =>
      const SvgGenImage('assets/svg/additionIdVerificiation.svg');

  /// File path: assets/svg/alert-circle.svg
  SvgGenImage get alertCircle =>
      const SvgGenImage('assets/svg/alert-circle.svg');

  /// File path: assets/svg/alert-triangle-ilos.svg
  SvgGenImage get alertTriangleIlos =>
      const SvgGenImage('assets/svg/alert-triangle-ilos.svg');

  /// File path: assets/svg/alert-triangle-orange.svg
  SvgGenImage get alertTriangleOrange =>
      const SvgGenImage('assets/svg/alert-triangle-orange.svg');

  /// File path: assets/svg/alert-triangle-purple.svg
  SvgGenImage get alertTrianglePurple =>
      const SvgGenImage('assets/svg/alert-triangle-purple.svg');

  /// File path: assets/svg/alert-triangle.svg
  SvgGenImage get alertTriangle =>
      const SvgGenImage('assets/svg/alert-triangle.svg');

  /// File path: assets/svg/allNetwork.svg
  SvgGenImage get allNetwork => const SvgGenImage('assets/svg/allNetwork.svg');

  /// File path: assets/svg/app-update-ilos.svg
  SvgGenImage get appUpdateIlos =>
      const SvgGenImage('assets/svg/app-update-ilos.svg');

  /// File path: assets/svg/apple pay.svg
  SvgGenImage get applePay => const SvgGenImage('assets/svg/apple pay.svg');

  /// File path: assets/svg/apple.svg
  SvgGenImage get apple => const SvgGenImage('assets/svg/apple.svg');

  /// File path: assets/svg/approved-bar.svg
  SvgGenImage get approvedBar =>
      const SvgGenImage('assets/svg/approved-bar.svg');

  /// File path: assets/svg/arrow-down-2.svg
  SvgGenImage get arrowDown2 =>
      const SvgGenImage('assets/svg/arrow-down-2.svg');

  /// File path: assets/svg/arrow-down-3.svg
  SvgGenImage get arrowDown3 =>
      const SvgGenImage('assets/svg/arrow-down-3.svg');

  /// File path: assets/svg/arrow-down-bold.svg
  SvgGenImage get arrowDownBold =>
      const SvgGenImage('assets/svg/arrow-down-bold.svg');

  /// File path: assets/svg/arrow-down-thick.svg
  SvgGenImage get arrowDownThick =>
      const SvgGenImage('assets/svg/arrow-down-thick.svg');

  /// File path: assets/svg/arrow-down.svg
  SvgGenImage get arrowDown => const SvgGenImage('assets/svg/arrow-down.svg');

  /// File path: assets/svg/arrow-right-2.svg
  SvgGenImage get arrowRight2 =>
      const SvgGenImage('assets/svg/arrow-right-2.svg');

  /// File path: assets/svg/arrow-right.svg
  SvgGenImage get arrowRight => const SvgGenImage('assets/svg/arrow-right.svg');

  /// File path: assets/svg/arrow-up-2.svg
  SvgGenImage get arrowUp2 => const SvgGenImage('assets/svg/arrow-up-2.svg');

  /// File path: assets/svg/arrow-up-right.svg
  SvgGenImage get arrowUpRight =>
      const SvgGenImage('assets/svg/arrow-up-right.svg');

  /// File path: assets/svg/arrow-up.svg
  SvgGenImage get arrowUp => const SvgGenImage('assets/svg/arrow-up.svg');

  /// File path: assets/svg/asset icon.svg
  SvgGenImage get assetIcon => const SvgGenImage('assets/svg/asset icon.svg');

  /// File path: assets/svg/asterik.svg
  SvgGenImage get asterik => const SvgGenImage('assets/svg/asterik.svg');

  /// File path: assets/svg/at-icon.svg
  SvgGenImage get atIcon => const SvgGenImage('assets/svg/at-icon.svg');

  /// File path: assets/svg/auth-illos.svg
  SvgGenImage get authIllos => const SvgGenImage('assets/svg/auth-illos.svg');

  /// File path: assets/svg/back.svg
  SvgGenImage get back => const SvgGenImage('assets/svg/back.svg');

  /// File path: assets/svg/bad-kyc-icon.svg
  SvgGenImage get badKycIcon =>
      const SvgGenImage('assets/svg/bad-kyc-icon.svg');

  /// File path: assets/svg/bank-icon.svg
  SvgGenImage get bankIcon => const SvgGenImage('assets/svg/bank-icon.svg');

  /// File path: assets/svg/bank-note.svg
  SvgGenImage get bankNote => const SvgGenImage('assets/svg/bank-note.svg');

  /// File path: assets/svg/bank-outlined.svg
  SvgGenImage get bankOutlined =>
      const SvgGenImage('assets/svg/bank-outlined.svg');

  /// File path: assets/svg/bank-purple-outlined-icon.svg
  SvgGenImage get bankPurpleOutlinedIcon =>
      const SvgGenImage('assets/svg/bank-purple-outlined-icon.svg');

  /// File path: assets/svg/bank-white-outlined-icon.svg
  SvgGenImage get bankWhiteOutlinedIcon =>
      const SvgGenImage('assets/svg/bank-white-outlined-icon.svg');

  /// File path: assets/svg/bank-yellow-icon.svg
  SvgGenImage get bankYellowIcon =>
      const SvgGenImage('assets/svg/bank-yellow-icon.svg');

  /// File path: assets/svg/bank.svg
  SvgGenImage get bank => const SvgGenImage('assets/svg/bank.svg');

  /// Directory path: assets/svg/base-light
  $AssetsSvgBaseLightGen get baseLight => const $AssetsSvgBaseLightGen();

  /// File path: assets/svg/black-start.svg
  SvgGenImage get blackStart => const SvgGenImage('assets/svg/black-start.svg');

  /// File path: assets/svg/blue-info-circle.svg
  SvgGenImage get blueInfoCircle =>
      const SvgGenImage('assets/svg/blue-info-circle.svg');

  /// File path: assets/svg/bold-checkmark.svg
  SvgGenImage get boldCheckmark =>
      const SvgGenImage('assets/svg/bold-checkmark.svg');

  /// File path: assets/svg/bold-dark-time-circle.svg
  SvgGenImage get boldDarkTimeCircle =>
      const SvgGenImage('assets/svg/bold-dark-time-circle.svg');

  /// File path: assets/svg/bold-info-circle.svg
  SvgGenImage get boldInfoCircle =>
      const SvgGenImage('assets/svg/bold-info-circle.svg');

  /// File path: assets/svg/bookmark-icon.svg
  SvgGenImage get bookmarkIcon =>
      const SvgGenImage('assets/svg/bookmark-icon.svg');

  /// File path: assets/svg/bug-gas-ilos.svg
  SvgGenImage get bugGasIlos =>
      const SvgGenImage('assets/svg/bug-gas-ilos.svg');

  /// Directory path: assets/svg/bulk-light
  $AssetsSvgBulkLightGen get bulkLight => const $AssetsSvgBulkLightGen();

  /// File path: assets/svg/buy-icon.svg
  SvgGenImage get buyIcon => const SvgGenImage('assets/svg/buy-icon.svg');

  /// File path: assets/svg/buy.svg
  SvgGenImage get buy => const SvgGenImage('assets/svg/buy.svg');

  /// File path: assets/svg/call-icon.svg
  SvgGenImage get callIcon => const SvgGenImage('assets/svg/call-icon.svg');

  /// File path: assets/svg/calque-scan.svg
  SvgGenImage get calqueScan => const SvgGenImage('assets/svg/calque-scan.svg');

  /// File path: assets/svg/calque-shield.svg
  SvgGenImage get calqueShield =>
      const SvgGenImage('assets/svg/calque-shield.svg');

  /// File path: assets/svg/card-auth-illos.svg
  SvgGenImage get cardAuthIllos =>
      const SvgGenImage('assets/svg/card-auth-illos.svg');

  /// File path: assets/svg/card-circle-icon.svg
  SvgGenImage get cardCircleIcon =>
      const SvgGenImage('assets/svg/card-circle-icon.svg');

  /// File path: assets/svg/card-creation-fee.svg
  SvgGenImage get cardCreationFee =>
      const SvgGenImage('assets/svg/card-creation-fee.svg');

  /// File path: assets/svg/card-ilos-spend.svg
  SvgGenImage get cardIlosSpend =>
      const SvgGenImage('assets/svg/card-ilos-spend.svg');

  /// File path: assets/svg/card-intro-ilos.svg
  SvgGenImage get cardIntroIlos =>
      const SvgGenImage('assets/svg/card-intro-ilos.svg');

  /// File path: assets/svg/card-name.svg
  SvgGenImage get cardName => const SvgGenImage('assets/svg/card-name.svg');

  /// File path: assets/svg/card-not-available-ilos.svg
  SvgGenImage get cardNotAvailableIlos =>
      const SvgGenImage('assets/svg/card-not-available-ilos.svg');

  /// File path: assets/svg/card-notification.svg
  SvgGenImage get cardNotification =>
      const SvgGenImage('assets/svg/card-notification.svg');

  /// File path: assets/svg/card-statement-icon.svg
  SvgGenImage get cardStatementIcon =>
      const SvgGenImage('assets/svg/card-statement-icon.svg');

  /// File path: assets/svg/card-type.svg
  SvgGenImage get cardType => const SvgGenImage('assets/svg/card-type.svg');

  /// File path: assets/svg/card.svg
  SvgGenImage get card => const SvgGenImage('assets/svg/card.svg');

  /// File path: assets/svg/cart-icon.svg
  SvgGenImage get cartIcon => const SvgGenImage('assets/svg/cart-icon.svg');

  /// File path: assets/svg/chat-icon.svg
  SvgGenImage get chatIcon => const SvgGenImage('assets/svg/chat-icon.svg');

  /// File path: assets/svg/chat.svg
  SvgGenImage get chat => const SvgGenImage('assets/svg/chat.svg');

  /// File path: assets/svg/check-ilos-icon.svg
  SvgGenImage get checkIlosIcon =>
      const SvgGenImage('assets/svg/check-ilos-icon.svg');

  /// File path: assets/svg/check-mark-signal-green.svg
  SvgGenImage get checkMarkSignalGreen =>
      const SvgGenImage('assets/svg/check-mark-signal-green.svg');

  /// File path: assets/svg/check-mark.svg
  SvgGenImage get checkMark => const SvgGenImage('assets/svg/check-mark.svg');

  /// File path: assets/svg/check_primary.svg
  SvgGenImage get checkPrimary =>
      const SvgGenImage('assets/svg/check_primary.svg');

  /// File path: assets/svg/checkbox-base.svg
  SvgGenImage get checkboxBase =>
      const SvgGenImage('assets/svg/checkbox-base.svg');

  /// File path: assets/svg/checked.svg
  SvgGenImage get checked => const SvgGenImage('assets/svg/checked.svg');

  /// File path: assets/svg/chevron-down.svg
  SvgGenImage get chevronDown =>
      const SvgGenImage('assets/svg/chevron-down.svg');

  /// File path: assets/svg/chevron-left.svg
  SvgGenImage get chevronLeft =>
      const SvgGenImage('assets/svg/chevron-left.svg');

  /// File path: assets/svg/chevron-right.svg
  SvgGenImage get chevronRight =>
      const SvgGenImage('assets/svg/chevron-right.svg');

  /// File path: assets/svg/chevron-right2.svg
  SvgGenImage get chevronRight2 =>
      const SvgGenImage('assets/svg/chevron-right2.svg');

  /// File path: assets/svg/chevron-up.svg
  SvgGenImage get chevronUp => const SvgGenImage('assets/svg/chevron-up.svg');

  /// File path: assets/svg/circled-close.svg
  SvgGenImage get circledClose =>
      const SvgGenImage('assets/svg/circled-close.svg');

  /// File path: assets/svg/circular-support-icon.svg
  SvgGenImage get circularSupportIcon =>
      const SvgGenImage('assets/svg/circular-support-icon.svg');

  /// File path: assets/svg/close-circle.svg
  SvgGenImage get closeCircle =>
      const SvgGenImage('assets/svg/close-circle.svg');

  /// File path: assets/svg/close-thick.svg
  SvgGenImage get closeThick => const SvgGenImage('assets/svg/close-thick.svg');

  /// File path: assets/svg/close-thin-icon.svg
  SvgGenImage get closeThinIcon =>
      const SvgGenImage('assets/svg/close-thin-icon.svg');

  /// File path: assets/svg/close.svg
  SvgGenImage get close => const SvgGenImage('assets/svg/close.svg');

  /// File path: assets/svg/cloud-key-ilos.svg
  SvgGenImage get cloudKeyIlos =>
      const SvgGenImage('assets/svg/cloud-key-ilos.svg');

  /// File path: assets/svg/cloud-key.svg
  SvgGenImage get cloudKey => const SvgGenImage('assets/svg/cloud-key.svg');

  /// File path: assets/svg/cloud-secure-green-ilos.svg
  SvgGenImage get cloudSecureGreenIlos =>
      const SvgGenImage('assets/svg/cloud-secure-green-ilos.svg');

  /// File path: assets/svg/cloud-secure-icon.svg
  SvgGenImage get cloudSecureIcon =>
      const SvgGenImage('assets/svg/cloud-secure-icon.svg');

  /// File path: assets/svg/cloud-secure-ilos.svg
  SvgGenImage get cloudSecureIlos =>
      const SvgGenImage('assets/svg/cloud-secure-ilos.svg');

  /// File path: assets/svg/cloud-white.svg
  SvgGenImage get cloudWhite => const SvgGenImage('assets/svg/cloud-white.svg');

  /// File path: assets/svg/cloud.svg
  SvgGenImage get cloud => const SvgGenImage('assets/svg/cloud.svg');

  /// File path: assets/svg/coin.svg
  SvgGenImage get coin => const SvgGenImage('assets/svg/coin.svg');

  /// File path: assets/svg/coins-02.svg
  SvgGenImage get coins02 => const SvgGenImage('assets/svg/coins-02.svg');

  /// File path: assets/svg/coins-swap-01.svg
  SvgGenImage get coinsSwap01 =>
      const SvgGenImage('assets/svg/coins-swap-01.svg');

  /// File path: assets/svg/coins-swap-02.svg
  SvgGenImage get coinsSwap02 =>
      const SvgGenImage('assets/svg/coins-swap-02.svg');

  /// File path: assets/svg/collection.svg
  SvgGenImage get collection => const SvgGenImage('assets/svg/collection.svg');

  /// File path: assets/svg/community-colored-illo.svg
  SvgGenImage get communityColoredIllo =>
      const SvgGenImage('assets/svg/community-colored-illo.svg');

  /// File path: assets/svg/community-ilos.svg
  SvgGenImage get communityIlos =>
      const SvgGenImage('assets/svg/community-ilos.svg');

  /// File path: assets/svg/community.svg
  SvgGenImage get community => const SvgGenImage('assets/svg/community.svg');

  /// File path: assets/svg/compass-logo.svg
  SvgGenImage get compassLogo =>
      const SvgGenImage('assets/svg/compass-logo.svg');

  /// File path: assets/svg/compass.svg
  SvgGenImage get compass => const SvgGenImage('assets/svg/compass.svg');

  /// File path: assets/svg/confirming-transaction-ilos.svg
  SvgGenImage get confirmingTransactionIlos =>
      const SvgGenImage('assets/svg/confirming-transaction-ilos.svg');

  /// File path: assets/svg/connected-site-icon.svg
  SvgGenImage get connectedSiteIcon =>
      const SvgGenImage('assets/svg/connected-site-icon.svg');

  /// File path: assets/svg/connected.svg
  SvgGenImage get connected => const SvgGenImage('assets/svg/connected.svg');

  /// File path: assets/svg/contactless.svg
  SvgGenImage get contactless =>
      const SvgGenImage('assets/svg/contactless.svg');

  /// File path: assets/svg/container-seedphrase.svg
  SvgGenImage get containerSeedphrase =>
      const SvgGenImage('assets/svg/container-seedphrase.svg');

  /// File path: assets/svg/copy-bold.svg
  SvgGenImage get copyBold => const SvgGenImage('assets/svg/copy-bold.svg');

  /// File path: assets/svg/copy-filled.svg
  SvgGenImage get copyFilled => const SvgGenImage('assets/svg/copy-filled.svg');

  /// File path: assets/svg/copy.svg
  SvgGenImage get copy => const SvgGenImage('assets/svg/copy.svg');

  /// Directory path: assets/svg/countries-flags
  $AssetsSvgCountriesFlagsGen get countriesFlags =>
      const $AssetsSvgCountriesFlagsGen();

  /// File path: assets/svg/credit-card-minus-black.svg
  SvgGenImage get creditCardMinusBlack =>
      const SvgGenImage('assets/svg/credit-card-minus-black.svg');

  /// File path: assets/svg/credit-card-minus.svg
  SvgGenImage get creditCardMinus =>
      const SvgGenImage('assets/svg/credit-card-minus.svg');

  /// File path: assets/svg/credit-card-search.svg
  SvgGenImage get creditCardSearch =>
      const SvgGenImage('assets/svg/credit-card-search.svg');

  /// File path: assets/svg/credit-card-x.svg
  SvgGenImage get creditCardX =>
      const SvgGenImage('assets/svg/credit-card-x.svg');

  /// File path: assets/svg/credit-card.svg
  SvgGenImage get creditCard => const SvgGenImage('assets/svg/credit-card.svg');

  /// File path: assets/svg/crypto-icon.svg
  SvgGenImage get cryptoIcon => const SvgGenImage('assets/svg/crypto-icon.svg');

  /// File path: assets/svg/cryptoGem.svg
  SvgGenImage get cryptoGem => const SvgGenImage('assets/svg/cryptoGem.svg');

  /// File path: assets/svg/currency-dollar.svg
  SvgGenImage get currencyDollar =>
      const SvgGenImage('assets/svg/currency-dollar.svg');

  /// Directory path: assets/svg/curved-light
  $AssetsSvgCurvedLightGen get curvedLight => const $AssetsSvgCurvedLightGen();

  /// File path: assets/svg/customer-icon.svg
  SvgGenImage get customerIcon =>
      const SvgGenImage('assets/svg/customer-icon.svg');

  /// File path: assets/svg/dapp icon.svg
  SvgGenImage get dappIcon => const SvgGenImage('assets/svg/dapp icon.svg');

  /// File path: assets/svg/dapp-error-icon.svg
  SvgGenImage get dappErrorIcon =>
      const SvgGenImage('assets/svg/dapp-error-icon.svg');

  /// File path: assets/svg/dashboard-arrow-up.svg
  SvgGenImage get dashboardArrowUp =>
      const SvgGenImage('assets/svg/dashboard-arrow-up.svg');

  /// File path: assets/svg/dashboard-menu-2.svg
  SvgGenImage get dashboardMenu2 =>
      const SvgGenImage('assets/svg/dashboard-menu-2.svg');

  /// File path: assets/svg/dashboard-plus.svg
  SvgGenImage get dashboardPlus =>
      const SvgGenImage('assets/svg/dashboard-plus.svg');

  /// File path: assets/svg/delete.svg
  SvgGenImage get delete => const SvgGenImage('assets/svg/delete.svg');

  /// File path: assets/svg/deposit-arrow.svg
  SvgGenImage get depositArrow =>
      const SvgGenImage('assets/svg/deposit-arrow.svg');

  /// File path: assets/svg/deposit-notification-icon.svg
  SvgGenImage get depositNotificationIcon =>
      const SvgGenImage('assets/svg/deposit-notification-icon.svg');

  /// File path: assets/svg/deposit_card_icon.svg
  SvgGenImage get depositCardIcon =>
      const SvgGenImage('assets/svg/deposit_card_icon.svg');

  /// File path: assets/svg/direct-account.svg
  SvgGenImage get directAccount =>
      const SvgGenImage('assets/svg/direct-account.svg');

  /// File path: assets/svg/disconnected.svg
  SvgGenImage get disconnected =>
      const SvgGenImage('assets/svg/disconnected.svg');

  /// File path: assets/svg/division.svg
  SvgGenImage get division => const SvgGenImage('assets/svg/division.svg');

  /// File path: assets/svg/document.svg
  SvgGenImage get document => const SvgGenImage('assets/svg/document.svg');

  /// File path: assets/svg/dollar-bag-icon.svg
  SvgGenImage get dollarBagIcon =>
      const SvgGenImage('assets/svg/dollar-bag-icon.svg');

  /// File path: assets/svg/dollar-square.svg
  SvgGenImage get dollarSquare =>
      const SvgGenImage('assets/svg/dollar-square.svg');

  /// File path: assets/svg/dot-icon.svg
  SvgGenImage get dotIcon => const SvgGenImage('assets/svg/dot-icon.svg');

  /// File path: assets/svg/dot.svg
  SvgGenImage get dot => const SvgGenImage('assets/svg/dot.svg');

  /// File path: assets/svg/double-arrow.svg
  SvgGenImage get doubleArrow =>
      const SvgGenImage('assets/svg/double-arrow.svg');

  /// File path: assets/svg/down.svg
  SvgGenImage get down => const SvgGenImage('assets/svg/down.svg');

  /// File path: assets/svg/download.svg
  SvgGenImage get download => const SvgGenImage('assets/svg/download.svg');

  /// File path: assets/svg/dropdown.svg
  SvgGenImage get dropdown => const SvgGenImage('assets/svg/dropdown.svg');

  /// File path: assets/svg/edit-icon.svg
  SvgGenImage get editIcon => const SvgGenImage('assets/svg/edit-icon.svg');

  /// File path: assets/svg/emoji-happy.svg
  SvgGenImage get emojiHappy => const SvgGenImage('assets/svg/emoji-happy.svg');

  /// File path: assets/svg/empty-check.svg
  SvgGenImage get emptyCheck => const SvgGenImage('assets/svg/empty-check.svg');

  /// File path: assets/svg/empty-nft.svg
  SvgGenImage get emptyNft => const SvgGenImage('assets/svg/empty-nft.svg');

  /// File path: assets/svg/empty-notification.svg
  SvgGenImage get emptyNotification =>
      const SvgGenImage('assets/svg/empty-notification.svg');

  /// File path: assets/svg/enable-2fa-icon.svg
  SvgGenImage get enable2faIcon =>
      const SvgGenImage('assets/svg/enable-2fa-icon.svg');

  /// File path: assets/svg/equal-icon.svg
  SvgGenImage get equalIcon => const SvgGenImage('assets/svg/equal-icon.svg');

  /// File path: assets/svg/expressYellow.svg
  SvgGenImage get expressYellow =>
      const SvgGenImage('assets/svg/expressYellow.svg');

  /// File path: assets/svg/external-link.svg
  SvgGenImage get externalLink =>
      const SvgGenImage('assets/svg/external-link.svg');

  /// File path: assets/svg/external-transfer.svg
  SvgGenImage get externalTransfer =>
      const SvgGenImage('assets/svg/external-transfer.svg');

  /// File path: assets/svg/face-id.svg
  SvgGenImage get faceId => const SvgGenImage('assets/svg/face-id.svg');

  /// File path: assets/svg/file-illos.svg
  SvgGenImage get fileIllos => const SvgGenImage('assets/svg/file-illos.svg');

  /// File path: assets/svg/file-minus.svg
  SvgGenImage get fileMinus => const SvgGenImage('assets/svg/file-minus.svg');

  /// File path: assets/svg/filter.svg
  SvgGenImage get filter => const SvgGenImage('assets/svg/filter.svg');

  /// File path: assets/svg/finger-print.svg
  SvgGenImage get fingerPrint =>
      const SvgGenImage('assets/svg/finger-print.svg');

  /// File path: assets/svg/fire.svg
  SvgGenImage get fire => const SvgGenImage('assets/svg/fire.svg');

  /// File path: assets/svg/flame-icon.svg
  SvgGenImage get flameIcon => const SvgGenImage('assets/svg/flame-icon.svg');

  /// File path: assets/svg/flame.svg
  SvgGenImage get flame => const SvgGenImage('assets/svg/flame.svg');

  /// File path: assets/svg/flash-2--flash-power-connect-charge-electricity-lightning.svg
  SvgGenImage get flash2FlashPowerConnectChargeElectricityLightning =>
      const SvgGenImage(
          'assets/svg/flash-2--flash-power-connect-charge-electricity-lightning.svg');

  /// File path: assets/svg/flash-icon.svg
  SvgGenImage get flashIcon => const SvgGenImage('assets/svg/flash-icon.svg');

  /// File path: assets/svg/free-icon.svg
  SvgGenImage get freeIcon => const SvgGenImage('assets/svg/free-icon.svg');

  /// File path: assets/svg/freeze.svg
  SvgGenImage get freeze => const SvgGenImage('assets/svg/freeze.svg');

  /// File path: assets/svg/frozen-illos.svg
  SvgGenImage get frozenIllos =>
      const SvgGenImage('assets/svg/frozen-illos.svg');

  /// File path: assets/svg/fund-card-icon.svg
  SvgGenImage get fundCardIcon =>
      const SvgGenImage('assets/svg/fund-card-icon.svg');

  /// File path: assets/svg/gallery.svg
  SvgGenImage get gallery => const SvgGenImage('assets/svg/gallery.svg');

  /// File path: assets/svg/gbp-and-eur-flag.svg
  SvgGenImage get gbpAndEurFlag =>
      const SvgGenImage('assets/svg/gbp-and-eur-flag.svg');

  /// File path: assets/svg/generic-crypto-tranx.svg
  SvgGenImage get genericCryptoTranx =>
      const SvgGenImage('assets/svg/generic-crypto-tranx.svg');

  /// File path: assets/svg/get-usd-card-ilos.svg
  SvgGenImage get getUsdCardIlos =>
      const SvgGenImage('assets/svg/get-usd-card-ilos.svg');

  /// File path: assets/svg/gift-02.svg
  SvgGenImage get gift02 => const SvgGenImage('assets/svg/gift-02.svg');

  /// File path: assets/svg/gift.svg
  SvgGenImage get gift => const SvgGenImage('assets/svg/gift.svg');

  /// File path: assets/svg/globe.svg
  SvgGenImage get globe => const SvgGenImage('assets/svg/globe.svg');

  /// File path: assets/svg/google-pay-icon.svg
  SvgGenImage get googlePayIcon =>
      const SvgGenImage('assets/svg/google-pay-icon.svg');

  /// File path: assets/svg/google_key.svg
  SvgGenImage get googleKey => const SvgGenImage('assets/svg/google_key.svg');

  /// File path: assets/svg/googleo-logo.svg
  SvgGenImage get googleoLogo =>
      const SvgGenImage('assets/svg/googleo-logo.svg');

  /// File path: assets/svg/green-checked.svg
  SvgGenImage get greenChecked =>
      const SvgGenImage('assets/svg/green-checked.svg');

  /// File path: assets/svg/grey-info-circle.svg
  SvgGenImage get greyInfoCircle =>
      const SvgGenImage('assets/svg/grey-info-circle.svg');

  /// File path: assets/svg/grid-black.svg
  SvgGenImage get gridBlack => const SvgGenImage('assets/svg/grid-black.svg');

  /// File path: assets/svg/grid.svg
  SvgGenImage get grid => const SvgGenImage('assets/svg/grid.svg');

  /// File path: assets/svg/happy-face-rounded.svg
  SvgGenImage get happyFaceRounded =>
      const SvgGenImage('assets/svg/happy-face-rounded.svg');

  /// File path: assets/svg/hash-sign.svg
  SvgGenImage get hashSign => const SvgGenImage('assets/svg/hash-sign.svg');

  /// File path: assets/svg/heads-up.svg
  SvgGenImage get headsUp => const SvgGenImage('assets/svg/heads-up.svg');

  /// File path: assets/svg/help-center.svg
  SvgGenImage get helpCenter => const SvgGenImage('assets/svg/help-center.svg');

  /// File path: assets/svg/help.svg
  SvgGenImage get help => const SvgGenImage('assets/svg/help.svg');

  /// File path: assets/svg/hide-grey.svg
  SvgGenImage get hideGrey => const SvgGenImage('assets/svg/hide-grey.svg');

  /// File path: assets/svg/hide-ilos.svg
  SvgGenImage get hideIlos => const SvgGenImage('assets/svg/hide-ilos.svg');

  /// File path: assets/svg/hide.svg
  SvgGenImage get hide => const SvgGenImage('assets/svg/hide.svg');

  /// File path: assets/svg/id-document-icon.svg
  SvgGenImage get idDocumentIcon =>
      const SvgGenImage('assets/svg/id-document-icon.svg');

  /// File path: assets/svg/id-number-icon.svg
  SvgGenImage get idNumberIcon =>
      const SvgGenImage('assets/svg/id-number-icon.svg');

  /// File path: assets/svg/info-circle-outline.svg
  SvgGenImage get infoCircleOutline =>
      const SvgGenImage('assets/svg/info-circle-outline.svg');

  /// File path: assets/svg/info-circle-rapid-orange.svg
  SvgGenImage get infoCircleRapidOrange =>
      const SvgGenImage('assets/svg/info-circle-rapid-orange.svg');

  /// File path: assets/svg/info-circle.svg
  SvgGenImage get infoCircle => const SvgGenImage('assets/svg/info-circle.svg');

  /// File path: assets/svg/info-yellow.svg
  SvgGenImage get infoYellow => const SvgGenImage('assets/svg/info-yellow.svg');

  /// File path: assets/svg/info.svg
  SvgGenImage get info => const SvgGenImage('assets/svg/info.svg');

  /// File path: assets/svg/instagram-icon.svg
  SvgGenImage get instagramIcon =>
      const SvgGenImage('assets/svg/instagram-icon.svg');

  /// File path: assets/svg/internal-transfer.svg
  SvgGenImage get internalTransfer =>
      const SvgGenImage('assets/svg/internal-transfer.svg');

  /// File path: assets/svg/key-outline-icon.svg
  SvgGenImage get keyOutlineIcon =>
      const SvgGenImage('assets/svg/key-outline-icon.svg');

  /// File path: assets/svg/key.svg
  SvgGenImage get key => const SvgGenImage('assets/svg/key.svg');

  /// File path: assets/svg/kyc-failed-icon.svg
  SvgGenImage get kycFailedIcon =>
      const SvgGenImage('assets/svg/kyc-failed-icon.svg');

  /// File path: assets/svg/kyc-failed-ilos.svg
  SvgGenImage get kycFailedIlos =>
      const SvgGenImage('assets/svg/kyc-failed-ilos.svg');

  /// File path: assets/svg/kyc-in-progress-icon.svg
  SvgGenImage get kycInProgressIcon =>
      const SvgGenImage('assets/svg/kyc-in-progress-icon.svg');

  /// File path: assets/svg/kyc-in-progress-ilos.svg
  SvgGenImage get kycInProgressIlos =>
      const SvgGenImage('assets/svg/kyc-in-progress-ilos.svg');

  /// File path: assets/svg/kyc-shield.svg
  SvgGenImage get kycShield => const SvgGenImage('assets/svg/kyc-shield.svg');

  /// File path: assets/svg/kyc-success-icon.svg
  SvgGenImage get kycSuccessIcon =>
      const SvgGenImage('assets/svg/kyc-success-icon.svg');

  /// File path: assets/svg/kyc-verified-ilos.svg
  SvgGenImage get kycVerifiedIlos =>
      const SvgGenImage('assets/svg/kyc-verified-ilos.svg');

  /// File path: assets/svg/kyc_shield_failed.svg
  SvgGenImage get kycShieldFailed =>
      const SvgGenImage('assets/svg/kyc_shield_failed.svg');

  /// File path: assets/svg/kyc_shield_progress.svg
  SvgGenImage get kycShieldProgress =>
      const SvgGenImage('assets/svg/kyc_shield_progress.svg');

  /// File path: assets/svg/kyc_shield_verified.svg
  SvgGenImage get kycShieldVerified =>
      const SvgGenImage('assets/svg/kyc_shield_verified.svg');

  /// File path: assets/svg/left icon.svg
  SvgGenImage get leftIcon => const SvgGenImage('assets/svg/left icon.svg');

  /// File path: assets/svg/light-bulb-illos.svg
  SvgGenImage get lightBulbIllos =>
      const SvgGenImage('assets/svg/light-bulb-illos.svg');

  /// File path: assets/svg/lightning-icon-green.svg
  SvgGenImage get lightningIconGreen =>
      const SvgGenImage('assets/svg/lightning-icon-green.svg');

  /// File path: assets/svg/lightning-icon.svg
  SvgGenImage get lightningIcon =>
      const SvgGenImage('assets/svg/lightning-icon.svg');

  /// File path: assets/svg/link-square.svg
  SvgGenImage get linkSquare => const SvgGenImage('assets/svg/link-square.svg');

  /// File path: assets/svg/link.svg
  SvgGenImage get link => const SvgGenImage('assets/svg/link.svg');

  /// File path: assets/svg/location.svg
  SvgGenImage get location => const SvgGenImage('assets/svg/location.svg');

  /// File path: assets/svg/lock-ilos.svg
  SvgGenImage get lockIlos => const SvgGenImage('assets/svg/lock-ilos.svg');

  /// File path: assets/svg/lock.svg
  SvgGenImage get lock => const SvgGenImage('assets/svg/lock.svg');

  /// File path: assets/svg/logout.svg
  SvgGenImage get logout => const SvgGenImage('assets/svg/logout.svg');

  /// File path: assets/svg/low-card-balance-ilos.svg
  SvgGenImage get lowCardBalanceIlos =>
      const SvgGenImage('assets/svg/low-card-balance-ilos.svg');

  /// File path: assets/svg/mail-ilos.svg
  SvgGenImage get mailIlos => const SvgGenImage('assets/svg/mail-ilos.svg');

  /// File path: assets/svg/mark-as-read.svg
  SvgGenImage get markAsRead =>
      const SvgGenImage('assets/svg/mark-as-read.svg');

  /// File path: assets/svg/marketing-banner-icon.svg
  SvgGenImage get marketingBannerIcon =>
      const SvgGenImage('assets/svg/marketing-banner-icon.svg');

  /// File path: assets/svg/master-card-icon.svg
  SvgGenImage get masterCardIcon =>
      const SvgGenImage('assets/svg/master-card-icon.svg');

  /// File path: assets/svg/math sign.svg
  SvgGenImage get mathSign => const SvgGenImage('assets/svg/math sign.svg');

  /// File path: assets/svg/menu.svg
  SvgGenImage get menu => const SvgGenImage('assets/svg/menu.svg');

  /// File path: assets/svg/menu2.svg
  SvgGenImage get menu2 => const SvgGenImage('assets/svg/menu2.svg');

  /// File path: assets/svg/message.svg
  SvgGenImage get message => const SvgGenImage('assets/svg/message.svg');

  /// File path: assets/svg/minus-thick.svg
  SvgGenImage get minusThick => const SvgGenImage('assets/svg/minus-thick.svg');

  /// File path: assets/svg/minus.svg
  SvgGenImage get minus => const SvgGenImage('assets/svg/minus.svg');

  /// File path: assets/svg/money-2.svg
  SvgGenImage get money2 => const SvgGenImage('assets/svg/money-2.svg');

  /// File path: assets/svg/more-circle.svg
  SvgGenImage get moreCircle => const SvgGenImage('assets/svg/more-circle.svg');

  /// File path: assets/svg/more-horizontal.svg
  SvgGenImage get moreHorizontal =>
      const SvgGenImage('assets/svg/more-horizontal.svg');

  /// File path: assets/svg/more-menu.svg
  SvgGenImage get moreMenu => const SvgGenImage('assets/svg/more-menu.svg');

  /// File path: assets/svg/multi images.svg
  SvgGenImage get multiImages =>
      const SvgGenImage('assets/svg/multi images.svg');

  /// File path: assets/svg/multiply-icon.svg
  SvgGenImage get multiplyIcon =>
      const SvgGenImage('assets/svg/multiply-icon.svg');

  /// File path: assets/svg/music-playlist.svg
  SvgGenImage get musicPlaylist =>
      const SvgGenImage('assets/svg/music-playlist.svg');

  /// File path: assets/svg/network-chain-icon.svg
  SvgGenImage get networkChainIcon =>
      const SvgGenImage('assets/svg/network-chain-icon.svg');

  /// File path: assets/svg/no-transaction-ilos.svg
  SvgGenImage get noTransactionIlos =>
      const SvgGenImage('assets/svg/no-transaction-ilos.svg');

  /// File path: assets/svg/not-approved.svg
  SvgGenImage get notApproved =>
      const SvgGenImage('assets/svg/not-approved.svg');

  /// File path: assets/svg/notification-bell-no-red-dot.svg
  SvgGenImage get notificationBellNoRedDot =>
      const SvgGenImage('assets/svg/notification-bell-no-red-dot.svg');

  /// File path: assets/svg/notification-bell-red-dot.svg
  SvgGenImage get notificationBellRedDot =>
      const SvgGenImage('assets/svg/notification-bell-red-dot.svg');

  /// File path: assets/svg/notification-bell.svg
  SvgGenImage get notificationBell =>
      const SvgGenImage('assets/svg/notification-bell.svg');

  /// File path: assets/svg/notification.svg
  SvgGenImage get notification =>
      const SvgGenImage('assets/svg/notification.svg');

  /// File path: assets/svg/onboard-direct-ilos-one.svg
  SvgGenImage get onboardDirectIlosOne =>
      const SvgGenImage('assets/svg/onboard-direct-ilos-one.svg');

  /// File path: assets/svg/onboard-directi-ilos-two.svg
  SvgGenImage get onboardDirectiIlosTwo =>
      const SvgGenImage('assets/svg/onboard-directi-ilos-two.svg');

  /// File path: assets/svg/onboard-logo-lock-up-carriage.svg
  SvgGenImage get onboardLogoLockUpCarriage =>
      const SvgGenImage('assets/svg/onboard-logo-lock-up-carriage.svg');

  /// File path: assets/svg/onboard-logomark-pull.svg
  SvgGenImage get onboardLogomarkPull =>
      const SvgGenImage('assets/svg/onboard-logomark-pull.svg');

  /// File path: assets/svg/onboard-meter.svg
  SvgGenImage get onboardMeter =>
      const SvgGenImage('assets/svg/onboard-meter.svg');

  /// File path: assets/svg/oneInchLogo.svg
  SvgGenImage get oneInchLogo =>
      const SvgGenImage('assets/svg/oneInchLogo.svg');

  /// File path: assets/svg/ongoing-order-icon.svg
  SvgGenImage get ongoingOrderIcon =>
      const SvgGenImage('assets/svg/ongoing-order-icon.svg');

  /// File path: assets/svg/oops-ilos.svg
  SvgGenImage get oopsIlos => const SvgGenImage('assets/svg/oops-ilos.svg');

  /// File path: assets/svg/open-chain.svg
  SvgGenImage get openChain => const SvgGenImage('assets/svg/open-chain.svg');

  /// File path: assets/svg/orange-onboard-logo.svg
  SvgGenImage get orangeOnboardLogo =>
      const SvgGenImage('assets/svg/orange-onboard-logo.svg');

  /// File path: assets/svg/order-dispute-icon.svg
  SvgGenImage get orderDisputeIcon =>
      const SvgGenImage('assets/svg/order-dispute-icon.svg');

  /// File path: assets/svg/p2p-icon.svg
  SvgGenImage get p2pIcon => const SvgGenImage('assets/svg/p2p-icon.svg');

  /// File path: assets/svg/paper-plane-top-right.svg
  SvgGenImage get paperPlaneTopRight =>
      const SvgGenImage('assets/svg/paper-plane-top-right.svg');

  /// File path: assets/svg/paper-plus.svg
  SvgGenImage get paperPlus => const SvgGenImage('assets/svg/paper-plus.svg');

  /// File path: assets/svg/party-icon.svg
  SvgGenImage get partyIcon => const SvgGenImage('assets/svg/party-icon.svg');

  /// File path: assets/svg/passkey.svg
  SvgGenImage get passkey => const SvgGenImage('assets/svg/passkey.svg');

  /// File path: assets/svg/pencil icon.svg
  SvgGenImage get pencilIcon => const SvgGenImage('assets/svg/pencil icon.svg');

  /// File path: assets/svg/pencil.svg
  SvgGenImage get pencil => const SvgGenImage('assets/svg/pencil.svg');

  /// File path: assets/svg/pending.svg
  SvgGenImage get pending => const SvgGenImage('assets/svg/pending.svg');

  /// File path: assets/svg/person.svg
  SvgGenImage get person => const SvgGenImage('assets/svg/person.svg');

  /// File path: assets/svg/phone-icon.svg
  SvgGenImage get phoneIcon => const SvgGenImage('assets/svg/phone-icon.svg');

  /// File path: assets/svg/phone_asterisk_icon.svg
  SvgGenImage get phoneAsteriskIcon =>
      const SvgGenImage('assets/svg/phone_asterisk_icon.svg');

  /// File path: assets/svg/pie-chart.svg
  SvgGenImage get pieChart => const SvgGenImage('assets/svg/pie-chart.svg');

  /// File path: assets/svg/piggy-bank-icon.svg
  SvgGenImage get piggyBankIcon =>
      const SvgGenImage('assets/svg/piggy-bank-icon.svg');

  /// File path: assets/svg/plus-dashboard.svg
  SvgGenImage get plusDashboard =>
      const SvgGenImage('assets/svg/plus-dashboard.svg');

  /// File path: assets/svg/plus-thick.svg
  SvgGenImage get plusThick => const SvgGenImage('assets/svg/plus-thick.svg');

  /// File path: assets/svg/plus-thick2.svg
  SvgGenImage get plusThick2 => const SvgGenImage('assets/svg/plus-thick2.svg');

  /// File path: assets/svg/plus.svg
  SvgGenImage get plus => const SvgGenImage('assets/svg/plus.svg');

  /// File path: assets/svg/polygon-token.svg
  SvgGenImage get polygonToken =>
      const SvgGenImage('assets/svg/polygon-token.svg');

  /// File path: assets/svg/portfolio-search.svg
  SvgGenImage get portfolioSearch =>
      const SvgGenImage('assets/svg/portfolio-search.svg');

  /// File path: assets/svg/preference.svg
  SvgGenImage get preference => const SvgGenImage('assets/svg/preference.svg');

  /// File path: assets/svg/profile.svg
  SvgGenImage get profile => const SvgGenImage('assets/svg/profile.svg');

  /// File path: assets/svg/protect-wallet-icon.svg
  SvgGenImage get protectWalletIcon =>
      const SvgGenImage('assets/svg/protect-wallet-icon.svg');

  /// File path: assets/svg/question-han-purple.svg
  SvgGenImage get questionHanPurple =>
      const SvgGenImage('assets/svg/question-han-purple.svg');

  /// File path: assets/svg/question-mark-fill-yellow.svg
  SvgGenImage get questionMarkFillYellow =>
      const SvgGenImage('assets/svg/question-mark-fill-yellow.svg');

  /// File path: assets/svg/question-mark-fill.svg
  SvgGenImage get questionMarkFill =>
      const SvgGenImage('assets/svg/question-mark-fill.svg');

  /// File path: assets/svg/question-mark-ilos.svg
  SvgGenImage get questionMarkIlos =>
      const SvgGenImage('assets/svg/question-mark-ilos.svg');

  /// File path: assets/svg/question-mark-white-border.svg
  SvgGenImage get questionMarkWhiteBorder =>
      const SvgGenImage('assets/svg/question-mark-white-border.svg');

  /// File path: assets/svg/question-mark.svg
  SvgGenImage get questionMark =>
      const SvgGenImage('assets/svg/question-mark.svg');

  /// File path: assets/svg/question_filled_grey.svg
  SvgGenImage get questionFilledGrey =>
      const SvgGenImage('assets/svg/question_filled_grey.svg');

  /// File path: assets/svg/rank-ilos.svg
  SvgGenImage get rankIlos => const SvgGenImage('assets/svg/rank-ilos.svg');

  /// File path: assets/svg/receive-square.svg
  SvgGenImage get receiveSquare =>
      const SvgGenImage('assets/svg/receive-square.svg');

  /// File path: assets/svg/receive.svg
  SvgGenImage get receive => const SvgGenImage('assets/svg/receive.svg');

  /// File path: assets/svg/red-alert-triangle.svg
  SvgGenImage get redAlertTriangle =>
      const SvgGenImage('assets/svg/red-alert-triangle.svg');

  /// File path: assets/svg/red-dot.svg
  SvgGenImage get redDot => const SvgGenImage('assets/svg/red-dot.svg');

  /// File path: assets/svg/red-info-circle.svg
  SvgGenImage get redInfoCircle =>
      const SvgGenImage('assets/svg/red-info-circle.svg');

  /// File path: assets/svg/redirect-arrow-ilos.svg
  SvgGenImage get redirectArrowIlos =>
      const SvgGenImage('assets/svg/redirect-arrow-ilos.svg');

  /// File path: assets/svg/refer-advert-ilos.svg
  SvgGenImage get referAdvertIlos =>
      const SvgGenImage('assets/svg/refer-advert-ilos.svg');

  /// File path: assets/svg/refresh-2.svg
  SvgGenImage get refresh2 => const SvgGenImage('assets/svg/refresh-2.svg');

  /// File path: assets/svg/refresh-ccw.svg
  SvgGenImage get refreshCcw => const SvgGenImage('assets/svg/refresh-ccw.svg');

  /// File path: assets/svg/refresh-circle.svg
  SvgGenImage get refreshCircle =>
      const SvgGenImage('assets/svg/refresh-circle.svg');

  /// Directory path: assets/svg/regular-light
  $AssetsSvgRegularLightGen get regularLight =>
      const $AssetsSvgRegularLightGen();

  /// File path: assets/svg/robot_illos.svg
  SvgGenImage get robotIllos => const SvgGenImage('assets/svg/robot_illos.svg');

  /// File path: assets/svg/rotate_left.svg
  SvgGenImage get rotateLeft => const SvgGenImage('assets/svg/rotate_left.svg');

  /// File path: assets/svg/sad-emoji-ilos.svg
  SvgGenImage get sadEmojiIlos =>
      const SvgGenImage('assets/svg/sad-emoji-ilos.svg');

  /// File path: assets/svg/sad-face-ilos.svg
  SvgGenImage get sadFaceIlos =>
      const SvgGenImage('assets/svg/sad-face-ilos.svg');

  /// File path: assets/svg/scan-bold.svg
  SvgGenImage get scanBold => const SvgGenImage('assets/svg/scan-bold.svg');

  /// File path: assets/svg/scan.svg
  SvgGenImage get scan => const SvgGenImage('assets/svg/scan.svg');

  /// File path: assets/svg/search-empty-state-ilos.svg
  SvgGenImage get searchEmptyStateIlos =>
      const SvgGenImage('assets/svg/search-empty-state-ilos.svg');

  /// File path: assets/svg/search-ilos.svg
  SvgGenImage get searchIlos => const SvgGenImage('assets/svg/search-ilos.svg');

  /// File path: assets/svg/search-thick.svg
  SvgGenImage get searchThick =>
      const SvgGenImage('assets/svg/search-thick.svg');

  /// File path: assets/svg/search.svg
  SvgGenImage get search => const SvgGenImage('assets/svg/search.svg');

  /// File path: assets/svg/security-filled.svg
  SvgGenImage get securityFilled =>
      const SvgGenImage('assets/svg/security-filled.svg');

  /// File path: assets/svg/security-ilos.svg
  SvgGenImage get securityIlos =>
      const SvgGenImage('assets/svg/security-ilos.svg');

  /// File path: assets/svg/security.svg
  SvgGenImage get security => const SvgGenImage('assets/svg/security.svg');

  /// File path: assets/svg/sell-crypto-ilos.svg
  SvgGenImage get sellCryptoIlos =>
      const SvgGenImage('assets/svg/sell-crypto-ilos.svg');

  /// File path: assets/svg/sell-icon.svg
  SvgGenImage get sellIcon => const SvgGenImage('assets/svg/sell-icon.svg');

  /// File path: assets/svg/sell.svg
  SvgGenImage get sell => const SvgGenImage('assets/svg/sell.svg');

  /// File path: assets/svg/send-2.svg
  SvgGenImage get send2 => const SvgGenImage('assets/svg/send-2.svg');

  /// File path: assets/svg/send-arrow.svg
  SvgGenImage get sendArrow => const SvgGenImage('assets/svg/send-arrow.svg');

  /// File path: assets/svg/send-sqaure.svg
  SvgGenImage get sendSqaure => const SvgGenImage('assets/svg/send-sqaure.svg');

  /// File path: assets/svg/send_blue.svg
  SvgGenImage get sendBlue => const SvgGenImage('assets/svg/send_blue.svg');

  /// File path: assets/svg/send_purple.svg
  SvgGenImage get sendPurple => const SvgGenImage('assets/svg/send_purple.svg');

  /// File path: assets/svg/share.svg
  SvgGenImage get share => const SvgGenImage('assets/svg/share.svg');

  /// File path: assets/svg/shield done.svg
  SvgGenImage get shieldDone => const SvgGenImage('assets/svg/shield done.svg');

  /// File path: assets/svg/shield-done-white.svg
  SvgGenImage get shieldDoneWhite =>
      const SvgGenImage('assets/svg/shield-done-white.svg');

  /// File path: assets/svg/shield-white-border.svg
  SvgGenImage get shieldWhiteBorder =>
      const SvgGenImage('assets/svg/shield-white-border.svg');

  /// File path: assets/svg/shield_grey.svg
  SvgGenImage get shieldGrey => const SvgGenImage('assets/svg/shield_grey.svg');

  /// File path: assets/svg/shopping-cart.svg
  SvgGenImage get shoppingCart =>
      const SvgGenImage('assets/svg/shopping-cart.svg');

  /// File path: assets/svg/show-grey.svg
  SvgGenImage get showGrey => const SvgGenImage('assets/svg/show-grey.svg');

  /// File path: assets/svg/show-ilos.svg
  SvgGenImage get showIlos => const SvgGenImage('assets/svg/show-ilos.svg');

  /// File path: assets/svg/show.svg
  SvgGenImage get show => const SvgGenImage('assets/svg/show.svg');

  /// File path: assets/svg/shuffle.svg
  SvgGenImage get shuffle => const SvgGenImage('assets/svg/shuffle.svg');

  /// File path: assets/svg/signature-ilos.svg
  SvgGenImage get signatureIlos =>
      const SvgGenImage('assets/svg/signature-ilos.svg');

  /// File path: assets/svg/smiley-ilos-with-stars.svg
  SvgGenImage get smileyIlosWithStars =>
      const SvgGenImage('assets/svg/smiley-ilos-with-stars.svg');

  /// File path: assets/svg/smiley-ilos.svg
  SvgGenImage get smileyIlos => const SvgGenImage('assets/svg/smiley-ilos.svg');

  /// File path: assets/svg/smiley-with-stars-green.svg
  SvgGenImage get smileyWithStarsGreen =>
      const SvgGenImage('assets/svg/smiley-with-stars-green.svg');

  /// File path: assets/svg/smiley-with-stars.svg
  SvgGenImage get smileyWithStars =>
      const SvgGenImage('assets/svg/smiley-with-stars.svg');

  /// File path: assets/svg/sms_recover_asterik.svg
  SvgGenImage get smsRecoverAsterik =>
      const SvgGenImage('assets/svg/sms_recover_asterik.svg');

  /// File path: assets/svg/snow-icon.svg
  SvgGenImage get snowIcon => const SvgGenImage('assets/svg/snow-icon.svg');

  /// File path: assets/svg/spot-wallet.svg
  SvgGenImage get spotWallet => const SvgGenImage('assets/svg/spot-wallet.svg');

  /// File path: assets/svg/stacked-coins.svg
  SvgGenImage get stackedCoins =>
      const SvgGenImage('assets/svg/stacked-coins.svg');

  /// File path: assets/svg/star-filled.svg
  SvgGenImage get starFilled => const SvgGenImage('assets/svg/star-filled.svg');

  /// File path: assets/svg/star-unfilled.svg
  SvgGenImage get starUnfilled =>
      const SvgGenImage('assets/svg/star-unfilled.svg');

  /// File path: assets/svg/start-kyc-ilos.svg
  SvgGenImage get startKycIlos =>
      const SvgGenImage('assets/svg/start-kyc-ilos.svg');

  /// File path: assets/svg/subtract-icon.svg
  SvgGenImage get subtractIcon =>
      const SvgGenImage('assets/svg/subtract-icon.svg');

  /// File path: assets/svg/success-ilos.svg
  SvgGenImage get successIlos =>
      const SvgGenImage('assets/svg/success-ilos.svg');

  /// File path: assets/svg/sunshine.svg
  SvgGenImage get sunshine => const SvgGenImage('assets/svg/sunshine.svg');

  /// File path: assets/svg/swap-icon.svg
  SvgGenImage get swapIcon => const SvgGenImage('assets/svg/swap-icon.svg');

  /// File path: assets/svg/swap-toggle.svg
  SvgGenImage get swapToggle => const SvgGenImage('assets/svg/swap-toggle.svg');

  /// File path: assets/svg/swipe_left.svg
  SvgGenImage get swipeLeft => const SvgGenImage('assets/svg/swipe_left.svg');

  /// File path: assets/svg/switch-icon-2.svg
  SvgGenImage get switchIcon2 =>
      const SvgGenImage('assets/svg/switch-icon-2.svg');

  /// File path: assets/svg/switch-icon.svg
  SvgGenImage get switchIcon => const SvgGenImage('assets/svg/switch-icon.svg');

  /// File path: assets/svg/telegram-icon.svg
  SvgGenImage get telegramIcon =>
      const SvgGenImage('assets/svg/telegram-icon.svg');

  /// File path: assets/svg/thumbs-up.svg
  SvgGenImage get thumbsUp => const SvgGenImage('assets/svg/thumbs-up.svg');

  /// File path: assets/svg/thunder-bolt-icon.svg
  SvgGenImage get thunderBoltIcon =>
      const SvgGenImage('assets/svg/thunder-bolt-icon.svg');

  /// File path: assets/svg/tick-circle.svg
  SvgGenImage get tickCircle => const SvgGenImage('assets/svg/tick-circle.svg');

  /// File path: assets/svg/time-circle-border.svg
  SvgGenImage get timeCircleBorder =>
      const SvgGenImage('assets/svg/time-circle-border.svg');

  /// File path: assets/svg/time-circle.svg
  SvgGenImage get timeCircle => const SvgGenImage('assets/svg/time-circle.svg');

  /// File path: assets/svg/toggle-icon.svg
  SvgGenImage get toggleIcon => const SvgGenImage('assets/svg/toggle-icon.svg');

  /// File path: assets/svg/token-illos.svg
  SvgGenImage get tokenIllos => const SvgGenImage('assets/svg/token-illos.svg');

  /// File path: assets/svg/top-token-list.svg
  SvgGenImage get topTokenList =>
      const SvgGenImage('assets/svg/top-token-list.svg');

  /// File path: assets/svg/trade-arrow.svg
  SvgGenImage get tradeArrow => const SvgGenImage('assets/svg/trade-arrow.svg');

  /// File path: assets/svg/trade-crypto-icon.svg
  SvgGenImage get tradeCryptoIcon =>
      const SvgGenImage('assets/svg/trade-crypto-icon.svg');

  /// File path: assets/svg/trade-intro-ilos.svg
  SvgGenImage get tradeIntroIlos =>
      const SvgGenImage('assets/svg/trade-intro-ilos.svg');

  /// File path: assets/svg/trade_intro_illos.svg
  SvgGenImage get tradeIntroIllos =>
      const SvgGenImage('assets/svg/trade_intro_illos.svg');

  /// File path: assets/svg/trading-wallet.svg
  SvgGenImage get tradingWallet =>
      const SvgGenImage('assets/svg/trading-wallet.svg');

  /// File path: assets/svg/transfer-ilos.svg
  SvgGenImage get transferIlos =>
      const SvgGenImage('assets/svg/transfer-ilos.svg');

  /// File path: assets/svg/transfer-up.svg
  SvgGenImage get transferUp => const SvgGenImage('assets/svg/transfer-up.svg');

  /// File path: assets/svg/trash-2.svg
  SvgGenImage get trash2 => const SvgGenImage('assets/svg/trash-2.svg');

  /// File path: assets/svg/trending-up.svg
  SvgGenImage get trendingUp => const SvgGenImage('assets/svg/trending-up.svg');

  /// File path: assets/svg/twitter-icon.svg
  SvgGenImage get twitterIcon =>
      const SvgGenImage('assets/svg/twitter-icon.svg');

  /// File path: assets/svg/uk-and-eur-flag.svg
  SvgGenImage get ukAndEurFlag =>
      const SvgGenImage('assets/svg/uk-and-eur-flag.svg');

  /// File path: assets/svg/unchecked.svg
  SvgGenImage get unchecked => const SvgGenImage('assets/svg/unchecked.svg');

  /// File path: assets/svg/us-circular-flag.svg
  SvgGenImage get usCircularFlag =>
      const SvgGenImage('assets/svg/us-circular-flag.svg');

  /// File path: assets/svg/user-info-icon.svg
  SvgGenImage get userInfoIcon =>
      const SvgGenImage('assets/svg/user-info-icon.svg');

  /// File path: assets/svg/user-profile-icon.svg
  SvgGenImage get userProfileIcon =>
      const SvgGenImage('assets/svg/user-profile-icon.svg');

  /// File path: assets/svg/validation-check.svg
  SvgGenImage get validationCheck =>
      const SvgGenImage('assets/svg/validation-check.svg');

  /// File path: assets/svg/verificationInReviewIllos.svg
  SvgGenImage get verificationInReviewIllos =>
      const SvgGenImage('assets/svg/verificationInReviewIllos.svg');

  /// File path: assets/svg/verify.svg
  SvgGenImage get verify => const SvgGenImage('assets/svg/verify.svg');

  /// File path: assets/svg/virtual-card-icon-round.svg
  SvgGenImage get virtualCardIconRound =>
      const SvgGenImage('assets/svg/virtual-card-icon-round.svg');

  /// File path: assets/svg/virtual-card-icon-rounded-2.svg
  SvgGenImage get virtualCardIconRounded2 =>
      const SvgGenImage('assets/svg/virtual-card-icon-rounded-2.svg');

  /// File path: assets/svg/virtual-card-icon-rounded.svg
  SvgGenImage get virtualCardIconRounded =>
      const SvgGenImage('assets/svg/virtual-card-icon-rounded.svg');

  /// File path: assets/svg/virtual-card-icon.svg
  SvgGenImage get virtualCardIcon =>
      const SvgGenImage('assets/svg/virtual-card-icon.svg');

  /// File path: assets/svg/visa-icon.svg
  SvgGenImage get visaIcon => const SvgGenImage('assets/svg/visa-icon.svg');

  /// File path: assets/svg/wallet-03.svg
  SvgGenImage get wallet03 => const SvgGenImage('assets/svg/wallet-03.svg');

  /// File path: assets/svg/wallet-2.svg
  SvgGenImage get wallet2 => const SvgGenImage('assets/svg/wallet-2.svg');

  /// File path: assets/svg/wallet-connect-illos.svg
  SvgGenImage get walletConnectIllos =>
      const SvgGenImage('assets/svg/wallet-connect-illos.svg');

  /// File path: assets/svg/wallet-heritage-blue.svg
  SvgGenImage get walletHeritageBlue =>
      const SvgGenImage('assets/svg/wallet-heritage-blue.svg');

  /// File path: assets/svg/wallet-info.svg
  SvgGenImage get walletInfo => const SvgGenImage('assets/svg/wallet-info.svg');

  /// File path: assets/svg/wallet-open.svg
  SvgGenImage get walletOpen => const SvgGenImage('assets/svg/wallet-open.svg');

  /// File path: assets/svg/wallet-secure-ilos.svg
  SvgGenImage get walletSecureIlos =>
      const SvgGenImage('assets/svg/wallet-secure-ilos.svg');

  /// File path: assets/svg/warning-info-circle.svg
  SvgGenImage get warningInfoCircle =>
      const SvgGenImage('assets/svg/warning-info-circle.svg');

  /// File path: assets/svg/whatsapp-svgrepo-com.svg
  SvgGenImage get whatsappSvgrepoCom =>
      const SvgGenImage('assets/svg/whatsapp-svgrepo-com.svg');

  /// File path: assets/svg/white-checked.svg
  SvgGenImage get whiteChecked =>
      const SvgGenImage('assets/svg/white-checked.svg');

  /// File path: assets/svg/wind-icon.svg
  SvgGenImage get windIcon => const SvgGenImage('assets/svg/wind-icon.svg');

  /// File path: assets/svg/withdraw-ilos-icon.svg
  SvgGenImage get withdrawIlosIcon =>
      const SvgGenImage('assets/svg/withdraw-ilos-icon.svg');

  /// File path: assets/svg/withdrawa-notification-icon.svg
  SvgGenImage get withdrawaNotificationIcon =>
      const SvgGenImage('assets/svg/withdrawa-notification-icon.svg');

  /// File path: assets/svg/world-icon-rounded.svg
  SvgGenImage get worldIconRounded =>
      const SvgGenImage('assets/svg/world-icon-rounded.svg');

  /// File path: assets/svg/world-icon.svg
  SvgGenImage get worldIcon => const SvgGenImage('assets/svg/world-icon.svg');

  /// File path: assets/svg/world-send-icon.svg
  SvgGenImage get worldSendIcon =>
      const SvgGenImage('assets/svg/world-send-icon.svg');

  /// File path: assets/svg/yellow-light-bulb-ilos.svg
  SvgGenImage get yellowLightBulbIlos =>
      const SvgGenImage('assets/svg/yellow-light-bulb-ilos.svg');

  /// File path: assets/svg/yellow_lightening.svg
  SvgGenImage get yellowLightening =>
      const SvgGenImage('assets/svg/yellow_lightening.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
        icon,
        inboxWRedIndicator,
        paper,
        star,
        swap,
        wallet,
        activateProfileIlos,
        ad,
        addThick,
        additionIdVerificiation,
        alertCircle,
        alertTriangleIlos,
        alertTriangleOrange,
        alertTrianglePurple,
        alertTriangle,
        allNetwork,
        appUpdateIlos,
        applePay,
        apple,
        approvedBar,
        arrowDown2,
        arrowDown3,
        arrowDownBold,
        arrowDownThick,
        arrowDown,
        arrowRight2,
        arrowRight,
        arrowUp2,
        arrowUpRight,
        arrowUp,
        assetIcon,
        asterik,
        atIcon,
        authIllos,
        back,
        badKycIcon,
        bankIcon,
        bankNote,
        bankOutlined,
        bankPurpleOutlinedIcon,
        bankWhiteOutlinedIcon,
        bankYellowIcon,
        bank,
        blackStart,
        blueInfoCircle,
        boldCheckmark,
        boldDarkTimeCircle,
        boldInfoCircle,
        bookmarkIcon,
        bugGasIlos,
        buyIcon,
        buy,
        callIcon,
        calqueScan,
        calqueShield,
        cardAuthIllos,
        cardCircleIcon,
        cardCreationFee,
        cardIlosSpend,
        cardIntroIlos,
        cardName,
        cardNotAvailableIlos,
        cardNotification,
        cardStatementIcon,
        cardType,
        card,
        cartIcon,
        chatIcon,
        chat,
        checkIlosIcon,
        checkMarkSignalGreen,
        checkMark,
        checkPrimary,
        checkboxBase,
        checked,
        chevronDown,
        chevronLeft,
        chevronRight,
        chevronRight2,
        chevronUp,
        circledClose,
        circularSupportIcon,
        closeCircle,
        closeThick,
        closeThinIcon,
        close,
        cloudKeyIlos,
        cloudKey,
        cloudSecureGreenIlos,
        cloudSecureIcon,
        cloudSecureIlos,
        cloudWhite,
        cloud,
        coin,
        coins02,
        coinsSwap01,
        coinsSwap02,
        collection,
        communityColoredIllo,
        communityIlos,
        community,
        compassLogo,
        compass,
        confirmingTransactionIlos,
        connectedSiteIcon,
        connected,
        contactless,
        containerSeedphrase,
        copyBold,
        copyFilled,
        copy,
        creditCardMinusBlack,
        creditCardMinus,
        creditCardSearch,
        creditCardX,
        creditCard,
        cryptoIcon,
        cryptoGem,
        currencyDollar,
        customerIcon,
        dappIcon,
        dappErrorIcon,
        dashboardArrowUp,
        dashboardMenu2,
        dashboardPlus,
        delete,
        depositArrow,
        depositNotificationIcon,
        depositCardIcon,
        directAccount,
        disconnected,
        division,
        document,
        dollarBagIcon,
        dollarSquare,
        dotIcon,
        dot,
        doubleArrow,
        down,
        download,
        dropdown,
        editIcon,
        emojiHappy,
        emptyCheck,
        emptyNft,
        emptyNotification,
        enable2faIcon,
        equalIcon,
        expressYellow,
        externalLink,
        externalTransfer,
        faceId,
        fileIllos,
        fileMinus,
        filter,
        fingerPrint,
        fire,
        flameIcon,
        flame,
        flash2FlashPowerConnectChargeElectricityLightning,
        flashIcon,
        freeIcon,
        freeze,
        frozenIllos,
        fundCardIcon,
        gallery,
        gbpAndEurFlag,
        genericCryptoTranx,
        getUsdCardIlos,
        gift02,
        gift,
        globe,
        googlePayIcon,
        googleKey,
        googleoLogo,
        greenChecked,
        greyInfoCircle,
        gridBlack,
        grid,
        happyFaceRounded,
        hashSign,
        headsUp,
        helpCenter,
        help,
        hideGrey,
        hideIlos,
        hide,
        idDocumentIcon,
        idNumberIcon,
        infoCircleOutline,
        infoCircleRapidOrange,
        infoCircle,
        infoYellow,
        info,
        instagramIcon,
        internalTransfer,
        keyOutlineIcon,
        key,
        kycFailedIcon,
        kycFailedIlos,
        kycInProgressIcon,
        kycInProgressIlos,
        kycShield,
        kycSuccessIcon,
        kycVerifiedIlos,
        kycShieldFailed,
        kycShieldProgress,
        kycShieldVerified,
        leftIcon,
        lightBulbIllos,
        lightningIconGreen,
        lightningIcon,
        linkSquare,
        link,
        location,
        lockIlos,
        lock,
        logout,
        lowCardBalanceIlos,
        mailIlos,
        markAsRead,
        marketingBannerIcon,
        masterCardIcon,
        mathSign,
        menu,
        menu2,
        message,
        minusThick,
        minus,
        money2,
        moreCircle,
        moreHorizontal,
        moreMenu,
        multiImages,
        multiplyIcon,
        musicPlaylist,
        networkChainIcon,
        noTransactionIlos,
        notApproved,
        notificationBellNoRedDot,
        notificationBellRedDot,
        notificationBell,
        notification,
        onboardDirectIlosOne,
        onboardDirectiIlosTwo,
        onboardLogoLockUpCarriage,
        onboardLogomarkPull,
        onboardMeter,
        oneInchLogo,
        ongoingOrderIcon,
        oopsIlos,
        openChain,
        orangeOnboardLogo,
        orderDisputeIcon,
        p2pIcon,
        paperPlaneTopRight,
        paperPlus,
        partyIcon,
        passkey,
        pencilIcon,
        pencil,
        pending,
        person,
        phoneIcon,
        phoneAsteriskIcon,
        pieChart,
        piggyBankIcon,
        plusDashboard,
        plusThick,
        plusThick2,
        plus,
        polygonToken,
        portfolioSearch,
        preference,
        profile,
        protectWalletIcon,
        questionHanPurple,
        questionMarkFillYellow,
        questionMarkFill,
        questionMarkIlos,
        questionMarkWhiteBorder,
        questionMark,
        questionFilledGrey,
        rankIlos,
        receiveSquare,
        receive,
        redAlertTriangle,
        redDot,
        redInfoCircle,
        redirectArrowIlos,
        referAdvertIlos,
        refresh2,
        refreshCcw,
        refreshCircle,
        robotIllos,
        rotateLeft,
        sadEmojiIlos,
        sadFaceIlos,
        scanBold,
        scan,
        searchEmptyStateIlos,
        searchIlos,
        searchThick,
        search,
        securityFilled,
        securityIlos,
        security,
        sellCryptoIlos,
        sellIcon,
        sell,
        send2,
        sendArrow,
        sendSqaure,
        sendBlue,
        sendPurple,
        share,
        shieldDone,
        shieldDoneWhite,
        shieldWhiteBorder,
        shieldGrey,
        shoppingCart,
        showGrey,
        showIlos,
        show,
        shuffle,
        signatureIlos,
        smileyIlosWithStars,
        smileyIlos,
        smileyWithStarsGreen,
        smileyWithStars,
        smsRecoverAsterik,
        snowIcon,
        spotWallet,
        stackedCoins,
        starFilled,
        starUnfilled,
        startKycIlos,
        subtractIcon,
        successIlos,
        sunshine,
        swapIcon,
        swapToggle,
        swipeLeft,
        switchIcon2,
        switchIcon,
        telegramIcon,
        thumbsUp,
        thunderBoltIcon,
        tickCircle,
        timeCircleBorder,
        timeCircle,
        toggleIcon,
        tokenIllos,
        topTokenList,
        tradeArrow,
        tradeCryptoIcon,
        tradeIntroIlos,
        tradeIntroIllos,
        tradingWallet,
        transferIlos,
        transferUp,
        trash2,
        trendingUp,
        twitterIcon,
        ukAndEurFlag,
        unchecked,
        usCircularFlag,
        userInfoIcon,
        userProfileIcon,
        validationCheck,
        verificationInReviewIllos,
        verify,
        virtualCardIconRound,
        virtualCardIconRounded2,
        virtualCardIconRounded,
        virtualCardIcon,
        visaIcon,
        wallet03,
        wallet2,
        walletConnectIllos,
        walletHeritageBlue,
        walletInfo,
        walletOpen,
        walletSecureIlos,
        warningInfoCircle,
        whatsappSvgrepoCom,
        whiteChecked,
        windIcon,
        withdrawIlosIcon,
        withdrawaNotificationIcon,
        worldIconRounded,
        worldIcon,
        worldSendIcon,
        yellowLightBulbIlos,
        yellowLightening
      ];
}

class $AssetsHavenJsonGen {
  const $AssetsHavenJsonGen();

  /// File path: assets/haven/json/loading_deposit.json
  String get loadingDeposit => 'assets/haven/json/loading_deposit.json';

  /// List of all assets
  List<String> get values => [loadingDeposit];
}

class $AssetsHavenPngGen {
  const $AssetsHavenPngGen();

  /// File path: assets/haven/png/empty_search.png
  AssetGenImage get emptySearch =>
      const AssetGenImage('assets/haven/png/empty_search.png');

  /// File path: assets/haven/png/logo-large.png
  AssetGenImage get logoLarge =>
      const AssetGenImage('assets/haven/png/logo-large.png');

  /// File path: assets/haven/png/logo.png
  AssetGenImage get logo => const AssetGenImage('assets/haven/png/logo.png');

  /// File path: assets/haven/png/qr_blur.png
  AssetGenImage get qrBlur =>
      const AssetGenImage('assets/haven/png/qr_blur.png');

  /// File path: assets/haven/png/usdt_ngn.png
  AssetGenImage get usdtNgn =>
      const AssetGenImage('assets/haven/png/usdt_ngn.png');

  /// List of all assets
  List<AssetGenImage> get values =>
      [emptySearch, logoLarge, logo, qrBlur, usdtNgn];
}

class $AssetsHavenSvgGen {
  const $AssetsHavenSvgGen();

  /// File path: assets/haven/svg/accounts.svg
  SvgGenImage get accounts =>
      const SvgGenImage('assets/haven/svg/accounts.svg');

  /// File path: assets/haven/svg/alert-circle.svg
  SvgGenImage get alertCircle =>
      const SvgGenImage('assets/haven/svg/alert-circle.svg');

  /// File path: assets/haven/svg/alert-triangle-ilos.svg
  SvgGenImage get alertTriangleIlos =>
      const SvgGenImage('assets/haven/svg/alert-triangle-ilos.svg');

  /// File path: assets/haven/svg/alert-triangle-orange.svg
  SvgGenImage get alertTriangleOrange =>
      const SvgGenImage('assets/haven/svg/alert-triangle-orange.svg');

  /// File path: assets/haven/svg/alert-triangle.svg
  SvgGenImage get alertTriangle =>
      const SvgGenImage('assets/haven/svg/alert-triangle.svg');

  /// File path: assets/haven/svg/all_transactions.svg
  SvgGenImage get allTransactions =>
      const SvgGenImage('assets/haven/svg/all_transactions.svg');

  /// File path: assets/haven/svg/android.svg
  SvgGenImage get android => const SvgGenImage('assets/haven/svg/android.svg');

  /// File path: assets/haven/svg/bank_with_bg.svg
  SvgGenImage get bankWithBg =>
      const SvgGenImage('assets/haven/svg/bank_with_bg.svg');

  /// File path: assets/haven/svg/bolt.svg
  SvgGenImage get bolt => const SvgGenImage('assets/haven/svg/bolt.svg');

  /// File path: assets/haven/svg/bsc-logo.svg
  SvgGenImage get bscLogo => const SvgGenImage('assets/haven/svg/bsc-logo.svg');

  /// File path: assets/haven/svg/caution.svg
  SvgGenImage get caution => const SvgGenImage('assets/haven/svg/caution.svg');

  /// File path: assets/haven/svg/checked_circle_type.svg
  SvgGenImage get checkedCircleType =>
      const SvgGenImage('assets/haven/svg/checked_circle_type.svg');

  /// File path: assets/haven/svg/cloud_lock.svg
  SvgGenImage get cloudLock =>
      const SvgGenImage('assets/haven/svg/cloud_lock.svg');

  /// File path: assets/haven/svg/coingecko_icon.svg
  SvgGenImage get coingeckoIcon =>
      const SvgGenImage('assets/haven/svg/coingecko_icon.svg');

  /// File path: assets/haven/svg/coins_swap.svg
  SvgGenImage get coinsSwap =>
      const SvgGenImage('assets/haven/svg/coins_swap.svg');

  /// File path: assets/haven/svg/confirming_deposit_illos.svg
  SvgGenImage get confirmingDepositIllos =>
      const SvgGenImage('assets/haven/svg/confirming_deposit_illos.svg');

  /// File path: assets/haven/svg/contactless.svg
  SvgGenImage get contactless =>
      const SvgGenImage('assets/haven/svg/contactless.svg');

  /// File path: assets/haven/svg/container-seedphrase.svg
  SvgGenImage get containerSeedphrase =>
      const SvgGenImage('assets/haven/svg/container-seedphrase.svg');

  /// File path: assets/haven/svg/copy_filled_block.svg
  SvgGenImage get copyFilledBlock =>
      const SvgGenImage('assets/haven/svg/copy_filled_block.svg');

  /// File path: assets/haven/svg/customer-icon.svg
  SvgGenImage get customerIcon =>
      const SvgGenImage('assets/haven/svg/customer-icon.svg');

  /// File path: assets/haven/svg/delete_mdi.svg
  SvgGenImage get deleteMdi =>
      const SvgGenImage('assets/haven/svg/delete_mdi.svg');

  /// File path: assets/haven/svg/deposit_confirmed_illos.svg
  SvgGenImage get depositConfirmedIllos =>
      const SvgGenImage('assets/haven/svg/deposit_confirmed_illos.svg');

  /// File path: assets/haven/svg/deposit_type.svg
  SvgGenImage get depositType =>
      const SvgGenImage('assets/haven/svg/deposit_type.svg');

  /// File path: assets/haven/svg/digital_assets.svg
  SvgGenImage get digitalAssets =>
      const SvgGenImage('assets/haven/svg/digital_assets.svg');

  /// File path: assets/haven/svg/discovery.svg
  SvgGenImage get discovery =>
      const SvgGenImage('assets/haven/svg/discovery.svg');

  /// File path: assets/haven/svg/dot_grey.svg
  SvgGenImage get dotGrey => const SvgGenImage('assets/haven/svg/dot_grey.svg');

  /// File path: assets/haven/svg/email_outlined.svg
  SvgGenImage get emailOutlined =>
      const SvgGenImage('assets/haven/svg/email_outlined.svg');

  /// File path: assets/haven/svg/facebook.svg
  SvgGenImage get facebook =>
      const SvgGenImage('assets/haven/svg/facebook.svg');

  /// File path: assets/haven/svg/generic_coin.svg
  SvgGenImage get genericCoin =>
      const SvgGenImage('assets/haven/svg/generic_coin.svg');

  /// File path: assets/haven/svg/github.svg
  SvgGenImage get github => const SvgGenImage('assets/haven/svg/github.svg');

  /// File path: assets/haven/svg/haven_cup.svg
  SvgGenImage get havenCup =>
      const SvgGenImage('assets/haven/svg/haven_cup.svg');

  /// File path: assets/haven/svg/haven_info_illos.svg
  SvgGenImage get havenInfoIllos =>
      const SvgGenImage('assets/haven/svg/haven_info_illos.svg');

  /// File path: assets/haven/svg/haven_text_logo.svg
  SvgGenImage get havenTextLogo =>
      const SvgGenImage('assets/haven/svg/haven_text_logo.svg');

  /// File path: assets/haven/svg/hide_low_balance.svg
  SvgGenImage get hideLowBalance =>
      const SvgGenImage('assets/haven/svg/hide_low_balance.svg');

  /// File path: assets/haven/svg/info_red.svg
  SvgGenImage get infoRed => const SvgGenImage('assets/haven/svg/info_red.svg');

  /// File path: assets/haven/svg/key_outline.svg
  SvgGenImage get keyOutline =>
      const SvgGenImage('assets/haven/svg/key_outline.svg');

  /// File path: assets/haven/svg/lock_screen.svg
  SvgGenImage get lockScreen =>
      const SvgGenImage('assets/haven/svg/lock_screen.svg');

  /// File path: assets/haven/svg/more.svg
  SvgGenImage get more => const SvgGenImage('assets/haven/svg/more.svg');

  /// File path: assets/haven/svg/no-transaction-ilos.svg
  SvgGenImage get noTransactionIlos =>
      const SvgGenImage('assets/haven/svg/no-transaction-ilos.svg');

  /// File path: assets/haven/svg/notification.svg
  SvgGenImage get notification =>
      const SvgGenImage('assets/haven/svg/notification.svg');

  /// File path: assets/haven/svg/onboard_illios.svg
  SvgGenImage get onboardIllios =>
      const SvgGenImage('assets/haven/svg/onboard_illios.svg');

  /// File path: assets/haven/svg/onboarding_term_icon.svg
  SvgGenImage get onboardingTermIcon =>
      const SvgGenImage('assets/haven/svg/onboarding_term_icon.svg');

  /// File path: assets/haven/svg/ongoing_order_icon.svg
  SvgGenImage get ongoingOrderIcon =>
      const SvgGenImage('assets/haven/svg/ongoing_order_icon.svg');

  /// File path: assets/haven/svg/passkey_illos.svg
  SvgGenImage get passkeyIllos =>
      const SvgGenImage('assets/haven/svg/passkey_illos.svg');

  /// File path: assets/haven/svg/passkey_outlined.svg
  SvgGenImage get passkeyOutlined =>
      const SvgGenImage('assets/haven/svg/passkey_outlined.svg');

  /// File path: assets/haven/svg/passkey_setup.svg
  SvgGenImage get passkeySetup =>
      const SvgGenImage('assets/haven/svg/passkey_setup.svg');

  /// File path: assets/haven/svg/pin_lock.svg
  SvgGenImage get pinLock => const SvgGenImage('assets/haven/svg/pin_lock.svg');

  /// File path: assets/haven/svg/pin_setup_illos.svg
  SvgGenImage get pinSetupIllos =>
      const SvgGenImage('assets/haven/svg/pin_setup_illos.svg');

  /// File path: assets/haven/svg/portfolio.svg
  SvgGenImage get portfolio =>
      const SvgGenImage('assets/haven/svg/portfolio.svg');

  /// File path: assets/haven/svg/premium_star.svg
  SvgGenImage get premiumStar =>
      const SvgGenImage('assets/haven/svg/premium_star.svg');

  /// File path: assets/haven/svg/profile.svg
  SvgGenImage get profile => const SvgGenImage('assets/haven/svg/profile.svg');

  /// File path: assets/haven/svg/receive-square.svg
  SvgGenImage get receiveSquare =>
      const SvgGenImage('assets/haven/svg/receive-square.svg');

  /// File path: assets/haven/svg/recommended_border_shape.svg
  SvgGenImage get recommendedBorderShape =>
      const SvgGenImage('assets/haven/svg/recommended_border_shape.svg');

  /// File path: assets/haven/svg/recommended_portfolio_chart.svg
  SvgGenImage get recommendedPortfolioChart =>
      const SvgGenImage('assets/haven/svg/recommended_portfolio_chart.svg');

  /// File path: assets/haven/svg/recommended_wallet.svg
  SvgGenImage get recommendedWallet =>
      const SvgGenImage('assets/haven/svg/recommended_wallet.svg');

  /// File path: assets/haven/svg/reddit.svg
  SvgGenImage get reddit => const SvgGenImage('assets/haven/svg/reddit.svg');

  /// File path: assets/haven/svg/ribbon.svg
  SvgGenImage get ribbon => const SvgGenImage('assets/haven/svg/ribbon.svg');

  /// File path: assets/haven/svg/sad-emoji-ilos.svg
  SvgGenImage get sadEmojiIlos =>
      const SvgGenImage('assets/haven/svg/sad-emoji-ilos.svg');

  /// File path: assets/haven/svg/scan-bold.svg
  SvgGenImage get scanBold =>
      const SvgGenImage('assets/haven/svg/scan-bold.svg');

  /// File path: assets/haven/svg/scan.svg
  SvgGenImage get scan => const SvgGenImage('assets/haven/svg/scan.svg');

  /// File path: assets/haven/svg/search-empty-state-ilos.svg
  SvgGenImage get searchEmptyStateIlos =>
      const SvgGenImage('assets/haven/svg/search-empty-state-ilos.svg');

  /// File path: assets/haven/svg/search.svg
  SvgGenImage get search => const SvgGenImage('assets/haven/svg/search.svg');

  /// File path: assets/haven/svg/security.svg
  SvgGenImage get security =>
      const SvgGenImage('assets/haven/svg/security.svg');

  /// File path: assets/haven/svg/security_outline.svg
  SvgGenImage get securityOutline =>
      const SvgGenImage('assets/haven/svg/security_outline.svg');

  /// File path: assets/haven/svg/sell-crypto-ilos.svg
  SvgGenImage get sellCryptoIlos =>
      const SvgGenImage('assets/haven/svg/sell-crypto-ilos.svg');

  /// File path: assets/haven/svg/sell.svg
  SvgGenImage get sell => const SvgGenImage('assets/haven/svg/sell.svg');

  /// File path: assets/haven/svg/send-arrow.svg
  SvgGenImage get sendArrow =>
      const SvgGenImage('assets/haven/svg/send-arrow.svg');

  /// File path: assets/haven/svg/send-sqaure.svg
  SvgGenImage get sendSqaure =>
      const SvgGenImage('assets/haven/svg/send-sqaure.svg');

  /// File path: assets/haven/svg/send_ilos.svg
  SvgGenImage get sendIlos =>
      const SvgGenImage('assets/haven/svg/send_ilos.svg');

  /// File path: assets/haven/svg/share.svg
  SvgGenImage get share => const SvgGenImage('assets/haven/svg/share.svg');

  /// File path: assets/haven/svg/shield-white-border.svg
  SvgGenImage get shieldWhiteBorder =>
      const SvgGenImage('assets/haven/svg/shield-white-border.svg');

  /// File path: assets/haven/svg/shield_done.svg
  SvgGenImage get shieldDone =>
      const SvgGenImage('assets/haven/svg/shield_done.svg');

  /// File path: assets/haven/svg/shopping-cart.svg
  SvgGenImage get shoppingCart =>
      const SvgGenImage('assets/haven/svg/shopping-cart.svg');

  /// File path: assets/haven/svg/show.svg
  SvgGenImage get show => const SvgGenImage('assets/haven/svg/show.svg');

  /// File path: assets/haven/svg/signature-ilos.svg
  SvgGenImage get signatureIlos =>
      const SvgGenImage('assets/haven/svg/signature-ilos.svg');

  /// File path: assets/haven/svg/sms_outlined.svg
  SvgGenImage get smsOutlined =>
      const SvgGenImage('assets/haven/svg/sms_outlined.svg');

  /// File path: assets/haven/svg/switch-icon.svg
  SvgGenImage get switchIcon =>
      const SvgGenImage('assets/haven/svg/switch-icon.svg');

  /// File path: assets/haven/svg/switch_icon_button.svg
  SvgGenImage get switchIconButton =>
      const SvgGenImage('assets/haven/svg/switch_icon_button.svg');

  /// File path: assets/haven/svg/time_circle_filled_yellow.svg
  SvgGenImage get timeCircleFilledYellow =>
      const SvgGenImage('assets/haven/svg/time_circle_filled_yellow.svg');

  /// File path: assets/haven/svg/trade_type.svg
  SvgGenImage get tradeType =>
      const SvgGenImage('assets/haven/svg/trade_type.svg');

  /// File path: assets/haven/svg/tx_inflow_failed.svg
  SvgGenImage get txInflowFailed =>
      const SvgGenImage('assets/haven/svg/tx_inflow_failed.svg');

  /// File path: assets/haven/svg/tx_inflow_pending.svg
  SvgGenImage get txInflowPending =>
      const SvgGenImage('assets/haven/svg/tx_inflow_pending.svg');

  /// File path: assets/haven/svg/tx_inflow_success.svg
  SvgGenImage get txInflowSuccess =>
      const SvgGenImage('assets/haven/svg/tx_inflow_success.svg');

  /// File path: assets/haven/svg/tx_outflow_failed.svg
  SvgGenImage get txOutflowFailed =>
      const SvgGenImage('assets/haven/svg/tx_outflow_failed.svg');

  /// File path: assets/haven/svg/tx_outflow_pending.svg
  SvgGenImage get txOutflowPending =>
      const SvgGenImage('assets/haven/svg/tx_outflow_pending.svg');

  /// File path: assets/haven/svg/tx_outflow_success.svg
  SvgGenImage get txOutflowSuccess =>
      const SvgGenImage('assets/haven/svg/tx_outflow_success.svg');

  /// File path: assets/haven/svg/unchecked_circle_type.svg
  SvgGenImage get uncheckedCircleType =>
      const SvgGenImage('assets/haven/svg/unchecked_circle_type.svg');

  /// File path: assets/haven/svg/unchecked_grey.svg
  SvgGenImage get uncheckedGrey =>
      const SvgGenImage('assets/haven/svg/unchecked_grey.svg');

  /// File path: assets/haven/svg/unlock_wallet_ilos.svg
  SvgGenImage get unlockWalletIlos =>
      const SvgGenImage('assets/haven/svg/unlock_wallet_ilos.svg');

  /// File path: assets/haven/svg/usd.svg
  SvgGenImage get usd => const SvgGenImage('assets/haven/svg/usd.svg');

  /// File path: assets/haven/svg/withdrawal_type.svg
  SvgGenImage get withdrawalType =>
      const SvgGenImage('assets/haven/svg/withdrawal_type.svg');

  /// File path: assets/haven/svg/world-icon-bare.svg
  SvgGenImage get worldIconBare =>
      const SvgGenImage('assets/haven/svg/world-icon-bare.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
        accounts,
        alertCircle,
        alertTriangleIlos,
        alertTriangleOrange,
        alertTriangle,
        allTransactions,
        android,
        bankWithBg,
        bolt,
        bscLogo,
        caution,
        checkedCircleType,
        cloudLock,
        coingeckoIcon,
        coinsSwap,
        confirmingDepositIllos,
        contactless,
        containerSeedphrase,
        copyFilledBlock,
        customerIcon,
        deleteMdi,
        depositConfirmedIllos,
        depositType,
        digitalAssets,
        discovery,
        dotGrey,
        emailOutlined,
        facebook,
        genericCoin,
        github,
        havenCup,
        havenInfoIllos,
        havenTextLogo,
        hideLowBalance,
        infoRed,
        keyOutline,
        lockScreen,
        more,
        noTransactionIlos,
        notification,
        onboardIllios,
        onboardingTermIcon,
        ongoingOrderIcon,
        passkeyIllos,
        passkeyOutlined,
        passkeySetup,
        pinLock,
        pinSetupIllos,
        portfolio,
        premiumStar,
        profile,
        receiveSquare,
        recommendedBorderShape,
        recommendedPortfolioChart,
        recommendedWallet,
        reddit,
        ribbon,
        sadEmojiIlos,
        scanBold,
        scan,
        searchEmptyStateIlos,
        search,
        security,
        securityOutline,
        sellCryptoIlos,
        sell,
        sendArrow,
        sendSqaure,
        sendIlos,
        share,
        shieldWhiteBorder,
        shieldDone,
        shoppingCart,
        show,
        signatureIlos,
        smsOutlined,
        switchIcon,
        switchIconButton,
        timeCircleFilledYellow,
        tradeType,
        txInflowFailed,
        txInflowPending,
        txInflowSuccess,
        txOutflowFailed,
        txOutflowPending,
        txOutflowSuccess,
        uncheckedCircleType,
        uncheckedGrey,
        unlockWalletIlos,
        usd,
        withdrawalType,
        worldIconBare
      ];
}

class $AssetsSvgBaseLightGen {
  const $AssetsSvgBaseLightGen();

  /// File path: assets/svg/base-light/help-circle.svg
  SvgGenImage get helpCircle =>
      const SvgGenImage('assets/svg/base-light/help-circle.svg');

  /// File path: assets/svg/base-light/upload.svg
  SvgGenImage get upload =>
      const SvgGenImage('assets/svg/base-light/upload.svg');

  /// List of all assets
  List<SvgGenImage> get values => [helpCircle, upload];
}

class $AssetsSvgBulkLightGen {
  const $AssetsSvgBulkLightGen();

  /// File path: assets/svg/bulk-light/Profile.svg
  SvgGenImage get profile =>
      const SvgGenImage('assets/svg/bulk-light/Profile.svg');

  /// List of all assets
  List<SvgGenImage> get values => [profile];
}

class $AssetsSvgCountriesFlagsGen {
  const $AssetsSvgCountriesFlagsGen();

  /// File path: assets/svg/countries-flags/ac.svg
  SvgGenImage get ac => const SvgGenImage('assets/svg/countries-flags/ac.svg');

  /// File path: assets/svg/countries-flags/ad.svg
  SvgGenImage get ad => const SvgGenImage('assets/svg/countries-flags/ad.svg');

  /// File path: assets/svg/countries-flags/ae.svg
  SvgGenImage get ae => const SvgGenImage('assets/svg/countries-flags/ae.svg');

  /// File path: assets/svg/countries-flags/af.svg
  SvgGenImage get af => const SvgGenImage('assets/svg/countries-flags/af.svg');

  /// File path: assets/svg/countries-flags/ag.svg
  SvgGenImage get ag => const SvgGenImage('assets/svg/countries-flags/ag.svg');

  /// File path: assets/svg/countries-flags/ai.svg
  SvgGenImage get ai => const SvgGenImage('assets/svg/countries-flags/ai.svg');

  /// File path: assets/svg/countries-flags/al.svg
  SvgGenImage get al => const SvgGenImage('assets/svg/countries-flags/al.svg');

  /// File path: assets/svg/countries-flags/am.svg
  SvgGenImage get am => const SvgGenImage('assets/svg/countries-flags/am.svg');

  /// File path: assets/svg/countries-flags/an.svg
  SvgGenImage get an => const SvgGenImage('assets/svg/countries-flags/an.svg');

  /// File path: assets/svg/countries-flags/ao.svg
  SvgGenImage get ao => const SvgGenImage('assets/svg/countries-flags/ao.svg');

  /// File path: assets/svg/countries-flags/aq.svg
  SvgGenImage get aq => const SvgGenImage('assets/svg/countries-flags/aq.svg');

  /// File path: assets/svg/countries-flags/ar.svg
  SvgGenImage get ar => const SvgGenImage('assets/svg/countries-flags/ar.svg');

  /// File path: assets/svg/countries-flags/as.svg
  SvgGenImage get as => const SvgGenImage('assets/svg/countries-flags/as.svg');

  /// File path: assets/svg/countries-flags/at.svg
  SvgGenImage get at => const SvgGenImage('assets/svg/countries-flags/at.svg');

  /// File path: assets/svg/countries-flags/au.svg
  SvgGenImage get au => const SvgGenImage('assets/svg/countries-flags/au.svg');

  /// File path: assets/svg/countries-flags/aw.svg
  SvgGenImage get aw => const SvgGenImage('assets/svg/countries-flags/aw.svg');

  /// File path: assets/svg/countries-flags/ax.svg
  SvgGenImage get ax => const SvgGenImage('assets/svg/countries-flags/ax.svg');

  /// File path: assets/svg/countries-flags/az.svg
  SvgGenImage get az => const SvgGenImage('assets/svg/countries-flags/az.svg');

  /// File path: assets/svg/countries-flags/ba.svg
  SvgGenImage get ba => const SvgGenImage('assets/svg/countries-flags/ba.svg');

  /// File path: assets/svg/countries-flags/bb.svg
  SvgGenImage get bb => const SvgGenImage('assets/svg/countries-flags/bb.svg');

  /// File path: assets/svg/countries-flags/bd.svg
  SvgGenImage get bd => const SvgGenImage('assets/svg/countries-flags/bd.svg');

  /// File path: assets/svg/countries-flags/be.svg
  SvgGenImage get be => const SvgGenImage('assets/svg/countries-flags/be.svg');

  /// File path: assets/svg/countries-flags/bf.svg
  SvgGenImage get bf => const SvgGenImage('assets/svg/countries-flags/bf.svg');

  /// File path: assets/svg/countries-flags/bg.svg
  SvgGenImage get bg => const SvgGenImage('assets/svg/countries-flags/bg.svg');

  /// File path: assets/svg/countries-flags/bh.svg
  SvgGenImage get bh => const SvgGenImage('assets/svg/countries-flags/bh.svg');

  /// File path: assets/svg/countries-flags/bi.svg
  SvgGenImage get bi => const SvgGenImage('assets/svg/countries-flags/bi.svg');

  /// File path: assets/svg/countries-flags/bj.svg
  SvgGenImage get bj => const SvgGenImage('assets/svg/countries-flags/bj.svg');

  /// File path: assets/svg/countries-flags/bl.svg
  SvgGenImage get bl => const SvgGenImage('assets/svg/countries-flags/bl.svg');

  /// File path: assets/svg/countries-flags/bm.svg
  SvgGenImage get bm => const SvgGenImage('assets/svg/countries-flags/bm.svg');

  /// File path: assets/svg/countries-flags/bn.svg
  SvgGenImage get bn => const SvgGenImage('assets/svg/countries-flags/bn.svg');

  /// File path: assets/svg/countries-flags/bo.svg
  SvgGenImage get bo => const SvgGenImage('assets/svg/countries-flags/bo.svg');

  /// File path: assets/svg/countries-flags/bq.svg
  SvgGenImage get bq => const SvgGenImage('assets/svg/countries-flags/bq.svg');

  /// File path: assets/svg/countries-flags/br.svg
  SvgGenImage get br => const SvgGenImage('assets/svg/countries-flags/br.svg');

  /// File path: assets/svg/countries-flags/bs.svg
  SvgGenImage get bs => const SvgGenImage('assets/svg/countries-flags/bs.svg');

  /// File path: assets/svg/countries-flags/bt.svg
  SvgGenImage get bt => const SvgGenImage('assets/svg/countries-flags/bt.svg');

  /// File path: assets/svg/countries-flags/bv.svg
  SvgGenImage get bv => const SvgGenImage('assets/svg/countries-flags/bv.svg');

  /// File path: assets/svg/countries-flags/bw.svg
  SvgGenImage get bw => const SvgGenImage('assets/svg/countries-flags/bw.svg');

  /// File path: assets/svg/countries-flags/by.svg
  SvgGenImage get by => const SvgGenImage('assets/svg/countries-flags/by.svg');

  /// File path: assets/svg/countries-flags/bz.svg
  SvgGenImage get bz => const SvgGenImage('assets/svg/countries-flags/bz.svg');

  /// File path: assets/svg/countries-flags/ca.svg
  SvgGenImage get ca => const SvgGenImage('assets/svg/countries-flags/ca.svg');

  /// File path: assets/svg/countries-flags/cc.svg
  SvgGenImage get cc => const SvgGenImage('assets/svg/countries-flags/cc.svg');

  /// File path: assets/svg/countries-flags/cd.svg
  SvgGenImage get cd => const SvgGenImage('assets/svg/countries-flags/cd.svg');

  /// File path: assets/svg/countries-flags/cefta.svg
  SvgGenImage get cefta =>
      const SvgGenImage('assets/svg/countries-flags/cefta.svg');

  /// File path: assets/svg/countries-flags/cf.svg
  SvgGenImage get cf => const SvgGenImage('assets/svg/countries-flags/cf.svg');

  /// File path: assets/svg/countries-flags/cg.svg
  SvgGenImage get cg => const SvgGenImage('assets/svg/countries-flags/cg.svg');

  /// File path: assets/svg/countries-flags/ch.svg
  SvgGenImage get ch => const SvgGenImage('assets/svg/countries-flags/ch.svg');

  /// File path: assets/svg/countries-flags/ci.svg
  SvgGenImage get ci => const SvgGenImage('assets/svg/countries-flags/ci.svg');

  /// File path: assets/svg/countries-flags/ck.svg
  SvgGenImage get ck => const SvgGenImage('assets/svg/countries-flags/ck.svg');

  /// File path: assets/svg/countries-flags/cl.svg
  SvgGenImage get cl => const SvgGenImage('assets/svg/countries-flags/cl.svg');

  /// File path: assets/svg/countries-flags/cm.svg
  SvgGenImage get cm => const SvgGenImage('assets/svg/countries-flags/cm.svg');

  /// File path: assets/svg/countries-flags/cn.svg
  SvgGenImage get cn => const SvgGenImage('assets/svg/countries-flags/cn.svg');

  /// File path: assets/svg/countries-flags/co.svg
  SvgGenImage get co => const SvgGenImage('assets/svg/countries-flags/co.svg');

  /// File path: assets/svg/countries-flags/cp.svg
  SvgGenImage get cp => const SvgGenImage('assets/svg/countries-flags/cp.svg');

  /// File path: assets/svg/countries-flags/cr.svg
  SvgGenImage get cr => const SvgGenImage('assets/svg/countries-flags/cr.svg');

  /// File path: assets/svg/countries-flags/cu.svg
  SvgGenImage get cu => const SvgGenImage('assets/svg/countries-flags/cu.svg');

  /// File path: assets/svg/countries-flags/cv.svg
  SvgGenImage get cv => const SvgGenImage('assets/svg/countries-flags/cv.svg');

  /// File path: assets/svg/countries-flags/cw.svg
  SvgGenImage get cw => const SvgGenImage('assets/svg/countries-flags/cw.svg');

  /// File path: assets/svg/countries-flags/cx.svg
  SvgGenImage get cx => const SvgGenImage('assets/svg/countries-flags/cx.svg');

  /// File path: assets/svg/countries-flags/cy.svg
  SvgGenImage get cy => const SvgGenImage('assets/svg/countries-flags/cy.svg');

  /// File path: assets/svg/countries-flags/cz.svg
  SvgGenImage get cz => const SvgGenImage('assets/svg/countries-flags/cz.svg');

  /// File path: assets/svg/countries-flags/de.svg
  SvgGenImage get de => const SvgGenImage('assets/svg/countries-flags/de.svg');

  /// File path: assets/svg/countries-flags/dg.svg
  SvgGenImage get dg => const SvgGenImage('assets/svg/countries-flags/dg.svg');

  /// File path: assets/svg/countries-flags/dj.svg
  SvgGenImage get dj => const SvgGenImage('assets/svg/countries-flags/dj.svg');

  /// File path: assets/svg/countries-flags/dk.svg
  SvgGenImage get dk => const SvgGenImage('assets/svg/countries-flags/dk.svg');

  /// File path: assets/svg/countries-flags/dm.svg
  SvgGenImage get dm => const SvgGenImage('assets/svg/countries-flags/dm.svg');

  /// File path: assets/svg/countries-flags/do_x.svg
  SvgGenImage get doX =>
      const SvgGenImage('assets/svg/countries-flags/do_x.svg');

  /// File path: assets/svg/countries-flags/dz.svg
  SvgGenImage get dz => const SvgGenImage('assets/svg/countries-flags/dz.svg');

  /// File path: assets/svg/countries-flags/ea.svg
  SvgGenImage get ea => const SvgGenImage('assets/svg/countries-flags/ea.svg');

  /// File path: assets/svg/countries-flags/ec.svg
  SvgGenImage get ec => const SvgGenImage('assets/svg/countries-flags/ec.svg');

  /// File path: assets/svg/countries-flags/ee.svg
  SvgGenImage get ee => const SvgGenImage('assets/svg/countries-flags/ee.svg');

  /// File path: assets/svg/countries-flags/eg.svg
  SvgGenImage get eg => const SvgGenImage('assets/svg/countries-flags/eg.svg');

  /// File path: assets/svg/countries-flags/eh.svg
  SvgGenImage get eh => const SvgGenImage('assets/svg/countries-flags/eh.svg');

  /// File path: assets/svg/countries-flags/er.svg
  SvgGenImage get er => const SvgGenImage('assets/svg/countries-flags/er.svg');

  /// File path: assets/svg/countries-flags/es-ct.svg
  SvgGenImage get esCt =>
      const SvgGenImage('assets/svg/countries-flags/es-ct.svg');

  /// File path: assets/svg/countries-flags/es-ga.svg
  SvgGenImage get esGa =>
      const SvgGenImage('assets/svg/countries-flags/es-ga.svg');

  /// File path: assets/svg/countries-flags/es-pv.svg
  SvgGenImage get esPv =>
      const SvgGenImage('assets/svg/countries-flags/es-pv.svg');

  /// File path: assets/svg/countries-flags/es.svg
  SvgGenImage get es => const SvgGenImage('assets/svg/countries-flags/es.svg');

  /// File path: assets/svg/countries-flags/et.svg
  SvgGenImage get et => const SvgGenImage('assets/svg/countries-flags/et.svg');

  /// File path: assets/svg/countries-flags/eu.svg
  SvgGenImage get eu => const SvgGenImage('assets/svg/countries-flags/eu.svg');

  /// File path: assets/svg/countries-flags/fi.svg
  SvgGenImage get fi => const SvgGenImage('assets/svg/countries-flags/fi.svg');

  /// File path: assets/svg/countries-flags/fj.svg
  SvgGenImage get fj => const SvgGenImage('assets/svg/countries-flags/fj.svg');

  /// File path: assets/svg/countries-flags/fk.svg
  SvgGenImage get fk => const SvgGenImage('assets/svg/countries-flags/fk.svg');

  /// File path: assets/svg/countries-flags/fm.svg
  SvgGenImage get fm => const SvgGenImage('assets/svg/countries-flags/fm.svg');

  /// File path: assets/svg/countries-flags/fo.svg
  SvgGenImage get fo => const SvgGenImage('assets/svg/countries-flags/fo.svg');

  /// File path: assets/svg/countries-flags/fr.svg
  SvgGenImage get fr => const SvgGenImage('assets/svg/countries-flags/fr.svg');

  /// File path: assets/svg/countries-flags/ga.svg
  SvgGenImage get ga => const SvgGenImage('assets/svg/countries-flags/ga.svg');

  /// File path: assets/svg/countries-flags/gb-eng.svg
  SvgGenImage get gbEng =>
      const SvgGenImage('assets/svg/countries-flags/gb-eng.svg');

  /// File path: assets/svg/countries-flags/gb-nir.svg
  SvgGenImage get gbNir =>
      const SvgGenImage('assets/svg/countries-flags/gb-nir.svg');

  /// File path: assets/svg/countries-flags/gb-sct.svg
  SvgGenImage get gbSct =>
      const SvgGenImage('assets/svg/countries-flags/gb-sct.svg');

  /// File path: assets/svg/countries-flags/gb-wls.svg
  SvgGenImage get gbWls =>
      const SvgGenImage('assets/svg/countries-flags/gb-wls.svg');

  /// File path: assets/svg/countries-flags/gb.svg
  SvgGenImage get gb => const SvgGenImage('assets/svg/countries-flags/gb.svg');

  /// File path: assets/svg/countries-flags/gd.svg
  SvgGenImage get gd => const SvgGenImage('assets/svg/countries-flags/gd.svg');

  /// File path: assets/svg/countries-flags/ge.svg
  SvgGenImage get ge => const SvgGenImage('assets/svg/countries-flags/ge.svg');

  /// File path: assets/svg/countries-flags/gf.svg
  SvgGenImage get gf => const SvgGenImage('assets/svg/countries-flags/gf.svg');

  /// File path: assets/svg/countries-flags/gg.svg
  SvgGenImage get gg => const SvgGenImage('assets/svg/countries-flags/gg.svg');

  /// File path: assets/svg/countries-flags/gh.svg
  SvgGenImage get gh => const SvgGenImage('assets/svg/countries-flags/gh.svg');

  /// File path: assets/svg/countries-flags/gi.svg
  SvgGenImage get gi => const SvgGenImage('assets/svg/countries-flags/gi.svg');

  /// File path: assets/svg/countries-flags/gl.svg
  SvgGenImage get gl => const SvgGenImage('assets/svg/countries-flags/gl.svg');

  /// File path: assets/svg/countries-flags/gm.svg
  SvgGenImage get gm => const SvgGenImage('assets/svg/countries-flags/gm.svg');

  /// File path: assets/svg/countries-flags/gn.svg
  SvgGenImage get gn => const SvgGenImage('assets/svg/countries-flags/gn.svg');

  /// File path: assets/svg/countries-flags/gp.svg
  SvgGenImage get gp => const SvgGenImage('assets/svg/countries-flags/gp.svg');

  /// File path: assets/svg/countries-flags/gq.svg
  SvgGenImage get gq => const SvgGenImage('assets/svg/countries-flags/gq.svg');

  /// File path: assets/svg/countries-flags/gr.svg
  SvgGenImage get gr => const SvgGenImage('assets/svg/countries-flags/gr.svg');

  /// File path: assets/svg/countries-flags/gs.svg
  SvgGenImage get gs => const SvgGenImage('assets/svg/countries-flags/gs.svg');

  /// File path: assets/svg/countries-flags/gt.svg
  SvgGenImage get gt => const SvgGenImage('assets/svg/countries-flags/gt.svg');

  /// File path: assets/svg/countries-flags/gu.svg
  SvgGenImage get gu => const SvgGenImage('assets/svg/countries-flags/gu.svg');

  /// File path: assets/svg/countries-flags/gw.svg
  SvgGenImage get gw => const SvgGenImage('assets/svg/countries-flags/gw.svg');

  /// File path: assets/svg/countries-flags/gy.svg
  SvgGenImage get gy => const SvgGenImage('assets/svg/countries-flags/gy.svg');

  /// File path: assets/svg/countries-flags/hk.svg
  SvgGenImage get hk => const SvgGenImage('assets/svg/countries-flags/hk.svg');

  /// File path: assets/svg/countries-flags/hm.svg
  SvgGenImage get hm => const SvgGenImage('assets/svg/countries-flags/hm.svg');

  /// File path: assets/svg/countries-flags/hn.svg
  SvgGenImage get hn => const SvgGenImage('assets/svg/countries-flags/hn.svg');

  /// File path: assets/svg/countries-flags/hr.svg
  SvgGenImage get hr => const SvgGenImage('assets/svg/countries-flags/hr.svg');

  /// File path: assets/svg/countries-flags/ht.svg
  SvgGenImage get ht => const SvgGenImage('assets/svg/countries-flags/ht.svg');

  /// File path: assets/svg/countries-flags/hu.svg
  SvgGenImage get hu => const SvgGenImage('assets/svg/countries-flags/hu.svg');

  /// File path: assets/svg/countries-flags/ic.svg
  SvgGenImage get ic => const SvgGenImage('assets/svg/countries-flags/ic.svg');

  /// File path: assets/svg/countries-flags/id.svg
  SvgGenImage get id => const SvgGenImage('assets/svg/countries-flags/id.svg');

  /// File path: assets/svg/countries-flags/ie.svg
  SvgGenImage get ie => const SvgGenImage('assets/svg/countries-flags/ie.svg');

  /// File path: assets/svg/countries-flags/il.svg
  SvgGenImage get il => const SvgGenImage('assets/svg/countries-flags/il.svg');

  /// File path: assets/svg/countries-flags/im.svg
  SvgGenImage get im => const SvgGenImage('assets/svg/countries-flags/im.svg');

  /// File path: assets/svg/countries-flags/in_x.svg
  SvgGenImage get inX =>
      const SvgGenImage('assets/svg/countries-flags/in_x.svg');

  /// File path: assets/svg/countries-flags/io.svg
  SvgGenImage get io => const SvgGenImage('assets/svg/countries-flags/io.svg');

  /// File path: assets/svg/countries-flags/iq.svg
  SvgGenImage get iq => const SvgGenImage('assets/svg/countries-flags/iq.svg');

  /// File path: assets/svg/countries-flags/ir.svg
  SvgGenImage get ir => const SvgGenImage('assets/svg/countries-flags/ir.svg');

  /// File path: assets/svg/countries-flags/is_x.svg
  SvgGenImage get isX =>
      const SvgGenImage('assets/svg/countries-flags/is_x.svg');

  /// File path: assets/svg/countries-flags/it.svg
  SvgGenImage get it => const SvgGenImage('assets/svg/countries-flags/it.svg');

  /// File path: assets/svg/countries-flags/je.svg
  SvgGenImage get je => const SvgGenImage('assets/svg/countries-flags/je.svg');

  /// File path: assets/svg/countries-flags/jm.svg
  SvgGenImage get jm => const SvgGenImage('assets/svg/countries-flags/jm.svg');

  /// File path: assets/svg/countries-flags/jo.svg
  SvgGenImage get jo => const SvgGenImage('assets/svg/countries-flags/jo.svg');

  /// File path: assets/svg/countries-flags/jp.svg
  SvgGenImage get jp => const SvgGenImage('assets/svg/countries-flags/jp.svg');

  /// File path: assets/svg/countries-flags/ke.svg
  SvgGenImage get ke => const SvgGenImage('assets/svg/countries-flags/ke.svg');

  /// File path: assets/svg/countries-flags/kg.svg
  SvgGenImage get kg => const SvgGenImage('assets/svg/countries-flags/kg.svg');

  /// File path: assets/svg/countries-flags/kh.svg
  SvgGenImage get kh => const SvgGenImage('assets/svg/countries-flags/kh.svg');

  /// File path: assets/svg/countries-flags/ki.svg
  SvgGenImage get ki => const SvgGenImage('assets/svg/countries-flags/ki.svg');

  /// File path: assets/svg/countries-flags/km.svg
  SvgGenImage get km => const SvgGenImage('assets/svg/countries-flags/km.svg');

  /// File path: assets/svg/countries-flags/kn.svg
  SvgGenImage get kn => const SvgGenImage('assets/svg/countries-flags/kn.svg');

  /// File path: assets/svg/countries-flags/kp.svg
  SvgGenImage get kp => const SvgGenImage('assets/svg/countries-flags/kp.svg');

  /// File path: assets/svg/countries-flags/kr.svg
  SvgGenImage get kr => const SvgGenImage('assets/svg/countries-flags/kr.svg');

  /// File path: assets/svg/countries-flags/kw.svg
  SvgGenImage get kw => const SvgGenImage('assets/svg/countries-flags/kw.svg');

  /// File path: assets/svg/countries-flags/ky.svg
  SvgGenImage get ky => const SvgGenImage('assets/svg/countries-flags/ky.svg');

  /// File path: assets/svg/countries-flags/kz.svg
  SvgGenImage get kz => const SvgGenImage('assets/svg/countries-flags/kz.svg');

  /// File path: assets/svg/countries-flags/la.svg
  SvgGenImage get la => const SvgGenImage('assets/svg/countries-flags/la.svg');

  /// File path: assets/svg/countries-flags/lb.svg
  SvgGenImage get lb => const SvgGenImage('assets/svg/countries-flags/lb.svg');

  /// File path: assets/svg/countries-flags/lc.svg
  SvgGenImage get lc => const SvgGenImage('assets/svg/countries-flags/lc.svg');

  /// File path: assets/svg/countries-flags/li.svg
  SvgGenImage get li => const SvgGenImage('assets/svg/countries-flags/li.svg');

  /// File path: assets/svg/countries-flags/lk.svg
  SvgGenImage get lk => const SvgGenImage('assets/svg/countries-flags/lk.svg');

  /// File path: assets/svg/countries-flags/lr.svg
  SvgGenImage get lr => const SvgGenImage('assets/svg/countries-flags/lr.svg');

  /// File path: assets/svg/countries-flags/ls.svg
  SvgGenImage get ls => const SvgGenImage('assets/svg/countries-flags/ls.svg');

  /// File path: assets/svg/countries-flags/lt.svg
  SvgGenImage get lt => const SvgGenImage('assets/svg/countries-flags/lt.svg');

  /// File path: assets/svg/countries-flags/lu.svg
  SvgGenImage get lu => const SvgGenImage('assets/svg/countries-flags/lu.svg');

  /// File path: assets/svg/countries-flags/lv.svg
  SvgGenImage get lv => const SvgGenImage('assets/svg/countries-flags/lv.svg');

  /// File path: assets/svg/countries-flags/ly.svg
  SvgGenImage get ly => const SvgGenImage('assets/svg/countries-flags/ly.svg');

  /// File path: assets/svg/countries-flags/ma.svg
  SvgGenImage get ma => const SvgGenImage('assets/svg/countries-flags/ma.svg');

  /// File path: assets/svg/countries-flags/mc.svg
  SvgGenImage get mc => const SvgGenImage('assets/svg/countries-flags/mc.svg');

  /// File path: assets/svg/countries-flags/md.svg
  SvgGenImage get md => const SvgGenImage('assets/svg/countries-flags/md.svg');

  /// File path: assets/svg/countries-flags/me.svg
  SvgGenImage get me => const SvgGenImage('assets/svg/countries-flags/me.svg');

  /// File path: assets/svg/countries-flags/mf.svg
  SvgGenImage get mf => const SvgGenImage('assets/svg/countries-flags/mf.svg');

  /// File path: assets/svg/countries-flags/mg.svg
  SvgGenImage get mg => const SvgGenImage('assets/svg/countries-flags/mg.svg');

  /// File path: assets/svg/countries-flags/mh.svg
  SvgGenImage get mh => const SvgGenImage('assets/svg/countries-flags/mh.svg');

  /// File path: assets/svg/countries-flags/mk.svg
  SvgGenImage get mk => const SvgGenImage('assets/svg/countries-flags/mk.svg');

  /// File path: assets/svg/countries-flags/ml.svg
  SvgGenImage get ml => const SvgGenImage('assets/svg/countries-flags/ml.svg');

  /// File path: assets/svg/countries-flags/mm.svg
  SvgGenImage get mm => const SvgGenImage('assets/svg/countries-flags/mm.svg');

  /// File path: assets/svg/countries-flags/mn.svg
  SvgGenImage get mn => const SvgGenImage('assets/svg/countries-flags/mn.svg');

  /// File path: assets/svg/countries-flags/mo.svg
  SvgGenImage get mo => const SvgGenImage('assets/svg/countries-flags/mo.svg');

  /// File path: assets/svg/countries-flags/mp.svg
  SvgGenImage get mp => const SvgGenImage('assets/svg/countries-flags/mp.svg');

  /// File path: assets/svg/countries-flags/mq.svg
  SvgGenImage get mq => const SvgGenImage('assets/svg/countries-flags/mq.svg');

  /// File path: assets/svg/countries-flags/mr.svg
  SvgGenImage get mr => const SvgGenImage('assets/svg/countries-flags/mr.svg');

  /// File path: assets/svg/countries-flags/ms.svg
  SvgGenImage get ms => const SvgGenImage('assets/svg/countries-flags/ms.svg');

  /// File path: assets/svg/countries-flags/mt.svg
  SvgGenImage get mt => const SvgGenImage('assets/svg/countries-flags/mt.svg');

  /// File path: assets/svg/countries-flags/mu.svg
  SvgGenImage get mu => const SvgGenImage('assets/svg/countries-flags/mu.svg');

  /// File path: assets/svg/countries-flags/mv.svg
  SvgGenImage get mv => const SvgGenImage('assets/svg/countries-flags/mv.svg');

  /// File path: assets/svg/countries-flags/mw.svg
  SvgGenImage get mw => const SvgGenImage('assets/svg/countries-flags/mw.svg');

  /// File path: assets/svg/countries-flags/mx.svg
  SvgGenImage get mx => const SvgGenImage('assets/svg/countries-flags/mx.svg');

  /// File path: assets/svg/countries-flags/my.svg
  SvgGenImage get my => const SvgGenImage('assets/svg/countries-flags/my.svg');

  /// File path: assets/svg/countries-flags/mz.svg
  SvgGenImage get mz => const SvgGenImage('assets/svg/countries-flags/mz.svg');

  /// File path: assets/svg/countries-flags/na.svg
  SvgGenImage get na => const SvgGenImage('assets/svg/countries-flags/na.svg');

  /// File path: assets/svg/countries-flags/nc.svg
  SvgGenImage get nc => const SvgGenImage('assets/svg/countries-flags/nc.svg');

  /// File path: assets/svg/countries-flags/ne.svg
  SvgGenImage get ne => const SvgGenImage('assets/svg/countries-flags/ne.svg');

  /// File path: assets/svg/countries-flags/nf.svg
  SvgGenImage get nf => const SvgGenImage('assets/svg/countries-flags/nf.svg');

  /// File path: assets/svg/countries-flags/ng.svg
  SvgGenImage get ng => const SvgGenImage('assets/svg/countries-flags/ng.svg');

  /// File path: assets/svg/countries-flags/ni.svg
  SvgGenImage get ni => const SvgGenImage('assets/svg/countries-flags/ni.svg');

  /// File path: assets/svg/countries-flags/nl.svg
  SvgGenImage get nl => const SvgGenImage('assets/svg/countries-flags/nl.svg');

  /// File path: assets/svg/countries-flags/no.svg
  SvgGenImage get no => const SvgGenImage('assets/svg/countries-flags/no.svg');

  /// File path: assets/svg/countries-flags/np.svg
  SvgGenImage get np => const SvgGenImage('assets/svg/countries-flags/np.svg');

  /// File path: assets/svg/countries-flags/nr.svg
  SvgGenImage get nr => const SvgGenImage('assets/svg/countries-flags/nr.svg');

  /// File path: assets/svg/countries-flags/nu.svg
  SvgGenImage get nu => const SvgGenImage('assets/svg/countries-flags/nu.svg');

  /// File path: assets/svg/countries-flags/nz.svg
  SvgGenImage get nz => const SvgGenImage('assets/svg/countries-flags/nz.svg');

  /// File path: assets/svg/countries-flags/om.svg
  SvgGenImage get om => const SvgGenImage('assets/svg/countries-flags/om.svg');

  /// File path: assets/svg/countries-flags/pa.svg
  SvgGenImage get pa => const SvgGenImage('assets/svg/countries-flags/pa.svg');

  /// File path: assets/svg/countries-flags/pe.svg
  SvgGenImage get pe => const SvgGenImage('assets/svg/countries-flags/pe.svg');

  /// File path: assets/svg/countries-flags/pf.svg
  SvgGenImage get pf => const SvgGenImage('assets/svg/countries-flags/pf.svg');

  /// File path: assets/svg/countries-flags/pg.svg
  SvgGenImage get pg => const SvgGenImage('assets/svg/countries-flags/pg.svg');

  /// File path: assets/svg/countries-flags/ph.svg
  SvgGenImage get ph => const SvgGenImage('assets/svg/countries-flags/ph.svg');

  /// File path: assets/svg/countries-flags/pk.svg
  SvgGenImage get pk => const SvgGenImage('assets/svg/countries-flags/pk.svg');

  /// File path: assets/svg/countries-flags/pl.svg
  SvgGenImage get pl => const SvgGenImage('assets/svg/countries-flags/pl.svg');

  /// File path: assets/svg/countries-flags/pm.svg
  SvgGenImage get pm => const SvgGenImage('assets/svg/countries-flags/pm.svg');

  /// File path: assets/svg/countries-flags/pn.svg
  SvgGenImage get pn => const SvgGenImage('assets/svg/countries-flags/pn.svg');

  /// File path: assets/svg/countries-flags/pr.svg
  SvgGenImage get pr => const SvgGenImage('assets/svg/countries-flags/pr.svg');

  /// File path: assets/svg/countries-flags/ps.svg
  SvgGenImage get ps => const SvgGenImage('assets/svg/countries-flags/ps.svg');

  /// File path: assets/svg/countries-flags/pt.svg
  SvgGenImage get pt => const SvgGenImage('assets/svg/countries-flags/pt.svg');

  /// File path: assets/svg/countries-flags/pw.svg
  SvgGenImage get pw => const SvgGenImage('assets/svg/countries-flags/pw.svg');

  /// File path: assets/svg/countries-flags/py.svg
  SvgGenImage get py => const SvgGenImage('assets/svg/countries-flags/py.svg');

  /// File path: assets/svg/countries-flags/qa.svg
  SvgGenImage get qa => const SvgGenImage('assets/svg/countries-flags/qa.svg');

  /// File path: assets/svg/countries-flags/re.svg
  SvgGenImage get re => const SvgGenImage('assets/svg/countries-flags/re.svg');

  /// File path: assets/svg/countries-flags/ro.svg
  SvgGenImage get ro => const SvgGenImage('assets/svg/countries-flags/ro.svg');

  /// File path: assets/svg/countries-flags/rs.svg
  SvgGenImage get rs => const SvgGenImage('assets/svg/countries-flags/rs.svg');

  /// File path: assets/svg/countries-flags/ru.svg
  SvgGenImage get ru => const SvgGenImage('assets/svg/countries-flags/ru.svg');

  /// File path: assets/svg/countries-flags/rw.svg
  SvgGenImage get rw => const SvgGenImage('assets/svg/countries-flags/rw.svg');

  /// File path: assets/svg/countries-flags/sa.svg
  SvgGenImage get sa => const SvgGenImage('assets/svg/countries-flags/sa.svg');

  /// File path: assets/svg/countries-flags/sb.svg
  SvgGenImage get sb => const SvgGenImage('assets/svg/countries-flags/sb.svg');

  /// File path: assets/svg/countries-flags/sc.svg
  SvgGenImage get sc => const SvgGenImage('assets/svg/countries-flags/sc.svg');

  /// File path: assets/svg/countries-flags/sd.svg
  SvgGenImage get sd => const SvgGenImage('assets/svg/countries-flags/sd.svg');

  /// File path: assets/svg/countries-flags/se.svg
  SvgGenImage get se => const SvgGenImage('assets/svg/countries-flags/se.svg');

  /// File path: assets/svg/countries-flags/sg.svg
  SvgGenImage get sg => const SvgGenImage('assets/svg/countries-flags/sg.svg');

  /// File path: assets/svg/countries-flags/sh.svg
  SvgGenImage get sh => const SvgGenImage('assets/svg/countries-flags/sh.svg');

  /// File path: assets/svg/countries-flags/si.svg
  SvgGenImage get si => const SvgGenImage('assets/svg/countries-flags/si.svg');

  /// File path: assets/svg/countries-flags/sj.svg
  SvgGenImage get sj => const SvgGenImage('assets/svg/countries-flags/sj.svg');

  /// File path: assets/svg/countries-flags/sk.svg
  SvgGenImage get sk => const SvgGenImage('assets/svg/countries-flags/sk.svg');

  /// File path: assets/svg/countries-flags/sl.svg
  SvgGenImage get sl => const SvgGenImage('assets/svg/countries-flags/sl.svg');

  /// File path: assets/svg/countries-flags/sm.svg
  SvgGenImage get sm => const SvgGenImage('assets/svg/countries-flags/sm.svg');

  /// File path: assets/svg/countries-flags/sn.svg
  SvgGenImage get sn => const SvgGenImage('assets/svg/countries-flags/sn.svg');

  /// File path: assets/svg/countries-flags/so.svg
  SvgGenImage get so => const SvgGenImage('assets/svg/countries-flags/so.svg');

  /// File path: assets/svg/countries-flags/sr.svg
  SvgGenImage get sr => const SvgGenImage('assets/svg/countries-flags/sr.svg');

  /// File path: assets/svg/countries-flags/ss.svg
  SvgGenImage get ss => const SvgGenImage('assets/svg/countries-flags/ss.svg');

  /// File path: assets/svg/countries-flags/st.svg
  SvgGenImage get st => const SvgGenImage('assets/svg/countries-flags/st.svg');

  /// File path: assets/svg/countries-flags/sv.svg
  SvgGenImage get sv => const SvgGenImage('assets/svg/countries-flags/sv.svg');

  /// File path: assets/svg/countries-flags/sx.svg
  SvgGenImage get sx => const SvgGenImage('assets/svg/countries-flags/sx.svg');

  /// File path: assets/svg/countries-flags/sy.svg
  SvgGenImage get sy => const SvgGenImage('assets/svg/countries-flags/sy.svg');

  /// File path: assets/svg/countries-flags/sz.svg
  SvgGenImage get sz => const SvgGenImage('assets/svg/countries-flags/sz.svg');

  /// File path: assets/svg/countries-flags/ta.svg
  SvgGenImage get ta => const SvgGenImage('assets/svg/countries-flags/ta.svg');

  /// File path: assets/svg/countries-flags/tc.svg
  SvgGenImage get tc => const SvgGenImage('assets/svg/countries-flags/tc.svg');

  /// File path: assets/svg/countries-flags/td.svg
  SvgGenImage get td => const SvgGenImage('assets/svg/countries-flags/td.svg');

  /// File path: assets/svg/countries-flags/tf.svg
  SvgGenImage get tf => const SvgGenImage('assets/svg/countries-flags/tf.svg');

  /// File path: assets/svg/countries-flags/tg.svg
  SvgGenImage get tg => const SvgGenImage('assets/svg/countries-flags/tg.svg');

  /// File path: assets/svg/countries-flags/th.svg
  SvgGenImage get th => const SvgGenImage('assets/svg/countries-flags/th.svg');

  /// File path: assets/svg/countries-flags/tj.svg
  SvgGenImage get tj => const SvgGenImage('assets/svg/countries-flags/tj.svg');

  /// File path: assets/svg/countries-flags/tk.svg
  SvgGenImage get tk => const SvgGenImage('assets/svg/countries-flags/tk.svg');

  /// File path: assets/svg/countries-flags/tl.svg
  SvgGenImage get tl => const SvgGenImage('assets/svg/countries-flags/tl.svg');

  /// File path: assets/svg/countries-flags/tm.svg
  SvgGenImage get tm => const SvgGenImage('assets/svg/countries-flags/tm.svg');

  /// File path: assets/svg/countries-flags/tn.svg
  SvgGenImage get tn => const SvgGenImage('assets/svg/countries-flags/tn.svg');

  /// File path: assets/svg/countries-flags/to.svg
  SvgGenImage get to => const SvgGenImage('assets/svg/countries-flags/to.svg');

  /// File path: assets/svg/countries-flags/tr.svg
  SvgGenImage get tr => const SvgGenImage('assets/svg/countries-flags/tr.svg');

  /// File path: assets/svg/countries-flags/tt.svg
  SvgGenImage get tt => const SvgGenImage('assets/svg/countries-flags/tt.svg');

  /// File path: assets/svg/countries-flags/tv.svg
  SvgGenImage get tv => const SvgGenImage('assets/svg/countries-flags/tv.svg');

  /// File path: assets/svg/countries-flags/tw.svg
  SvgGenImage get tw => const SvgGenImage('assets/svg/countries-flags/tw.svg');

  /// File path: assets/svg/countries-flags/tz.svg
  SvgGenImage get tz => const SvgGenImage('assets/svg/countries-flags/tz.svg');

  /// File path: assets/svg/countries-flags/ua.svg
  SvgGenImage get ua => const SvgGenImage('assets/svg/countries-flags/ua.svg');

  /// File path: assets/svg/countries-flags/ug.svg
  SvgGenImage get ug => const SvgGenImage('assets/svg/countries-flags/ug.svg');

  /// File path: assets/svg/countries-flags/um.svg
  SvgGenImage get um => const SvgGenImage('assets/svg/countries-flags/um.svg');

  /// File path: assets/svg/countries-flags/un.svg
  SvgGenImage get un => const SvgGenImage('assets/svg/countries-flags/un.svg');

  /// File path: assets/svg/countries-flags/us.svg
  SvgGenImage get us => const SvgGenImage('assets/svg/countries-flags/us.svg');

  /// File path: assets/svg/countries-flags/uy.svg
  SvgGenImage get uy => const SvgGenImage('assets/svg/countries-flags/uy.svg');

  /// File path: assets/svg/countries-flags/uz.svg
  SvgGenImage get uz => const SvgGenImage('assets/svg/countries-flags/uz.svg');

  /// File path: assets/svg/countries-flags/va.svg
  SvgGenImage get va => const SvgGenImage('assets/svg/countries-flags/va.svg');

  /// File path: assets/svg/countries-flags/vc.svg
  SvgGenImage get vc => const SvgGenImage('assets/svg/countries-flags/vc.svg');

  /// File path: assets/svg/countries-flags/ve.svg
  SvgGenImage get ve => const SvgGenImage('assets/svg/countries-flags/ve.svg');

  /// File path: assets/svg/countries-flags/vg.svg
  SvgGenImage get vg => const SvgGenImage('assets/svg/countries-flags/vg.svg');

  /// File path: assets/svg/countries-flags/vi.svg
  SvgGenImage get vi => const SvgGenImage('assets/svg/countries-flags/vi.svg');

  /// File path: assets/svg/countries-flags/vn.svg
  SvgGenImage get vn => const SvgGenImage('assets/svg/countries-flags/vn.svg');

  /// File path: assets/svg/countries-flags/vu.svg
  SvgGenImage get vu => const SvgGenImage('assets/svg/countries-flags/vu.svg');

  /// File path: assets/svg/countries-flags/wf.svg
  SvgGenImage get wf => const SvgGenImage('assets/svg/countries-flags/wf.svg');

  /// File path: assets/svg/countries-flags/ws.svg
  SvgGenImage get ws => const SvgGenImage('assets/svg/countries-flags/ws.svg');

  /// File path: assets/svg/countries-flags/xk.svg
  SvgGenImage get xk => const SvgGenImage('assets/svg/countries-flags/xk.svg');

  /// File path: assets/svg/countries-flags/xx.svg
  SvgGenImage get xx => const SvgGenImage('assets/svg/countries-flags/xx.svg');

  /// File path: assets/svg/countries-flags/ye.svg
  SvgGenImage get ye => const SvgGenImage('assets/svg/countries-flags/ye.svg');

  /// File path: assets/svg/countries-flags/yt.svg
  SvgGenImage get yt => const SvgGenImage('assets/svg/countries-flags/yt.svg');

  /// File path: assets/svg/countries-flags/za.svg
  SvgGenImage get za => const SvgGenImage('assets/svg/countries-flags/za.svg');

  /// File path: assets/svg/countries-flags/zm.svg
  SvgGenImage get zm => const SvgGenImage('assets/svg/countries-flags/zm.svg');

  /// File path: assets/svg/countries-flags/zw.svg
  SvgGenImage get zw => const SvgGenImage('assets/svg/countries-flags/zw.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
        ac,
        ad,
        ae,
        af,
        ag,
        ai,
        al,
        am,
        an,
        ao,
        aq,
        ar,
        as,
        at,
        au,
        aw,
        ax,
        az,
        ba,
        bb,
        bd,
        be,
        bf,
        bg,
        bh,
        bi,
        bj,
        bl,
        bm,
        bn,
        bo,
        bq,
        br,
        bs,
        bt,
        bv,
        bw,
        by,
        bz,
        ca,
        cc,
        cd,
        cefta,
        cf,
        cg,
        ch,
        ci,
        ck,
        cl,
        cm,
        cn,
        co,
        cp,
        cr,
        cu,
        cv,
        cw,
        cx,
        cy,
        cz,
        de,
        dg,
        dj,
        dk,
        dm,
        doX,
        dz,
        ea,
        ec,
        ee,
        eg,
        eh,
        er,
        esCt,
        esGa,
        esPv,
        es,
        et,
        eu,
        fi,
        fj,
        fk,
        fm,
        fo,
        fr,
        ga,
        gbEng,
        gbNir,
        gbSct,
        gbWls,
        gb,
        gd,
        ge,
        gf,
        gg,
        gh,
        gi,
        gl,
        gm,
        gn,
        gp,
        gq,
        gr,
        gs,
        gt,
        gu,
        gw,
        gy,
        hk,
        hm,
        hn,
        hr,
        ht,
        hu,
        ic,
        id,
        ie,
        il,
        im,
        inX,
        io,
        iq,
        ir,
        isX,
        it,
        je,
        jm,
        jo,
        jp,
        ke,
        kg,
        kh,
        ki,
        km,
        kn,
        kp,
        kr,
        kw,
        ky,
        kz,
        la,
        lb,
        lc,
        li,
        lk,
        lr,
        ls,
        lt,
        lu,
        lv,
        ly,
        ma,
        mc,
        md,
        me,
        mf,
        mg,
        mh,
        mk,
        ml,
        mm,
        mn,
        mo,
        mp,
        mq,
        mr,
        ms,
        mt,
        mu,
        mv,
        mw,
        mx,
        my,
        mz,
        na,
        nc,
        ne,
        nf,
        ng,
        ni,
        nl,
        no,
        np,
        nr,
        nu,
        nz,
        om,
        pa,
        pe,
        pf,
        pg,
        ph,
        pk,
        pl,
        pm,
        pn,
        pr,
        ps,
        pt,
        pw,
        py,
        qa,
        re,
        ro,
        rs,
        ru,
        rw,
        sa,
        sb,
        sc,
        sd,
        se,
        sg,
        sh,
        si,
        sj,
        sk,
        sl,
        sm,
        sn,
        so,
        sr,
        ss,
        st,
        sv,
        sx,
        sy,
        sz,
        ta,
        tc,
        td,
        tf,
        tg,
        th,
        tj,
        tk,
        tl,
        tm,
        tn,
        to,
        tr,
        tt,
        tv,
        tw,
        tz,
        ua,
        ug,
        um,
        un,
        us,
        uy,
        uz,
        va,
        vc,
        ve,
        vg,
        vi,
        vn,
        vu,
        wf,
        ws,
        xk,
        xx,
        ye,
        yt,
        za,
        zm,
        zw
      ];
}

class $AssetsSvgCurvedLightGen {
  const $AssetsSvgCurvedLightGen();

  /// File path: assets/svg/curved-light/Location.svg
  SvgGenImage get location =>
      const SvgGenImage('assets/svg/curved-light/Location.svg');

  /// File path: assets/svg/curved-light/Shield-Done.svg
  SvgGenImage get shieldDone =>
      const SvgGenImage('assets/svg/curved-light/Shield-Done.svg');

  /// List of all assets
  List<SvgGenImage> get values => [location, shieldDone];
}

class $AssetsSvgRegularLightGen {
  const $AssetsSvgRegularLightGen();

  /// File path: assets/svg/regular-light/Document.svg
  SvgGenImage get document =>
      const SvgGenImage('assets/svg/regular-light/Document.svg');

  /// List of all assets
  List<SvgGenImage> get values => [document];
}

class Assets {
  const Assets._();

  static const String aEnv = '.env';
  static const $AssetsFontsGen fonts = $AssetsFontsGen();
  static const $AssetsGifGen gif = $AssetsGifGen();
  static const $AssetsHavenGen haven = $AssetsHavenGen();
  static const $AssetsJavascriptGen javascript = $AssetsJavascriptGen();
  static const $AssetsJsonGen json = $AssetsJsonGen();
  static const $AssetsPngGen png = $AssetsPngGen();
  static const $AssetsSvgGen svg = $AssetsSvgGen();

  /// List of all assets
  static List<String> get values => [aEnv];
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  });

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class SvgGenImage {
  const SvgGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  }) : _isVecFormat = false;

  const SvgGenImage.vec(
    this._assetName, {
    this.size,
    this.flavors = const {},
  }) : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter: colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
