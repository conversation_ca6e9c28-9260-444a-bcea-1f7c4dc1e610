import 'package:dio/dio.dart';
import 'package:nestcoinco_core_crypto_public_api/nestcoinco_core_crypto_public_api.dart';
import 'package:nestcoinco_core_data_encryptor/nestcoinco_core_data_encryptor.dart';
import 'package:nestcoinco_onboard_api_gateway/nestcoinco_onboard_api_gateway.dart';
import 'package:nestcoinco_onboard_api_gateway_lite/nestcoinco_onboard_api_gateway_lite.dart';
import 'package:nestcoinco_onboard_bridge_integration/nestcoinco_onboard_bridge_integration.dart';
import 'package:nestcoinco_onboard_defi_bridge/nestcoinco_onboard_defi_bridge.dart';
import 'package:onboard_wallet/app/app.logger.dart';
import 'package:onboard_wallet/flavors.dart';

import 'interceptors.dart';

BaseOptions _baseOption(String baseUrl) => BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(milliseconds: 95000),
      receiveTimeout: const Duration(milliseconds: 95000),
      headers: {
        'Content-Type': 'application/json',
        'Accept-Encoding': 'gzip, deflate',
      },
    );

List<Interceptor> get _interceptors => [
      LogInterceptor(
          requestBody: F.isStaging,
          responseBody: F.isStaging,
          logPrint: (object) {
            getLogger('API_LOG').i(object);
          }),
      ExtraFieldsInterceptor(),
      AuthorizationTokenInjector(),
      UnAuthorizedErrorHandler(),
    ];

NestcoincoOnboardApiGateway get getOnboardApiGateway =>
    NestcoincoOnboardApiGateway(
      dio: Dio(
        _baseOption(F.onboardApiBaseUrl),
      ),
      basePathOverride: F.onboardApiBaseUrl,
      interceptors: _interceptors,
    );

NestcoincoOnboardApiGateway getOnboardApiGatewayClient(
    {bool handleUnauthorisedError = true}) {
  return NestcoincoOnboardApiGateway(
    dio: Dio(
      _baseOption(F.onboardApiBaseUrl),
    ),
    basePathOverride: F.onboardApiBaseUrl,
    interceptors: [
      LogInterceptor(
          requestBody: F.isStaging,
          responseBody: F.isStaging,
          logPrint: (object) {
            getLogger('API_LOG').i(object);
          }),
      AuthorizationTokenInjector(),
      if (handleUnauthorisedError) ...[
        UnAuthorizedErrorHandler(),
      ]
    ],
  );
}

NestcoincoCoreCryptoPublicApi get getCoreCryptoPublicApi =>
    NestcoincoCoreCryptoPublicApi(
      dio: Dio(
        _baseOption(F.coreCryptoApiBaseUrl),
      ),
      basePathOverride: F.coreCryptoApiBaseUrl,
      interceptors: _interceptors,
    );

NestcoincoCoreDataEncryptor get getCoreDataEncryptorPublicApi =>
    NestcoincoCoreDataEncryptor(
      dio: Dio(
        _baseOption(F.coreDataEncryptorBaseUrl),
      ),
      basePathOverride: F.coreDataEncryptorBaseUrl,
      interceptors: [
        LogInterceptor(requestBody: F.isStaging, responseBody: F.isStaging),
      ],
    );

NestcoincoOnboardDefiBridge get getOnboardDefiBridge =>
    NestcoincoOnboardDefiBridge(
      dio: Dio(
        _baseOption(F.onboardDefiBridgeBaseUrl),
      ),
      basePathOverride: F.onboardDefiBridgeBaseUrl,
      interceptors: _interceptors,
    );

NestcoincoOnboardBridgeIntegration get getBridgeIntegration =>
    NestcoincoOnboardBridgeIntegration(
      dio: Dio(
        _baseOption(F.bridgeIntegrationBaseUrl),
      ),
      basePathOverride: F.bridgeIntegrationBaseUrl,
      interceptors: _interceptors,
    );

NestcoincoOnboardApiGatewayLite get getOnboardLiteApiGateway =>
    NestcoincoOnboardApiGatewayLite(
      dio: Dio(
        _baseOption(F.havenBaseUrl),
      ),
      basePathOverride: F.havenBaseUrl,
      interceptors: _interceptors,
    );

BlockchainGatewayConfigsClient get getBlockchainGatewayConfigsClient =>
    getOnboardApiGateway.getBlockchainGatewayConfigsClient();
