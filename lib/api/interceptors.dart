import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:onboard_wallet/services/services.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:stacked_services/stacked_services.dart';

import '../app/index.dart';
import '../constants/constants.dart';

class AuthorizationTokenInjector extends Interceptor {
  static const authKey = 'x-auth-token';
  static const sessionKey = 'x-session-id';

  @override
  void onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    final SecureStorageService secureStorageService =
        locator<SecureStorageService>();
    String? token = await secureStorageService.read(key: authTokenKey);
    if (token != null && options.headers[authKey] == null) {
      options.headers[authKey] = token;
    }

    // Add current session ID to track requests
    if (UnAuthorizedErrorHandler._currentSessionId != null) {
      options.extra[sessionKey] = UnAuthorizedErrorHandler._currentSessionId;
    }

    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    final buildNumber = packageInfo.buildNumber;
    options.headers['x-app-build'] = buildNumber;
    options.headers['x-app-id'] = packageInfo.packageName;
    locator<AuthenticationService>().checkAndRefreshToken();
    super.onRequest(options, handler);
  }
}

class UnAuthorizedErrorHandler extends Interceptor {
  Dio dio = Dio();
  static const refreshTokenPath = "/users/authentication/refresh";
  static const passkeyRegistrationPath =
      "/users/authentication/passkeys/registration";

  // Track the current session to ignore errors from previous sessions
  static String? _currentSessionId;

  static void updateSessionId(String? sessionId) {
    _currentSessionId = sessionId;
  }

  static void clearSession() {
    _currentSessionId = null;
  }

  static String? get currentSessionId => _currentSessionId;

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    // Check if this request belongs to the current session
    final requestSessionId =
        err.requestOptions.extra[AuthorizationTokenInjector.sessionKey];
    if (requestSessionId != null && requestSessionId != _currentSessionId) {
      // This request is from a previous session, ignore the error
      getLogger('UnAuthorizedErrorHandler').d(
          'Ignoring error from previous session: $requestSessionId vs current: $_currentSessionId');
      handler.next(err);
      return;
    }

    final AuthenticationService authenticationService =
        locator<AuthenticationService>();
    var loginState = await authenticationService.isLoggedIn();
    final path = err.requestOptions.uri.path;
    if (err.response?.statusCode == 401 &&
        loginState &&
        path != refreshTokenPath &&
        path != passkeyRegistrationPath) {
      /// Whatever error handling for 401 decided with backend
      /// Handle logout
      ///
      try {
        final refreshResponse = await authenticationService.refreshAuthToken();
        refreshResponse.when(success: (token) {
          if (token != null) {
            _completeRequest(err, handler, token);
          } else {
            _manualAuth(err, handler);
          }
        }, failure: (failure) {
          _manualAuth(err, handler);
        });
      } on Exception catch (_) {}
    } else {
      handler.next(err);
    }
  }

  _completeRequest(DioException err, ErrorInterceptorHandler handler,
      String accessToken) async {
    RequestOptions requestOptions = err.requestOptions;
    final opts = Options(method: requestOptions.method);
    dio.options.headers[AuthorizationTokenInjector.authKey] = accessToken;
    dio.options.headers["Accept"] = "*/*";

    try {
      final response = await dio.request(
        requestOptions.uri.toString(),
        options: opts,
        cancelToken: requestOptions.cancelToken,
        onReceiveProgress: requestOptions.onReceiveProgress,
        data: requestOptions.data,
        queryParameters: requestOptions.queryParameters,
        onSendProgress: requestOptions.onSendProgress,
      );
      handler.resolve(response);
    } catch (e) {
      _manualAuth(err, handler);
    }
  }

  _manualAuth(DioException err, ErrorInterceptorHandler handler) async {
    final AuthenticationService authenticationService =
        locator<AuthenticationService>();
    await authenticationService.onboardLogout();
    final NavigationService navigationService = locator<NavigationService>();
    final userService = locator<UserService>();
    String? email = userService.getCurrentUser()?.email;

    if (email == null) {
      return;
    }

    //todo: this needs to be updated to include otp refresh.
    navigationService.replaceWith(
      Routes.setEmailView,
      arguments: SetEmailViewArguments(
        initialEmail: email,
      ),
    );
    super.onError(err, handler);
  }
}

class ExtraFieldsInterceptor extends Interceptor {
  // List of paths that support additional properties
  final List<String> supportedPaths = ['/authorizations/sessions'];

  @override
  void onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    final isPathSupported =
        supportedPaths.any((path) => options.path.endsWith(path));

    if (isPathSupported && options.extra.containsKey('_extraFields')) {
      try {
        // Get the extra fields from the request
        final extraFields =
            options.extra['_extraFields'] as Map<String, dynamic>?;

        if (extraFields != null && extraFields.isNotEmpty) {
          if (options.data is Map<String, dynamic>) {
            getLogger(toString())
                .d("Adding extra fields to ${options.path}: $extraFields");

            final data = options.data as Map<String, dynamic>;
            if (data.containsKey("details") && data["details"] is Map) {
              (data['details'] as Map<String, dynamic>).addAll(extraFields);
            }

            getLogger(toString()).d(
                "Final Request after addition ${options.path}: ${options.data.toString()}");
          } else if (options.data is String) {
            final Map<String, dynamic> jsonData =
                jsonDecode(options.data as String);
            if (jsonData.containsKey('details') && jsonData['details'] is Map) {
              (jsonData['details'] as Map<String, dynamic>).addAll(extraFields);
              options.data = jsonEncode(jsonData);
            }
          }
        }
      } catch (e) {
        getLogger(toString()).e(e.toString());
      }
    }
    super.onRequest(options, handler);
  }
}
