# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/
*.properties
*.keystore
*.jks
android/onboard-wallet

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

/ios/build/ios/


# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/.cxx
/android/app/release
/android/app/src/main/assets
/ios/Runner/smile_config.json
/ios/build/


.env
.aiexclude
.vscode
android/app/build.gradle


# FVM Version Cache
.fvm/