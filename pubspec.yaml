name: onboard_wallet
description: An app build with the stacked framework

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 3.0.0+840

environment:
  sdk: ">=3.2.0 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2

  stacked: ^3.4.3
  stacked_services: ^1.5.1
  flutter_flavorizr: ^2.2.3
  flutter_screenutil: ^5.9.0
  flutter_svg: ^2.0.10+1
  dotted_decoration: 2.0.0
  shimmer: ^3.0.0
  freezed: ^2.2.1
  json_annotation: ^4.9.0
  keyboard_dismisser: ^3.0.0
  flutter_secure_storage: ^9.2.2
  logger: ^2.2.0
  shared_preferences: ^2.2.3
  animations: ^2.0.7
  intl: 0.19.0
  firebase_core: ^3.4.0
  firebase_messaging: ^15.1.0
  firebase_crashlytics: ^4.1.0
  firebase_analytics: ^11.3.0
  flutter_inappwebview: ^6.0.0
  flutter_dotenv: ^5.0.2
  web3dart: 2.7.3
  bip39: ^1.0.6
  eth_sig_util: 0.0.9
  enhance_expansion_panel: ^0.7.0
  blurrycontainer: ^2.1.0
  web3auth_flutter: 5.0.4
  flutter_locker: 2.1.6
  encrypt: ^5.0.1
  sealed_annotations: ^1.13.0
  flutter_easyloading: ^3.0.5
  url_launcher: ^6.1.7
  queue: ^3.1.0+2
  email_validator: ^3.0.0
  pin_code_fields: ^8.0.1
  nestcoinco_onboard_api_gateway:
    git:
      url: **************:NestcoinCo/flutter-artifacts.git
      path: nestcoinco_onboard_api_gateway
  nestcoinco_core_data_encryptor:
    git:
      url: **************:NestcoinCo/flutter-artifacts.git
      path: nestcoinco_core_data_encryptor
  nestcoinco_onboard_defi_bridge:
    git:
      url: **************:NestcoinCo/flutter-artifacts.git
      path: nestcoinco_onboard_defi_bridge
  nestcoinco_core_crypto_public_api:
    git:
      url: **************:NestcoinCo/flutter-artifacts.git
      path: nestcoinco_core_crypto_public_api
  nestcoinco_onboard_bridge_integration:
    git:
      url: **************:NestcoinCo/flutter-artifacts.git
      path: nestcoinco_onboard_bridge_integration
  permission_handler: ^11.3.1
  sliver_tools: ^0.2.12
  extended_text: ^14.1.0
  modal_bottom_sheet: ^3.0.0
  qr_flutter: ^4.1.0
  share_plus: ^10.0.0
  hive_flutter: 1.1.0
  cached_network_image: ^3.4.0
  flutter_windowmanager_plus: ^1.0.1
  jwt_decoder: 2.0.1
  flutter_local_notifications: 17.2.2
  ethereum_addresses: ^1.0.2
  bip32: ^2.0.0
  lottie: ^3.1.2
  fluttertoast: ^8.2.12
  tuple: ^2.0.1
  dotted_border: ^2.0.0+3
  flutter_idensic_mobile_sdk_plugin: ^1.35.1
  debounce_throttle: ^2.0.0
  decimal: ^3.0.2
  package_info_plus: 8.1.2
  appsflyer_sdk: ^6.15.2
  just_the_tooltip: ^0.0.12
  flutter_slidable: ^3.1.2
  firebase_remote_config: ^5.1.0
  badges: ^3.1.2
  firebase_dynamic_links: ^6.0.5
  dotted_line: ^3.2.2
  uuid: ^4.4.0
  smile_id: 10.3.2
  crypton: ^2.1.0
  flutter_cupertino_date_picker_fork: 1.0.7
  convert: 3.1.2
  very_good_infinite_list: ^0.8.0
  firebase_auth: ^5.2.0
  cloud_firestore: 5.4.0
  infinite_scroll_pagination: 4.0.0
  flutter_native_splash: ^2.4.1
  device_info_plus: ^10.1.2
  screenshot_callback: ^3.0.1
  googleapis: ^13.2.0  # For Google Drive.
  googleapis_auth: ^1.6.0  # For Google Drive auth.
  google_sign_in: ^6.2.2 
  path_provider:
  confetti: ^0.7.0
  phone_numbers_parser: ^9.0.3
  country: ^4.0.0
  customer_io: ^1.5.2
  flutter_html: 3.0.0-beta.2
  in_app_review: ^2.0.8
  app_links: ^6.3.2
  flutter_multi_formatter: ^2.12.4
  flutter_custom_tab_bar: ^1.2.1
  fl_chart: ^0.69.0
  rxdart: ^0.28.0
  inner_shadow_widget: ^0.0.3
  equatable: ^2.0.5
  dio: ^5.4.3+1
  collection: ^1.18.0
  carousel_slider: ^5.0.0
  rfw: ^1.0.29
  firebase_storage: ^12.2.0
  readmore: ^3.0.0
  flutter_injected_web3:
    git:
      url: **************:NestcoinCo/flutter_injected_web3-main.git
  sleek_circular_slider: ^2.0.1
  currency_code_to_currency_symbol: ^0.0.4
  gal: ^2.3.0
  onboarding_overlay: ^3.2.2
  passkeys: 2.1.0
  reown_walletkit: 1.1.5+1
  segment_analytics:
    git:
      url: **************:NestcoinCo/analytics_flutter.git
      path: packages/core
  segment_analytics_plugin_appsflyer:
    git:
      url: **************:NestcoinCo/analytics_flutter.git
      ref: main
      path: packages/plugins/plugin_appsflyer
  ai_barcode_scanner: ^6.0.1
  figma_squircle_updated: 1.0.1
  persistent_bottom_nav_bar: ^6.2.1
  adaptive_dialog: ^2.4.0
  image_picker: ^1.1.2
  file_picker: ^8.3.2
  webview_flutter: ^4.10.0
  mime: ^2.0.0
  nestcoinco_onboard_api_gateway_lite:
    git:
      url: **************:NestcoinCo/flutter-artifacts.git
      path: nestcoinco_onboard_api_gateway_lite
  built_collection: ^5.1.1
  step_progress_indicator: ^1.0.2
  timelines: ^0.1.0
  local_auth: ^2.3.0
  flutter_custom_tabs: ^2.0.0+1
  phone_number: ^2.0.1
  cryptography: ^2.7.0
  crisp_chat: ^2.2.5

dependency_overrides: 
  js: ^0.7.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0
  build_runner: ^2.4.12
  analyzer: ^6.7.0
  stacked_generator: ^2.0.0-pre.1
  mockito: ^5.1.0
  json_serializable: ^6.6.2
  flutter_gen_runner: ^5.7.0
  hive_generator: 2.0.1
  sealed_generators:
    git:
      url: https://github.com/6thsolution/dart_sealed.git
      path: sealed_generators

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/json/
    - assets/svg/
    - assets/gif/
    - assets/fonts/
    - assets/javascript/
    - assets/png/
    - .env
    - assets/svg/countries-flags/
    - assets/svg/base-light/
    - assets/svg/bulk-light/
    - assets/svg/curved-light/
    - assets/svg/regular-light/
    - assets/haven/png/
    - assets/haven/svg/
    - assets/haven/json/

    # An image asset can refer to one or more resolution-specific "variants", see
    # https://flutter.dev/assets-and-images/#resolution-aware.

    # For details regarding adding assets from package dependencies, see
    # https://flutter.dev/assets-and-images/#from-packages

    # To add custom fonts to your application, add a fonts section here,
    # in this "flutter" section. Each entry in this list should have a
    # "family" key with the font family name, and a "fonts" key with a
    # list giving the asset and other descriptors for the font. For
    # example:
  fonts:
    - family: Satoshi
      fonts:
        - asset: assets/fonts/Satoshi/Satoshi-Light.otf
          weight: 300
        - asset: assets/fonts/Satoshi/Satoshi-LightItalic.otf
          weight: 300
          style: italic
        - asset: assets/fonts/Satoshi/Satoshi-Regular.otf
          weight: 400
        - asset: assets/fonts/Satoshi/Satoshi-Medium.otf
          weight: 500
        - asset: assets/fonts/Satoshi/Satoshi-Black.otf
          weight: 600
        - asset: assets/fonts/Satoshi/Satoshi-Bold.otf
          weight: 700
    - family: Euclid
      fonts:
        - asset: assets/fonts/EuclidCircularA/EuclidCircularA-Light.ttf
          weight: 300
        - asset: assets/fonts/EuclidCircularA/EuclidCircularA-LightItalic.ttf
          weight: 300
          style: italic
        - asset: assets/fonts/EuclidCircularA/EuclidCircularA-Regular.ttf
          weight: 400
        - asset: assets/fonts/EuclidCircularA/EuclidCircularA-Medium.ttf
          weight: 500
        - asset: assets/fonts/EuclidCircularA/EuclidCircularA-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/EuclidCircularA/EuclidCircularA-Bold.ttf
          weight: 700
    - family: SF-Pro-Text-Font-Family
      fonts:
        - asset: assets/fonts/SF-Pro-Text-Font-Family/SF-Pro-Text-Light.otf
          weight: 300
        - asset: assets/fonts/SF-Pro-Text-Font-Family/SF-Pro-Text-LightItalic.otf
          weight: 300
          style: italic
        - asset: assets/fonts/SF-Pro-Text-Font-Family/SF-Pro-Text-Regular.otf
          weight: 400
        - asset: assets/fonts/SF-Pro-Text-Font-Family/SF-Pro-Text-Medium.otf
          weight: 500
        - asset: assets/fonts/SF-Pro-Text-Font-Family/SF-Pro-Text-SemiBold.otf
          weight: 600
        - asset: assets/fonts/SF-Pro-Text-Font-Family/SF-Pro-Text-Bold.otf
          weight: 700
        - asset: assets/fonts/SF-Pro-Text-Font-Family/SF-Pro-Text-Heavy.otf
          weight: 800
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter/Inter-Light.otf
          weight: 300
        - asset: assets/fonts/Inter/Inter-LightItalic.otf
          weight: 300
          style: italic
        - asset: assets/fonts/Inter/Inter-Regular.otf
          weight: 400
        - asset: assets/fonts/Inter/Inter-Medium.otf
          weight: 500
        - asset: assets/fonts/Inter/Inter-SemiBold.otf
          weight: 600
        - asset: assets/fonts/Inter/Inter-Bold.otf
          weight: 700
        - asset: assets/fonts/Inter/Inter-Thin.otf
          weight: 800
    - family: Midnight
      fonts:
        - asset: assets/fonts/midnight-sans/trial/midnight-sans-rd-12-regular-trial.otf
          weight: 400
        - asset: assets/fonts/midnight-sans/trial/midnight-sans-rd-12-medium-trial.otf
          weight: 500
        - asset: assets/fonts/midnight-sans/trial/midnight-sans-rd-12-semibold-trial.otf
          weight: 600
        - asset: assets/fonts/midnight-sans/trial/midnight-sans-rd-12-bold-trial.otf
          weight: 700
        - asset: assets/fonts/midnight-sans/trial/midnight-sans-rd-12-heavy-trial.otf
          weight: 800
    - family: FeatureDisplayFamily
      fonts:
        - asset: assets/fonts/Feature_Display_Family/FeatureDisplay-Light-Trial.otf
          weight: 300
        - asset: assets/fonts/Feature_Display_Family/FeatureDisplay-LightItalic-Trial.otf
          weight: 300
          style: italic
        - asset: assets/fonts/Feature_Display_Family/FeatureDisplay-Regular-Trial.otf
          weight: 400
        - asset: assets/fonts/Feature_Display_Family/FeatureDisplay-Medium-Trial.otf
          weight: 500
        - asset: assets/fonts/Feature_Display_Family/FeatureDisplay-Bold-Trial.otf
          weight: 700



  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
flutter_intl:
  enabled: true

flutter_gen:
  output: lib/gen/ # Optional (default: lib/gen/)
  line_length: 80 # Optional (default: 80)

  integrations:
    flutter_svg: true

flavorizr:
  app:
    android:
      flavorDimensions: "flavor-type"
    ios:
  flavors:
    prod:
      app:
        name: "Onboard Wallet"

      android:
        applicationId: "com.onboard.wallet"
        firebase:
          config: "firebase/prod/google-services.json"

      ios:
        bundleId: "com.onboard.wallet"
        firebase:
          config: "firebase/prod/GoogleService-Info.plist"

    stage:
      app:
        name: "Onboard Wallet Stage"

      android:
        applicationId: "com.onboard.wallet.stage"
        firebase:
          config: "firebase/stage/google-services.json"

      ios:
        bundleId: "com.onboard.wallet.stage"
        firebase:
          config: "firebase/stage/GoogleService-Info.plist"
